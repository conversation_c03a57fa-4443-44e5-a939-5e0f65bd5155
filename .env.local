# 日志配置
LOG_LEVEL=info

# Database
DATABASE_URL="postgresql://samrise:samrise123456@localhost:5432/vgen?schema=public"
# ... if you use Supabase
DIRECT_URL=""

# Site url
#NEXT_PUBLIC_SITE_URL="http://localhost:3000"
NEXT_PUBLIC_SITE_URL=https://test.connectionshinttoday.tips/

# Authentication
BETTER_AUTH_SECRET="KqU4XfzwRiqdg2ozsfTGC4RmhNAq7k2q"
# ... for Github
GITHUB_CLIENT_ID="********************"
GITHUB_CLIENT_SECRET="2c799b6c204bfd7f5666359a31dd645bb0cec5b6"
# ... for Google
GOOGLE_CLIENT_ID="509425877611-bu98ikhncv6u8c7ososdd0pgd3o3g6fo.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"

# Mails
# ... with nodemailer
MAIL_HOST=""
MAIL_PORT=""
MAIL_USER=""
MAIL_PASS=""
# ... with Plunk
PLUNK_API_KEY=""
# ... with Resend
RESEND_API_KEY="re_XMcrkAr9_4hiioutv8dREp2MLkCffmhaw"
RESEND_AUDIENCE_ID=4f69c3cf-9655-4095-9922-7ae0b46b76f3
# ... with Postmark
POSTMARK_SERVER_TOKEN=""
# ... with Mailgun
MAILGUN_API_KEY=""
MAILGUN_DOMAIN=""

# Payments
# ... with Lemonsqueezy
LEMONSQUEEZY_API_KEY=""
LEMONSQUEEZY_WEBHOOK_SECRET=""
LEMONSQUEEZY_STORE_ID=""
# ... with Stripe
STRIPE_SECRET_KEY="sk_test_51OpinTCijGWUYDM7q2R68DYErVUDcF1XO2qFh0iof9i2JUzGF0XZ1mLAT6hdajsEF74ycTe8FwBq2MdQih3rZsd400oZ23yo1b"
STRIPE_WEBHOOK_SECRET="whsec_VqDwdA3dQHykEcZf61Fy8pxkeXyRBtHW"
# ... with Creem
CREEM_API_KEY=""
CREEM_WEBHOOK_SECRET=""
# ... with Polar
POLAR_ACCESS_TOKEN=""
POLAR_WEBHOOK_SECRET=""

# Product price ids
NEXT_PUBLIC_PRICE_ID_PRO_MONTHLY="asdf"
NEXT_PUBLIC_PRICE_ID_PRO_YEARLY="asdf"
NEXT_PUBLIC_PRICE_ID_LIFETIME="asdf"

# Analytics
# ... for Pirsch
NEXT_PUBLIC_PIRSCH_CODE=""
# ... for Plausible
NEXT_PUBLIC_PLAUSIBLE_URL=""
# ... for Mixpanel
NEXT_PUBLIC_MIXPANEL_TOKEN=""
# ... for Google Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID="G-X1ET6315QZ88"

# Storage
S3_ACCESS_KEY_ID=76b1897e8369f088e5b740252710679f
S3_SECRET_ACCESS_KEY=6b7fab872511182800f7f1f576a9e0018697697d5b1db4448cb46b8ffa4cbcca
S3_ENDPOINT=https://a2e9175e60b90e5f0cb603fc05355193.r2.cloudflarestorage.com
S3_REGION=auto
NEXT_PUBLIC_AVATARS_BUCKET_NAME=avatars
NEXT_PUBLIC_MEDIA_BUCKET_NAME=fluxfly
NEXT_PUBLIC_VIDEO_CDN_BASE_URL=https://videocdn.fluxfly.ai

# AI
# ... with OpenAI
OPENAI_API_KEY=""

# Redis 配置

# Upstash Redis 配置
UPSTASH_REDIS_REST_URL=https://stirred-sparrow-22139.upstash.io

REDIS_URL=rediss://default:<EMAIL>:6379
UPSTASH_REDIS_TOKEN=AVZ7AAIjcDEwOGE1NzMxNWM4OWM0ZGVkYWIzY2RlMTA5MmJmY2ZjOXAxMA

 # Fal.ai API 配置
FAL_API_KEY=74951b3e-ca65-4c4b-8193-b4cd36fe7a26:e210ce805ec9534a8193504ef4138419

# Replicate API 配置
REPLICATE_API_TOKEN=****************************************
REPLICATE_WEBHOOK_SIGNING_KEY=whsec_nHvdRlBS8bYw3OtmWJ03Wi05HsbTuUx4
VIDEO_UPLOAD_STREAMING=true

#######################################
# Consumer Config
#######################################
# === 高负载优化配置 ===
WORKER_CONCURRENCY=8                    # 🚀 优化: 提升并发处理能力

# 健康检查配置 (高负载优化)
HEALTH_CHECK_INTERVAL=60                # ✅ 保持1分钟检查
REDIS_POOL_SIZE=8                       # 🚀 优化: 增加连接池
CONFIG_CHECK_INTERVAL=600000            # 🎯 保持10分钟检查配置
REDIS_POOL_CHECK_INTERVAL=120000        # 🎯 保持2分钟检查Redis池

# 流式上传配置 (高负载优化)
S3_POOL_SIZE=8                          # 🚀 优化: 匹配8个Worker并发
ENABLE_STREAMING_UPLOAD=true            # ✅ 保持流式上传优势
S3_HEALTH_CHECK_INTERVAL=300000         # 🎯 MVP: 5分钟检查S3健康
S3_MAX_RETRIES=3                        # ✅ 保持重试次数

# 批处理配置 (高负载优化)
BATCH_MAX_SIZE=20                       # 🚀 优化: 增加批次大小
BATCH_MAX_CONCURRENCY=8                 # 🚀 优化: 匹配Worker并发
BATCH_PRIORITIZE_SMALL_FILES=true       # ✅ 保持小文件优先

# === 高负载单实例配置 ===
# BullMQ 防重复配置 (高负载优化)
BULLMQ_REMOVE_ON_COMPLETE=100           # 🚀 优化: 增加任务保留
BULLMQ_REMOVE_ON_FAIL=50                # 🚀 优化: 增加失败任务保留
BULLMQ_STALLED_INTERVAL=30000           # ⏰ 停滞检查间隔(30秒)
BULLMQ_MAX_STALLED_COUNT=1              # 🔄 最大停滞次数

# 分布式锁配置 (为扩展准备)
DISTRIBUTED_LOCK_TTL=120000             # ✅ 分布式锁TTL(2分钟)
DUPLICATE_CHECK_ENABLED=true            # ✅ 启用重复检查 (扩展必需)
INSTANCE_ID=video-generator-prod-001    # 🚀 生产实例标识

# 监控配置 (MVP简化)
DUPLICATE_MONITORING_ENABLED=true       # 📊 启用基础监控
DUPLICATE_ALERT_THRESHOLD=20            # 🚨 MVP: 提高告警阈值