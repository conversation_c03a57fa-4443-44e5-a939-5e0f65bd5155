# Auto detect text files and perform LF normalization
* text=auto

# Force LF for these file types
*.js text eol=lf
*.jsx text eol=lf
*.ts text eol=lf
*.tsx text eol=lf
*.json text eol=lf
*.md text eol=lf
*.mdx text eol=lf
*.yml text eol=lf
*.yaml text eol=lf
*.prisma text eol=lf
*.sql text eol=lf
*.css text eol=lf
*.scss text eol=lf
*.html text eol=lf
*.xml text eol=lf
*.cjs text eol=lf
*.npmrc text eol=lf
*.svg text eol=lf
*.sh text eol=lf
*.editorconfig text eol=lf

# Keep CRLF for Windows specific files
*.bat text eol=crlf
*.cmd text eol=crlf

# Binary files
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.woff binary
*.woff2 binary
*.ttf binary
*.eot binary
*.pdf binary
*.zip binary