# Design Document

## Overview

本设计将改造现有的 SampleVideoCard 组件，使其支持根据不同页面和功能模块显示不同的样本视频。设计将采用配置驱动的方式，既支持直接传入视频源，也支持通过配置文件管理样本视频。

## Architecture

### 配置层次结构
1. **全局配置** - 在 `config/index.ts` 中定义默认样本视频配置
2. **组件属性** - 通过 props 直接传入视频配置，优先级最高
3. **页面上下文** - 根据页面类型自动选择相应的样本视频

### 组件设计模式
采用"配置优先，属性覆盖"的设计模式：
- 默认从全局配置读取样本视频
- 支持通过 props 覆盖特定配置
- 提供类型安全的配置接口

## Components and Interfaces

### 1. 样本视频配置接口

```typescript
interface SampleVideoConfig {
  src: string;
  title?: string;
  description?: string;
  poster?: string;
  maxHeight?: string;
  autoPlay?: boolean;
  muted?: boolean;
}

interface SampleVideosByPage {
  'image-to-video': SampleVideoConfig[];
  'text-to-video': SampleVideoConfig[];
  'video-enhancement': SampleVideoConfig[];
  default: SampleVideoConfig[];
}
```

### 2. 增强的 SampleVideoCard 组件

```typescript
interface SampleVideoCardProps {
  // 直接指定视频配置（优先级最高）
  videoConfig?: SampleVideoConfig;
  
  // 或者指定页面类型，自动选择配置
  pageType?: keyof SampleVideosByPage;
  
  // 传统的单个属性支持（向后兼容）
  videoSrc?: string;
  maxHeight?: string;
  className?: string;
  autoPlay?: boolean;
  muted?: boolean;
  
  // 新增属性
  showTitle?: boolean;
  showDescription?: boolean;
}
```

### 3. 配置管理器

```typescript
class SampleVideoConfigManager {
  static getVideoConfig(pageType: keyof SampleVideosByPage): SampleVideoConfig[];
  static getDefaultVideo(): SampleVideoConfig;
  static validateVideoConfig(config: SampleVideoConfig): boolean;
}
```

## Data Models

### 独立配置模块

参考 `config/vmodel/` 的结构，创建 `config/sample-video/` 目录：

**config/sample-video/types.ts**
```typescript
export interface SampleVideoConfig {
  src: string;
  title?: string;
  description?: string;
  poster?: string;
  maxHeight?: string;
  autoPlay?: boolean;
  muted?: boolean;
}

export interface SampleVideosByPage {
  'image-to-video': SampleVideoConfig[];
  'text-to-video': SampleVideoConfig[];
  'video-enhancement': SampleVideoConfig[];
  default: SampleVideoConfig[];
}

export interface SampleVideoConfiguration {
  videos: SampleVideosByPage;
}
```

**config/sample-video/index.ts**
```typescript
import type { SampleVideoConfiguration } from "./types";

export const sampleVideoConfig: SampleVideoConfiguration = {
  videos: {
    'image-to-video': [
      {
        src: '/videos/samples/image-to-video-demo.mp4',
        title: 'Image to Video Demo',
        description: 'See how static images transform into dynamic videos',
        poster: '/images/samples/image-to-video-poster.jpg',
        maxHeight: '400px',
        autoPlay: false,
        muted: true
      }
    ],
    'text-to-video': [
      {
        src: '/videos/samples/text-to-video-demo.mp4',
        title: 'Text to Video Demo',
        description: 'Watch text prompts come to life as videos',
        poster: '/images/samples/text-to-video-poster.jpg'
      }
    ],
    'video-enhancement': [],
    default: [
      {
        src: '/videos/sample.mp4',
        title: 'AI Video Generation',
        description: 'Experience the power of AI video generation'
      }
    ]
  }
};

export * from "./types";
```

**config/index.ts 集成**
```typescript
import { sampleVideoConfig } from "./sample-video";

export const config = {
  // ... 现有配置
  sampleVideos: sampleVideoConfig,
} as const satisfies Config;
```

## Error Handling

### 视频加载失败处理
1. **渐进式降级**：如果指定视频加载失败，自动尝试默认视频
2. **友好错误提示**：显示有意义的错误信息而不是空白区域
3. **日志记录**：记录视频加载失败的详细信息用于调试

### 配置验证
1. **运行时验证**：验证视频源URL的有效性
2. **类型安全**：通过TypeScript确保配置的类型正确性
3. **默认值处理**：为所有可选配置提供合理的默认值

## Testing Strategy

### 单元测试
1. **组件渲染测试**：验证不同配置下的组件渲染结果
2. **配置管理测试**：测试配置读取和验证逻辑
3. **错误处理测试**：测试各种错误场景的处理

### 集成测试
1. **页面级测试**：验证在不同页面中的组件行为
2. **配置加载测试**：测试从配置文件加载视频配置的流程
3. **向后兼容测试**：确保现有使用方式仍然正常工作

### 视觉回归测试
1. **样式一致性**：确保改造后的组件样式保持一致
2. **响应式布局**：验证在不同屏幕尺寸下的显示效果
3. **视频播放功能**：测试视频播放控制功能的正常工作