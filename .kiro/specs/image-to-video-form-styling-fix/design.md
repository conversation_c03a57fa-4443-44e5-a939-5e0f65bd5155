# 设计文档

## 概述

这个修复主要涉及调整 `VideoGenerationForm` 组件中底部区域的样式，使其与表单其他部分保持视觉一致性，并改善用户体验。

## 架构

修改将集中在 `VideoGenerationForm.tsx` 文件中，具体是底部固定区域的样式类。不需要修改组件的整体结构或逻辑。

## 组件和接口

### 受影响的组件

- `VideoGenerationForm` (`apps/web/modules/marketing/image-to-video/components/VideoGenerationForm.tsx`)

### 修改范围

- 底部区域的 CSS 类名
- 移除固定定位相关的样式
- 调整背景颜色以匹配表单主体

## 数据模型

无需修改数据模型，这是纯样式修复。

## 具体设计变更

### 当前实现问题

```tsx
{/* 底部固定区域 */}
<div className="sticky bottom-0 bg-gray-100 border-t border-gray-200 p-8 mt-auto">
```

### 设计解决方案

1. **移除固定定位**：删除 `sticky bottom-0` 类
2. **统一背景色**：将 `bg-gray-100` 改为 `bg-white` 或完全移除（继承父元素的白色背景）
3. **保持边框和间距**：保留 `border-t border-gray-200 p-8` 用于视觉分隔
4. **移除 mt-auto**：因为不再需要将元素推到底部

### 修改后的实现

```tsx
{/* 底部区域 */}
<div className="border-t border-gray-200 p-8">
```

## 错误处理

无需特殊错误处理，这是纯样式修改。

## 测试策略

### 视觉测试
1. 验证底部区域背景色与表单其他部分一致
2. 确认滚动行为正常，底部内容可以正常滚动
3. 检查不同屏幕尺寸下的表现

### 功能测试
1. 确认积分显示功能正常
2. 确认生成按钮点击功能正常
3. 验证表单提交流程未受影响

### 兼容性测试
1. 测试不同浏览器下的表现
2. 测试移动端和桌面端的响应式布局