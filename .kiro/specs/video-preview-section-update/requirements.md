# 需求文档

## 介绍

更新 VideoPreviewSection 组件，移除 FilterControls 组件的使用，并替换为包含标题和AI视频生成时间说明的新头部区域。

## 需求

### 需求 1

**用户故事：** 作为查看视频预览区域的用户，我希望看到清晰的标题和关于视频生成时间的有用信息，以便我了解在AI视频生成过程中应该期待什么。

#### 验收标准

1. 当 VideoPreviewSection 组件渲染时，系统应该显示一个 h2 标题，内容为 "Image To Video AI Generator Output Preview"
2. 当 VideoPreviewSection 组件渲染时，系统应该在标题下方显示描述文字 "AI Video generation usually takes 1 to 5 minutes, depending on the selected model and parameters."
3. 当 VideoPreviewSection 组件渲染时，系统不应该显示 FilterControls 组件
4. 当 VideoPreviewSection 组件渲染时，系统应该继续像之前一样显示视频卡片区域

### 需求 2

**用户故事：** 作为维护代码库的开发者，我希望 FilterControls 组件保留在代码库中但不在 VideoPreviewSection 中使用，以便将来需要时可以在其他地方使用。

#### 验收标准

1. 当更新 VideoPreviewSection 组件时，系统应该移除 FilterControls 的导入和使用
2. 当更新 VideoPreviewSection 组件时，系统不应该删除 FilterControls 组件文件
3. 当更新 VideoPreviewSection 组件时，系统应该移除任何仅用于 FilterControls 的过滤器相关状态和逻辑