# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Package Management
- `pnpm install` - Install dependencies
- `pnpm dev` - Start development server with hot reload (all workspaces)
- `pnpm build` - Build production version
- `pnpm start` - Start production server

**Important**: When adding dependencies to specific workspaces, always use the `--filter` flag:
- `pnpm add <package> --filter @repo/api` - Add to API package
- `pnpm add <package> --filter @repo/web` - Add to web app
- `pnpm add <package> --filter @repo/database` - Add to database package
- Example: `pnpm add @paralleldrive/cuid2 --filter @repo/api`

### Code Quality
- `pnpm lint` - Run Biome linter with auto-fix
- `pnpm check` - Run Biome checks without fixing
- `pnpm format` - Format code with Biome
- `pnpm type-check` - Run TypeScript type checking (in apps/web)

**Important**: Always run code quality commands from the project root directory:
- ✅ `pnpm check packages/api/src/routes/generations/` (from root)
- ❌ `cd packages/api && pnpm check` (from subdirectory - will fail)
- The Biome configuration uses relative paths that only work from the root directory

### Testing
- `pnpm e2e` - Run Playwright tests with UI
- `pnpm e2e:ci` - Run Playwright tests in CI mode

### Development Tools
- `pnpm clean` - Clean build artifacts
- `pnpm shadcn-ui` - Add shadcn/ui components

## Architecture Overview

This is a **monorepo** built with **pnpm workspaces** and **Turbo** for a SaaS video generation application with both marketing and application features.

### Repository Structure

```
video-generator-web/
├── apps/web/              # Next.js 15 App Router application
├── packages/              # Shared packages
├── config/               # Application configuration
└── tooling/              # Build and development tools
```

### Key Technologies
- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **UI**: Shadcn/ui, Radix UI components
- **Backend**: Hono API, Better Auth
- **Database**: PostgreSQL with Prisma ORM
- **Monorepo**: pnpm workspaces, Turbo
- **Code Quality**: Biome (linting, formatting)
- **Testing**: Playwright E2E tests

### Apps Structure

#### apps/web/
The main Next.js application with two distinct sections:

**Marketing Site** (`(marketing)` route group):
- Internationalized (i18n) with multi-language support (English, German, Chinese)
- Dynamic routing: `[locale]/page.tsx`
- Content: Blog, Changelog, Docs, Contact, Legal pages
- Uses MDX for content with `content-collections`
- Navigation: `modules/marketing/shared/components/NavBar.tsx`

**SaaS Application** (`(saas)` route group):
- Authentication pages: login, signup, forgot password
- Protected app routes with organization-based access
- User settings, billing, admin panels
- Organization management with role-based access
- AI chatbot and video generation features
- Navigation: `modules/saas/shared/components/NavBar.tsx`

#### Module Organization
Code is organized into feature-based modules under `apps/web/modules/`:

- `marketing/` - Marketing site components and logic
- `saas/` - SaaS application features (auth, organizations, payments, settings, AI)
- `shared/` - Shared components and utilities
- `ui/` - Shadcn/ui component library
- `analytics/` - Analytics provider integrations
- `i18n/` - Internationalization utilities

### Packages Structure

- `@repo/api` - Hono-based API with OpenAPI documentation
- `@repo/auth` - Better Auth configuration and helpers
- `@repo/database` - Prisma schema and database queries
- `@repo/i18n` - Translation files and i18n utilities
- `@repo/mail` - Email templates and providers (Resend, Postmark, etc.)
- `@repo/payments` - Payment processing (Stripe, LemonSqueezy, etc.)
- `@repo/storage` - File storage providers (S3, etc.)
- `@repo/ai` - AI/LLM integration utilities
- `@repo/logs` - Logging configuration
- `@repo/utils` - Shared utility functions

### Configuration System

The application uses a centralized config system in `config/index.ts`:

- **Feature toggles**: Enable/disable SaaS features, authentication methods
- **Internationalization**: Locale and currency settings (USD, CNY, TWD)
- **Organizations**: Multi-tenant configuration
- **Payments**: Pricing plans (Free, Pro, Lifetime, Enterprise) and billing settings
- **Authentication**: Social login, MFA, passkeys, session settings

### Database Architecture

Uses **Prisma** with PostgreSQL:
- User management with roles and permissions
- Organization/tenant isolation
- Billing and subscription tracking
- AI chat history
- Session management with Better Auth

### API Architecture

**Hono-based API** with:
- OpenAPI schema generation
- Middleware for auth, CORS, logging
- Route organization by feature
- Type-safe request/response handling

### Authentication Flow

Uses **Better Auth** with:
- Multiple sign-in methods (email/password, magic links, social, passkeys)
- Two-factor authentication
- Session management
- Organization-based access control

### Internationalization

- **next-intl** for React components
- Route-based localization: `/[locale]/page`
- Translation files in `packages/i18n/translations/`
- Locale-specific currency and formatting
- Supports: English (USD), German (USD), Simplified Chinese (CNY), Traditional Chinese (TWD)

### Key Development Patterns

1. **Server-first approach**: Prefer React Server Components
2. **Type safety**: Full TypeScript with Prisma-generated types
3. **Module boundaries**: Clean separation between marketing/saas/shared
4. **Configuration-driven**: Feature flags and environment-based config
5. **Workspace imports**: Use `@repo/` prefix for internal packages
6. **Functional programming**: Avoid classes, use functional and declarative patterns
7. **Naming conventions**: 
   - Lowercase with dashes for directories (`auth-wizard`)
   - PascalCase for components
   - camelCase for variables and methods
   - Favor named exports

### Important Files

- `config/index.ts` - Main application configuration
- `apps/web/middleware.ts` - Next.js middleware for auth and i18n
- `packages/database/prisma/schema.prisma` - Database schema
- `packages/api/src/app.ts` - API application entry point
- `.windsurfrules` - Development guidelines and conventions
- `.cursor/rules/` - Cursor IDE-specific rules and patterns

### Environment Setup

The application requires:
- Node.js 20+
- PostgreSQL database
- Environment variables for auth, payments, storage providers
- pnpm as package manager

### Route Protection

- Marketing routes: Public access with i18n
- SaaS routes: Protected by authentication middleware
- Organization routes: Additional organization membership checks
- Admin routes: Role-based access control

### Video Generation Features

The application includes AI-powered video generation:
- Image-to-video AI functionality
- Chatbot integration for video creation assistance
- Organization-scoped video projects
- Billing integration for video generation usage

### Development Environment Setup

#### Line Endings Configuration
- Uses Unix-style line endings (LF) across all text files
- Configured via `.gitattributes` for consistent behavior across platforms
- Git setting: `core.autocrlf = false` (prevents automatic conversion)
- EditorConfig enforces `end_of_line = lf` in all editors