# Redis包迁移方案

## 🎯 目标
解决 `@repo/jobs` 不合理依赖 `@repo/api` 的问题，将Redis功能提取到独立的 `@repo/redis` 包中。

## 🏗️ 当前架构问题

```
packages/
├── api/src/lib/redis.ts           # Redis工具类
├── jobs/src/lib/generations-manager.ts  # ❌ 依赖 @repo/api
└── 其他包...
```

**问题：**
- `@repo/jobs` 依赖 `@repo/api`，违反了包的职责分离原则
- 创建了不合理的循环依赖风险
- Redis功能应该是底层基础设施

## 🎨 目标架构

```
packages/
├── redis/                        # ✅ 新建独立Redis包
│   ├── src/
│   │   ├── index.ts             # 导出所有Redis功能
│   │   ├── client.ts            # Redis客户端配置
│   │   ├── likes.ts             # 点赞相关Redis操作
│   │   └── utils.ts             # 通用Redis工具
│   ├── package.json
│   └── tsconfig.json
├── api/src/lib/                  # ✅ 移除redis.ts，改为引用@repo/redis
├── jobs/src/lib/                 # ✅ 引用@repo/redis替代@repo/api
└── 其他包...
```

## 📋 迁移步骤

### 1. 创建 `@repo/redis` 包结构

#### 1.1 创建基础文件
```bash
mkdir -p packages/redis/src
touch packages/redis/package.json
touch packages/redis/tsconfig.json
touch packages/redis/src/index.ts
touch packages/redis/src/client.ts
touch packages/redis/src/likes.ts
touch packages/redis/src/utils.ts
```

#### 1.2 `packages/redis/package.json`
```json
{
  "name": "@repo/redis",
  "version": "0.0.0",
  "private": true,
  "main": "./src/index.ts",
  "types": "./src/index.ts",
  "scripts": {
    "build": "tsc",
    "dev": "tsc --watch",
    "clean": "rm -rf dist"
  },
  "dependencies": {
    "@upstash/redis": "workspace:*",
    "@repo/database": "workspace:*",
    "@repo/logs": "workspace:*"
  },
  "devDependencies": {
    "typescript": "^5.0.0"
  }
}
```

#### 1.3 `packages/redis/tsconfig.json`
```json
{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    "outDir": "./dist",
    "rootDir": "./src"
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
```

### 2. 迁移Redis代码

#### 2.1 `packages/redis/src/client.ts`
```typescript
import { Redis } from '@upstash/redis';

// Upstash Redis 连接
export const redis = new Redis({
  url: process.env.UPSTASH_REDIS_URL!,
  token: process.env.UPSTASH_REDIS_TOKEN!,
});

// 连接测试
export async function testRedisConnection() {
  try {
    const result = await redis.ping();
    console.log('✅ Redis connected successfully:', result);
    return true;
  } catch (error) {
    console.error('❌ Redis connection failed:', error);
    return false;
  }
}
```

#### 2.2 `packages/redis/src/likes.ts`
```typescript
import { redis } from './client';

/**
 * 点赞相关的Redis操作
 */
export class LikesRedis {
  /**
   * 超快速切换点赞状态 - 纯Redis实现
   * 响应时间: ~20-50ms (vs 数据库600ms)
   */
  static async toggleLikeFast(generationId: string, userId: string): Promise<{
    success: boolean;
    likeCount: number;
    isLiked: boolean;
  }> {
    const likeKey = `likes:${generationId}`;
    
    // 检查当前状态
    const isCurrentlyLiked = await redis.sismember(likeKey, userId);
    
    if (isCurrentlyLiked) {
      // 取消点赞 - 原子操作
      const multi = redis.multi();
      multi.srem(likeKey, userId);
      multi.scard(likeKey);
      const results = await multi.exec();
      
      const newCount = Math.max(0, (results[1] || 0));
      
      // 异步同步到数据库 (fire-and-forget)
      this.asyncSyncLikeToDatabase(generationId, userId, false, newCount);
      
      return {
        success: true,
        likeCount: newCount,
        isLiked: false
      };
    } else {
      // 添加点赞 - 原子操作
      const multi = redis.multi();
      multi.sadd(likeKey, userId);
      multi.scard(likeKey);
      const results = await multi.exec();
      
      const newCount = results[1] || 1;
      
      // 异步同步到数据库 (fire-and-forget)
      this.asyncSyncLikeToDatabase(generationId, userId, true, newCount);
      
      return {
        success: true,
        likeCount: newCount,
        isLiked: true
      };
    }
  }

  /**
   * 批量获取点赞状态 - 用于列表页面优化
   */
  static async batchGetLikeStatus(generationIds: string[], userId: string): Promise<Record<string, {
    isLiked: boolean;
    likeCount: number;
  }>> {
    if (generationIds.length === 0) return {};
    
    const pipeline = redis.pipeline();
    
    // 批量查询点赞状态和数量
    for (const genId of generationIds) {
      pipeline.sismember(`likes:${genId}`, userId);
      pipeline.scard(`likes:${genId}`);
    }
    
    const results = await pipeline.exec();
    const status: Record<string, { isLiked: boolean; likeCount: number }> = {};
    
    for (let i = 0; i < generationIds.length; i++) {
      const genId = generationIds[i];
      status[genId] = {
        isLiked: results[i * 2] === 1,
        likeCount: results[i * 2 + 1] || 0
      };
    }
    
    return status;
  }

  /**
   * 异步同步到数据库 - fire-and-forget
   * 不阻塞主响应，后台执行
   */
  private static asyncSyncLikeToDatabase(generationId: string, userId: string, isLiked: boolean, newCount: number) {
    // 不用await，让它在后台异步执行
    setTimeout(async () => {
      try {
        // 导入数据库操作（避免循环依赖）
        const { db } = await import("@repo/database");
        
        if (isLiked) {
          // 添加点赞：更新Generation表和创建Like记录
          await Promise.all([
            // 更新starNum
            db.generation.update({
              where: { id: generationId },
              data: { starNum: newCount }
            }),
            // 确保Like记录存在
            db.generationLike.upsert({
              where: {
                userId_generationId: {
                  userId,
                  generationId
                }
              },
              create: { userId, generationId },
              update: {} // 如果已存在就不更新
            })
          ]);
        } else {
          // 取消点赞：更新Generation表和删除Like记录
          await Promise.all([
            // 更新starNum
            db.generation.update({
              where: { id: generationId },
              data: { starNum: newCount }
            }),
            // 删除Like记录
            db.generationLike.deleteMany({
              where: { userId, generationId }
            })
          ]);
        }
        
        console.log(`[Redis] Background DB sync completed: ${generationId} -> ${isLiked ? 'liked' : 'unliked'} by ${userId}, count: ${newCount}`);
      } catch (error) {
        console.error('[Redis] Background DB sync failed:', error);
        // 可以添加重试逻辑或错误报告
      }
    }, 50); // 50ms后异步执行，不阻塞主响应
  }

  /**
   * 初始化/迁移现有数据到Redis
   * 一次性操作，将数据库中的点赞数据同步到Redis
   */
  static async migrateLikesToRedis(): Promise<{ success: boolean; migrated: number }> {
    try {
      const { db } = await import("@repo/database");
      
      // 获取所有Generation的点赞数据
      const generations = await db.generation.findMany({
        where: { deleted: false },
        select: { id: true, starNum: true },
        include: {
          likes: {
            select: { userId: true }
          }
        }
      });
      
      let migratedCount = 0;
      
      // 批量迁移到Redis
      for (const gen of generations) {
        const likeKey = `likes:${gen.id}`;
        
        if (gen.likes.length > 0) {
          // 添加所有点赞用户到Redis SET
          const userIds = gen.likes.map(like => like.userId);
          await redis.sadd(likeKey, ...userIds);
          migratedCount++;
        }
      }
      
      console.log(`[Redis] Migration completed: ${migratedCount} generations migrated`);
      return { success: true, migrated: migratedCount };
      
    } catch (error) {
      console.error('[Redis] Migration failed:', error);
      return { success: false, migrated: 0 };
    }
  }
}
```

#### 2.3 `packages/redis/src/utils.ts`
```typescript
import { redis } from './client';

export class RedisUtils {
  // 缓存任务状态
  static async cacheJobStatus(jobId: string, status: any, ttl: number = 3600) {
    await redis.setex(`job:${jobId}`, ttl, JSON.stringify(status));
  }
  
  // 获取缓存的任务状态
  static async getCachedJobStatus(jobId: string) {
    const cached = await redis.get(`job:${jobId}`);
    return cached ? JSON.parse(cached) : null;
  }
  
  // 清除任务缓存
  static async clearJobCache(jobId: string) {
    await redis.del(`job:${jobId}`);
  }
  
  // 限流计数
  static async incrementCounter(key: string, ttl: number = 60) {
    const count = await redis.incr(key);
    if (count === 1) {
      await redis.expire(key, ttl);
    }
    return count;
  }
}
```

#### 2.4 `packages/redis/src/index.ts`
```typescript
// 导出所有Redis功能
export { redis, testRedisConnection } from './client';
export { LikesRedis } from './likes';
export { RedisUtils } from './utils';

// 为了向后兼容，导出原有的redisUtils格式
export const redisUtils = {
  ...RedisUtils,
  ...LikesRedis,
  // 点赞相关方法的别名
  toggleLikeFast: LikesRedis.toggleLikeFast,
  batchGetLikeStatus: LikesRedis.batchGetLikeStatus,
  migrateLikesToRedis: LikesRedis.migrateLikesToRedis,
};
```

### 3. 更新依赖关系

#### 3.1 更新 `packages/api/package.json`
```json
{
  "dependencies": {
    "@repo/redis": "workspace:*",
    // ... 其他依赖
  }
}
```

#### 3.2 更新 `packages/jobs/package.json`
```json
{
  "dependencies": {
    "@repo/redis": "workspace:*",
    // ... 其他依赖
  }
}
```

### 4. 更新引用代码

#### 4.1 删除 `packages/api/src/lib/redis.ts`
这个文件将被完全替换

#### 4.2 更新 `packages/jobs/src/lib/generations-manager.ts`
```typescript
// 原来的导入
const { redisUtils } = await import("../../../api/src/lib/redis");

// 改为
const { LikesRedis } = await import("@repo/redis");
const result = await LikesRedis.toggleLikeFast(generationId, userId);
```

#### 4.3 更新 `packages/api/src/routes/generations/router.ts`
```typescript
// 原来的导入
const { redisUtils } = await import("../../lib/redis");

// 改为
const { LikesRedis } = await import("@repo/redis");
const result = await LikesRedis.migrateLikesToRedis();
```

#### 4.4 更新 `packages/api/src/middleware/rate-limiter.ts`
```typescript
// 原来的导入
import { redis } from '../lib/redis';

// 改为
import { redis } from '@repo/redis';
```

#### 4.5 更新 `packages/api/src/routes/webhooks/shared/processor.ts`
```typescript
// 原来的导入
import { redisUtils } from "../../../lib/redis";

// 改为
import { redisUtils } from "@repo/redis";
```

### 5. 完整的引用文件列表

通过全局搜索，发现以下文件引用了 `packages/api/src/lib/redis.ts`：

1. ✅ `packages/jobs/src/lib/generations-manager.ts:461` - 点赞功能
2. ✅ `packages/api/src/routes/generations/router.ts:648` - 迁移接口(已注释)
3. ✅ `packages/api/src/middleware/rate-limiter.ts:3` - 限流功能
4. ✅ `packages/api/src/routes/webhooks/shared/processor.ts:1` - Webhook处理

**注意：以下Redis系统是独立的，不需要迁移：**
- `packages/queue/src/redis-config.ts` - 使用ioredis的队列系统
- `apps/consumer/src/config/redis.ts` - 消费者独立Redis配置
- `apps/consumer/src/services/distributed-lock.ts` - 分布式锁使用ioredis

## 🧪 验证步骤

### 1. 构建验证
```bash
# 构建新的redis包
cd packages/redis && pnpm build

# 构建其他依赖包
pnpm build
```

### 2. 功能验证
```bash
# 启动开发服务器
pnpm dev

# 测试点赞功能是否正常
# 检查响应时间是否从600ms降到50ms以内
```

### 3. 类型检查
```bash
pnpm type-check
```

## 📈 迁移后的优势

1. **清晰的包职责**：`@repo/redis` 专门负责Redis操作
2. **消除不合理依赖**：`@repo/jobs` 不再依赖 `@repo/api`
3. **更好的复用性**：其他包也可以直接使用 `@repo/redis`
4. **便于维护**：Redis相关功能集中管理
5. **向后兼容**：通过 `redisUtils` 导出保持API兼容性

## ⚠️ 注意事项

1. **渐进式迁移**：可以先保留原有代码，逐步切换引用
2. **测试充分**：每个步骤都要验证功能正常
3. **环境变量**：确保Redis连接配置在所有环境中正确
4. **错误处理**：确保Redis连接失败时有合适的降级机制

## 🔄 回滚方案

如果迁移过程中出现问题，可以：
1. 恢复 `packages/api/src/lib/redis.ts` 文件
2. 撤销包依赖更改
3. 恢复原有的导入语句

这样确保系统随时可以回滚到迁移前的状态。