# 🚀 服务启动指南

## 📋 **服务架构**
```
Web App (@repo/web)     → 用户界面 + API + Webhook接收
Consumer (video-consumer) → 视频处理 + 上传 + 队列消费
```

## ⚡ **启动命令**

### 1. **仅启动Web应用** (推荐)
```bash
pnpm dev
# 或
pnpm dev:web
```
- 端口: `http://localhost:3000`
- 功能: 用户界面、API、Webhook接收、任务创建

### 2. **仅启动Consumer** 
```bash
pnpm dev:consumer
```
- 功能: 队列消费、视频处理、重复防护、上传

### 3. **同时启动两个服务**
```bash
pnpm dev:all
```
- 同时运行Web App + Consumer

## 🎯 **推荐的开发流程**

### 方式A: 分离启动 (生产环境推荐)
```bash
# 终端1: 启动Web应用
pnpm dev:web

# 终端2: 启动Consumer
pnpm dev:consumer
```

### 方式B: 一键启动 (开发便利)
```bash
# 一个命令启动所有服务
pnpm dev:all
```

## 📊 **服务状态检查**

### Web App健康检查
```bash
curl http://localhost:3000/api/health
```

### Consumer健康检查  
```bash
curl http://localhost:3001/health
# 或检查Consumer日志
tail -f logs/consumer.log
```

## 🔄 **服务重启**

### 重启Web App
```bash
# 如果使用分离启动
Ctrl+C  # 停止Web服务
pnpm dev:web  # 重新启动

# 如果使用一键启动
Ctrl+C  # 停止所有服务  
pnpm dev:all  # 重新启动
```

### 重启Consumer
```bash
# 如果使用分离启动
Ctrl+C  # 停止Consumer服务
pnpm dev:consumer  # 重新启动
```

## 🎯 **默认行为变更**

**之前**: `pnpm dev` 启动Web + Consumer  
**现在**: `pnpm dev` 仅启动Web App

**原因**: 
- ✅ 清晰的服务边界
- ✅ 避免重复Consumer日志混淆
- ✅ 独立的服务生命周期
- ✅ 更好的资源控制

## 🚀 **生产部署**

### Web App部署
```bash
pnpm build
pnpm start  # Web App
```

### Consumer部署 
```bash
# 在Consumer目录
cd apps/consumer
pnpm build
pnpm start  # Consumer服务
```

## 💡 **开发技巧**

1. **日志区分**: 分离启动让日志更清晰
2. **调试专注**: 单独重启有问题的服务
3. **资源优化**: 开发时可选择性启动服务
4. **部署模拟**: 分离启动更接近生产环境

**🎉 现在Web App和Consumer完全分离！**