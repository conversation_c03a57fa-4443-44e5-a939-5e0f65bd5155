# 🚀 Video Consumer MVP部署指南

## 📋 概述

MVP阶段优化配置，单实例部署，资源友好，保留扩展能力。

## ⚙️ **当前MVP配置**

```bash
# === 核心配置 ===
WORKER_CONCURRENCY=4                    # 🎯 适中并发
REDIS_POOL_SIZE=4                       # 🎯 小连接池
S3_POOL_SIZE=2                          # 🎯 2个S3客户端
BATCH_MAX_SIZE=10                       # 🎯 小批次处理
INSTANCE_ID=video-generator-mvp-001     # 🎯 MVP实例标识

# === 防重复配置 ===
BULLMQ_REMOVE_ON_COMPLETE=50            # 🎯 减少内存占用
BULLMQ_REMOVE_ON_FAIL=20                # 🎯 保留关键失败任务
DUPLICATE_CHECK_ENABLED=true            # ✅ 为扩展准备
DISTRIBUTED_LOCK_TTL=120000             # ✅ 2分钟锁超时
```

## 🚀 **快速启动**

### 1. 检查配置
```bash
node apps/consumer/start-mvp.js
```

### 2. 启动Consumer
```bash
# 方式1: 直接启动
cd apps/consumer && npm start

# 方式2: 使用pnpm
pnpm --filter @repo/consumer start

# 方式3: 开发模式
pnpm dev
```

### 3. 验证运行
```bash
# 健康检查
curl http://localhost:3000/health

# 监控日志
tail -f logs/consumer.log

# MVP监控面板
node apps/consumer/monitor-mvp.js
```

## 📊 **MVP性能预期**

| 指标 | MVP配置 | 说明 |
|------|---------|------|
| **处理能力** | ~20 videos/min | 4个Worker并发 |
| **内存占用** | ~400MB | 优化后的资源使用 |
| **Redis连接** | 4个 | 适中的连接池 |
| **S3连接** | 2个 | 足够的上传并发 |
| **批处理** | 10个/批次 | 小批次快速处理 |

## 🔍 **监控关键指标**

### 健康状态
```bash
# 检查Consumer状态
curl http://localhost:3000/health

# 期望输出
{
  "status": "healthy",
  "instance": "video-generator-mvp-001",
  "uptime": "2h 35m",
  "processing": 3
}
```

### 性能指标
```bash
# 查看处理日志
tail -f logs/consumer.log | grep -E "(processed|completed|failed)"

# 重复防护日志
tail -f logs/consumer.log | grep -i duplicate
```

## 🚨 **MVP阶段告警阈值**

| 指标 | 告警阈值 | 建议操作 |
|------|----------|----------|
| **队列积压** | >30个 | 增加WORKER_CONCURRENCY |
| **内存使用** | >80% | 减少BATCH_MAX_SIZE |
| **CPU使用** | >80% | 考虑优化或扩展 |
| **处理耗时** | >2分钟 | 检查网络和S3连接 |
| **成功率** | <95% | 检查配置和依赖 |

## 📈 **扩展路径**

### 阶段1: 单实例优化 (负载增加20-50%)
```bash
# 增加Worker并发
WORKER_CONCURRENCY=6

# 增加连接池
REDIS_POOL_SIZE=6
S3_POOL_SIZE=3

# 增加批处理
BATCH_MAX_SIZE=15
```

### 阶段2: 资源扩展 (负载增加50-100%)
```bash
# 进一步优化
WORKER_CONCURRENCY=8
REDIS_POOL_SIZE=8
S3_POOL_SIZE=4
BATCH_MAX_SIZE=20

# 增加任务保留
BULLMQ_REMOVE_ON_COMPLETE=100
BULLMQ_REMOVE_ON_FAIL=50
```

### 阶段3: 横向扩展 (负载增加100%+)
```bash
# 启动第二个实例
INSTANCE_ID=video-generator-prod-002

# 第三个实例
INSTANCE_ID=video-generator-prod-003

# 负载均衡配置
# (参考架构文档中的K8s配置)
```

## 🔧 **故障排除**

### 常见问题

#### 1. Consumer启动失败
```bash
# 检查环境变量
node apps/consumer/start-mvp.js

# 检查Redis连接
redis-cli -u $REDIS_URL ping

# 检查数据库连接
psql $DATABASE_URL -c "SELECT 1;"
```

#### 2. 处理速度慢
```bash
# 检查队列积压
curl http://localhost:3000/stats

# 临时增加并发
export WORKER_CONCURRENCY=6
# 重启Consumer
```

#### 3. 内存使用高
```bash
# 减少批处理大小
export BATCH_MAX_SIZE=5

# 减少任务保留数量
export BULLMQ_REMOVE_ON_COMPLETE=30
```

#### 4. S3上传失败
```bash
# 检查S3配置
aws s3 ls --endpoint-url=$S3_ENDPOINT

# 增加重试次数
export S3_MAX_RETRIES=5
```

## 📱 **日常运维**

### 启动/停止
```bash
# 启动
pm2 start apps/consumer/app.js --name video-consumer-mvp

# 停止
pm2 stop video-consumer-mvp

# 重启
pm2 restart video-consumer-mvp

# 查看日志
pm2 logs video-consumer-mvp
```

### 监控脚本
```bash
# 实时监控
node apps/consumer/monitor-mvp.js

# 定时检查健康状态
*/5 * * * * curl -sf http://localhost:3000/health || echo "Consumer down"
```

### 备份恢复
```bash
# Redis数据备份
redis-cli -u $REDIS_URL --rdb backup-$(date +%Y%m%d).rdb

# 日志轮转
logrotate -f /etc/logrotate.d/consumer
```

## 🎯 **MVP成功指标**

- ✅ **稳定运行**: 7天无故障
- ✅ **处理效率**: 平均处理时间 <1分钟
- ✅ **成功率**: >98%
- ✅ **资源使用**: 内存 <500MB, CPU <50%
- ✅ **零重复**: 无重复处理事件

## 🚀 **准备生产**

当达到以下条件时，准备扩展到生产环境：

1. **负载持续增长**: 队列积压 >20个
2. **资源利用率高**: CPU >70% 或内存 >70%  
3. **处理延迟**: 平均处理时间 >90秒
4. **业务需求**: 需要更高的处理能力

**扩展步骤**:
1. 按阶段1优化单实例
2. 监控性能提升效果
3. 如仍不够，启动第二个实例
4. 配置负载均衡和监控

## 💡 **MVP最佳实践**

- 🎯 **渐进式优化**: 先优化单实例，再考虑扩展
- 📊 **数据驱动**: 基于实际监控数据调整配置  
- 🔄 **预留能力**: 保持30%资源余量
- 🛡️ **防护优先**: 确保重复防护机制正常工作
- 📱 **监控第一**: 建立完善的监控和告警

**MVP阶段完成！准备处理您的视频生成任务！** 🎉