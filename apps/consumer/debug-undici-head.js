#!/usr/bin/env node

/**
 * 调试undici HEAD请求失败问题
 * 对比undici与curl的行为差异
 */

const testUrl = 'https://replicate.delivery/xezq/TJH6dHje2WU5NCUpxGNCbJerNeJTvBYLNBQRZYSuQVNno8JqA/tmpxiu0tenv.mp4';

async function testUndiciHead() {
  console.log('🧪 测试undici HEAD请求');
  console.log(`URL: ${testUrl}\n`);
  
  try {
    // 使用动态import加载undici（避免require问题）
    const { request } = await import('undici');
    
    console.log('📤 发送undici HEAD请求...');
    const startTime = Date.now();
    
    const result = await request(testUrl, {
      method: 'HEAD',
      headers: {
        'User-Agent': 'VideoProcessor/1.0',
        'Accept': '*/*',
        'Cache-Control': 'no-cache',
      },
      headersTimeout: 10000,
      bodyTimeout: 10000,
    });
    
    const duration = Date.now() - startTime;
    
    console.log('✅ undici HEAD请求成功！');
    console.log(`   状态码: ${result.statusCode}`);
    console.log(`   Headers:`, JSON.stringify(result.headers, null, 2));
    console.log(`   Duration: ${duration}ms`);
    
    // 关闭body
    if (result.body) {
      result.body.destroy();
    }
    
    return { success: true, result, duration };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    
    console.log('❌ undici HEAD请求失败');
    console.log(`   错误类型: ${typeof error}`);
    console.log(`   错误构造器: ${error?.constructor?.name}`);
    console.log(`   错误消息: "${error?.message}"`);
    console.log(`   错误代码: ${error?.code}`);
    console.log(`   错误原因: ${error?.cause}`);
    console.log(`   Duration: ${duration}ms`);
    console.log(`   完整错误:`, JSON.stringify(error, Object.getOwnPropertyNames(error), 2));
    
    return { success: false, error, duration };
  }
}

async function testNetworkEnvironment() {
  console.log('\n🌐 网络环境检查:');
  
  // 检查代理设置
  const proxyEnvs = ['http_proxy', 'https_proxy', 'all_proxy', 'HTTP_PROXY', 'HTTPS_PROXY', 'ALL_PROXY'];
  console.log('\n📡 代理环境变量:');
  proxyEnvs.forEach(env => {
    const value = process.env[env];
    console.log(`   ${env}: ${value || '(未设置)'}`);
  });
  
  // 检查DNS解析
  console.log('\n🔍 DNS解析测试:');
  try {
    const dns = require('dns').promises;
    const { address } = await dns.lookup('replicate.delivery');
    console.log(`   replicate.delivery -> ${address}`);
    
    // 获取所有IP地址
    const addresses = await dns.resolve4('replicate.delivery');
    console.log(`   所有IPv4地址:`, addresses);
    
    try {
      const ipv6Addresses = await dns.resolve6('replicate.delivery');
      console.log(`   所有IPv6地址:`, ipv6Addresses);
    } catch (e) {
      console.log(`   IPv6解析: 失败 (${e.message})`);
    }
    
  } catch (error) {
    console.log(`   DNS解析失败: ${error.message}`);
  }
}

async function testCurlComparison() {
  console.log('\n🔄 对比curl结果:');
  
  const { spawn } = require('child_process');
  
  return new Promise((resolve) => {
    const curl = spawn('curl', [
      '-I', '-v', '--max-time', '10',
      '--user-agent', 'VideoProcessor/1.0',  // 使用相同的User-Agent
      testUrl
    ]);
    
    let output = '';
    let errorOutput = '';
    
    curl.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    curl.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    
    curl.on('close', (code) => {
      console.log(`curl退出代码: ${code}`);
      if (code === 0) {
        console.log('✅ curl HEAD成功');
        // 提取关键信息
        const statusMatch = errorOutput.match(/< HTTP\/[12](?:\.[01])? (\d+)/);
        const contentLengthMatch = output.match(/content-length: (\d+)/i);
        const contentTypeMatch = output.match(/content-type: ([^\r\n]+)/i);
        
        console.log(`   状态码: ${statusMatch ? statusMatch[1] : '未知'}`);
        console.log(`   内容长度: ${contentLengthMatch ? contentLengthMatch[1] : '未知'}`);
        console.log(`   内容类型: ${contentTypeMatch ? contentTypeMatch[1] : '未知'}`);
      } else {
        console.log('❌ curl HEAD失败');
        console.log('错误输出:', errorOutput);
      }
      resolve();
    });
  });
}

async function testWithDifferentConfigs() {
  console.log('\n⚙️ 测试不同undici配置:');
  
  const configs = [
    {
      name: '标准配置',
      options: {
        method: 'HEAD',
        headers: {
          'User-Agent': 'VideoProcessor/1.0',
          'Accept': '*/*',
          'Cache-Control': 'no-cache',
        },
        headersTimeout: 10000,
        bodyTimeout: 10000,
      }
    },
    {
      name: 'curl相同User-Agent',
      options: {
        method: 'HEAD',
        headers: {
          'User-Agent': 'curl/8.5.0',
          'Accept': '*/*',
        },
        headersTimeout: 10000,
        bodyTimeout: 10000,
      }
    },
    {
      name: '简化headers',
      options: {
        method: 'HEAD',
        headers: {
          'User-Agent': 'VideoProcessor/1.0',
        },
        headersTimeout: 15000,
        bodyTimeout: 15000,
      }
    },
    {
      name: '强制HTTP/1.1',
      options: {
        method: 'HEAD',
        headers: {
          'User-Agent': 'VideoProcessor/1.0',
          'Accept': '*/*',
          'Connection': 'close',
        },
        headersTimeout: 10000,
        bodyTimeout: 10000,
        // 尝试禁用HTTP/2
      }
    }
  ];
  
  try {
    const { request } = await import('undici');
    
    for (const config of configs) {
      console.log(`\n🧪 测试配置: ${config.name}`);
      try {
        const startTime = Date.now();
        const result = await request(testUrl, config.options);
        const duration = Date.now() - startTime;
        
        console.log(`   ✅ 成功 - 状态码: ${result.statusCode}, 时间: ${duration}ms`);
        
        if (result.body) {
          result.body.destroy();
        }
        
      } catch (error) {
        const errorMsg = error?.message || String(error);
        console.log(`   ❌ 失败 - 错误: "${errorMsg}"`);
      }
    }
    
  } catch (importError) {
    console.log(`❌ 无法导入undici: ${importError.message}`);
  }
}

async function runFullDiagnostic() {
  console.log('🔍 undici HEAD请求失败 - 完整诊断\n');
  console.log('=' * 80);
  
  // 1. 测试基本undici HEAD请求
  await testUndiciHead();
  
  // 2. 检查网络环境
  await testNetworkEnvironment();
  
  // 3. 对比curl
  await testCurlComparison();
  
  // 4. 测试不同配置
  await testWithDifferentConfigs();
  
  console.log('\n' + '=' * 80);
  console.log('🎯 诊断总结:');
  console.log('=' * 80);
  console.log('1. 如果undici在所有配置下都失败，问题可能是代理或网络环境');
  console.log('2. 如果某些配置成功，问题在于headers或协议设置');
  console.log('3. 对比curl结果可以确定是否为undici特有问题');
  console.log('4. 检查代理设置是否影响undici的行为');
}

// 运行完整诊断
runFullDiagnostic().catch(console.error);