#!/usr/bin/env node

/**
 * MVP Consumer监控脚本
 * 简化版监控，关注核心指标
 */

const fs = require('fs');
const path = require('path');

console.log('📊 Video Consumer MVP监控面板\n');

// 模拟获取统计数据的函数
function getConsumerStats() {
  // 在实际实现中，这些数据会从Redis或健康检查API获取
  return {
    instance: {
      id: process.env.INSTANCE_ID || 'video-generator-mvp-001',
      uptime: '2小时35分钟',
      status: 'healthy'
    },
    processing: {
      totalProcessed: 127,
      currentlyProcessing: 3,
      queueWaiting: 8,
      avgProcessingTime: '45秒',
      successRate: '98.4%'
    },
    resources: {
      memoryUsage: '285MB',
      cpuUsage: '15%',
      redisConnections: '4/4',
      s3Connections: '2/2'
    },
    duplicateProtection: {
      duplicateAttempts: 2,
      preventionRate: '100%',
      lockAcquisitions: 127,
      lockFailures: 0
    }
  };
}

// 显示监控数据
function displayStats() {
  const stats = getConsumerStats();
  
  console.log('🎯 实例状态:');
  console.log(`   ID: ${stats.instance.id}`);
  console.log(`   运行时间: ${stats.instance.uptime}`);
  console.log(`   状态: ${stats.instance.status === 'healthy' ? '✅ 健康' : '❌ 异常'}`);
  
  console.log('\n📈 处理统计:');
  console.log(`   总处理数: ${stats.processing.totalProcessed}`);
  console.log(`   当前处理: ${stats.processing.currentlyProcessing}`);
  console.log(`   队列等待: ${stats.processing.queueWaiting}`);
  console.log(`   平均耗时: ${stats.processing.avgProcessingTime}`);
  console.log(`   成功率: ${stats.processing.successRate}`);
  
  console.log('\n💾 资源使用:');
  console.log(`   内存占用: ${stats.resources.memoryUsage}`);
  console.log(`   CPU使用: ${stats.resources.cpuUsage}`);
  console.log(`   Redis连接: ${stats.resources.redisConnections}`);
  console.log(`   S3连接: ${stats.resources.s3Connections}`);
  
  console.log('\n🛡️ 重复防护:');
  console.log(`   重复尝试: ${stats.duplicateProtection.duplicateAttempts}`);
  console.log(`   防护率: ${stats.duplicateProtection.preventionRate}`);
  console.log(`   锁获取: ${stats.duplicateProtection.lockAcquisitions}`);
  console.log(`   锁失败: ${stats.duplicateProtection.lockFailures}`);
}

// MVP性能建议
function getPerformanceRecommendations(stats) {
  const recommendations = [];
  
  // 基于模拟数据给出建议
  const queueWaiting = 8;
  const memoryUsageMB = 285;
  const successRate = 98.4;
  
  if (queueWaiting > 20) {
    recommendations.push('🚀 考虑增加Worker并发数 (WORKER_CONCURRENCY=6)');
  }
  
  if (memoryUsageMB > 500) {
    recommendations.push('💾 内存使用偏高，考虑减少BATCH_MAX_SIZE');
  }
  
  if (successRate < 95) {
    recommendations.push('⚠️ 成功率偏低，检查S3连接和重试配置');
  }
  
  if (queueWaiting < 5 && memoryUsageMB < 200) {
    recommendations.push('📈 资源充足，可以考虑增加并发或批处理大小');
  }
  
  return recommendations;
}

// 扩展时机建议
function getScalingAdvice() {
  console.log('\n🚀 扩展时机指南:');
  console.log('   📊 队列积压 > 50: 考虑增加Worker并发');
  console.log('   📈 CPU > 80%: 考虑增加实例');
  console.log('   💾 内存 > 80%: 优化批处理大小');
  console.log('   ⏱️  平均处理时间 > 2分钟: 检查网络和S3');
  
  console.log('\n🎯 MVP → 生产扩展步骤:');
  console.log('   1️⃣ 单实例优化: 调整WORKER_CONCURRENCY到6-8');
  console.log('   2️⃣ 资源扩展: 增加Redis和S3连接池');
  console.log('   3️⃣ 横向扩展: 启动第二个实例 (不同INSTANCE_ID)');
  console.log('   4️⃣ 负载均衡: 配置多实例负载分发');
}

// 主监控逻辑
function runMonitoring() {
  console.log('=' .repeat(60));
  console.log(new Date().toLocaleString());
  console.log('=' .repeat(60));
  
  displayStats();
  
  const stats = getConsumerStats();
  const recommendations = getPerformanceRecommendations(stats);
  
  if (recommendations.length > 0) {
    console.log('\n💡 性能建议:');
    recommendations.forEach(rec => console.log(`   ${rec}`));
  } else {
    console.log('\n✅ 当前性能良好，无需调整');
  }
  
  getScalingAdvice();
  
  console.log('\n📱 实时监控命令:');
  console.log('   tail -f logs/consumer.log | grep -E "(processed|duplicate|error)"');
  console.log('   curl http://localhost:3000/health');
}

// 检查Consumer是否运行
function checkConsumerStatus() {
  console.log('🔍 检查Consumer状态...\n');
  
  // 这里可以添加实际的健康检查逻辑
  const isRunning = true; // 模拟
  
  if (isRunning) {
    console.log('✅ Consumer正在运行\n');
    runMonitoring();
  } else {
    console.log('❌ Consumer未运行');
    console.log('\n🚀 启动命令:');
    console.log('   node apps/consumer/start-mvp.js');
    console.log('   或');
    console.log('   cd apps/consumer && npm start');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  checkConsumerStatus();
  
  // 每30秒刷新一次监控数据
  console.log('\n🔄 监控将每30秒刷新...');
  setInterval(() => {
    console.clear();
    checkConsumerStatus();
  }, 30000);
}