{"name": "video-consumer", "version": "0.0.0", "private": true, "main": "dist/app.js", "scripts": {"dev": "tsx watch src/app.ts", "build": "tsc", "start": "node dist/app.js", "type-check": "tsc --noEmit"}, "dependencies": {"@repo/api": "workspace:*", "@repo/database": "workspace:*", "@repo/logs": "workspace:*", "@repo/queue": "workspace:*", "bullmq": "^5.56.7", "dotenv": "^17.2.0", "ioredis": "^5.6.1"}, "devDependencies": {"@repo/tsconfig": "workspace:*", "@types/node": "^24.0.13", "tsx": "^4.20.3", "typescript": "^5.8.3"}}