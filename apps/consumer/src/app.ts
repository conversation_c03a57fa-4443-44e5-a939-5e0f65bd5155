import dotenv from 'dotenv';
dotenv.config({ path: '../../.env.local' });
import { logger } from '@repo/logs';
import { VideoProcessorWorker } from './workers/video-processor';
import { HealthChecker } from './services/health-checker';
import { getRedisPool } from '@repo/queue';

/**
 * Consumer 应用主入口
 */
class ConsumerApp {
  private worker: VideoProcessorWorker;
  private healthChecker: HealthChecker;
  private isShuttingDown = false;
  private configCheckTimer?: NodeJS.Timeout;

  constructor() {
    this.worker = new VideoProcessorWorker();
    this.healthChecker = new HealthChecker();
  }

  /**
   * 启动应用
   */
  async start() {
    logger.info('[ConsumerApp] Starting video generator consumer');

    // Worker 已经在构造函数中自动开始监听队列
    logger.info('[ConsumerApp] Video processor worker is listening for jobs');

    // 设置优雅关闭
    this.setupGracefulShutdown();

    // 定期健康检查 (可选)
    if (process.env.HEALTH_CHECK_INTERVAL) {
      this.startHealthCheckInterval(
        parseInt(process.env.HEALTH_CHECK_INTERVAL) * 1000
      );
    }

    // 启动配置优化检查
    this.startConfigurationMonitoring();

    // 启动Redis连接池健康检查
    this.startRedisPoolMonitoring();

    logger.info('[ConsumerApp] Consumer started successfully with optimizations enabled', {
      workerStats: await this.worker.getStats(),
      redisPoolStats: getRedisPool().getStats()
    });
  }

  /**
   * 设置优雅关闭
   */
  private setupGracefulShutdown() {
    const shutdown = async (signal: string) => {
      if (this.isShuttingDown) {
        logger.warn('[ConsumerApp] Shutdown already in progress');
        return;
      }

      this.isShuttingDown = true;
      logger.info(`[ConsumerApp] ${signal} received, shutting down gracefully`);

      try {
        // 清理定时器
        if (this.configCheckTimer) {
          clearInterval(this.configCheckTimer);
        }
        
        // 关闭Worker
        await this.worker.shutdown();
        
        // 关闭Redis连接池
        await getRedisPool().shutdown();
        
        logger.info('[ConsumerApp] Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error('[ConsumerApp] Error during shutdown', { 
          error: error instanceof Error ? error.message : String(error) 
        });
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));

    // 处理未捕获的异常
    process.on('uncaughtException', (error) => {
      logger.error('[ConsumerApp] Uncaught exception', { error: error.message });
      shutdown('UNCAUGHT_EXCEPTION');
    });

    process.on('unhandledRejection', (reason) => {
      logger.error('[ConsumerApp] Unhandled rejection', { 
        reason: reason instanceof Error ? reason.message : String(reason) 
      });
      shutdown('UNHANDLED_REJECTION');
    });
  }

  /**
   * 启动健康检查定时器
   */
  private startHealthCheckInterval(intervalMs: number) {
    setInterval(async () => {
      try {
        const health = await this.healthChecker.checkHealth();
        if (health.status !== 'healthy') {
          logger.warn('[ConsumerApp] Health check warning', { health });
        }
      } catch (error) {
        logger.error('[ConsumerApp] Health check failed', { 
          error: error instanceof Error ? error.message : String(error) 
        });
      }
    }, intervalMs);

    logger.info('[ConsumerApp] Health check interval started', { 
      intervalMs: intervalMs / 1000 + 's' 
    });
  }

  /**
   * 启动配置优化监控
   */
  private startConfigurationMonitoring() {
    const configCheckInterval = parseInt(process.env.CONFIG_CHECK_INTERVAL || '300000'); // 5分钟
    
    this.configCheckTimer = setInterval(async () => {
      try {
        const suggestedChanges = await this.worker.adjustConfiguration();
        if (suggestedChanges) {
          logger.info('[ConsumerApp] Configuration optimization suggestions available', {
            suggestedChanges,
            note: 'Apply these changes to environment variables and restart for optimal performance'
          });
        }
      } catch (error) {
        logger.error('[ConsumerApp] Configuration monitoring failed', {
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }, configCheckInterval);

    logger.info('[ConsumerApp] Configuration monitoring started', {
      interval: `${configCheckInterval / 1000}s`
    });
  }

  /**
   * 启动Redis连接池监控
   */
  private startRedisPoolMonitoring() {
    const redisCheckInterval = parseInt(process.env.REDIS_POOL_CHECK_INTERVAL || '60000'); // 1分钟
    
    setInterval(async () => {
      try {
        const redisPool = getRedisPool();
        const stats = await redisPool.performHealthCheck();
        
        if (stats.healthyConnections < stats.totalConnections * 0.5) {
          logger.warn('[ConsumerApp] Redis connection pool health warning', {
            healthyConnections: stats.healthyConnections,
            totalConnections: stats.totalConnections,
            healthRate: `${Math.round((stats.healthyConnections / stats.totalConnections) * 100)}%`
          });
        }
      } catch (error) {
        logger.error('[ConsumerApp] Redis pool monitoring failed', {
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }, redisCheckInterval);

    logger.info('[ConsumerApp] Redis pool monitoring started', {
      interval: `${redisCheckInterval / 1000}s`
    });
  }
}

/**
 * 启动应用
 */
async function main() {
  try {
    const app = new ConsumerApp();
    await app.start();
  } catch (error) {
    logger.error('[ConsumerApp] Failed to start application', { 
      error: error instanceof Error ? error.message : String(error) 
    });
    process.exit(1);
  }
}

// 启动应用
if (require.main === module) {
  main();
}