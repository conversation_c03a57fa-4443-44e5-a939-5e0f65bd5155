import { logger } from '@repo/logs';
import { cpus } from 'os';

export interface WorkerConfig {
  concurrency: number;
  removeOnComplete: number;
  removeOnFail: number;
  stalledInterval: number;
  maxStalledCount: number;
}

export interface SystemMetrics {
  cpuCount: number;
  availableMemory: number;
  nodeEnv: string;
  platform: string;
}

/**
 * 动态计算最优Worker配置
 */
export class WorkerConfigManager {
  private static instance: WorkerConfigManager;
  
  private constructor() {}
  
  static getInstance(): WorkerConfigManager {
    if (!this.instance) {
      this.instance = new WorkerConfigManager();
    }
    return this.instance;
  }

  /**
   * 获取优化的Worker配置
   */
  getOptimalConfig(): WorkerConfig {
    const envConcurrency = parseInt(process.env.WORKER_CONCURRENCY || '0');
    const systemConcurrency = this.calculateSystemConcurrency();
    
    // 使用环境变量或系统计算值
    const concurrency = envConcurrency > 0 ? envConcurrency : systemConcurrency;
    
    const config: WorkerConfig = {
      concurrency,
      removeOnComplete: Math.max(20, concurrency * 4), // 动态调整保留任务数
      removeOnFail: Math.max(10, concurrency * 2),
      stalledInterval: this.calculateStalledInterval(concurrency),
      maxStalledCount: 1
    };

    logger.info('[WorkerConfig] Calculated optimal worker configuration', {
      envConcurrency,
      systemConcurrency,
      finalConfig: config,
      systemMetrics: this.getSystemMetrics()
    });

    return config;
  }

  /**
   * 基于系统资源计算并发数
   */
  private calculateSystemConcurrency(): number {
    const metrics = this.getSystemMetrics();
    const { cpuCount, availableMemory, nodeEnv } = metrics;
    
    // 生产环境更保守，开发环境更激进
    const isProduction = nodeEnv === 'production';
    const cpuUtilization = isProduction ? 0.75 : 0.5;
    
    const baseConcurrency = Math.min(6, Math.max(2, Math.floor(cpuCount * cpuUtilization)));
    
    // 根据可用内存调整（每个并发任务假设需要200MB内存）
    const memoryBasedConcurrency = Math.floor(availableMemory / (200 * 1024 * 1024));
    
    // 取两者最小值，但不超过8个并发
    const finalConcurrency = Math.min(baseConcurrency, memoryBasedConcurrency, 8);
    
    logger.debug('[WorkerConfig] Concurrency calculation details', {
      cpuCount,
      cpuUtilization,
      baseConcurrency,
      availableMemoryMB: Math.round(availableMemory / (1024 * 1024)),
      memoryBasedConcurrency,
      finalConcurrency
    });
    
    return Math.max(2, finalConcurrency); // 至少保证2个并发
  }

  /**
   * 获取系统指标
   */
  private getSystemMetrics(): SystemMetrics {
    const totalMemory = require('os').totalmem();
    const freeMemory = require('os').freemem();
    const usedMemory = process.memoryUsage().heapUsed;
    
    return {
      cpuCount: cpus().length,
      availableMemory: Math.min(freeMemory, totalMemory - usedMemory),
      nodeEnv: process.env.NODE_ENV || 'development',
      platform: process.platform
    };
  }

  /**
   * 根据并发数计算stalled检查间隔
   */
  private calculateStalledInterval(concurrency: number): number {
    // 并发数越高，检查间隔越短
    return Math.max(10000, 30000 - (concurrency * 2000));
  }

  /**
   * 运行时调整配置
   */
  async adjustConfigBasedOnMetrics(currentQueueStats: any): Promise<Partial<WorkerConfig>> {
    const { waiting, active, failed } = currentQueueStats;
    
    logger.debug('[WorkerConfig] Evaluating config adjustment', {
      waiting,
      active,
      failed
    });
    
    // 如果队列积压严重且当前并发不足，建议增加并发
    if (waiting > 20 && active < 6) {
      const newConcurrency = Math.min(8, active + 2);
      logger.info('[WorkerConfig] Suggesting concurrency increase', {
        currentActive: active,
        suggestedConcurrency: newConcurrency,
        reason: 'high queue backlog'
      });
      return { concurrency: newConcurrency };
    }
    
    // 如果失败率高，建议降低并发
    if (failed > 10 && active > 2) {
      const newConcurrency = Math.max(2, active - 1);
      logger.info('[WorkerConfig] Suggesting concurrency decrease', {
        currentActive: active,
        suggestedConcurrency: newConcurrency,
        failedCount: failed,
        reason: 'high failure rate'
      });
      return { concurrency: newConcurrency };
    }
    
    return {};
  }

  /**
   * 获取推荐的环境变量配置
   */
  getRecommendedEnvVars(): Record<string, string> {
    const config = this.getOptimalConfig();
    const metrics = this.getSystemMetrics();
    
    return {
      WORKER_CONCURRENCY: config.concurrency.toString(),
      WORKER_REMOVE_ON_COMPLETE: config.removeOnComplete.toString(),
      WORKER_REMOVE_ON_FAIL: config.removeOnFail.toString(),
      WORKER_STALLED_INTERVAL: config.stalledInterval.toString(),
      // 系统信息参考
      SYSTEM_CPU_COUNT: metrics.cpuCount.toString(),
      SYSTEM_AVAILABLE_MEMORY_MB: Math.round(metrics.availableMemory / (1024 * 1024)).toString()
    };
  }
}