import { logger } from '@repo/logs';
import type { VideoWebhookEvent } from '@repo/database';
import { FilePreCheckService } from './file-precheck';
import type { FileInfo, UploadStrategy } from './file-precheck';

export interface BatchProcessingOptions {
  maxBatchSize: number;
  maxConcurrency: number;
  prioritizeSmallFiles: boolean;
  enableDynamicBatching: boolean;
  timeoutMs: number;
}

export interface BatchGroup {
  type: 'small' | 'medium' | 'large';
  events: Array<{
    event: VideoWebhookEvent;
    fileInfo: FileInfo;
    strategy: UploadStrategy;
  }>;
  totalSize: number;
  avgSize: number;
  priority: number;
}

export interface BatchResult {
  totalProcessed: number;
  successful: number;
  failed: number;
  processingTime: number;
  groupResults: Array<{
    groupType: string;
    processed: number;
    successful: number;
    failed: number;
    avgProcessingTime: number;
  }>;
}

/**
 * 批量视频处理器
 * 基于文件大小智能分组和并发处理
 */
export class BatchVideoProcessor {
  private static instance: BatchVideoProcessor;
  private preCheckService: FilePreCheckService;
  private processingStats: Map<string, number[]> = new Map();
  
  private constructor() {
    this.preCheckService = FilePreCheckService.getInstance();
  }
  
  static getInstance(): BatchVideoProcessor {
    if (!this.instance) {
      this.instance = new BatchVideoProcessor();
    }
    return this.instance;
  }

  /**
   * 批量处理视频事件（按文件大小分组）
   */
  async processBatchBySize(
    events: VideoWebhookEvent[],
    options: Partial<BatchProcessingOptions> = {}
  ): Promise<BatchResult> {
    const startTime = Date.now();
    const config = this.getProcessingConfig(options);
    
    logger.info('[BatchProcessor] Starting batch processing by file size', {
      eventCount: events.length,
      config
    });

    try {
      // 1. 批量预检所有文件信息
      const fileInfos = await this.batchPreCheckFiles(events);
      
      // 2. 按文件大小分组并生成策略
      const groups = this.groupEventsByFileSize(events, fileInfos);
      
      // 3. 按优先级排序分组
      const sortedGroups = this.prioritizeGroups(groups, config);
      
      // 4. 执行分层批处理
      const groupResults = await this.executeGroupProcessing(sortedGroups, config);
      
      // 5. 汇总结果
      const result = this.aggregateResults(groupResults, Date.now() - startTime);
      
      logger.info('[BatchProcessor] Batch processing completed', {
        result,
        groupCount: groups.length
      });
      
      return result;
      
    } catch (error) {
      const processingTime = Date.now() - startTime;
      logger.error('[BatchProcessor] Batch processing failed', {
        eventCount: events.length,
        error: error instanceof Error ? error.message : String(error),
        processingTime: `${processingTime}ms`
      });
      
      // 返回失败结果
      return {
        totalProcessed: events.length,
        successful: 0,
        failed: events.length,
        processingTime,
        groupResults: []
      };
    }
  }

  /**
   * 批量预检文件信息
   */
  private async batchPreCheckFiles(events: VideoWebhookEvent[]): Promise<FileInfo[]> {
    logger.info('[BatchProcessor] Starting batch file precheck', {
      eventCount: events.length
    });

    // 提取所有有效的URL
    const urls = events
      .map(event => event.resultUrl)
      .filter(url => url && typeof url === 'string') as string[];

    if (urls.length === 0) {
      logger.warn('[BatchProcessor] No valid URLs found for precheck');
      return events.map(() => ({
        url: '',
        size: 0,
        contentType: 'unknown',
        isValid: false,
        supportsRangeRequests: false,
        error: 'No valid URL'
      }));
    }

    // 批量预检，控制并发数
    const concurrency = Math.min(10, urls.length);
    const fileInfos = await this.preCheckService.batchPreCheck(urls, concurrency);
    
    // 确保返回的数组长度与事件数组相匹配
    const paddedFileInfos: FileInfo[] = [];
    let urlIndex = 0;
    
    for (const event of events) {
      if (event.resultUrl && typeof event.resultUrl === 'string') {
        paddedFileInfos.push(fileInfos[urlIndex] || {
          url: event.resultUrl,
          size: 0,
          contentType: 'unknown',
          isValid: false,
          supportsRangeRequests: false,
          error: 'Precheck failed'
        });
        urlIndex++;
      } else {
        paddedFileInfos.push({
          url: '',
          size: 0,
          contentType: 'unknown',
          isValid: false,
          supportsRangeRequests: false,
          error: 'Invalid URL'
        });
      }
    }

    const validCount = fileInfos.filter(info => info.isValid).length;
    logger.info('[BatchProcessor] Batch file precheck completed', {
      total: events.length,
      validUrls: urls.length,
      validFiles: validCount,
      invalidFiles: events.length - validCount
    });

    return paddedFileInfos;
  }

  /**
   * 按文件大小分组事件
   */
  private groupEventsByFileSize(
    events: VideoWebhookEvent[],
    fileInfos: FileInfo[]
  ): BatchGroup[] {
    const small: Array<{ event: VideoWebhookEvent; fileInfo: FileInfo; strategy: UploadStrategy }> = [];
    const medium: Array<{ event: VideoWebhookEvent; fileInfo: FileInfo; strategy: UploadStrategy }> = [];
    const large: Array<{ event: VideoWebhookEvent; fileInfo: FileInfo; strategy: UploadStrategy }> = [];

    events.forEach((event, index) => {
      const fileInfo = fileInfos[index];
      const strategy = this.preCheckService.selectUploadStrategy(fileInfo);
      const item = { event, fileInfo, strategy };
      
      const sizeMB = fileInfo.size / (1024 * 1024);
      
      if (sizeMB < 20) {
        small.push(item);
      } else if (sizeMB < 100) {
        medium.push(item);
      } else {
        large.push(item);
      }
    });

    const groups: BatchGroup[] = [];
    
    if (small.length > 0) {
      groups.push({
        type: 'small',
        events: small,
        totalSize: small.reduce((sum, item) => sum + item.fileInfo.size, 0),
        avgSize: small.reduce((sum, item) => sum + item.fileInfo.size, 0) / small.length,
        priority: 1 // 最高优先级
      });
    }
    
    if (medium.length > 0) {
      groups.push({
        type: 'medium',
        events: medium,
        totalSize: medium.reduce((sum, item) => sum + item.fileInfo.size, 0),
        avgSize: medium.reduce((sum, item) => sum + item.fileInfo.size, 0) / medium.length,
        priority: 2
      });
    }
    
    if (large.length > 0) {
      groups.push({
        type: 'large',
        events: large,
        totalSize: large.reduce((sum, item) => sum + item.fileInfo.size, 0),
        avgSize: large.reduce((sum, item) => sum + item.fileInfo.size, 0) / large.length,
        priority: 3 // 最低优先级
      });
    }

    logger.info('[BatchProcessor] Events grouped by file size', {
      small: small.length,
      medium: medium.length,
      large: large.length,
      totalGroups: groups.length
    });

    return groups;
  }

  /**
   * 按优先级排序分组
   */
  private prioritizeGroups(
    groups: BatchGroup[],
    config: BatchProcessingOptions
  ): BatchGroup[] {
    if (!config.prioritizeSmallFiles) {
      return groups;
    }

    // 按优先级排序（小文件优先）
    const sortedGroups = [...groups].sort((a, b) => a.priority - b.priority);
    
    logger.debug('[BatchProcessor] Groups prioritized', {
      originalOrder: groups.map(g => g.type),
      prioritizedOrder: sortedGroups.map(g => g.type)
    });
    
    return sortedGroups;
  }

  /**
   * 执行分组处理
   */
  private async executeGroupProcessing(
    groups: BatchGroup[],
    config: BatchProcessingOptions
  ): Promise<Array<{
    groupType: string;
    processed: number;
    successful: number;
    failed: number;
    avgProcessingTime: number;
  }>> {
    const groupResults = [];

    for (const group of groups) {
      const groupStartTime = Date.now();
      
      logger.info('[BatchProcessor] Processing group', {
        groupType: group.type,
        eventCount: group.events.length,
        totalSizeMB: Math.round(group.totalSize / (1024 * 1024)),
        avgSizeMB: Math.round(group.avgSize / (1024 * 1024))
      });

      let groupResult;
      
      try {
        switch (group.type) {
          case 'small':
            groupResult = await this.processSmallFilesBatch(group, config);
            break;
          case 'medium':
            groupResult = await this.processMediumFilesBatch(group, config);
            break;
          case 'large':
            groupResult = await this.processLargeFilesBatch(group, config);
            break;
          default:
            throw new Error(`Unknown group type: ${group.type}`);
        }
      } catch (error) {
        logger.error('[BatchProcessor] Group processing failed', {
          groupType: group.type,
          error: error instanceof Error ? error.message : String(error)
        });
        
        groupResult = {
          groupType: group.type,
          processed: group.events.length,
          successful: 0,
          failed: group.events.length,
          avgProcessingTime: Date.now() - groupStartTime
        };
      }

      groupResults.push(groupResult);
      
      logger.info('[BatchProcessor] Group processing completed', {
        ...groupResult,
        processingTime: `${Date.now() - groupStartTime}ms`
      });
    }

    return groupResults;
  }

  /**
   * 处理小文件批次（高并发）
   */
  private async processSmallFilesBatch(
    group: BatchGroup,
    config: BatchProcessingOptions
  ): Promise<{
    groupType: string;
    processed: number;
    successful: number;
    failed: number;
    avgProcessingTime: number;
  }> {
    const concurrency = Math.min(8, config.maxConcurrency); // 小文件高并发
    
    logger.info('[BatchProcessor] Processing small files batch', {
      eventCount: group.events.length,
      concurrency
    });

    const results = await this.processConcurrentBatch(
      group.events,
      concurrency,
      'small-file-processing'
    );

    return this.calculateGroupResult('small', group.events.length, results);
  }

  /**
   * 处理中等文件批次（中等并发）
   */
  private async processMediumFilesBatch(
    group: BatchGroup,
    config: BatchProcessingOptions
  ): Promise<{
    groupType: string;
    processed: number;
    successful: number;
    failed: number;
    avgProcessingTime: number;
  }> {
    const concurrency = Math.min(4, config.maxConcurrency); // 中等文件中等并发
    
    logger.info('[BatchProcessor] Processing medium files batch', {
      eventCount: group.events.length,
      concurrency
    });

    const results = await this.processConcurrentBatch(
      group.events,
      concurrency,
      'medium-file-processing'
    );

    return this.calculateGroupResult('medium', group.events.length, results);
  }

  /**
   * 处理大文件批次（低并发）
   */
  private async processLargeFilesBatch(
    group: BatchGroup,
    config: BatchProcessingOptions
  ): Promise<{
    groupType: string;
    processed: number;
    successful: number;
    failed: number;
    avgProcessingTime: number;
  }> {
    const concurrency = Math.min(2, config.maxConcurrency); // 大文件低并发
    
    logger.info('[BatchProcessor] Processing large files batch', {
      eventCount: group.events.length,
      concurrency
    });

    const results = await this.processConcurrentBatch(
      group.events,
      concurrency,
      'large-file-processing'
    );

    return this.calculateGroupResult('large', group.events.length, results);
  }

  /**
   * 并发处理批次
   */
  private async processConcurrentBatch(
    items: Array<{ event: VideoWebhookEvent; fileInfo: FileInfo; strategy: UploadStrategy }>,
    concurrency: number,
    batchType: string
  ): Promise<Array<{ success: boolean; duration: number; error?: string }>> {
    const results: Array<{ success: boolean; duration: number; error?: string }> = [];
    
    // 分批处理，控制并发数
    for (let i = 0; i < items.length; i += concurrency) {
      const batch = items.slice(i, i + concurrency);
      
      const batchResults = await Promise.allSettled(
        batch.map(item => this.processSingleItem(item, batchType))
      );
      
      batchResults.forEach(result => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push({
            success: false,
            duration: 0,
            error: result.reason?.message || 'Unknown error'
          });
        }
      });
    }

    return results;
  }

  /**
   * 处理单个项目
   */
  private async processSingleItem(
    item: { event: VideoWebhookEvent; fileInfo: FileInfo; strategy: UploadStrategy },
    batchType: string
  ): Promise<{ success: boolean; duration: number; error?: string }> {
    const startTime = Date.now();
    
    try {
      // 动态导入处理器以避免循环依赖
      const { VideoWebhookProcessor } = await import('@repo/api/src/lib/services/video-webhook-processor');
      const processor = new VideoWebhookProcessor();
      
      // 处理事件，传入文件信息和策略
      await processor.processEvent(item.event, {
        fileInfo: item.fileInfo,
        uploadStrategy: item.strategy
      });
      
      const duration = Date.now() - startTime;
      
      // 记录处理时间统计
      this.recordProcessingTime(batchType, duration);
      
      return { success: true, duration };
      
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      logger.debug('[BatchProcessor] Single item processing failed', {
        eventId: item.event.id,
        batchType,
        duration: `${duration}ms`,
        error: errorMessage
      });
      
      return { success: false, duration, error: errorMessage };
    }
  }

  /**
   * 计算分组结果
   */
  private calculateGroupResult(
    groupType: string,
    totalCount: number,
    results: Array<{ success: boolean; duration: number; error?: string }>
  ): {
    groupType: string;
    processed: number;
    successful: number;
    failed: number;
    avgProcessingTime: number;
  } {
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    const avgProcessingTime = results.length > 0 
      ? results.reduce((sum, r) => sum + r.duration, 0) / results.length 
      : 0;

    return {
      groupType,
      processed: totalCount,
      successful,
      failed,
      avgProcessingTime
    };
  }

  /**
   * 汇总最终结果
   */
  private aggregateResults(
    groupResults: Array<{
      groupType: string;
      processed: number;
      successful: number;
      failed: number;
      avgProcessingTime: number;
    }>,
    totalTime: number
  ): BatchResult {
    const totalProcessed = groupResults.reduce((sum, r) => sum + r.processed, 0);
    const successful = groupResults.reduce((sum, r) => sum + r.successful, 0);
    const failed = groupResults.reduce((sum, r) => sum + r.failed, 0);

    return {
      totalProcessed,
      successful,
      failed,
      processingTime: totalTime,
      groupResults
    };
  }

  /**
   * 获取处理配置
   */
  private getProcessingConfig(options: Partial<BatchProcessingOptions>): BatchProcessingOptions {
    return {
      maxBatchSize: options.maxBatchSize || 50,
      maxConcurrency: options.maxConcurrency || 8,
      prioritizeSmallFiles: options.prioritizeSmallFiles ?? true,
      enableDynamicBatching: options.enableDynamicBatching ?? true,
      timeoutMs: options.timeoutMs || 300000 // 5分钟
    };
  }

  /**
   * 记录处理时间统计
   */
  private recordProcessingTime(batchType: string, duration: number) {
    if (!this.processingStats.has(batchType)) {
      this.processingStats.set(batchType, []);
    }
    
    const stats = this.processingStats.get(batchType)!;
    stats.push(duration);
    
    // 只保留最近100个记录
    if (stats.length > 100) {
      stats.shift();
    }
  }

  /**
   * 获取处理统计信息
   */
  getProcessingStats(): Record<string, {
    count: number;
    avgDuration: number;
    minDuration: number;
    maxDuration: number;
  }> {
    const result: Record<string, any> = {};
    
    for (const [batchType, durations] of this.processingStats.entries()) {
      if (durations.length > 0) {
        result[batchType] = {
          count: durations.length,
          avgDuration: durations.reduce((sum, d) => sum + d, 0) / durations.length,
          minDuration: Math.min(...durations),
          maxDuration: Math.max(...durations)
        };
      }
    }
    
    return result;
  }
}