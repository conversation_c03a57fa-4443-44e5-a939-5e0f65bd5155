import { Redis } from 'ioredis';
import { getRedisConnection } from '@repo/queue';
import { logger } from '@repo/logs';

/**
 * 分布式锁服务 - 防止多实例重复处理
 */
export class DistributedLock {
  private redis: Redis;
  
  constructor() {
    this.redis = getRedisConnection();
  }
  
  /**
   * 获取分布式锁
   */
  async acquireLock(key: string, ttl: number = 60000): Promise<string | null> {
    const lockValue = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      // 使用Redis SET key value NX PX ttl实现分布式锁
      const result = await this.redis.set(
        `lock:consumer:${key}`,
        lockValue,
        'NX',       // 仅当key不存在时设置
        'PX', ttl   // 毫秒级TTL
      );
      
      if (result === 'OK') {
        logger.debug('[DistributedLock] Lock acquired successfully', {
          key,
          lockValue,
          ttl
        });
        return lockValue;
      } else {
        logger.debug('[DistributedLock] Failed to acquire lock - already exists', {
          key,
          ttl
        });
        return null;
      }
    } catch (error) {
      logger.error('[DistributedLock] Error acquiring lock', {
        key,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }
  
  /**
   * 释放分布式锁（原子性确保）
   */
  async releaseLock(key: string, lockValue: string): Promise<boolean> {
    // Lua脚本确保原子性释放（只有lock owner能释放）
    const luaScript = `
      if redis.call("get", KEYS[1]) == ARGV[1] then
        return redis.call("del", KEYS[1])
      else
        return 0
      end
    `;
    
    try {
      const result = await this.redis.eval(
        luaScript, 
        1, 
        `lock:consumer:${key}`, 
        lockValue
      ) as number;
      
      const released = result === 1;
      
      if (released) {
        logger.debug('[DistributedLock] Lock released successfully', {
          key,
          lockValue
        });
      } else {
        logger.warn('[DistributedLock] Failed to release lock - not owner or expired', {
          key,
          lockValue
        });
      }
      
      return released;
    } catch (error) {
      logger.error('[DistributedLock] Error releasing lock', {
        key,
        lockValue,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }
  
  /**
   * 检查锁是否存在
   */
  async isLocked(key: string): Promise<boolean> {
    try {
      const exists = await this.redis.exists(`lock:consumer:${key}`);
      return exists === 1;
    } catch (error) {
      logger.error('[DistributedLock] Error checking lock existence', {
        key,
        error: error instanceof Error ? error.message : String(error)
      });
      return false; // 默认假设没有锁，允许处理
    }
  }
  
  /**
   * 获取锁剩余TTL
   */
  async getLockTTL(key: string): Promise<number> {
    try {
      const ttl = await this.redis.pttl(`lock:consumer:${key}`);
      return ttl;
    } catch (error) {
      logger.error('[DistributedLock] Error getting lock TTL', {
        key,
        error: error instanceof Error ? error.message : String(error)
      });
      return -1;
    }
  }
  
  /**
   * 强制释放锁（管理员操作）
   */
  async forceReleaseLock(key: string): Promise<boolean> {
    try {
      const result = await this.redis.del(`lock:consumer:${key}`);
      
      logger.warn('[DistributedLock] Lock force released', {
        key,
        result
      });
      
      return result === 1;
    } catch (error) {
      logger.error('[DistributedLock] Error force releasing lock', {
        key,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }
  
  /**
   * 获取所有活跃锁信息（监控用）
   */
  async getActiveLocks(): Promise<Array<{key: string, ttl: number}>> {
    try {
      const lockKeys = await this.redis.keys('lock:consumer:*');
      const locks = [];
      
      for (const lockKey of lockKeys) {
        const ttl = await this.redis.pttl(lockKey);
        if (ttl > 0) {
          locks.push({
            key: lockKey.replace('lock:consumer:', ''),
            ttl
          });
        }
      }
      
      return locks;
    } catch (error) {
      logger.error('[DistributedLock] Error getting active locks', {
        error: error instanceof Error ? error.message : String(error)
      });
      return [];
    }
  }
  
  /**
   * 清理过期锁（定期维护）
   */
  async cleanupExpiredLocks(): Promise<number> {
    try {
      const lockKeys = await this.redis.keys('lock:consumer:*');
      let cleanedCount = 0;
      
      for (const lockKey of lockKeys) {
        const ttl = await this.redis.pttl(lockKey);
        if (ttl === -1) { // 没有TTL的锁，可能是异常情况
          await this.redis.del(lockKey);
          cleanedCount++;
          
          logger.warn('[DistributedLock] Cleaned up lock without TTL', {
            lockKey
          });
        }
      }
      
      if (cleanedCount > 0) {
        logger.info('[DistributedLock] Cleanup completed', {
          cleanedCount
        });
      }
      
      return cleanedCount;
    } catch (error) {
      logger.error('[DistributedLock] Error during cleanup', {
        error: error instanceof Error ? error.message : String(error)
      });
      return 0;
    }
  }
}

/**
 * 单例模式，确保全局只有一个锁管理器
 */
let distributedLockInstance: DistributedLock | null = null;

export function getDistributedLock(): DistributedLock {
  if (!distributedLockInstance) {
    distributedLockInstance = new DistributedLock();
  }
  return distributedLockInstance;
}