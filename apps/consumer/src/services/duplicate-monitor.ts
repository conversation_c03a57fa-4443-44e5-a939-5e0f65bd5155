import { logger } from '@repo/logs';
import { getRedisConnection } from '@repo/queue';

/**
 * 重复消费监控服务
 */
export class DuplicateProcessingMonitor {
  private duplicateCount = 0;
  private processedEvents = new Set<string>();
  private redis = getRedisConnection();
  
  /**
   * 记录重复处理尝试
   */
  logDuplicateAttempt(eventId: string, reason: string, details?: any) {
    this.duplicateCount++;
    
    logger.warn('[DuplicateMonitor] Duplicate processing attempt detected', {
      eventId,
      reason,
      totalDuplicates: this.duplicateCount,
      timestamp: new Date().toISOString(),
      details
    });
    
    // 📢 如果重复率过高，发送告警
    if (this.duplicateCount > 10) {
      this.sendDuplicateAlert();
    }
    
    // 记录到Redis用于跨实例统计
    this.recordDuplicateInRedis(eventId, reason).catch(error => {
      logger.error('[DuplicateMonitor] Failed to record duplicate in Redis', {
        eventId,
        error: error instanceof Error ? error.message : String(error)
      });
    });
  }
  
  /**
   * 标记事件已处理
   */
  markEventProcessed(eventId: string) {
    this.processedEvents.add(eventId);
    
    // 📝 定期清理已处理事件记录，防止内存泄漏
    if (this.processedEvents.size > 10000) {
      const eventsArray = Array.from(this.processedEvents);
      this.processedEvents = new Set(eventsArray.slice(-5000));
      
      logger.info('[DuplicateMonitor] Cleaned up old processed events', {
        oldSize: eventsArray.length,
        newSize: this.processedEvents.size
      });
    }
  }
  
  /**
   * 检查事件是否已处理
   */
  isEventProcessed(eventId: string): boolean {
    return this.processedEvents.has(eventId);
  }
  
  /**
   * 检查事件是否正在被其他实例处理
   */
  async isEventBeingProcessed(eventId: string): Promise<boolean> {
    try {
      const processingKey = `processing:${eventId}`;
      const exists = await this.redis.exists(processingKey);
      return exists === 1;
    } catch (error) {
      logger.error('[DuplicateMonitor] Error checking if event is being processed', {
        eventId,
        error: error instanceof Error ? error.message : String(error)
      });
      return false; // 默认假设没有在处理
    }
  }
  
  /**
   * 标记事件开始处理
   */
  async markEventProcessingStart(eventId: string, instanceId?: string): Promise<boolean> {
    try {
      const processingKey = `processing:${eventId}`;
      const value = instanceId || process.env.INSTANCE_ID || 'unknown';
      
      // 使用SET NX确保原子性
      const result = await this.redis.set(
        processingKey,
        value,
        'PX', 300000, // 5分钟TTL
        'NX'
      );
      
      const success = result === 'OK';
      
      if (success) {
        logger.debug('[DuplicateMonitor] Marked event processing start', {
          eventId,
          instanceId: value
        });
      } else {
        logger.warn('[DuplicateMonitor] Event already being processed by another instance', {
          eventId
        });
      }
      
      return success;
    } catch (error) {
      logger.error('[DuplicateMonitor] Error marking event processing start', {
        eventId,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }
  
  /**
   * 标记事件处理完成
   */
  async markEventProcessingComplete(eventId: string): Promise<void> {
    try {
      const processingKey = `processing:${eventId}`;
      await this.redis.del(processingKey);
      
      logger.debug('[DuplicateMonitor] Marked event processing complete', {
        eventId
      });
    } catch (error) {
      logger.error('[DuplicateMonitor] Error marking event processing complete', {
        eventId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
  
  /**
   * 记录重复尝试到Redis
   */
  private async recordDuplicateInRedis(eventId: string, reason: string): Promise<void> {
    try {
      const duplicateKey = `duplicate:${eventId}`;
      const duplicateData = JSON.stringify({
        eventId,
        reason,
        timestamp: Date.now(),
        instanceId: process.env.INSTANCE_ID || 'unknown'
      });
      
      // 记录重复尝试，24小时TTL
      await this.redis.setex(duplicateKey, 86400, duplicateData);
      
      // 增加全局重复计数
      const globalCountKey = 'global:duplicate:count';
      await this.redis.incr(globalCountKey);
      await this.redis.expire(globalCountKey, 86400);
      
    } catch (error) {
      logger.error('[DuplicateMonitor] Error recording duplicate in Redis', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
  
  /**
   * 发送重复处理告警
   */
  private sendDuplicateAlert() {
    logger.error('[DuplicateMonitor] HIGH DUPLICATE RATE ALERT', {
      duplicateCount: this.duplicateCount,
      threshold: 10,
      instanceId: process.env.INSTANCE_ID || 'unknown',
      timestamp: new Date().toISOString(),
      recommendation: [
        'Check for multiple consumer instances processing same events',
        'Verify Redis connection stability',
        'Review distributed lock configuration',
        'Check network partitioning issues'
      ]
    });
    
    // 可以在这里集成外部告警系统，如Slack、PagerDuty等
  }
  
  /**
   * 获取监控统计信息
   */
  async getStats() {
    try {
      const globalCountKey = 'global:duplicate:count';
      const globalDuplicateCount = await this.redis.get(globalCountKey);
      
      return {
        localDuplicateCount: this.duplicateCount,
        globalDuplicateCount: parseInt(globalDuplicateCount || '0'),
        localProcessedEvents: this.processedEvents.size,
        instanceId: process.env.INSTANCE_ID || 'unknown'
      };
    } catch (error) {
      logger.error('[DuplicateMonitor] Error getting stats', {
        error: error instanceof Error ? error.message : String(error)
      });
      
      return {
        localDuplicateCount: this.duplicateCount,
        globalDuplicateCount: 0,
        localProcessedEvents: this.processedEvents.size,
        instanceId: process.env.INSTANCE_ID || 'unknown'
      };
    }
  }
  
  /**
   * 清理过期的处理标记
   */
  async cleanupExpiredProcessingMarks(): Promise<number> {
    try {
      const pattern = 'processing:*';
      const keys = await this.redis.keys(pattern);
      let cleanedCount = 0;
      
      for (const key of keys) {
        const ttl = await this.redis.pttl(key);
        if (ttl === -1) { // 没有TTL，可能是异常情况
          await this.redis.del(key);
          cleanedCount++;
          
          const eventId = key.replace('processing:', '');
          logger.warn('[DuplicateMonitor] Cleaned up processing mark without TTL', {
            eventId,
            key
          });
        }
      }
      
      if (cleanedCount > 0) {
        logger.info('[DuplicateMonitor] Cleanup completed', {
          cleanedCount
        });
      }
      
      return cleanedCount;
    } catch (error) {
      logger.error('[DuplicateMonitor] Error during cleanup', {
        error: error instanceof Error ? error.message : String(error)
      });
      return 0;
    }
  }
  
  /**
   * 重置监控统计
   */
  reset() {
    this.duplicateCount = 0;
    this.processedEvents.clear();
    
    logger.info('[DuplicateMonitor] Monitor stats reset', {
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * 单例模式，确保全局只有一个监控器
 */
let duplicateMonitorInstance: DuplicateProcessingMonitor | null = null;

export function getDuplicateMonitor(): DuplicateProcessingMonitor {
  if (!duplicateMonitorInstance) {
    duplicateMonitorInstance = new DuplicateProcessingMonitor();
  }
  return duplicateMonitorInstance;
}