import { logger } from '@repo/logs';
import { request, Agent } from 'undici';

export interface FileInfo {
  url: string;
  size: number;
  contentType: string;
  isValid: boolean;
  supportsRangeRequests: boolean;
  lastModified?: string;
  etag?: string;
  error?: string;
}

export interface UploadStrategy {
  method: 'direct-stream' | 'chunked-stream' | 'parallel-multipart' | 'temp-file-fallback';
  chunkSize: number;
  concurrency: number;
  useMultipart: boolean;
  priority: number; // 1-5, 1=highest
}

/**
 * 文件预检和策略选择服务
 */
export class FilePreCheckService {
  private static instance: FilePreCheckService;
  private undiciAgent: Agent;
  
  private constructor() {
    // 创建专用的 undici Agent，与VideoUploadService保持一致
    this.undiciAgent = new Agent({
      connect: {
        family: 4,        // 强制IPv4解决连接问题
        timeout: 30000,
        keepAlive: true,
        keepAliveInitialDelay: 10000,
      },
    });
  }
  
  static getInstance(): FilePreCheckService {
    if (!this.instance) {
      this.instance = new FilePreCheckService();
    }
    return this.instance;
  }

  /**
   * 预检文件信息
   */
  async preCheckFile(url: string, timeoutMs = 10000): Promise<FileInfo> {
    const startTime = Date.now();
    
    try {
      logger.debug('[FilePreCheck] Starting file precheck', { url });
      
      const { statusCode, headers } = await request(url, {
        method: 'HEAD',
        headers: {
          'User-Agent': 'VideoProcessor/1.0',
          'Accept': '*/*',
          'Cache-Control': 'no-cache',
        },
        headersTimeout: timeoutMs,
        bodyTimeout: timeoutMs,
        dispatcher: this.undiciAgent,  // 使用IPv4 Agent
      });

      const fileInfo: FileInfo = {
        url,
        size: this.parseContentLength(headers['content-length']),
        contentType: (headers['content-type'] as string) || 'video/mp4',
        isValid: statusCode >= 200 && statusCode < 300,
        supportsRangeRequests: (headers['accept-ranges'] as string) === 'bytes',
        lastModified: headers['last-modified'] as string,
        etag: headers['etag'] as string,
      };

      const duration = Date.now() - startTime;
      
      logger.info('[FilePreCheck] File precheck completed', {
        url,
        size: fileInfo.size,
        sizeMB: Math.round(fileInfo.size / (1024 * 1024)),
        contentType: fileInfo.contentType,
        isValid: fileInfo.isValid,
        supportsRangeRequests: fileInfo.supportsRangeRequests,
        duration: `${duration}ms`
      });

      return fileInfo;

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      // 详细错误分析
      const errorDetails = {
        errorType: typeof error,
        errorConstructor: error?.constructor?.name,
        errorMessage,
        errorString: String(error),
        errorCode: (error as any)?.code,
        errorCause: (error as any)?.cause,
        hasMessage: error instanceof Error && 'message' in error,
        messageLength: errorMessage?.length || 0,
        isEmptyMessage: errorMessage === '',
        errorKeys: error && typeof error === 'object' ? Object.keys(error) : [],
        fullError: JSON.stringify(error, Object.getOwnPropertyNames(error))
      };
      
      logger.error('[FilePreCheck] File precheck failed - DETAILED ANALYSIS', {
        url,
        error: errorMessage,
        duration: `${duration}ms`,
        errorDetails
      });

      return {
        url,
        size: 0,
        contentType: 'unknown',
        isValid: false,
        supportsRangeRequests: false,
        error: errorMessage
      };
    }
  }

  /**
   * 批量预检文件
   */
  async batchPreCheck(urls: string[], concurrency = 5): Promise<FileInfo[]> {
    logger.info('[FilePreCheck] Starting batch precheck', { 
      urlCount: urls.length, 
      concurrency 
    });

    const results: FileInfo[] = [];
    
    // 分批处理，控制并发
    for (let i = 0; i < urls.length; i += concurrency) {
      const batch = urls.slice(i, i + concurrency);
      const batchResults = await Promise.allSettled(
        batch.map(url => this.preCheckFile(url))
      );
      
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push({
            url: batch[index],
            size: 0,
            contentType: 'unknown',
            isValid: false,
            supportsRangeRequests: false,
            error: result.reason?.message || 'Unknown error'
          });
        }
      });
    }

    const validCount = results.filter(r => r.isValid).length;
    logger.info('[FilePreCheck] Batch precheck completed', {
      total: results.length,
      valid: validCount,
      invalid: results.length - validCount
    });

    return results;
  }

  /**
   * 根据文件信息选择最优上传策略
   */
  selectUploadStrategy(fileInfo: FileInfo): UploadStrategy {
    if (!fileInfo.isValid) {
      return {
        method: 'temp-file-fallback',
        chunkSize: 0,
        concurrency: 1,
        useMultipart: false,
        priority: 5 // 最低优先级
      };
    }

    const { size } = fileInfo;
    const sizeMB = size / (1024 * 1024);

    // 小文件策略 (< 10MB)
    if (sizeMB < 10) {
      logger.debug('[FilePreCheck] Selected strategy: direct-stream (small file)', { 
        sizeMB: Math.round(sizeMB) 
      });
      
      return {
        method: 'direct-stream',
        chunkSize: 0,
        concurrency: 1,
        useMultipart: false,
        priority: 1 // 高优先级
      };
    }

    // 中等文件策略 (10-100MB)
    if (sizeMB < 100) {
      const strategy: UploadStrategy = {
        method: fileInfo.supportsRangeRequests ? 'chunked-stream' : 'direct-stream',
        chunkSize: 5 * 1024 * 1024, // 5MB chunks
        concurrency: 2,
        useMultipart: true,
        priority: 2
      };
      
      logger.debug('[FilePreCheck] Selected strategy: chunked-stream (medium file)', { 
        sizeMB: Math.round(sizeMB),
        supportsRangeRequests: fileInfo.supportsRangeRequests
      });
      
      return strategy;
    }

    // 大文件策略 (> 100MB)
    const strategy: UploadStrategy = {
      method: fileInfo.supportsRangeRequests ? 'parallel-multipart' : 'temp-file-fallback',
      chunkSize: 10 * 1024 * 1024, // 10MB chunks
      concurrency: fileInfo.supportsRangeRequests ? 4 : 1,
      useMultipart: true,
      priority: 3
    };
    
    logger.debug('[FilePreCheck] Selected strategy: parallel-multipart (large file)', { 
      sizeMB: Math.round(sizeMB),
      supportsRangeRequests: fileInfo.supportsRangeRequests
    });
    
    return strategy;
  }

  /**
   * 批量生成策略
   */
  generateBatchStrategies(fileInfos: FileInfo[]): Array<{ fileInfo: FileInfo; strategy: UploadStrategy }> {
    const strategiesWithFiles = fileInfos.map(fileInfo => ({
      fileInfo,
      strategy: this.selectUploadStrategy(fileInfo)
    }));

    // 按优先级排序
    strategiesWithFiles.sort((a, b) => a.strategy.priority - b.strategy.priority);

    logger.info('[FilePreCheck] Generated batch strategies', {
      total: strategiesWithFiles.length,
      directStream: strategiesWithFiles.filter(s => s.strategy.method === 'direct-stream').length,
      chunkedStream: strategiesWithFiles.filter(s => s.strategy.method === 'chunked-stream').length,
      parallelMultipart: strategiesWithFiles.filter(s => s.strategy.method === 'parallel-multipart').length,
      tempFileFallback: strategiesWithFiles.filter(s => s.strategy.method === 'temp-file-fallback').length
    });

    return strategiesWithFiles;
  }

  /**
   * 解析Content-Length头
   */
  private parseContentLength(contentLength: unknown): number {
    if (typeof contentLength === 'string') {
      const size = parseInt(contentLength, 10);
      return isNaN(size) ? 0 : size;
    }
    return 0;
  }

  /**
   * 验证文件是否为有效视频
   */
  isValidVideoFile(fileInfo: FileInfo): boolean {
    if (!fileInfo.isValid || fileInfo.size === 0) {
      return false;
    }

    const validVideoTypes = [
      'video/mp4',
      'video/mpeg',
      'video/quicktime',
      'video/webm',
      'video/x-msvideo', // .avi
      'application/octet-stream' // 有时候视频文件会返回这个类型
    ];

    const hasValidContentType = validVideoTypes.some(type => 
      fileInfo.contentType.toLowerCase().includes(type.toLowerCase())
    );

    // 至少1MB的文件才认为是有效视频
    const hasValidSize = fileInfo.size >= 1024 * 1024;

    return hasValidContentType && hasValidSize;
  }

  /**
   * 获取策略统计信息
   */
  getStrategyStats(strategies: Array<{ strategy: UploadStrategy }>): Record<string, number> {
    const stats: Record<string, number> = {};
    
    strategies.forEach(({ strategy }) => {
      stats[strategy.method] = (stats[strategy.method] || 0) + 1;
    });

    return stats;
  }
}