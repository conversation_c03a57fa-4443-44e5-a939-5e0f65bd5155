import { VideoQueue, createRedisConnection } from '@repo/queue';
import { logger } from '@repo/logs';

/**
 * 健康检查服务
 */
export class HealthChecker {
  private queue: VideoQueue;

  constructor() {
    this.queue = new VideoQueue();
  }

  /**
   * 执行健康检查
   */
  async checkHealth() {
    const checks: Record<string, any> = {};
    let overallStatus = 'healthy';

    try {
      // 检查 Redis 连接
      const redis = createRedisConnection();
      await redis.ping();
      checks.redis = 'healthy';
      await redis.disconnect();
    } catch (error) {
      checks.redis = 'unhealthy';
      overallStatus = 'critical';
      logger.error('[HealthChecker] Redis check failed', { 
        error: error instanceof Error ? error.message : String(error) 
      });
    }

    try {
      // 检查数据库连接
      const { db } = await import('@repo/database');
      await db.$queryRaw`SELECT 1`;
      checks.database = 'healthy';
    } catch (error) {
      checks.database = 'unhealthy';
      overallStatus = 'critical';
      logger.error('[HealthChecker] Database check failed', { 
        error: error instanceof Error ? error.message : String(error) 
      });
    }

    try {
      // 检查队列状态
      const queueStats = await this.queue.getStats();
      checks.queue = {
        status: 'healthy',
        ...queueStats
      };

      // 判断队列健康状态
      if (queueStats.failed > 50) {
        checks.queue.status = 'warning';
        overallStatus = overallStatus === 'healthy' ? 'warning' : overallStatus;
      }

      if (queueStats.waiting > 100 || queueStats.failed > 100) {
        checks.queue.status = 'critical';
        overallStatus = 'critical';
      }
    } catch (error) {
      checks.queue = 'unhealthy';
      overallStatus = 'critical';
      logger.error('[HealthChecker] Queue check failed', { 
        error: error instanceof Error ? error.message : String(error) 
      });
    }

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      checks
    };
  }
}