import { Worker, Job } from 'bullmq';
import { logger } from '@repo/logs';
import { getRedisConnection } from '@repo/queue';
import type { VideoProcessingJobData } from '@repo/queue';
import { WorkerConfigManager } from '../config/worker-config';
import { FilePreCheckService } from '../services/file-precheck';
import { BatchVideoProcessor } from '../services/batch-processor';
import { getDistributedLock } from '../services/distributed-lock';

/**
 * 视频处理 Worker
 */
export class VideoProcessorWorker {
  private worker: Worker<VideoProcessingJobData>;
  private configManager: WorkerConfigManager;
  private preCheckService: FilePreCheckService;
  private batchProcessor: BatchVideoProcessor;

  constructor() {
    this.configManager = WorkerConfigManager.getInstance();
    this.preCheckService = FilePreCheckService.getInstance();
    this.batchProcessor = BatchVideoProcessor.getInstance();
    
    // 获取优化的配置
    const config = this.configManager.getOptimalConfig();
    
    this.worker = new Worker('video-processing', this.processJob.bind(this), {
      connection: getRedisConnection(), // 使用连接池
      concurrency: config.concurrency,
      removeOnComplete: config.removeOnComplete,
      removeOnFail: config.removeOnFail,
      stalledInterval: config.stalledInterval,
      maxStalledCount: config.maxStalledCount
    });

    this.setupEventHandlers();
    
    logger.info('[VideoProcessorWorker] Worker initialized with optimized config', { 
      config,
      recommendedEnvVars: this.configManager.getRecommendedEnvVars()
    });
  }

  /**
   * 处理视频任务（优化版 + 分布式锁防重复）
   */
  private async processJob(job: Job<VideoProcessingJobData>) {
    const { event, events, batchMode } = job.data;

    // 检查是否为批处理任务
    if (batchMode && events && events.length > 1) {
      return await this.processBatchJobWithLock(job, events);
    }

    // 单个任务处理
    const singleEvent = event || events?.[0];
    if (!singleEvent) {
      throw new Error('No event data provided in job');
    }

    // 🔒 使用分布式锁保护事件处理
    return await this.processJobWithLock(job, singleEvent);
  }

  /**
   * 使用分布式锁保护的单个任务处理
   */
  private async processJobWithLock(job: Job<VideoProcessingJobData>, singleEvent: any) {
    const lockKey = `process-event-${singleEvent.id}`;
    const lock = getDistributedLock();
    
    const lockValue = await lock.acquireLock(lockKey, 120000); // 2分钟锁
    if (!lockValue) {
      logger.warn('[VideoProcessorWorker] Could not acquire lock, event may be processing elsewhere', {
        eventId: singleEvent.id,
        jobId: job.id
      });
      return { 
        success: true, 
        skipped: true, 
        reason: 'lock_failed',
        eventId: singleEvent.id 
      };
    }
    
    try {
      return await this.processSingleEventJob(job, singleEvent);
    } finally {
      await lock.releaseLock(lockKey, lockValue);
    }
  }

  /**
   * 实际的单个事件处理逻辑（从原processJob中提取）
   */
  private async processSingleEventJob(job: Job<VideoProcessingJobData>, singleEvent: any) {
    const startTime = Date.now();

    try {
      // 从rawPayload中解析resultUrl
      let resultUrl: string | null = null;
      if (singleEvent.rawPayload && typeof singleEvent.rawPayload === 'object') {
        const payload = singleEvent.rawPayload as any;
        
        // 调试日志：查看rawPayload结构
        logger.info('[VideoProcessorWorker] Parsing rawPayload', {
          eventId: singleEvent.id,
          payloadKeys: Object.keys(payload),
          output: payload.output,
          result_url: payload.result_url,
          video_url: payload.video_url,
          url: payload.url
        });
        
        // 根据提供商解析URL
        if (singleEvent.provider === 'replicate') {
          // Replicate: output[0] 或直接output字符串
          if (Array.isArray(payload.output) && payload.output.length > 0) {
            resultUrl = payload.output[0];
          } else if (typeof payload.output === 'string') {
            resultUrl = payload.output;
          }
        } else if (singleEvent.provider === 'fal') {
          // Fal: video.url 或 result_url 或 output.url
          resultUrl = payload.video?.url || payload.result_url || payload.output?.url || null;
        } else {
          // 通用格式
          resultUrl = payload.output?.[0] || payload.result_url || payload.video_url || payload.url || null;
        }
        
        logger.info('[VideoProcessorWorker] Resolved resultUrl', {
          eventId: singleEvent.id,
          resultUrl,
          resultUrlLength: resultUrl?.length
        });
      }
      
      logger.info('[VideoProcessorWorker] Processing single video job with optimization', {
        jobId: job.id,
        eventId: singleEvent.id,
        attempt: job.attemptsMade + 1,
        hasResultUrl: !!resultUrl
      });

      // 更新进度
      await job.updateProgress(10);
      
      // 如果有视频URL，进行预检和策略选择
      let fileInfo;
      let uploadStrategy;
      
      if (resultUrl) {
        // 并行执行：预检文件 + 加载处理器
        const [preCheckResult, { VideoWebhookProcessor }] = await Promise.all([
          this.preCheckService.preCheckFile(resultUrl).catch(error => {
            logger.warn('[VideoProcessorWorker] File precheck failed, will use fallback', {
              error: error instanceof Error ? error.message : String(error)
            });
            return null;
          }),
          import('@repo/api/src/lib/services/video-webhook-processor')
        ]);
        
        await job.updateProgress(30);
        
        if (preCheckResult && preCheckResult.isValid) {
          fileInfo = preCheckResult;
          uploadStrategy = this.preCheckService.selectUploadStrategy(fileInfo);
          
          logger.info('[VideoProcessorWorker] File precheck completed', {
            jobId: job.id,
            eventId: singleEvent.id,
            fileSize: fileInfo.size,
            fileSizeMB: Math.round(fileInfo.size / (1024 * 1024)),
            contentType: fileInfo.contentType,
            uploadStrategy: uploadStrategy.method,
            priority: uploadStrategy.priority
          });
        }
        
        // 处理事件（传入文件信息和策略）
        const processor = new VideoWebhookProcessor();
        await processor.processEvent(singleEvent, { 
          fileInfo, 
          uploadStrategy,
          useStreamingUpload: true 
        });
        
      } else {
        // 没有预检文件信息，但仍启用流式上传
        const { VideoWebhookProcessor } = await import('@repo/api/src/lib/services/video-webhook-processor');
        const processor = new VideoWebhookProcessor();
        await processor.processEvent(singleEvent, {
          useStreamingUpload: true  // 确保启用流式上传
        });
      }

      // 完成进度
      await job.updateProgress(100);

      const duration = Date.now() - startTime;
      logger.info('[VideoProcessorWorker] Video job completed successfully', {
        jobId: job.id,
        eventId: singleEvent.id,
        duration: `${duration}ms`,
        fileSize: fileInfo?.size,
        strategy: uploadStrategy?.method
      });

      return { 
        success: true, 
        eventId: singleEvent.id, 
        duration,
        fileInfo: fileInfo ? {
          size: fileInfo.size,
          contentType: fileInfo.contentType
        } : undefined,
        strategy: uploadStrategy?.method
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('[VideoProcessorWorker] Video job failed', {
        jobId: job.id,
        eventId: singleEvent.id,
        duration: `${duration}ms`,
        error: error instanceof Error ? error.message : String(error),
        attempt: job.attemptsMade + 1
      });
      throw error;
    }
  }

  /**
   * 使用分布式锁保护的批处理任务
   */
  private async processBatchJobWithLock(job: Job<VideoProcessingJobData>, events: any[]) {
    const batchId = `batch-${job.id}`;
    const lockKey = `process-batch-${batchId}`;
    const lock = getDistributedLock();
    
    const lockValue = await lock.acquireLock(lockKey, 300000); // 5分钟锁
    if (!lockValue) {
      logger.warn('[VideoProcessorWorker] Could not acquire batch lock, may be processing elsewhere', {
        batchId,
        jobId: job.id,
        eventCount: events.length
      });
      return { 
        success: true, 
        skipped: true, 
        reason: 'batch_lock_failed',
        batchId,
        eventCount: events.length 
      };
    }
    
    try {
      return await this.processBatchJob(job, events);
    } finally {
      await lock.releaseLock(lockKey, lockValue);
    }
  }

  /**
   * 实际的批量任务处理逻辑
   */
  private async processBatchJob(job: Job<VideoProcessingJobData>, events: any[]) {
    const startTime = Date.now();
    
    logger.info('[VideoProcessorWorker] Processing batch video job', {
      jobId: job.id,
      eventCount: events.length,
      attempt: job.attemptsMade + 1
    });

    try {
      // 更新进度
      await job.updateProgress(10);
      
      // 使用批处理器处理
      const result = await this.batchProcessor.processBatchBySize(events, {
        maxBatchSize: 20,
        maxConcurrency: 6,
        prioritizeSmallFiles: true,
        enableDynamicBatching: true,
        timeoutMs: 600000 // 10分钟
      });
      
      // 更新进度
      await job.updateProgress(100);
      
      const duration = Date.now() - startTime;
      
      logger.info('[VideoProcessorWorker] Batch video job completed', {
        jobId: job.id,
        eventCount: events.length,
        duration: `${duration}ms`,
        result
      });

      return {
        success: true,
        batchResult: result,
        duration,
        eventCount: events.length
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('[VideoProcessorWorker] Batch video job failed', {
        jobId: job.id,
        eventCount: events.length,
        duration: `${duration}ms`,
        error: error instanceof Error ? error.message : String(error),
        attempt: job.attemptsMade + 1
      });
      throw error;
    }
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers() {
    this.worker.on('completed', (job: Job) => {
      logger.info('[VideoProcessorWorker] Job completed successfully', { 
        jobId: job.id 
      });
    });

    this.worker.on('failed', (job: Job | undefined, err: Error) => {
      logger.error('[VideoProcessorWorker] Job failed', {
        jobId: job?.id,
        error: err.message,
        attempts: job?.attemptsMade
      });
    });

    this.worker.on('stalled', (jobId: string) => {
      logger.warn('[VideoProcessorWorker] Job stalled', { jobId });
    });

    this.worker.on('error', (error: Error) => {
      logger.error('[VideoProcessorWorker] Worker error', { 
        error: error.message 
      });
    });
  }

  /**
   * 优雅关闭
   */
  async shutdown() {
    logger.info('[VideoProcessorWorker] Shutting down worker');
    await this.worker.close();
    logger.info('[VideoProcessorWorker] Worker shut down complete');
  }

  /**
   * 获取 Worker 统计信息（增强版）
   */
  async getStats() {
    const queueStats = await this.getQueueStats();
    const config = this.configManager.getOptimalConfig();
    
    return {
      name: this.worker.name,
      concurrency: this.worker.opts.concurrency,
      config,
      queueStats,
      systemMetrics: this.configManager.getRecommendedEnvVars()
    };
  }

  /**
   * 获取队列统计信息
   */
  private async getQueueStats() {
    try {
      // 使用VideoQueue来获取统计信息
      const { VideoQueue } = await import('@repo/queue');
      const queue = new VideoQueue();
      
      const stats = await queue.getStats();
      return stats;
    } catch (error) {
      logger.error('[VideoProcessorWorker] Failed to get queue stats', {
        error: error instanceof Error ? error.message : String(error)
      });
      return {
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0
      };
    }
  }

  /**
   * 动态调整配置
   */
  async adjustConfiguration() {
    try {
      const queueStats = await this.getQueueStats();
      const suggestedChanges = await this.configManager.adjustConfigBasedOnMetrics(queueStats);
      
      if (Object.keys(suggestedChanges).length > 0) {
        logger.info('[VideoProcessorWorker] Configuration adjustment suggested', {
          currentConfig: {
            concurrency: this.worker.opts.concurrency
          },
          suggestedChanges,
          queueStats
        });
        
        // 注意：动态调整Worker配置需要重启Worker
        // 这里只记录建议，实际调整需要重启服务
        return suggestedChanges;
      }
      
      return null;
    } catch (error) {
      logger.error('[VideoProcessorWorker] Failed to adjust configuration', {
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }
}