#!/usr/bin/env node

/**
 * MVP阶段Consumer启动脚本
 * 单实例部署，优化资源使用，保留扩展能力
 */

// 手动加载环境变量
const fs = require('fs');
const path = require('path');

function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, '../../.env.local');
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    envContent.split('\n').forEach(line => {
      line = line.trim();
      if (line && !line.startsWith('#') && line.includes('=')) {
        const [key, ...valueParts] = line.split('=');
        const value = valueParts.join('=').replace(/^["']|["']$/g, '');
        process.env[key.trim()] = value.trim();
      }
    });
    
    console.log('✅ 环境变量已加载');
  } catch (error) {
    console.log('⚠️  无法加载 .env.local，使用系统环境变量');
  }
}

loadEnvFile();
console.log('🚀 启动Video Consumer - MVP模式\n');

// 检查环境
const requiredEnvs = [
  'REDIS_URL',
  'DATABASE_URL', 
  'S3_ACCESS_KEY_ID',
  'S3_SECRET_ACCESS_KEY',
  'S3_ENDPOINT'
];

console.log('🔍 环境检查:');
let envValid = true;
requiredEnvs.forEach(env => {
  const value = process.env[env];
  if (value) {
    console.log(`   ✅ ${env}: 已配置`);
  } else {
    console.log(`   ❌ ${env}: 缺失`);
    envValid = false;
  }
});

if (!envValid) {
  console.log('\n❌ 环境变量配置不完整，请检查 .env.local');
  process.exit(1);
}

// MVP配置检查
const mvpConfig = {
  workerConcurrency: process.env.WORKER_CONCURRENCY || '4',
  redisPoolSize: process.env.REDIS_POOL_SIZE || '4',
  s3PoolSize: process.env.S3_POOL_SIZE || '2',
  batchMaxSize: process.env.BATCH_MAX_SIZE || '10',
  instanceId: process.env.INSTANCE_ID || 'video-generator-mvp-001',
  duplicateCheckEnabled: process.env.DUPLICATE_CHECK_ENABLED || 'true'
};

console.log('\n📋 MVP配置:');
Object.entries(mvpConfig).forEach(([key, value]) => {
  console.log(`   ${key}: ${value}`);
});

// 资源占用预估
console.log('\n📊 预估资源占用:');
console.log(`   💾 内存: ~${200 + parseInt(mvpConfig.workerConcurrency) * 50}MB`);
console.log(`   🔗 Redis连接: ${mvpConfig.redisPoolSize}个`);
console.log(`   ☁️  S3连接: ${mvpConfig.s3PoolSize}个`);
console.log(`   ⚡ 处理能力: ~${parseInt(mvpConfig.workerConcurrency) * 5} videos/min`);

// 扩展提示
console.log('\n🚀 扩展路径:');
console.log('   📈 增加并发: WORKER_CONCURRENCY=6-8');
console.log('   🔗 增加连接池: REDIS_POOL_SIZE=8, S3_POOL_SIZE=4');
console.log('   📦 增加批处理: BATCH_MAX_SIZE=20');
console.log('   🎯 多实例部署: 修改INSTANCE_ID后启动新实例');

console.log('\n✅ MVP Consumer配置完成，准备启动...');

// 启动Consumer (这里可以集成实际的启动逻辑)
console.log('\n🎯 启动命令:');
console.log('   cd apps/consumer && npm start');
console.log('\n或者:');
console.log('   pnpm --filter @repo/consumer start');

console.log('\n📱 监控检查:');
console.log('   健康检查: http://localhost:3000/health');
console.log('   处理统计: 查看日志中的性能数据');
console.log('   重复防护: 监控duplicate相关日志');

console.log('\n🎉 MVP Consumer启动完成！');