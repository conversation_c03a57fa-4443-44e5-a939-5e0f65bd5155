#!/usr/bin/env node

/**
 * 测试Replicate CDN对不同HTTP方法的支持
 */

const { request } = require('undici');

// 使用最近失败的URL进行测试
const testUrl = 'https://replicate.delivery/xezq/TJH6dHje2WU5NCUpxGNCbJerNeJTvBYLNBQRZYSuQVNno8JqA/tmpxiu0tenv.mp4';

async function testHttpMethod(method, description) {
  console.log(`\n🧪 测试 ${method} 方法 - ${description}`);
  console.log(`URL: ${testUrl}`);
  
  const startTime = Date.now();
  
  try {
    const options = {
      method,
      headers: {
        'User-Agent': 'VideoProcessor/1.0',
        'Accept': '*/*',
        'Cache-Control': 'no-cache',
      },
      headersTimeout: 10000,
      bodyTimeout: 10000,
    };
    
    // 对于GET请求，添加Range来减少下载
    if (method === 'GET') {
      options.headers['Range'] = 'bytes=0-1023'; // 只要前1KB
    }
    
    console.log(`📤 发送请求...`);
    const response = await request(testUrl, options);
    const duration = Date.now() - startTime;
    
    // 立即关闭body避免下载
    if (response.body) {
      response.body.destroy();
    }
    
    console.log(`✅ ${method} 请求成功`);
    console.log(`   状态码: ${response.statusCode}`);
    console.log(`   内容类型: ${response.headers['content-type']}`);
    console.log(`   内容长度: ${response.headers['content-length']}`);
    console.log(`   接受范围: ${response.headers['accept-ranges']}`);
    console.log(`   Last-Modified: ${response.headers['last-modified']}`);
    console.log(`   ETag: ${response.headers['etag']}`);
    console.log(`   Duration: ${duration}ms`);
    
    return {
      success: true,
      statusCode: response.statusCode,
      headers: response.headers,
      duration
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    
    console.log(`❌ ${method} 请求失败`);
    console.log(`   错误类型: ${typeof error}`);
    console.log(`   错误构造器: ${error?.constructor?.name}`);
    console.log(`   错误消息: "${error?.message}"`);
    console.log(`   错误字符串: "${String(error)}"`);
    console.log(`   错误代码: ${error?.code}`);
    console.log(`   错误原因: ${error?.cause}`);
    console.log(`   消息长度: ${error?.message?.length || 0}`);
    console.log(`   是否空消息: ${error?.message === ''}`);
    console.log(`   Duration: ${duration}ms`);
    console.log(`   完整错误:`, JSON.stringify(error, Object.getOwnPropertyNames(error), 2));
    
    return {
      success: false,
      error,
      duration
    };
  }
}

async function runCdnMethodTest() {
  console.log('🔍 Replicate CDN HTTP方法支持测试\n');
  console.log('=' * 80);
  
  const results = {};
  
  // 测试不同HTTP方法
  const methods = [
    { method: 'HEAD', description: '获取头信息（不下载body）' },
    { method: 'OPTIONS', description: '获取支持的方法' },
    { method: 'GET', description: '获取内容（带Range限制）' }
  ];
  
  for (const { method, description } of methods) {
    results[method] = await testHttpMethod(method, description);
    
    // 间隔避免被限制
    if (method !== 'GET') {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  console.log('\n' + '=' * 80);
  console.log('📊 测试结果汇总:');
  console.log('=' * 80);
  
  Object.entries(results).forEach(([method, result]) => {
    const status = result.success ? '✅ 成功' : '❌ 失败';
    const statusCode = result.success ? ` (${result.statusCode})` : '';
    const duration = ` - ${result.duration}ms`;
    
    console.log(`${method.padEnd(8)}: ${status}${statusCode}${duration}`);
    
    if (!result.success && result.error) {
      const errorMsg = result.error.message || String(result.error) || '未知错误';
      console.log(`${''.padEnd(10)} 错误: "${errorMsg}"`);
    }
  });
  
  console.log('\n🎯 结论分析:');
  
  if (!results.HEAD.success && results.GET.success) {
    console.log('✅ 假设验证: Replicate CDN不支持HEAD请求');
    console.log('   建议: 修改FilePreCheck使用GET请求 + Range头');
  } else if (results.HEAD.success) {
    console.log('❓ 意外结果: HEAD请求实际可用');
    console.log('   需要: 深入分析HEAD失败的其他原因');
  }
  
  if (!results.OPTIONS.success) {
    console.log('❗ OPTIONS方法不支持 - 正常现象');
  }
  
  console.log('\n📝 下一步行动:');
  if (!results.HEAD.success) {
    console.log('1. 修改FilePreCheck.preCheckFile()使用GET+Range方法');
    console.log('2. 在Consumer中重新测试文件预检功能');
    console.log('3. 验证流式上传策略选择正常工作');
  }
}

// 运行测试
runCdnMethodTest().catch(console.error);