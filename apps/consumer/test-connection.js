// Upstash Redis 连接测试脚本
require('dotenv').config({ path: '.env.local' });

console.log('Testing Upstash Redis connection...');
console.log('REDIS_URL:', process.env.REDIS_URL ? 'Set' : 'Not set');

async function testUpstashConnection() {
  try {
    // 测试 Redis 连接 (Upstash)
    const { Redis } = require('ioredis');
    
    let redis;
    if (process.env.REDIS_URL) {
      console.log('Using REDIS_URL for connection');
      redis = new Redis(process.env.REDIS_URL, {
        retryDelayOnFailover: 100,
        enableReadyCheck: false,
        maxRetriesPerRequest: 3,
        connectTimeout: 60000,
        commandTimeout: 60000,
        tls: process.env.REDIS_URL.includes('rediss://') ? {} : undefined
      });
    } else {
      console.log('Using individual Redis config');
      redis = new Redis({
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
        retryDelayOnFailover: 100,
        enableReadyCheck: false,
        maxRetriesPerRequest: 3,
        tls: process.env.REDIS_HOST?.includes('upstash.io') ? {} : undefined
      });
    }

    console.log('Attempting Redis ping...');
    const pong = await redis.ping();
    console.log('✅ Redis connection successful:', pong);
    
    // 测试基本 Redis 操作
    await redis.set('test-key', 'test-value');
    const value = await redis.get('test-key');
    console.log('✅ Redis SET/GET test successful:', value);
    await redis.del('test-key');
    
    await redis.disconnect();

    // 测试 BullMQ
    console.log('Testing BullMQ with Upstash...');
    const { Queue } = require('bullmq');
    
    const connectionConfig = process.env.REDIS_URL ? 
      process.env.REDIS_URL : 
      {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
        ...(process.env.REDIS_HOST?.includes('upstash.io') && { tls: {} })
      };

    const testQueue = new Queue('test-queue', {
      connection: connectionConfig
    });

    await testQueue.add('test-job', { 
      message: 'Hello from Consumer!',
      timestamp: new Date().toISOString()
    });
    console.log('✅ BullMQ queue test successful');
    
    const stats = await testQueue.getJobs(['waiting', 'active', 'completed']);
    console.log('📊 Queue stats:', stats.length, 'jobs');
    
    await testQueue.close();

    console.log('🎉 All Upstash tests passed! Ready to start Consumer.');

  } catch (error) {
    console.error('❌ Upstash connection test failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
}

testUpstashConnection();