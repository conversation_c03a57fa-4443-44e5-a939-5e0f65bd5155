#!/usr/bin/env node

/**
 * 测试修复后的FilePreCheck服务
 */

const testUrl = 'https://replicate.delivery/xezq/TJH6dHje2WU5NCUpxGNCbJerNeJTvBYLNBQRZYSuQVNno8JqA/tmpxiu0tenv.mp4';

async function testFilePreCheck() {
  console.log('🧪 测试修复后的FilePreCheck服务\n');
  console.log(`测试URL: ${testUrl}\n`);
  
  try {
    // 动态导入 tsx 编译的 TypeScript 模块
    const { spawn } = require('child_process');
    
    // 使用tsx运行TypeScript代码测试
    const testCode = `
import { FilePreCheckService } from './src/services/file-precheck.js';

async function runTest() {
  console.log('🔧 初始化FilePreCheckService...');
  const service = FilePreCheckService.getInstance();
  
  console.log('📡 开始文件预检...');
  const startTime = Date.now();
  
  try {
    const fileInfo = await service.preCheckFile('${testUrl}');
    const duration = Date.now() - startTime;
    
    console.log('✅ 文件预检成功！');
    console.log('📊 文件信息:');
    console.log(\`   URL: \${fileInfo.url}\`);
    console.log(\`   大小: \${fileInfo.size} bytes (\${Math.round(fileInfo.size / (1024 * 1024))} MB)\`);
    console.log(\`   类型: \${fileInfo.contentType}\`);
    console.log(\`   有效: \${fileInfo.isValid}\`);
    console.log(\`   支持Range: \${fileInfo.supportsRangeRequests}\`);
    console.log(\`   Last-Modified: \${fileInfo.lastModified || '未设置'}\`);
    console.log(\`   ETag: \${fileInfo.etag || '未设置'}\`);
    console.log(\`   处理时间: \${duration}ms\`);
    
    if (fileInfo.error) {
      console.log(\`   错误信息: \${fileInfo.error}\`);
    }
    
    if (fileInfo.isValid) {
      console.log('\\n🎯 测试上传策略选择...');
      const strategy = service.selectUploadStrategy(fileInfo);
      console.log('📋 推荐策略:');
      console.log(\`   方法: \${strategy.method}\`);
      console.log(\`   优先级: \${strategy.priority}\`);
      console.log(\`   块大小: \${strategy.chunkSize} bytes\`);
      console.log(\`   并发数: \${strategy.concurrency}\`);
      console.log(\`   多部分上传: \${strategy.useMultipart}\`);
      
      console.log('\\n🎉 FilePreCheck修复验证成功！');
      console.log('✅ 现在应该能正确支持流式上传了');
    } else {
      console.log('\\n❌ 文件预检显示文件无效');
      console.log('🔍 需要进一步调查问题');
    }
    
  } catch (error) {
    const duration = Date.now() - startTime;
    console.log('❌ 文件预检失败');
    console.log(\`   错误类型: \${typeof error}\`);
    console.log(\`   错误构造器: \${error?.constructor?.name}\`);
    console.log(\`   错误消息: "\${error?.message}"\`);
    console.log(\`   错误代码: \${error?.code}\`);
    console.log(\`   处理时间: \${duration}ms\`);
    console.log('   完整错误:', JSON.stringify(error, Object.getOwnPropertyNames(error), 2));
  }
}

runTest().catch(console.error);
`;
    
    // 将测试代码写入临时文件
    const fs = require('fs');
    const tempFile = './test-precheck-temp.ts';
    fs.writeFileSync(tempFile, testCode);
    
    console.log('🚀 运行FilePreCheck测试...\n');
    
    // 使用tsx运行测试
    const tsx = spawn('npx', ['tsx', tempFile], {
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    tsx.on('close', (code) => {
      // 清理临时文件
      try {
        fs.unlinkSync(tempFile);
      } catch (e) {
        // 忽略清理错误
      }
      
      console.log(`\n📊 测试完成，退出码: ${code}`);
      
      if (code === 0) {
        console.log('\n🎯 下一步:');
        console.log('1. 重新处理一个视频任务');
        console.log('2. 观察日志中是否显示预检成功');
        console.log('3. 确认使用了流式上传策略');
        console.log('4. 验证处理速度提升');
      } else {
        console.log('\n❌ 测试失败，可能需要进一步调试');
        console.log('检查是否有TypeScript编译错误或导入问题');
      }
    });
    
    tsx.on('error', (error) => {
      console.log(`❌ 启动测试失败: ${error.message}`);
      
      // 清理临时文件
      try {
        fs.unlinkSync(tempFile);
      } catch (e) {
        // 忽略清理错误
      }
    });
    
  } catch (error) {
    console.log(`❌ 测试脚本错误: ${error.message}`);
  }
}

// 运行测试
testFilePreCheck();