// ES Module 测试脚本，支持从 packages 导入
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

console.log('Testing Upstash Redis connection with workspace packages...');
console.log('REDIS_URL:', process.env.REDIS_URL ? 'Set ✅' : 'Not set ❌');

async function testConnection() {
  try {
    // 从 workspace package 导入
    const { createRedisConnection, VideoQueue } = await import('@repo/queue');
    
    console.log('📦 Imported workspace packages successfully');
    
    // 测试 Redis 连接
    console.log('🔄 Testing Redis connection...');
    const redis = createRedisConnection();
    
    const pong = await redis.ping();
    console.log('✅ Redis PING successful:', pong);
    
    // 测试基本操作
    await redis.set('test-key', 'hello-upstash');
    const value = await redis.get('test-key');
    console.log('✅ Redis GET/SET successful:', value);
    await redis.del('test-key');
    
    await redis.disconnect();
    
    // 测试 VideoQueue
    console.log('🔄 Testing VideoQueue...');
    const videoQueue = new VideoQueue();
    
    // 添加测试任务
    const jobId = await videoQueue.addVideoProcessing('test-event-123', 1);
    console.log('✅ VideoQueue job added:', jobId);
    
    // 获取队列统计
    const stats = await videoQueue.getStats();
    console.log('📊 Queue stats:', stats);
    
    await videoQueue.close();
    
    console.log('🎉 All tests passed! Upstash Redis + BullMQ working correctly!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
}

testConnection();