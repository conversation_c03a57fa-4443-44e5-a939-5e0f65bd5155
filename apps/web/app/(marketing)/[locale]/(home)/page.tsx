import { FaqSection } from "@marketing/home/<USER>/FaqSection";
import { Features } from "@marketing/home/<USER>/Features";
import { <PERSON> } from "@marketing/home/<USER>/Hero";
import { Newsletter } from "@marketing/home/<USER>/Newsletter";
import type { Metadata } from "next";
import { getLocale, getTranslations, setRequestLocale } from "next-intl/server";
import { generateCanonicalUrl, generateHreflangUrls } from "@repo/utils";

// Get site URL from environment variables, use default value if not set
const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://fluxfly.ai";

// Generate metadata for the home page
export async function generateMetadata(): Promise<Metadata> {
	const locale = await getLocale();
	const t = await getTranslations({ locale, namespace: "home" });

	// Get SEO content from translations
	const seoTitle = t("seo.title");
	const seoDescription = t("seo.description");

	return {
		title: seoTitle,
		description: seoDescription,
		authors: [{ name: "SUPAPI" }],
		metadataBase: new URL(siteUrl),
		robots: {
			index: true,
			follow: true,
		},
		alternates: {
			canonical: generateCanonicalUrl(locale, ""),
			languages: generateHreflangUrls(""),
		},
		openGraph: {
			title: seoTitle,
			type: "website",
			images: [
				{
					url: "/images/supapi-main.png",
					width: 1200,
					height: 630,
					alt: "SUPAPI Video to Text API",
				},
			],
			siteName: "SUPAPI",
			description: seoDescription,
			locale: locale.replace("-", "_"), // Convert zh-cn to zh_CN format
		},
		twitter: {
			title: seoTitle,
			description: seoDescription,
			card: "summary_large_image",
		},
	};
}

export default async function Home({
	params,
}: {
	params: Promise<{ locale: string }>;
}) {
	const { locale } = await params;
	setRequestLocale(locale);

	return (
		<>
			<Hero />
			<Features />
			<FaqSection />
			<Newsletter />
		</>
	);
}
