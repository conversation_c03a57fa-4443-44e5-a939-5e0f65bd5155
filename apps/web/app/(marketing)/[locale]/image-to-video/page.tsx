import type { Locale } from "@repo/i18n";
import { ImageToVideoPage } from "@marketing/image-to-video/components/ImageToVideoPage";
import type { Metadata } from "next";
import { getLocale, getTranslations, setRequestLocale } from "next-intl/server";

import {
	AnimateImagesShowcase,
	CTASection,
	CustomizationOptions,
	FAQSection,
	HeroSection,
	ResourcesSection,
	TutorialSection,
	VersatilityShowcase,
} from "@marketing/image-to-video/components/marketing";

type PageParams = {
	params: Promise<{ locale: string }>;
};

export async function generateMetadata(): Promise<Metadata> {
	const locale = await getLocale();
	const t = await getTranslations("imageToVideo");

	return {
		title: t("meta.title"),
		description: t("meta.description"),
		openGraph: {
			title: t("meta.ogTitle"),
			description: t("meta.ogDescription"),
			images: [
				{
					url: "/images/og-image-to-video.jpg",
					width: 1200,
					height: 630,
					alt: t("meta.ogImageAlt"),
				},
			],
		},
	};
}

export default async function Page({ params }: PageParams) {
	const resolvedParams = await params;
	const locale = resolvedParams.locale as Locale;

	await setRequestLocale(locale);

	return (
		<>
			{/* 主要工具区域 - 使用与首页一致的渐变背景和容器宽度 */}
			<div className="bg-gradient-to-b from-card to-background">
				<div className="container mx-auto pt-24 md:pt-32 pb-12 lg:pb-16">
					<ImageToVideoPage />
				</div>
			</div>

			{/* 营销内容区域 */}
			<div className="container mx-auto py-12 mt-8 space-y-24">
				{/* 
				<HeroSection locale={locale} />
				<AnimateImagesShowcase locale={locale} />
				<VersatilityShowcase locale={locale} />
				<CustomizationOptions locale={locale} />
				<TutorialSection locale={locale} />
				<ResourcesSection locale={locale} />
				<FAQSection locale={locale} />
				<CTASection locale={locale} />
				*/}
			</div>
		</>
	);
}