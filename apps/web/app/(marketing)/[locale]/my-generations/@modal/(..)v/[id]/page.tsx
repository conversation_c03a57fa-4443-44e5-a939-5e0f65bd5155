"use client";

import { VideoDetailView } from "@shared/components/video-detail";
import type { VideoData } from "@shared/components/video-detail/types";
import { useVideoStore } from "@shared/stores";
import { useRouter, useParams } from "next/navigation";
import { buildVideoPath } from "@shared/utils/buildLocalePath";
import React, { useEffect, useState } from "react";

export default function VideoModalPage() {
	console.log("🔥 Intercepting Route - VideoModalPage rendered!");

	const params = useParams();
	const locale = params.locale as string;
	const urlVideoId = params.id as string;

	const router = useRouter();
	const { videos } = useVideoStore();

	// 🆕 状态驱动导航解决方案：
	// 1. 避免重复API调用 - 完全依赖store数据
	// 2. 消除模态框闪烁 - 组件保持挂载，只更新状态
	const [currentVideoId, setCurrentVideoId] = useState(urlVideoId);

	// 同步URL变化到状态（处理浏览器前进后退）
	useEffect(() => {
		setCurrentVideoId(urlVideoId);
		console.log(`🔄 URL changed: ${urlVideoId}`);
	}, [urlVideoId]);

	console.log(`📊 Store videos count: ${videos.length}`);

	// 计算当前视频索引和导航状态
	const currentIndex = videos.findIndex(v => v.id === currentVideoId);
	const hasPrevious = currentIndex > 0;
	const hasNext = currentIndex < videos.length - 1;

	// ESC键监听
	useEffect(() => {
		const handleEsc = (e: KeyboardEvent) => {
			if (e.key === 'Escape') {
				router.back();
			}
		};

		document.addEventListener('keydown', handleEsc);
		// 防止背景滚动
		document.body.style.overflow = 'hidden';

		return () => {
			document.removeEventListener('keydown', handleEsc);
			document.body.style.overflow = '';
		};
	}, [router]);

	// 🆕 从 store 中获取视频数据（使用状态中的ID，不是URL参数）
	const video = videos.find((v: VideoData) => v.id === currentVideoId);

	console.log(`🔥 Current video from store: ${currentVideoId}`, video ? '✅ Found' : '❌ Not found');

	if (!video) {
		return (
			<div className="fixed inset-0 z-50 flex items-center justify-center">
				<div className="bg-background p-8 rounded-lg">
					<p>Video not found</p>
				</div>
			</div>
		);
	}

	const handleRegenerate = () => {
		console.log("Regenerate video:", currentVideoId);
	};

	const handleUpscale = () => {
		console.log("Upscale video:", currentVideoId);
	};

	const handleVideoToVideo = () => {
		console.log("Video to Video:", currentVideoId);
	};

	const handleCanvas = () => {
		console.log("Canvas edit:", currentVideoId);
	};

	const handleAddSoundEffects = () => {
		console.log("Add sound effects:", currentVideoId);
	};

	const handleLipSync = () => {
		console.log("Lip sync:", currentVideoId);
	};

	const handleLike = (liked: boolean) => {
		console.log("Like video:", currentVideoId, liked);
	};

	const handleFavorite = (favorited: boolean) => {
		console.log("Favorite video:", currentVideoId, favorited);
	};

	const handleShare = () => {
		console.log("Share video:", currentVideoId);
	};

	const handleCreateSimilar = () => {
		console.log("Create similar video:", currentVideoId);
	};

	const handleNavigatePrevious = () => {
		if (hasPrevious) {
			const previousVideo = videos[currentIndex - 1];
			console.log(`🔥 Navigate Previous: ${currentVideoId} → ${previousVideo.id}`);

			// 🆕 状态驱动导航 + 正确的本地化路径构建
			setCurrentVideoId(previousVideo.id);
			const newPath = buildVideoPath(locale, previousVideo.id, 'my-generations');
			window.history.replaceState(null, '', newPath);
			console.log(`🌐 URL updated: ${newPath}`);
		}
	};

	const handleNavigateNext = () => {
		if (hasNext) {
			const nextVideo = videos[currentIndex + 1];
			console.log(`🔥 Navigate Next: ${currentVideoId} → ${nextVideo.id}`);

			// 🆕 状态驱动导航 + 正确的本地化路径构建
			setCurrentVideoId(nextVideo.id);
			const newPath = buildVideoPath(locale, nextVideo.id, 'my-generations');
			window.history.replaceState(null, '', newPath);
			console.log(`🌐 URL updated: ${newPath}`);
		}
	};

	return (
		<>
			{/* 背景遮罩 */}
			<div
				className="fixed inset-0 z-[9999] bg-background/80 animate-in fade-in-0 duration-200"
				onClick={() => router.back()}
			/>

			{/* 模态框内容 */}
			<div className="fixed top-[50%] left-[50%] z-[10000] w-[96vw] max-w-[1600px] h-[88vh] translate-x-[-50%] translate-y-[-50%] bg-background border rounded-lg shadow-2xl animate-in zoom-in-95 fade-in-0 duration-200">
				{/* 关闭按钮 */}
				<button
					onClick={() => router.back()}
					className="absolute top-1 right-1 z-[10001] inline-flex h-6 w-6 items-center justify-center text-muted-foreground hover:text-foreground hover:bg-accent transition-all duration-200 p-0 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
					aria-label="Close"
				>
					<svg
						className="size-4"
						fill="none"
						viewBox="0 0 24 24"
						stroke="currentColor"
					>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M6 18L18 6M6 6l12 12"
						/>
					</svg>
					<span className="sr-only">Close</span>
				</button>

				{/* 视频详情内容 */}
				<VideoDetailView
					video={video}
					onRegenerate={handleRegenerate}
					onUpscale={handleUpscale}
					onVideoToVideo={handleVideoToVideo}
					onCanvas={handleCanvas}
					onAddSoundEffects={handleAddSoundEffects}
					onLipSync={handleLipSync}
					onLike={handleLike}
					onFavorite={handleFavorite}
					onShare={handleShare}
					onCreateSimilar={handleCreateSimilar}
					onNavigatePrevious={handleNavigatePrevious}
					onNavigateNext={handleNavigateNext}
					hasPrevious={hasPrevious}
					hasNext={hasNext}
					className="h-full"
				/>
			</div>
		</>
	);
}