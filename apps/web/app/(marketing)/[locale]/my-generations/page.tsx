import type { Locale } from "@repo/i18n";
import { UnifiedMediaPage } from '@marketing/shared/components/UnifiedMediaPage';
import { setRequestLocale } from "next-intl/server";

interface PageProps {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ tab?: string }>;
}

export default async function MyGenerationsPage({ params, searchParams }: PageProps) {
  const resolvedParams = await params;
  const resolvedSearchParams = await searchParams;
  const locale = resolvedParams.locale as Locale;
  
  // 从URL参数中获取初始tab，默认为videos
  const initialTab = resolvedSearchParams.tab === 'images' ? 'images' : 'videos';

  await setRequestLocale(locale);

  return (
    <UnifiedMediaPage 
      locale={locale}
      initialTab={initialTab}
    />
  );
}