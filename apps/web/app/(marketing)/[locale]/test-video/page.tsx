import Link from 'next/link'

export default function TestVideoPage() {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Video Detail Modal Test</h1>
      
      <div className="space-y-4">
        <div>
          <h2 className="text-xl font-semibold mb-2">模态框测试链接 (从列表页访问)</h2>
          <p className="text-gray-600 mb-4">
            这些链接将在my-generations页面中以模态框形式打开
          </p>
          <div className="space-x-4">
            <Link 
              href="/en/my-generations"
              className="inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Go to My Generations
            </Link>
            <Link 
              href="/en/v/cm84hl47d01qnkvxudh1tv25y?from=my-generations"
              className="inline-block px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            >
              Test Modal (from list)
            </Link>
          </div>
        </div>
        
        <div>
          <h2 className="text-xl font-semibold mb-2">完整页面测试链接 (直接访问)</h2>
          <p className="text-gray-600 mb-4">
            这些链接将显示带导航栏的完整页面
          </p>
          <div className="space-x-4">
            <Link 
              href="/en/v/cm84hl47d01qnkvxudh1tv25y"
              className="inline-block px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
            >
              Direct Access
            </Link>
            <Link 
              href="/en/v/cm84hl47d01qnkvxudh1tv25y?source=share"
              className="inline-block px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
            >
              From Share
            </Link>
          </div>
        </div>
        
        <div className="mt-8 p-4 bg-gray-100 rounded">
          <h3 className="font-semibold mb-2">测试说明:</h3>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>蓝色按钮：去到My Generations列表页面</li>
            <li>绿色按钮：测试模态框模式 (from=my-generations参数)</li>
            <li>紫色按钮：测试直接访问完整页面</li>
            <li>橙色按钮：测试分享链接访问 (source=share参数)</li>
          </ul>
        </div>
      </div>
    </div>
  )
}