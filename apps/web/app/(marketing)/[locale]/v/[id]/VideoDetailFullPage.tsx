'use client'

import React, { useEffect, useState } from 'react'
import type { Locale } from "@repo/i18n"
import { VideoDetailView } from '@shared/components/video-detail'
import type { VideoData } from '@shared/components/video-detail/types'

interface VideoDetailFullPageProps {
  videoId: string
  locale: Locale
  fromParam?: string
  sourceParam?: string
}

// 真实API调用 - 获取单个视频详情
async function fetchVideoDetails(videoId: string): Promise<VideoData | null> {
  try {
    console.log(`🔍 [fetchVideoDetails] Fetching video: ${videoId}`)
    const response = await fetch(`/api/generations/${videoId}`)
    
    console.log(`🔍 [fetchVideoDetails] Response status: ${response.status}`)
    
    if (!response.ok) {
      const errorText = await response.text()
      console.error('API request failed:', response.status, response.statusText, errorText)
      return null
    }

    const result = await response.json()
    console.log(`🔍 [fetchVideoDetails] API response:`, result)
    
    // 检查是否是错误响应（RESTful风格下，错误响应仍然包含success字段）
    if ('success' in result && !result.success) {
      console.error('API response error:', result.error)
      return null
    }

    // RESTful风格：直接从根级别获取数据
    const generation = result.generation
    const job = result.job
    const user = result.user

    console.log(`🔍 [fetchVideoDetails] Parsed data:`, { generation, job, user })

    if (!generation) {
      console.error('Generation not found in response')
      return null
    }

    // 转换数据格式以匹配VideoData类型
    return {
      id: generation.id,
      title: job?.prompt || 'Untitled',
      videoUrl: generation.videoUrl || generation.mediaUrl || '',
      posterUrl: generation.cover || generation.thumbnail || '',
      duration: job?.duration || 5,
      author: {
        id: user?.id || generation.userId,
        name: user?.name || 'Unknown User',
        avatar: user?.image || ''
      },
      createdAt: generation.createdAt || new Date().toISOString(),
      status: generation.publishStatus || 'unpublished',
      type: 'image-to-video',
      originalImage: job?.image || '',
      prompt: job?.prompt || '',
      model: job?.modelCode || 'Pollo 1.6',
      resolution: job?.resolution || '480P',
      seed: job?.seed?.toString() || '',
      outputDimension: job?.aspectRatio || '',
      project: 'Default Project',
      likes: generation.starNum || 0,
      isLiked: generation.isLiked || false,
      isFavorite: generation.favorite || false
    }
  } catch (error) {
    console.error('Error fetching video details:', error)
    return null
  }
}

export function VideoDetailFullPage({
  videoId,
  locale,
  fromParam,
  sourceParam
}: VideoDetailFullPageProps) {
  console.log("📄 Full Page - VideoDetailFullPage rendered!");
  console.log("📄 Full Page - props:", { videoId, locale, fromParam, sourceParam });
  
  const [videoData, setVideoData] = useState<VideoData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function loadVideoData() {
      try {
        setLoading(true)
        const data = await fetchVideoDetails(videoId)
        setVideoData(data)
      } catch (err) {
        setError('Failed to load video data')
        console.error('Error loading video data:', err)
      } finally {
        setLoading(false)
      }
    }

    loadVideoData()
  }, [videoId])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p>Loading video...</p>
        </div>
      </div>
    )
  }

  if (error || !videoData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-500">{error || 'Video not found'}</p>
        </div>
      </div>
    )
  }

  const isFromList = fromParam === 'my-generations'
  const isFromShare = sourceParam === 'share'

  const handleRegenerate = () => {
    console.log('Regenerate video:', videoId)
  }

  const handleUpscale = () => {
    console.log('Upscale video:', videoId)
  }

  const handleVideoToVideo = () => {
    console.log('Video to Video:', videoId)
  }

  const handleCanvas = () => {
    console.log('Canvas edit:', videoId)
  }

  const handleAddSoundEffects = () => {
    console.log('Add sound effects:', videoId)
  }

  const handleLipSync = () => {
    console.log('Lip sync:', videoId)
  }

  const handleLike = (liked: boolean) => {
    console.log('Like video:', videoId, liked)
  }

  const handleFavorite = (favorited: boolean) => {
    console.log('Favorite video:', videoId, favorited)
  }

  const handleShare = () => {
    console.log('Share video:', videoId)
  }

  const handleCreateSimilar = () => {
    console.log('Create similar video:', videoId)
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="pt-24 md:pt-30 pb-4 lg:px-4">
        <div className="relative flex flex-col gap-y-4 px-0 lg:flex-row lg:px-10">
          <div className="relative mx-auto w-full max-w-[1200px]">
            <VideoDetailView
              video={videoData}
              onRegenerate={isFromList ? handleRegenerate : undefined}
              onUpscale={handleUpscale}
              onVideoToVideo={handleVideoToVideo}
              onCanvas={handleCanvas}
              onAddSoundEffects={handleAddSoundEffects}
              onLipSync={handleLipSync}
              onLike={handleLike}
              onFavorite={handleFavorite}
              onShare={handleShare}
              onCreateSimilar={handleCreateSimilar}
              // 直接访问页面时不显示导航箭头
              onNavigatePrevious={undefined}
              onNavigateNext={undefined}
              hasPrevious={false}
              hasNext={false}
            />
          </div>
        </div>
      </div>
    </div>
  )
}