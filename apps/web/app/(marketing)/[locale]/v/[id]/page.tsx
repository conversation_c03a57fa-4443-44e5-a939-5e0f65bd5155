import type { Locale } from "@repo/i18n"
import { setRequestLocale } from "next-intl/server"
import { VideoDetailFullPage } from './VideoDetailFullPage'

interface VideoPageProps {
  params: Promise<{ id: string; locale: string }>
  searchParams: Promise<{ from?: string; source?: string }>
}

export default async function VideoPage({ params, searchParams }: VideoPageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams
  const locale = resolvedParams.locale as Locale
  
  await setRequestLocale(locale)
  
  return (
    <VideoDetailFullPage
      videoId={resolvedParams.id}
      locale={locale}
      fromParam={resolvedSearchParams.from}
      sourceParam={resolvedSearchParams.source}
    />
  )
}