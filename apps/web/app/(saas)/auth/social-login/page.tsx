"use client";

import { authClient } from "@repo/auth/client";
import { config } from "@repo/config";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

export default function SocialLoginPage() {
	const [status, setStatus] = useState<"loading" | "redirecting" | "success" | "error">("loading");
	const [error, setError] = useState("");
	const searchParams = useSearchParams();
	const router = useRouter();

	const provider = searchParams.get("provider");
	const originUrl = searchParams.get("originUrl");

	useEffect(() => {
		if (!provider) {
			setError("缺少登录提供商参数");
			setStatus("error");
			return;
		}

		initSocialLogin();
	}, [provider]);

	const initSocialLogin = async () => {
		try {
			setStatus("redirecting");
			
			// 调用 Better Auth 的社交登录
			await authClient.signIn.social({
				provider: provider as any,
				callbackURL: originUrl || config.auth.redirectAfterSignIn,
			});
		} catch (error) {
			console.error("社交登录失败:", error);
			setError(error instanceof Error ? error.message : "登录失败");
			setStatus("error");
		}
	};

	// 渲染不同状态的UI
	const renderContent = () => {
		switch (status) {
			case "loading":
				return (
					<div className="text-center">
						<div className="mx-auto mb-4 size-8 animate-spin rounded-full border-2 border-primary border-t-transparent" />
						<p className="text-sm text-foreground/60">初始化登录...</p>
					</div>
				);

			case "redirecting":
				return (
					<div className="text-center">
						<div className="mx-auto mb-4 size-8 animate-spin rounded-full border-2 border-primary border-t-transparent" />
						<p className="text-sm text-foreground/60">
							正在跳转到 {provider} 登录...
						</p>
					</div>
				);

			case "error":
				return (
					<div className="text-center">
						<div className="mx-auto mb-4 size-8 rounded-full bg-red-100 flex items-center justify-center">
							<svg className="size-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
							</svg>
						</div>
						<p className="text-sm text-red-600 mb-2">登录失败</p>
						<p className="text-xs text-foreground/60">{error}</p>
						<button
							onClick={() => window.close()}
							className="mt-4 px-4 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
						>
							关闭窗口
						</button>
					</div>
				);

			default:
				return null;
		}
	};

	return (
		<div className="flex items-center justify-center min-h-screen bg-background">
			<div className="w-full max-w-sm mx-auto p-6">
				{renderContent()}
			</div>
		</div>
	);
}