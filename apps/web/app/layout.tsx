import type { <PERSON>ada<PERSON> } from "next";
import type { PropsWithChildren } from "react";
import "./globals.css";
import "cropperjs/dist/cropper.css";

// Get site URL from environment variables, use default value if not set
const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://fluxfly.ai";

// Function to generate FAQ JSON-LD from translation messages
function generateFAQJsonLd(
	faqItems: Record<string, { question: string; answer: string }>,
) {
	const mainEntity = Object.values(faqItems).map((item) => ({
		"@type": "Question",
		name: item.question,
		acceptedAnswer: {
			"@type": "Answer",
			text: item.answer,
		},
	}));

	return {
		"@context": "https://schema.org",
		"@type": "FAQPage",
		mainEntity,
	};
}

// Function to generate Organization JSON-LD
function generateOrganizationJsonLd(locale: string) {
	return {
		"@context": "https://schema.org",
		"@type": "Organization",
		url: siteUrl,
		logo: "/images/logo.png",
		name: "SUPAP<PERSON>",
		contactPoint: {
			"@type": "ContactPoint",
			email: "<EMAIL>",
			contactType: "Customer Support",
		},
		sameAs: ["https://tiktok.com/@supapi", "https://x.com/supapi"],
	};
}

export default function RootLayout({ children }: PropsWithChildren) {
	return children;
}
