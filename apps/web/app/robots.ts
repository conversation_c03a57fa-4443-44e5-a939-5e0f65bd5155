import type { MetadataRoute } from "next";
import { getBaseUrl } from "@repo/utils";

export default function robots(): MetadataRoute.Robots {
	const siteUrl = getBaseUrl();
	return {
		rules: {
			userAgent: "*",
			allow: "/",
			disallow: [
				// API endpoints
				"/api/",
				"/api/docs-search",

				// Image proxy - no need to index
				"/image-proxy/",

				// Revalidation endpoints
				"/revalidate/",

				// Assets that don't need indexing
				"/favicon.ico",
				"/favicon.svg",

				// Next.js generated assets
				"/_next/",

				// Vercel specific paths
				"/_vercel/",
				"/static/",

				// Admin and internal paths
				"/admin/",

			],
		},
		sitemap: `${siteUrl}/sitemap.xml`,
	};
}
