import { config } from "@repo/config";
import { getBaseUrl } from "@repo/utils";
// Temporarily comment out imports, can be uncommented when needed  
// import { getAllPosts } from "@marketing/blog/utils/lib/posts";
import { allLegalPages } from "content-collections";
import type { MetadataRoute } from "next";
// import { docsSource } from "./docs-source";

const baseUrl = getBaseUrl();
const locales = Object.keys(config.i18n.locales);
const defaultLocale = config.i18n.defaultLocale;

// Use standard UTC time format (ISO 8601)
const lastModifiedDate = new Date().toISOString();

// Static marketing pages to include in sitemap
const staticMarketingPages = [""];

// Page paths to exclude from sitemap
const excludedPaths = ["terms", "privacy-policy"];

export default function sitemap(): MetadataRoute.Sitemap {
	return [
		// Static marketing pages for all locales
		...staticMarketingPages.flatMap((page) =>
			locales.map((locale) => {
				const isDefaultLocale = locale === defaultLocale;
				const localePrefix = isDefaultLocale ? "" : `/${locale}`;
				return {
					url: new URL(`${localePrefix}${page}`, baseUrl).href,
					lastModified: lastModifiedDate,
				};
			})
		),

		// Legal pages (filtered by excluded paths)
		...allLegalPages
			.filter((page) => !excludedPaths.includes(page.path))
			.map((page) => {
				const isDefaultLocale = page.locale === defaultLocale;
				const localePrefix = isDefaultLocale ? "" : `/${page.locale}`;
				return {
					url: new URL(`${localePrefix}/legal/${page.path}`, baseUrl).href,
					lastModified: lastModifiedDate,
				};
			}),

		// Temporarily disable blog entries, can be uncommented when needed
		// ...posts.map((post) => ({
		// 	url: new URL(`/${post.locale}/blog/${post.path}`, baseUrl).href,
		// 	lastModified: lastModifiedDate,
		// })),

		// Temporarily disable docs entries, can be uncommented when needed
		// ...docsSource.getLanguages().flatMap((locale) =>
		// 	docsSource.getPages(locale.language).map((page) => ({
		// 		url: new URL(`/${locale.language}/docs/${page.slugs.join("/")}`, baseUrl).href,
		// 		lastModified: lastModifiedDate,
		// 	}))
		// ),
	];
}
