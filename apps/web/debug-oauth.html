<!DOCTYPE html>
<html>
<head>
    <title>Debug OAuth URLs</title>
</head>
<body>
    <h1>Debug Better Auth OAuth URLs</h1>
    <button onclick="testGitHub()">Test GitHub URL</button>
    <button onclick="testGoogle()">Test Google URL</button>
    <div id="result"></div>

    <script>
        // 临时覆盖 window.location 来捕获 URL
        const originalLocation = window.location;
        
        function captureURL(callback) {
            let capturedURL = null;
            
            // 覆盖 location.href setter
            Object.defineProperty(window, 'location', {
                get() {
                    return {
                        ...originalLocation,
                        set href(url) {
                            capturedURL = url;
                            console.log('Captured URL:', url);
                            document.getElementById('result').innerHTML = `<p><strong>Captured URL:</strong><br>${url}</p>`;
                        }
                    };
                }
            });
            
            // 覆盖 window.open
            const originalOpen = window.open;
            window.open = function(url, target, features) {
                capturedURL = url;
                console.log('Captured window.open URL:', url);
                document.getElementById('result').innerHTML = `<p><strong>Captured window.open URL:</strong><br>${url}</p>`;
                return { closed: false, close: () => {} };
            };
            
            callback();
            
            // 恢复
            setTimeout(() => {
                Object.defineProperty(window, 'location', {
                    value: originalLocation,
                    writable: true
                });
                window.open = originalOpen;
            }, 1000);
        }

        function testGitHub() {
            captureURL(() => {
                // 模拟 authClient.signIn.social 调用
                fetch('/api/auth/sign-in/github?callbackURL=http://localhost:3000/auth/callback')
                    .then(response => {
                        console.log('Response:', response);
                        document.getElementById('result').innerHTML += `<p>Response Status: ${response.status}</p>`;
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        document.getElementById('result').innerHTML += `<p>Error: ${error.message}</p>`;
                    });
            });
        }

        function testGoogle() {
            captureURL(() => {
                // 模拟 authClient.signIn.social 调用
                fetch('/api/auth/sign-in/google?callbackURL=http://localhost:3000/auth/callback')
                    .then(response => {
                        console.log('Response:', response);
                        document.getElementById('result').innerHTML += `<p>Response Status: ${response.status}</p>`;
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        document.getElementById('result').innerHTML += `<p>Error: ${error.message}</p>`;
                    });
            });
        }
    </script>
</body>
</html>