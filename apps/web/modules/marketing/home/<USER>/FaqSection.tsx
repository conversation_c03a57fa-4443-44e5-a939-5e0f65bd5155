import { cn } from "@ui/lib";
import { getTranslations } from "next-intl/server";

interface FaqSectionProps {
	className?: string;
}

export async function FaqSection({ className }: FaqSectionProps) {
	const t = await getTranslations();

	const title = t("faq.title");
	const itemsObject = t.raw("home.faq.items" as any) as Record<
		string,
		{
			question: string;
			answer: string;
		}
	>;

	// Define the order of FAQ items
	const faqKeys = [
		"sdkSupport",
		"rateLimit",
		"transcriptFormat",
		"isLegal",
	];

	// Convert object to array in the desired order
	const items = faqKeys.map((key) => itemsObject[key]).filter(Boolean);

	if (!items) {
		return null;
	}

	return (
		<section
			className={cn("scroll-mt-20 border-t py-12 lg:py-16", className)}
			id="faq"
		>
			<div className="container max-w-5xl">
				<div className="mb-12 lg:text-center">
					<h2 className="mb-2 font-bold text-4xl lg:text-5xl">
						{title}
					</h2>
					<p className="text-lg opacity-50">{t("faq.description")}</p>
				</div>
				<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
					{items.map((item, i) => (
						<div
							key={`faq-item-${i}`}
							className="rounded-lg bg-card border p-4 lg:p-6"
						>
							<h3 className="mb-2 font-semibold text-lg">
								{item.question}
							</h3>
							<p className="text-foreground/60">{item.answer}</p>
						</div>
					))}
				</div>
			</div>
		</section>
	);
}
