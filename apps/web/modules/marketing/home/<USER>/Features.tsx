import { cn } from "@ui/lib";
import { useTranslations } from "next-intl";

// 定义features的显示顺序
const FEATURE_KEYS = [
	"multiPlatformSupport",
	"accurateSpeechToText",
	"developerFriendlyApi",
	"structuredOutput",
	"multilingualSupport",
	"autoLanguageDetection",
	"subtitleFormatSupport",
	"scalableAndSecure",
	"usageMetricsAndLogs",
] as const;

export function Features({ className }: { className?: string }) {
	const t = useTranslations("home.features");

	const featuresData = (t.raw as any)("items") as Record<
		string,
		{
			subtitle: string;
			description: string;
		}
	>;

	// 根据预定义的顺序创建features数组
	const features = FEATURE_KEYS.map((key) => ({
		key,
		subtitle: featuresData[key]?.subtitle || "",
		description: featuresData[key]?.description || "",
	}));

	return (
		<section className={cn("w-full py-8 md:py-12 px-2 sm:px-4", className)}>
			<div className="max-w-6xl mx-auto">
				<h2 className="mb-8 md:mb-12 font-black text-xl md:text-2xl lg:text-3xl text-center leading-snug">
					{t("title")}
				</h2>

				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
					{features.map((feature, index) => (
						<div
							key={feature.key}
							className="rounded-lg bg-card border p-4 lg:p-6"
						>
							<h3 className="mb-2 font-semibold text-lg">
								{feature.subtitle}
							</h3>
							<p className="text-foreground/60">
								{feature.description}
							</p>
						</div>
					))}
				</div>
			</div>
		</section>
	);
}
