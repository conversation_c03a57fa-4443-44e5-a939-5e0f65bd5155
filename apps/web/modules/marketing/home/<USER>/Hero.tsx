import { LocaleLink } from "@i18n/routing";
import { But<PERSON> } from "@ui/components/button";
import { ArrowRightIcon } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import heroImage from "../../../../public/images/hero-image.png";
import heroImageDark from "../../../../public/images/hero-image-dark.png";

export function Hero() {
	return (
		<div className="relative max-w-full overflow-hidden bg-gradient-to-b from-card to-background">
			<div className="absolute left-1/2 z-10 ml-[-500px] h-[500px] w-[1000px] rounded-full bg-gradient-to-r from-primary to-primary/50 opacity-20 blur-[150px]" />
			<div className="container relative z-20 pt-44 pb-12 text-center lg:pb-16">

				<h1 className="mx-auto max-w-3xl text-balance font-bold text-5xl lg:text-7xl">
				Video to text API
				</h1>

				<p className="mx-auto mt-4 max-w-2xl text-balance text-foreground/60 text-lg">
				Convert video, audio,  into structured text in seconds.	No scraping, no manual work — just clean JSON ready to power your tools.
				</p>

				<div className="mt-6 flex flex-col items-center justify-center gap-3 md:flex-row">
					<Button size="lg" variant="primary" asChild>
						<Link href="/auth/login">
							Get started
							<ArrowRightIcon className="ml-2 size-4" />
						</Link>
					</Button>
					{/* <Button variant="light" size="lg" asChild>
						<LocaleLink href="/docs">Documentation</LocaleLink>
					</Button> */}
				</div>

			</div>
		</div>
	);
}
