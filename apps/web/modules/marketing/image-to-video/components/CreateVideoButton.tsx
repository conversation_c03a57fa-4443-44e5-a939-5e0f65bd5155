"use client";

import { useTranslations } from "next-intl";
import { useSession } from "@saas/auth/hooks/use-session";
import { Button } from "@ui/components/button";
import { Loader2 } from "lucide-react";
import { useUserCredits } from "@marketing/shared/hooks/useUserCredits";

interface CreateVideoButtonProps {
  onCreateVideo: () => Promise<void>;
  isGenerating: boolean;
  requiredCredits: number;
}

export function CreateVideoButton({
  onCreateVideo,
  isGenerating,
  requiredCredits
}: CreateVideoButtonProps) {
  const t = useTranslations("imageToVideo");
  const session = useSession();
  const { credits, isLoading: isLoadingCredits } = useUserCredits();
  
  const hasEnoughCredits = credits !== undefined && credits >= requiredCredits;
  const isDisabled = isGenerating || isLoadingCredits || !session || !hasEnoughCredits;
  
  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <div className="text-sm">
          {session ? (
            <>
              {isLoadingCredits ? (
                <span className="text-muted-foreground">{"Loading credits..."}</span>
              ) : (
                <span>
                  {`Available: ${credits || 0} | Required: ${requiredCredits}`}
                </span>
              )}
            </>
          ) : (
            <span className="text-muted-foreground">{"Please login to generate videos"}</span>
          )}
        </div>
        
        {!hasEnoughCredits && session && !isLoadingCredits && (
          <Button variant="link" size="sm" className="text-primary">
            {"Buy Credits"}
          </Button>
        )}
      </div>
      
      <Button
        className="w-full"
        size="lg"
        disabled={isDisabled}
        onClick={onCreateVideo}
      >
        {isGenerating ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            {t("form.generating")}
          </>
        ) : (
          t("form.createButton")
        )}
      </Button>
      
      <p className="text-xs text-muted-foreground text-center">
        {"AI-generated content may vary in quality"}
      </p>
    </div>
  );
}
