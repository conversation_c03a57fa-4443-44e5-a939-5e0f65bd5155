"use client";

import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>onte<PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	Too<PERSON><PERSON><PERSON>rig<PERSON>,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import { Info } from "lucide-react";
import { useTranslations } from "next-intl";
import type { CreditCalculationResult } from "../hooks/useCreditCalculation";

interface CreditDisplayProps {
	calculation: CreditCalculationResult;
	className?: string;
	showDetails?: boolean;
	modelName?: string;
	modeName?: string;
	videoLength?: number;
	resolution?: string;
}

export function CreditDisplay({
	calculation,
	className,
	showDetails = false,
	modelName,
	modeName,
	videoLength,
	resolution,
}: CreditDisplayProps) {
	const t = useTranslations("imageToVideo");

	const {
		totalCredits,
		singleCredits,
		calculationMethod,
		details,
		pricingKey,
	} = calculation;

	// 根据计算方法确定显示样式
	const getMethodColor = (method: string) => {
		switch (method) {
			case "exact":
				return "text-green-600";
			case "partial":
				return "text-amber-600";
			case "fallback":
				return "text-gray-600";
			default:
				return "text-gray-600";
		}
	};

	const getMethodIcon = (method: string) => {
		switch (method) {
			case "exact":
				return "✓";
			case "partial":
				return "≈";
			case "fallback":
				return "!";
			default:
				return "?";
		}
	};

	const getMethodDescription = (method: string) => {
		switch (method) {
			case "exact":
				return "Exact pricing match found";
			case "partial":
				return "Approximate pricing based on available data";
			case "fallback":
				return "Using model default pricing";
			default:
				return "Unknown calculation method";
		}
	};

	return (
		<div className={cn("flex items-center gap-1 text-gray-600", className)}>
			<span className="text-base">🎟️</span>
			<span className="text-sm">
				{t("form.creditsRequired")}:{" "}
				<span className="font-medium">{totalCredits}</span>
				{totalCredits > singleCredits && (
					<span className="text-xs text-gray-500 ml-1">
						({singleCredits} ×{" "}
						{Math.round(totalCredits / singleCredits)})
					</span>
				)}
			</span>

			{/* 感叹号图标 - 显示计算详情 */}
			<TooltipProvider delayDuration={0}>
				<Tooltip>
					<TooltipTrigger asChild>
						<div className="flex items-center cursor-help">
							<Info className="h-4 w-4 text-gray-400 hover:text-gray-600 transition-colors" />
						</div>
					</TooltipTrigger>
					<TooltipContent side="top" className="max-w-xs">
						<div className="space-y-2">
							<p className="font-medium text-sm">
								{t("form.creditCalculation.title")}
							</p>
							<div className="space-y-1 text-xs">
								{modelName && (
									<div className="flex justify-between">
										<span>{t("form.creditCalculation.model")}:</span>
										<span className="font-mono">
											{modelName}
										</span>
									</div>
								)}
								{modeName && (
									<div className="flex justify-between">
										<span>{t("form.creditCalculation.mode")}:</span>
										<span className="font-mono">
											{modeName}
										</span>
									</div>
								)}
								{videoLength && (
									<div className="flex justify-between">
										<span>{t("form.creditCalculation.duration")}:</span>
										<span className="font-mono">
											{videoLength}s
										</span>
									</div>
								)}
								{resolution && (
									<div className="flex justify-between">
										<span>{t("form.creditCalculation.resolution")}:</span>
										<span className="font-mono">
											{resolution}
										</span>
									</div>
								)}
								<div className="flex justify-between">
									<span>{t("form.creditCalculation.quantity")}:</span>
									<span className="font-mono">
										{Math.round(
											totalCredits / singleCredits,
										)}
									</span>
								</div>
								<hr className="border-gray-600" />
								<div className="flex justify-between font-medium">
									<span>{t("form.creditCalculation.total")}:</span>
									<span>{totalCredits} {t("form.creditCalculation.credits")}</span>
								</div>
							</div>
						</div>
					</TooltipContent>
				</Tooltip>
			</TooltipProvider>

			{/* 计算方法指示器和详细信息 - 只在开发模式或有问题时显示 */}
			{showDetails && calculationMethod !== "exact" && (
				<TooltipProvider delayDuration={0}>
					<Tooltip>
						<TooltipTrigger asChild>
							<div className="flex items-center gap-1 cursor-help">
								<Info className="h-3 w-3 text-gray-400" />
							</div>
						</TooltipTrigger>
						<TooltipContent side="top" className="max-w-xs">
							<div className="space-y-1">
								<p className="font-medium">
									{getMethodDescription(calculationMethod)}
								</p>
								{details && (
									<p className="text-xs text-gray-300">
										{details}
									</p>
								)}
							</div>
						</TooltipContent>
					</Tooltip>
				</TooltipProvider>
			)}
		</div>
	);
}
 