"use client";

import { But<PERSON> } from "@ui/components/button";
import { cn } from "@ui/lib";
import { Grid3X3, Image, Play, Wand2 } from "lucide-react";
import { useState } from "react";

type FilterType = "all" | "videos" | "images" | "effects";

interface FilterControlsProps {
	activeFilter?: FilterType;
	onFilterChange?: (filter: FilterType) => void;
	className?: string;
}

interface FilterButtonProps {
	active: boolean;
	onClick: () => void;
	children: React.ReactNode;
	icon?: React.ReactNode;
}

function FilterButton({ active, onClick, children, icon }: FilterButtonProps) {
	return (
		<Button
			variant={active ? "primary" : "secondary"}
			size="sm"
			onClick={onClick}
			className={cn(
				"flex items-center gap-2 transition-all duration-200",
				active
					? "bg-blue-100 text-blue-700 border-blue-200 hover:bg-blue-200"
					: "bg-gray-100 text-gray-600 hover:bg-gray-200 border-gray-200",
			)}
		>
			{icon && <span className="text-sm">{icon}</span>}
			{children}
		</Button>
	);
}

export function FilterControls({
	activeFilter = "all",
	onFilterChange,
	className,
}: FilterControlsProps) {
	const [filter, setFilter] = useState<FilterType>(activeFilter);

	const handleFilterChange = (newFilter: FilterType) => {
		setFilter(newFilter);
		onFilterChange?.(newFilter);
	};

	return (
		<div
			className={cn(
				"sticky top-0 z-20 bg-gray-100 pt-8 pb-4 px-8 border-b border-gray-200/50 -mx-8 flex items-center justify-between",
				className,
			)}
		>
			{/* 左侧：4个filter按钮 */}
			<div className="flex gap-2">
				<FilterButton
					active={filter === "all"}
					onClick={() => handleFilterChange("all")}
					icon={<Grid3X3 size={16} />}
				>
					All
				</FilterButton>
				<FilterButton
					active={filter === "videos"}
					onClick={() => handleFilterChange("videos")}
					icon={<Play size={16} />}
				>
					Videos
				</FilterButton>
				<FilterButton
					active={filter === "images"}
					onClick={() => handleFilterChange("images")}
					icon={<Image size={16} />}
				>
					Images
				</FilterButton>
				<FilterButton
					active={filter === "effects"}
					onClick={() => handleFilterChange("effects")}
					icon={<Wand2 size={16} />}
				>
					AI Video Effects
				</FilterButton>
			</div>

			{/* 右侧：操作按钮 */}
			<div className="flex items-center gap-2 cursor-pointer items-center justify-center rounded-md">
				<Button
					variant="ghost"
					size="sm"
					className="text-gray-600 hover:text-blue-600 hover:bg-blue-50"
				>
					<Grid3X3 size={18} />
				</Button>
			</div>
		</div>
	);
}
