// apps/web/modules/marketing/image-to-video/components/ImageToVideoPage.tsx
import { getTranslations } from "next-intl/server";
import { ModelOptionsProvider } from "../providers/ModelOptionsProvider";
import { VideoGenerationContainer } from "./VideoGenerationContainer";

interface Task {
	id: string;
	jobId: string;
	status: string;
	progress: number;
	// 其他任务字段...
}

interface ModelConfig {
	// 模型配置字段...
}

interface ImageToVideoPageProps {
	initialTasks?: Task[];
	initialConfig?: ModelConfig;
}

export async function ImageToVideoPage({ 
	initialTasks = [], 
	initialConfig 
}: ImageToVideoPageProps = {}) {
	// 使用服务器端的 getTranslations 加载翻译
	const t = await getTranslations("imageToVideo");
	
	return (
		<ModelOptionsProvider feature="image-to-video">
			<VideoGenerationContainer 
				initialTasks={initialTasks}
				initialConfig={initialConfig}
			/>
		</ModelOptionsProvider>
	);
}
