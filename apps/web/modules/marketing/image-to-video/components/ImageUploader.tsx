"use client";

import { useState, useCallback } from "react";
import { useTranslations } from "next-intl";
import { useDropzone } from "react-dropzone";
import { cn } from "@ui/lib";
import { Upload, X } from "lucide-react";
import { But<PERSON> } from "@ui/components/button";
import { Card } from "@ui/components/card";
import Image from "next/image";

interface ImageUploaderProps {
  image: File | null;
  setImage: (file: File | null) => void;
}

export function ImageUploader({ image, setImage }: ImageUploaderProps) {
  const t = useTranslations("imageToVideo.shared.imageUploader");
  const [preview, setPreview] = useState<string | null>(null);
  
  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      setImage(file);
      
      // Create preview URL
      const objectUrl = URL.createObjectURL(file);
      setPreview(objectUrl);
      
      // Clean up preview URL when component unmounts
      return () => URL.revokeObjectURL(objectUrl);
    }
  }, [setImage]);
  
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': [],
      'image/png': [],
      'image/webp': []
    },
    maxSize: 10485760, // 10MB
    maxFiles: 1
  });
  
  const handleRemoveImage = () => {
    if (preview) {
      URL.revokeObjectURL(preview);
    }
    setImage(null);
    setPreview(null);
  };
  
  return (
    <div className="space-y-3">
      <div className="flex justify-between items-center">
        <h3 className="text-sm font-medium">Upload Image</h3>
        {image && (
          <div className="text-xs text-gray-500 font-medium">
            {Math.round(image.size / 1024)} KB
          </div>
        )}
      </div>
      
      {!image ? (
        <div
          {...getRootProps()}
          className={cn(
            "border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all",
            isDragActive 
              ? "border-blue-500 bg-blue-50" 
              : "border-gray-200 hover:border-blue-300 hover:bg-gray-50"
          )}
          aria-label={t("dragOrClick")}
        >
          <input {...getInputProps()} aria-label={t("browseFiles")} />
          <div className="flex flex-col items-center gap-2">
            <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-1">
              <Upload className="h-6 w-6 text-gray-500" />
            </div>
            <p className="text-sm font-medium text-gray-700">
              {isDragActive ? t("dropHere") : t("dragOrClick")}
            </p>
            <p className="text-xs text-gray-500 max-w-[80%] mx-auto">
              {t("supportedFormats")}
            </p>
            <Button 
              variant="secondary" 
              size="sm" 
              className="mt-3 bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 shadow-sm"
            >
              {t("browseFiles")}
            </Button>
          </div>
        </div>
      ) : (
        <Card className="relative overflow-hidden border border-gray-200 shadow-sm rounded-lg">
          <div className="aspect-video relative bg-[#f8f9fa]">
            <Image
              src={preview || ''}
              alt="Uploaded image"
              fill
              sizes="(max-width: 768px) 100vw, 50vw"
              priority
              className="object-contain"
            />
          </div>
          <Button
            variant="error"
            size="icon"
            className="absolute top-2 right-2 h-8 w-8 rounded-full bg-white/80 hover:bg-white border border-gray-200 shadow-sm text-gray-700 hover:text-red-600 transition-colors"
            onClick={handleRemoveImage}
            aria-label={t("removeImage")}
          >
            <X className="h-4 w-4" />
          </Button>
          <div className="p-2 text-xs text-gray-500 border-t border-gray-100 bg-gray-50">
            {image.name} ({Math.round(image.size / 1024)} KB)
          </div>
        </Card>
      )}
      
      <p className="text-xs text-gray-500">
        Max file size: 10MB. Supported formats: JPEG, PNG, WebP
      </p>
    </div>
  );
}
