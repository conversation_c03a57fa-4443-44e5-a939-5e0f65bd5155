"use client";

import { useEffect, useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Button } from "@ui/components/button";
import { Pencil, RefreshCw } from "lucide-react";
import { Video } from "./Video";
import { ProgressOverlay } from "./ProgressOverlay";

interface Generation {
  id: string;
  status: "creating" | "waiting" | "processing" | "succeeded" | "failed";
  inputImage?: string;
  outputVideo?: string;
  videoUrl?: string;
  error?: string;
  createdAt: string;
  estimatedDuration?: number; // 估算完成时间（秒）
  prompt?: string; // 用户输入的提示词
}

interface MediaPreviewCardProps {
  generation?: Generation; // 兼容单个generation（向后兼容）
  generations?: Generation[]; // 新增：多个generation数组
  containerId: string; // 新增：容器ID
  onRefresh?: () => void;
  onEdit?: () => void;
  appName?: string;
  appType?: string;
  modelName?: string; // 新增：模型名称
  prompt?: string; // 新增：作业级别的提示词
}

export function MediaPreviewCard({
  generation,
  generations,
  containerId,
  onRefresh,
  onEdit,
  appName = "Pollo.ai",
  appType = "Image to Video",
  modelName = "Kling 2.0", // 新增：默认模型名称
  prompt,
}: MediaPreviewCardProps) {
  // 处理单个generation或多个generations的兼容性
  const generationList = generations || (generation ? [generation] : []);
  const primaryGeneration = generationList[0]; // 用于显示顶部信息的主generation
  
  const [startTime] = useState(() => new Date(primaryGeneration?.createdAt || new Date().toISOString()).getTime());
  const [currentTime, setCurrentTime] = useState(Date.now());

  // 更新当前时间，用于计算流逝时间
  useEffect(() => {
    const hasProcessing = generationList.some(gen => gen.status === "processing");
    if (hasProcessing) {
      const interval = setInterval(() => {
        setCurrentTime(Date.now());
      }, 100); // 每100ms更新一次，使进度条更平滑

      return () => clearInterval(interval);
    }
  }, [generationList]);

  // 计算流逝时间（秒）
  const elapsedTime = Math.floor((currentTime - startTime) / 1000);

  // 格式化时间戳
  const formatTimestamp = (date: string) => {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = d.getMonth() + 1;
    const day = d.getDate();
    const hours = d.getHours().toString().padStart(2, '0');
    const minutes = d.getMinutes().toString().padStart(2, '0');
    const seconds = d.getSeconds().toString().padStart(2, '0');
    
    return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
  };

  // 状态判断逻辑：基于所有generation的状态
  const statusCounts = generationList.reduce((counts, gen) => {
    counts[gen.status] = (counts[gen.status] || 0) + 1;
    return counts;
  }, {} as Record<string, number>);
  
  const totalCount = generationList.length;
  const isCreating = statusCounts.creating === totalCount;
  const isWaiting = statusCounts.waiting === totalCount;
  const isProcessing = statusCounts.processing > 0; // 有任何一个在处理中就显示进度

  return (
    <div 
      id={containerId} // 添加容器ID
      className="bg-white rounded-lg border border-gray-200 w-full flex flex-col"
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <div className="flex items-center">
          <span className="text-gray-900 font-medium text-base">{appName}</span>
          <div className="mx-3 h-5 w-px bg-gray-300"></div>
          <span className="text-gray-600 text-base">{appType}</span>
          <div className="mx-3 h-5 w-px bg-gray-300"></div>
          <span className="text-gray-600 text-base">{modelName}</span>
        </div>
        <div className="text-right">
          <p className="text-gray-500 text-sm">{formatTimestamp(primaryGeneration?.createdAt || new Date().toISOString())}</p>
        </div>
      </div>
      
      {/* Input Info Bar */}
      <div className="flex items-center gap-4 px-4 py-3 bg-white border-b border-gray-100">
        {/* Input Image Thumbnail */}
        {primaryGeneration?.inputImage && (
          <div className="w-10 h-10 rounded overflow-hidden flex-shrink-0">
            <img 
              src={primaryGeneration.inputImage} 
              alt="Input" 
              className="w-full h-full object-cover"
            />
          </div>
        )}
        {/* User Prompt */}
        <div className="flex-1 min-w-0">
          <span className="text-gray-700 text-sm truncate block">
            {prompt || primaryGeneration?.prompt || "No prompt provided"}
          </span>
        </div>
        {/* Generation Count Badge */}
        {generationList.length > 1 && (
          <div className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
            {generationList.length} videos
          </div>
        )}
      </div>

      {/* Media Container */}
      <div className="relative overflow-hidden mx-4 py-4" style={{minHeight: '280px'}}>
        {/* 根据generation数量选择布局 */}
        <div className={`h-full ${generationList.length > 1 ? 'grid grid-cols-2 gap-4' : ''}`}>
          {generationList.map((gen, index) => (
            <div key={gen.id} className="relative overflow-hidden">
              <Video
                src={gen.status === "succeeded" ? (gen.videoUrl || gen.outputVideo || "/videos/banner-video2.mp4") : gen.inputImage}
                isVideo={gen.status === "succeeded"}
                alt={`Media content ${index + 1}`}
                generationId={gen.id}
              />
              
              {/* 单个generation的状态覆盖层 */}
              {gen.status === "creating" && (
                <div className="absolute inset-0 z-10 bg-slate-800/90 backdrop-blur-sm flex items-center justify-center">
                  <div className="text-center text-white">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
                    <p className="text-lg font-medium">Create a job for generating</p>
                  </div>
                </div>
              )}
              
              {gen.status === "waiting" && (
                <div className="absolute inset-0 z-10 bg-slate-800/90 backdrop-blur-sm flex items-center justify-center">
                  <div className="text-center text-white">
                    {/* 蓝色音频波形图标 - 恢复原始样式 */}
                    <div className="mb-4 flex items-center justify-center space-x-1">
                      <div className="w-1 bg-blue-500 rounded-full animate-bounce" style={{ height: '16px', animationDelay: '0ms' }}></div>
                      <div className="w-1 bg-blue-500 rounded-full animate-bounce" style={{ height: '24px', animationDelay: '100ms' }}></div>
                      <div className="w-1 bg-blue-500 rounded-full animate-bounce" style={{ height: '20px', animationDelay: '200ms' }}></div>
                      <div className="w-1 bg-blue-500 rounded-full animate-bounce" style={{ height: '28px', animationDelay: '300ms' }}></div>
                      <div className="w-1 bg-blue-500 rounded-full animate-bounce" style={{ height: '16px', animationDelay: '400ms' }}></div>
                      <div className="w-1 bg-blue-500 rounded-full animate-bounce" style={{ height: '22px', animationDelay: '500ms' }}></div>
                    </div>
                    
                    {/* 提交文本 */}
                    <div className="text-white text-center">
                      <p className="text-lg font-medium">Submitting your task...</p>
                    </div>
                  </div>
                </div>
              )}
              
              {gen.status === "processing" && gen.inputImage && (
                <ProgressOverlay
                  estimatedDuration={gen.estimatedDuration || 60}
                  elapsedTime={elapsedTime}
                  backgroundImage={gen.inputImage}
                />
              )}

              {gen.status === "failed" && (
                <div className="absolute inset-0 bg-black/80 flex items-center justify-center">
                  <div className="text-center text-white p-4">
                    <p className="text-red-400 mb-2">Generation Failed</p>
                    <p className="text-sm text-gray-300">{gen.error || "An error occurred"}</p>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Action Bar */}
      <div className="flex gap-2 p-4 border-t border-gray-100">
        <Button
          variant="outline"
          size="icon"
          onClick={onEdit}
          disabled={isProcessing || isWaiting || isCreating}
          className="bg-white border-gray-300 text-gray-700 hover:bg-gray-50"
        >
          <Pencil className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={onRefresh}
          disabled={isProcessing || isWaiting || isCreating}
          className="bg-white border-gray-300 text-gray-700 hover:bg-gray-50"
        >
          <RefreshCw className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}