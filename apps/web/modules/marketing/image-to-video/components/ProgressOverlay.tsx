"use client";

import { useMemo, useState } from "react";
import Image from "next/image";

interface ProgressOverlayProps {
  estimatedDuration: number; // 估算时间（秒）
  elapsedTime: number; // 已流逝时间（秒）
  backgroundImage?: string;
}

export function ProgressOverlay({ 
  estimatedDuration, 
  elapsedTime, 
  backgroundImage 
}: ProgressOverlayProps) {
  // 检测是否在测试环境中，如果是则禁用图片优化
  const isTestEnvironment = typeof window !== 'undefined' && 
    window.location.hostname.includes('test.connectionshinttoday.tips');
  const shouldUseUnoptimized = isTestEnvironment;

  // 计算进度百分比，最高99%
  const progressPercentage = useMemo(() => {
    const rawProgress = (elapsedTime / estimatedDuration) * 100;
    return Math.min(Math.floor(rawProgress), 99);
  }, [elapsedTime, estimatedDuration]);

  // 格式化剩余时间
  const remainingTime = Math.max(0, estimatedDuration - elapsedTime);
  const formattedRemainingTime = remainingTime > 0 
    ? `${Math.ceil(remainingTime)} seconds remaining`
    : "Almost done...";

  return (
    <div className="absolute inset-0 z-10">
      {/* 背景图片 */}
      {backgroundImage && (
        <div className="absolute inset-0">
          <Image
            src={backgroundImage}
            alt="Background"
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            priority={true}
            quality={75}
            unoptimized={shouldUseUnoptimized}
          />
        </div>
      )}
      
      {/* 半透明灰色遮罩 */}
      <div className="absolute inset-0 bg-gray-500/30" />
      
      {/* 进度内容 */}
      <div className="absolute inset-0 flex flex-col items-center justify-center p-6">
        {/* 进度条容器 */}
        <div className="w-full max-w-xs mb-4">
          <div className="relative h-2 bg-white/20 rounded-full overflow-hidden">
            {/* 进度条填充 */}
            <div
              className="absolute left-0 top-0 h-full bg-gradient-to-r from-blue-400 to-blue-600 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${progressPercentage}%` }}
            />
            
            {/* 进度条光晕效果 */}
            <div
              className="absolute left-0 top-0 h-full bg-gradient-to-r from-blue-300/50 to-blue-500/50 rounded-full blur-sm transition-all duration-300 ease-out"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
          
          {/* 进度百分比 */}
          <div className="text-center mt-2">
            <span className="text-blue-400 font-bold text-lg">{progressPercentage}%</span>
          </div>
        </div>
        
        {/* 提示文字 */}
        <div className="text-center">
          <p className="text-white text-base mb-1">
            Pollo AI is generating your video, which may take {estimatedDuration} seconds.
          </p>
          <p className="text-white/80 text-sm">
            Once finished, the video will be saved in{" "}
            <span className="text-blue-400">My Creations</span>.
          </p>
          <p className="text-white/60 text-xs mt-2">
            {formattedRemainingTime}
          </p>
        </div>
      </div>
    </div>
  );
}