"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { Textarea } from "@ui/components/textarea";
import { Button } from "@ui/components/button";
import { Sparkles } from "lucide-react";
import { cn } from "@ui/lib";

interface PromptInputProps {
  prompt: string;
  setPrompt: (prompt: string) => void;
}

// 示例提示词
const EXAMPLE_PROMPTS = [
  "Two little groundhogs playfully frolicking in a sunny meadow, their tiny paws swaying at each other while rolling in the grass.",
  "A serene mountain lake reflecting the colorful autumn trees and clear blue sky.",
  "A futuristic cityscape with flying vehicles and neon lights at night.",
  "A cozy cabin in the woods with smoke coming from the chimney during winter."
];

export function PromptInput({ prompt, setPrompt }: PromptInputProps) {
  const t = useTranslations("imageToVideo.promptInput");
  const [charCount, setCharCount] = useState(0);
  const MAX_CHARS = 500;
  
  const handlePromptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newPrompt = e.target.value;
    if (newPrompt.length <= MAX_CHARS) {
      setPrompt(newPrompt);
      setCharCount(newPrompt.length);
    }
  };
  
  const handleExampleClick = (example: string) => {
    setPrompt(example);
    setCharCount(example.length);
  };
  
  const handleGenerateAI = () => {
    // 这里可以集成AI生成提示词的功能
    const randomExample = EXAMPLE_PROMPTS[Math.floor(Math.random() * EXAMPLE_PROMPTS.length)];
    setPrompt(randomExample);
    setCharCount(randomExample.length);
  };
  
  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">{t("title")}</h3>
        <div className={cn(
          "text-xs",
          charCount > MAX_CHARS * 0.9 ? "text-red-500" : "text-muted-foreground"
        )}>
          {charCount}/{MAX_CHARS}
        </div>
      </div>
      
      <div className="relative">
        <Textarea
          placeholder={t("placeholder")}
          className="min-h-[100px] resize-y"
          value={prompt}
          onChange={handlePromptChange}
        />
        <Button
          variant="ghost"
          size="sm"
          className="absolute right-2 top-2"
          onClick={handleGenerateAI}
        >
          <Sparkles className="h-4 w-4 mr-1" />
          {t("generateAI")}
        </Button>
      </div>
      
      <div className="space-y-1">
        <p className="text-xs text-muted-foreground">
          {t("examples")}:
        </p>
        <div className="flex flex-wrap gap-2">
          {EXAMPLE_PROMPTS.map((example, index) => (
            <Button
              key={index}
              variant="outline"
              size="sm"
              className="text-xs"
              onClick={() => handleExampleClick(example)}
            >
              {example.substring(0, 20)}...
            </Button>
          ))}
        </div>
      </div>
    </div>
  );
}
