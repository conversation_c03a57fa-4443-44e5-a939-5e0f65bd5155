"use client";

import type { VideoType } from "@marketing/image-to-video/types";
import { cn } from "@ui/lib";
import { formatDistanceToNow } from "date-fns";
import {
	Download,
	Edit,
	Heart,
	MessageSquare,
	MoreHorizontal,
	RefreshCw,
	Share2,
	Video,
	Zap,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useRef, useState } from "react";
import videojs from "video.js";
import "video.js/dist/video-js.css";

interface RichVideoCardProps {
	video: VideoType;
	brandName?: string;
	brandLogo?: string;
	category?: string;
	onDownload?: () => void;
	onShare?: () => void;
	onFavorite?: () => void;
	onEdit?: () => void;
	onRemix?: () => void;
	className?: string;
}

export function RichVideoCard({
	video,
	brandName = "FluxFly",
	brandLogo,
	category = "AI Video Generator",
	onDownload,
	onShare,
	onFavorite,
	onEdit,
	onRemix,
	className,
}: RichVideoCardProps) {
	const t = useTranslations("imageToVideo.richVideoCard");
	const [isPlaying, setIsPlaying] = useState(true);
	const [isMuted, setIsMuted] = useState(true);
	const [isFavorited, setIsFavorited] = useState(video.isFavorite);
	const [isHovered, setIsHovered] = useState(false);
	const [playbackRate, setPlaybackRate] = useState(1);
	const [isPictureInPicture, setIsPictureInPicture] = useState(false);
	const [isSpeedMenuVisible, setIsSpeedMenuVisible] = useState(false);
	const videoRef = useRef<HTMLVideoElement>(null);
	const playerRef = useRef<any>(null);
	const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null);
	const speedMenuTimeoutRef = useRef<NodeJS.Timeout | null>(null);
	const [currentTime, setCurrentTime] = useState(0);
	const [duration, setDuration] = useState(0);
	const [isDragging, setIsDragging] = useState(false);

	// 处理收藏
	const handleFavoriteToggle = () => {
		setIsFavorited(!isFavorited);
		onFavorite?.();
	};

	// 显示控制栏（立即显示）
	const showControls = () => {
		if (hideTimeoutRef.current) {
			clearTimeout(hideTimeoutRef.current);
			hideTimeoutRef.current = null;
		}
		setIsHovered(true);
	};

	// 隐藏控制栏（延时隐藏）
	const hideControls = () => {
		hideTimeoutRef.current = setTimeout(() => {
			setIsHovered(false);
		}, 300); // 200ms后隐藏
	};

	// 处理视频hover播放
	const handleVideoHover = () => {
		if (videoRef.current) {
			videoRef.current.play().catch(console.error);
			setIsPlaying(true);
		}
	};

	// 处理视频移出停止
	const handleVideoLeave = () => {
		if (videoRef.current) {
			videoRef.current.pause();
			setIsPlaying(false);
		}
	};

	// 处理播放速度变化
	const handlePlaybackRateChange = (rate: number) => {
		if (videoRef.current) {
			videoRef.current.playbackRate = rate;
			setPlaybackRate(rate);
		}
	};

	// 显示速度菜单
	const showSpeedMenu = () => {
		if (speedMenuTimeoutRef.current) {
			clearTimeout(speedMenuTimeoutRef.current);
			speedMenuTimeoutRef.current = null;
		}
		setIsSpeedMenuVisible(true);
	};

	// 隐藏速度菜单（延时）
	const hideSpeedMenu = () => {
		speedMenuTimeoutRef.current = setTimeout(() => {
			setIsSpeedMenuVisible(false);
		}, 150);
	};

	// 处理进度条点击跳转
	const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
		if (!videoRef.current) return;
		
		const rect = e.currentTarget.getBoundingClientRect();
		const clickX = e.clientX - rect.left;
		const percentage = clickX / rect.width;
		const newTime = percentage * duration;
		
		videoRef.current.currentTime = newTime;
		setCurrentTime(newTime);
	};

	// 处理进度条拖拽开始
	const handleProgressMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
		setIsDragging(true);
		handleProgressClick(e);
	};

	// 处理进度条拖拽
	const handleProgressMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
		if (!isDragging || !videoRef.current) return;
		handleProgressClick(e);
	};

	// 处理进度条拖拽结束
	const handleProgressMouseUp = () => {
		setIsDragging(false);
	};

	// 格式化时间显示
	const formatTime = (time: number) => {
		const minutes = Math.floor(time / 60);
		const seconds = Math.floor(time % 60);
		return `${minutes}:${seconds.toString().padStart(2, '0')}`;
	};

	// Video.js 初始化 - 暂时禁用
	/*
	useEffect(() => {
		if (videoRef.current && !playerRef.current) {
			const player = videojs(videoRef.current, {
				controls: true,
				responsive: true,
				fluid: false,
				fill: true,
				preload: 'metadata',
				muted: true,
				loop: true,
				playsinline: true,
				bigPlayButton: true,
				// 启用播放速度控制
				playbackRates: [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2],
				// 启用画中画
				enablePictureInPicture: true
			});

			// 事件监听
			player.on('play', () => setIsPlaying(true));
			player.on('pause', () => setIsPlaying(false));
			player.on('ended', () => setIsPlaying(false));
			player.on('volumechange', () => {
				setIsMuted(player.muted() || false);
			});
			player.on('ratechange', () => {
				setPlaybackRate(player.playbackRate() || 1);
			});

			// 初始状态
			player.muted(true);
			setIsMuted(true);

			playerRef.current = player;
		}

		return () => {
			if (playerRef.current) {
				playerRef.current.dispose();
				playerRef.current = null;
			}
		};
	}, []);
	*/

	const handlePictureInPicture = async () => {
		if (!videoRef.current) return;
		
		try {
			if (isPictureInPicture) {
				await document.exitPictureInPicture();
				setIsPictureInPicture(false);
			} else {
				await videoRef.current.requestPictureInPicture();
				setIsPictureInPicture(true);
			}
		} catch (error) {
			console.error('画中画模式切换失败:', error);
		}
	};

	// 清理定时器
	useEffect(() => {
		return () => {
			if (hideTimeoutRef.current) {
				clearTimeout(hideTimeoutRef.current);
			}
			if (speedMenuTimeoutRef.current) {
				clearTimeout(speedMenuTimeoutRef.current);
			}
		};
	}, []);

	// 视频时间更新监听
	useEffect(() => {
		const video = videoRef.current;
		if (!video) return;

		const handleTimeUpdate = () => {
			if (!isDragging) {
				setCurrentTime(video.currentTime);
			}
		};

		const handleLoadedMetadata = () => {
			setDuration(video.duration);
		};

		const handleMouseUpGlobal = () => {
			setIsDragging(false);
		};

		video.addEventListener('timeupdate', handleTimeUpdate);
		video.addEventListener('loadedmetadata', handleLoadedMetadata);
		document.addEventListener('mouseup', handleMouseUpGlobal);

		return () => {
			video.removeEventListener('timeupdate', handleTimeUpdate);
			video.removeEventListener('loadedmetadata', handleLoadedMetadata);
			document.removeEventListener('mouseup', handleMouseUpGlobal);
		};
	}, [isDragging]);

	return (
		<div
			id={`item-${video.id}`}
			className={cn("bg-card rounded-lg p-5", className)}
		>
			<div className="flex max-w-full flex-col md:max-w-[1260px] lg:max-w-[1370px]">
				{/* Header - 完全复刻对手结构 */}
				<div className="flex flex-col gap-2 pb-2">
					{/* 第一行：品牌信息 + 时间 */}
					<div className="flex max-w-full flex-wrap items-center gap-2">
						<div className="flex flex-wrap items-center gap-2">
							{brandLogo ? (
								<img
									src={brandLogo}
									alt={brandName}
									className="size-5"
								/>
							) : (
								<Video className="size-5 text-primary" />
							)}
							<span className="text-md font-semibold">
								{brandName}
							</span>
							<span className="bg-border h-[12px] w-[2px]" />
							<span className="border-border text-muted-foreground rounded-[4px] border px-[6px] py-[2px] text-xs">
								{category}
							</span>
						</div>
						<div className="flex items-center gap-2">
							<span className="text-muted-foreground text-xs">
								{formatDistanceToNow(
									new Date(video.createdAt),
									{
										addSuffix: true,
									},
								)}
							</span>
						</div>
					</div>
					{/* 第二行：空行保持结构 */}
					<div className="flex w-full max-w-full items-center gap-2">
						<div className="flex max-w-screen-sm shrink-0 flex-wrap items-center gap-2" />
					</div>
				</div>

				{/* Video Container - 核心视频区域 */}
				<div className="relative flex max-w-full flex-col flex-wrap gap-2">
					<div className="flex flex-wrap gap-2">
						<div
							id={`g-${video.id}`}
							className="relative flex min-h-[164px] w-full max-w-full cursor-pointer flex-col gap-4 rounded-md md:h-[288px] md:w-[438px]"
							onMouseEnter={() => {
								showControls();
								handleVideoHover();
							}}
							onMouseLeave={() => {
								hideControls();
								handleVideoLeave();
							}}
						>
							<div className="group relative w-full md:h-[288px] md:max-w-[438px]">
								{/* 视频容器 */}
								<div 
									className="relative w-full h-full bg-black overflow-hidden"
								>
									{/* 视频播放器 */}
									<video
										ref={videoRef}
										className="w-full h-full object-cover"
										poster={video.thumbnailUrl}
										muted
										loop
										autoPlay
										onClick={() => {
											if (videoRef.current) {
												if (isPlaying) {
													videoRef.current.pause();
												} else {
													videoRef.current.play();
												}
											}
										}}
									>
										<source src="/videos/banner-video2.mp4" type="video/mp4" />
										您的浏览器不支持视频播放。
									</video>

									{/* 右上角操作按钮 */}
									<div className={`absolute top-2 right-2 flex items-center gap-1 transition-opacity duration-300 ${
										isHovered ? 'opacity-100' : 'opacity-0'
									}`}>
										<button className="p-2 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors">
											<Download className="size-5" />
										</button>
										<button className="p-2 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors">
											<Share2 className="size-5" />
										</button>
										<button className="p-2 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors">
											<MoreHorizontal className="size-5" />
										</button>
										<button 
											onClick={handleFavoriteToggle}
											className={`p-2 bg-black/50 rounded-full hover:bg-black/70 transition-colors ${isFavorited ? 'text-red-500' : 'text-white'}`}
										>
											<Heart className="size-5" fill={isFavorited ? "currentColor" : "none"} />
										</button>
									</div>

									{/* 右侧抖音风格按钮组 */}
									<div className={`absolute right-2 bottom-16 flex flex-col gap-2 transition-opacity duration-300 ${
										isHovered ? 'opacity-100' : 'opacity-0'
									}`}>
										<button className="p-2 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors">
											<Heart className="size-5" />
										</button>
										<button className="p-2 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors">
											<MessageSquare className="size-5" />
										</button>
										<button className="p-2 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors">
											<Share2 className="size-5" />
										</button>
									</div>

									{/* 底部控制栏 */}
									<div 
										className={`absolute bottom-0 left-0 right-0 bg-gray-900 text-white transition-opacity duration-300 ${
											isHovered ? 'opacity-100' : 'opacity-0'
										}`}
									>
										<div className="flex items-center px-4 py-2 gap-4">
											{/* 播放/暂停按钮 */}
											<button
												onClick={() => {
													if (videoRef.current) {
														if (isPlaying) {
															videoRef.current.pause();
														} else {
															videoRef.current.play();
														}
													}
												}}
												className="text-white hover:text-gray-300 transition-colors flex items-center justify-center w-6 h-6"
											>
												{isPlaying ? (
													<span className="vjs-icon-pause text-lg" />
												) : (
													<span className="vjs-icon-play text-lg" />
												)}
											</button>
											
											{/* 音量按钮 */}
											<button
												onClick={() => {
													if (videoRef.current) {
														videoRef.current.muted = !videoRef.current.muted;
														setIsMuted(videoRef.current.muted);
													}
												}}
												className="text-white hover:text-gray-300 transition-colors flex items-center justify-center w-6 h-6"
											>
												{isMuted ? (
													<span className="vjs-icon-volume-mute text-lg" />
												) : (
													<span className="vjs-icon-volume-high text-lg" />
												)}
											</button>

											{/* 进度条 */}
											<div className="flex-1 mx-2">
												<div 
													className="vjs-progress-control vjs-control vjs-progress-control-enabled"
													onMouseDown={handleProgressMouseDown}
													onMouseMove={handleProgressMouseMove}
													onMouseUp={handleProgressMouseUp}
													onClick={handleProgressClick}
												>
													<div className="vjs-progress-holder w-full h-1 bg-gray-600 rounded-full cursor-pointer relative">
														{/* 加载进度 */}
														<div className="vjs-load-progress absolute left-0 top-0 h-full bg-gray-500 rounded-full" style={{ width: '60%' }}></div>
														{/* 播放进度 */}
														<div 
															className="vjs-play-progress absolute left-0 top-0 h-full bg-white rounded-full transition-all" 
															style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
														>
															{/* 进度手柄 */}
															<div className="vjs-progress-handle absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-white rounded-full opacity-0 hover:opacity-100 transition-opacity"></div>
														</div>
													</div>
												</div>
											</div>

											{/* 时间显示 */}
											<span className="text-white text-sm font-mono">{formatTime(duration > 0 ? currentTime : 0)}</span>

											{/* 播放速度 - 原生风格菜单 */}
											<div 
												className="relative"
												onMouseEnter={showSpeedMenu}
												onMouseLeave={hideSpeedMenu}
											>
												<button 
													className="vjs-playback-rate vjs-menu-button vjs-menu-button-popup vjs-control vjs-button text-white hover:text-gray-300 transition-colors text-[14px] font-bold min-w-[3rem] h-6 flex items-center justify-center"
													type="button"
													title="播放速度"
												>
													<span className="vjs-playback-rates-value">{playbackRate}×</span>
												</button>
												
												{/* 原生风格的速率菜单 */}
												{isSpeedMenuVisible && (
													<div className="vjs-menu-content absolute bottom-full left-1/2 transform -translate-x-1/2 bg-gray-900 rounded shadow-lg border border-gray-700 py-1 z-50 min-w-[4rem]">
														<ul className="vjs-menu">
															{[2, 1.5, 1, 0.75].map((rate) => (
																<li key={rate} className="vjs-menu-item">
																	<button
																		onClick={() => {
																			handlePlaybackRateChange(rate);
																			hideSpeedMenu();
																		}}
																		className={`vjs-menu-item-text w-full px-3 py-1 text-[14px] text-left hover:bg-gray-700 transition-colors ${
																			playbackRate === rate ? 'text-blue-400 bg-gray-800 font-bold' : 'text-white font-semibold'
																		}`}
																	>
																		{rate}×
																	</button>
																</li>
															))}
														</ul>
													</div>
												)}
											</div>

											{/* 画中画按钮 */}
											<button
												onClick={handlePictureInPicture}
												className={`text-white hover:text-gray-300 transition-colors flex items-center justify-center w-6 h-6 ${isPictureInPicture ? 'text-blue-400' : ''}`}
												title={isPictureInPicture ? '退出画中画' : '进入画中画'}
											>
												{isPictureInPicture ? (
													<span className="vjs-icon-picture-in-picture-exit text-lg" />
												) : (
													<span className="vjs-icon-picture-in-picture-enter text-lg" />
												)}
											</button>

											{/* 全屏按钮 */}
											<button
												onClick={() => {
													if (videoRef.current) {
														if (document.fullscreenElement) {
															document.exitFullscreen();
														} else {
															videoRef.current.requestFullscreen();
														}
													}
												}}
												className="text-white hover:text-gray-300 transition-colors flex items-center justify-center w-6 h-6"
											>
												{document.fullscreenElement ? (
													<span className="vjs-icon-fullscreen-exit text-lg" />
												) : (
													<span className="vjs-icon-fullscreen-enter text-lg" />
												)}
											</button>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					{/* 底部编辑工具栏 */}
					<div className="toolbar flex w-full flex-wrap items-center justify-start gap-2 md:w-[560px]">
						<div className="flex flex-wrap items-center gap-2 gap-x-1.5">
							<button
								type="button"
								className="text-muted-foreground hover:bg-accent hover:text-primary cursor-pointer gap-x-1 rounded p-1 transition-all bg-background/80 size-7 flex items-center justify-center"
								onClick={onEdit}
							>
								<Edit className="size-4" />
							</button>
							<button
								type="button"
								className="text-muted-foreground hover:bg-accent hover:text-primary cursor-pointer gap-x-1 rounded p-1 transition-all bg-background/80 size-7 flex items-center justify-center"
								onClick={onRemix}
							>
								<RefreshCw className="size-4" />
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
