"use client";

import type { VideoType } from "@marketing/image-to-video/types";
import { RichVideoCard } from "./RichVideoCard";

// 示例视频数据
const sampleVideo: VideoType = {
	id: "sample-video-1",
	prompt: "A peaceful sunset over the ocean with gentle waves",
	thumbnailUrl: "/images/sample-video-thumbnail.jpg",
	url: "/videos/banner-video2.mp4",
	status: "completed",
	mode: "professional",
	createdAt: new Date().toISOString(),
	updatedAt: new Date().toISOString(),
	isFavorite: false,
	isPublic: true,
	hasCopyProtection: false,
	userId: "user-123",
};

export function RichVideoCardExample() {
	const handleDownload = () => {
		console.log("Download video");
	};

	const handleShare = () => {
		console.log("Share video");
	};

	const handleFavorite = () => {
		console.log("Toggle favorite");
	};

	const handleEdit = () => {
		console.log("Edit video");
	};

	const handleRemix = () => {
		console.log("Remix video");
	};

	return (
		<RichVideoCard
			video={sampleVideo}
			brandName="FluxFly"
			category="AI Video Generator"
			onDownload={handleDownload}
			onShare={handleShare}
			onFavorite={handleFavorite}
			onEdit={handleEdit}
			onRemix={handleRemix}
		/>
	);
}
