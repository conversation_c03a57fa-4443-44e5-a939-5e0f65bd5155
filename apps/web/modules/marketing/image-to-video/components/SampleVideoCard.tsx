"use client";

interface SampleVideoCardProps {
    videoSrc?: string;           // 视频源路径
    maxHeight?: string;          // 最大高度
    className?: string;          // 自定义样式
    autoPlay?: boolean;          // 是否自动播放
    muted?: boolean;            // 是否静音
}

export function SampleVideoCard({
    videoSrc = "/videos/banner-video2.mp4",
    maxHeight = "500px",
    className = "",
    autoPlay = true,
    muted = true
}: SampleVideoCardProps) {
    return (
        <div className={`w-full ${className}`}>
            <video
                className="w-full h-auto shadow-sm"
                controls
                preload="metadata"
                autoPlay={autoPlay}
                muted={muted}
                style={{ maxHeight }}
            >
                <source src={videoSrc} type="video/mp4" />
                <p className="text-gray-600 p-4 text-center">
                    Your browser does not support the video tag.
                </p>
            </video>
        </div>
    );
}