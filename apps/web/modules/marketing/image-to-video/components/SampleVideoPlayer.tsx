// apps/web/modules/tooling/image-to-video/components/preview/SampleVideoPlayer.tsx
"use client";

import { Slider } from "@ui/components/slider";
import { Maximize, Pause, Play, Volume2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useRef, useState } from "react";

export function SampleVideoPlayer() {
	const t = useTranslations("imageToVideo.preview");
	const [isPlaying, setIsPlaying] = useState(true);
	const [progress, setProgress] = useState(0);
	const [duration, setDuration] = useState(0);
	const videoRef = useRef<HTMLVideoElement>(null);

	const togglePlay = () => {
		if (videoRef.current) {
			if (isPlaying) {
				videoRef.current.pause();
			} else {
				videoRef.current.play();
			}
			setIsPlaying(!isPlaying);
		}
	};

	const handleProgress = () => {
		if (videoRef.current) {
			const currentProgress =
				(videoRef.current.currentTime / videoRef.current.duration) *
				100;
			setProgress(currentProgress);
		}
	};

	const handleSeek = (value: number[]) => {
		if (videoRef.current) {
			const seekTime = (value[0] / 100) * videoRef.current.duration;
			videoRef.current.currentTime = seekTime;
			setProgress(value[0]);
		}
	};

	const formatTime = (seconds: number) => {
		const mins = Math.floor(seconds / 60);
		const secs = Math.floor(seconds % 60);
		return `${mins}:${secs < 10 ? "0" : ""}${secs}`;
	};

	useEffect(() => {
		if (videoRef.current) {
			const video = videoRef.current;

			const handleLoadedMetadata = () => {
				setDuration(video.duration);
			};

			const handleTimeUpdate = () => {
				handleProgress();
			};

			const handleEnded = () => {
				setIsPlaying(false);
				setProgress(0);
				video.currentTime = 0;
			};

			video.addEventListener("loadedmetadata", handleLoadedMetadata);
			video.addEventListener("timeupdate", handleTimeUpdate);
			video.addEventListener("ended", handleEnded);

			return () => {
				video.removeEventListener(
					"loadedmetadata",
					handleLoadedMetadata,
				);
				video.removeEventListener("timeupdate", handleTimeUpdate);
				video.removeEventListener("ended", handleEnded);
			};
		}
	}, []);

	return (
		<div className="relative overflow-hidden">
			{/* 视频播放器 */}
			<div className="bg-black aspect-video flex items-center justify-center">
				<video
					ref={videoRef}
					className="w-full h-full object-contain"
					poster="/images/sample-video-poster.jpg"
					controls={false}
					autoPlay={true}
					muted={true}
					loop={true}
				>
					<source
						src="/videos/sample-groundhogs-video.mp4"
						type="video/mp4"
					/>
					{/* 添加字幕轨道以提高可访问性 */}
					<track
						kind="captions"
						src="/videos/captions/sample-video-captions.vtt"
						srcLang="en"
						label="English"
					/>
					{t("browserNotSupported")}
				</video>
				
				{/* 中央播放按钮，仅在暂停时显示 */}
				{!isPlaying && (
					<div className="absolute inset-0 flex items-center justify-center">
						<button
							type="button"
							className="bg-white/20 backdrop-blur-sm rounded-full p-3 hover:bg-white/30 transition-colors"
							onClick={togglePlay}
						>
							<Play size={24} className="text-white" />
						</button>
					</div>
				)}
			</div>

			{/* 控制栏 */}
			<div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent px-4 pb-3 pt-12">
				{/* 进度条 */}
				<div className="mb-2">
					<Slider
						value={[progress]}
						max={100}
						step={0.1}
						onValueChange={handleSeek}
						className="cursor-pointer"
					/>
				</div>

				{/* 控制按钮和时间显示 */}
				<div className="flex items-center justify-between text-white">
					<div className="flex items-center gap-3">
						<button
							type="button"
							className="p-1.5 hover:bg-white/10 rounded-full transition-colors"
							onClick={togglePlay}
							aria-label={isPlaying ? t("pause") : t("play")}
						>
							{isPlaying ? (
								<Pause size={16} />
							) : (
								<Play size={16} />
							)}
						</button>

						<button
							type="button"
							className="p-1.5 hover:bg-white/10 rounded-full transition-colors"
							aria-label={t("volume")}
						>
							<Volume2 size={16} />
						</button>

						<span className="text-xs text-gray-200 font-medium">
							{videoRef.current
								? formatTime(videoRef.current.currentTime)
								: "0:00"}{" "}
							/ {formatTime(duration)}
						</span>
					</div>

					<div>
						<button
							type="button"
							className="p-1.5 hover:bg-white/10 rounded-full transition-colors"
							aria-label={t("fullscreen")}
						>
							<Maximize size={16} />
						</button>
					</div>
				</div>
			</div>
		</div>
	);
}
