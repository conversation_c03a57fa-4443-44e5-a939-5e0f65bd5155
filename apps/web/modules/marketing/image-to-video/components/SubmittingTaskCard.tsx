"use client";

interface SubmittingTaskCardProps {
    className?: string;
}

export function SubmittingTaskCard({ className = "" }: SubmittingTaskCardProps) {
    return (
        <div className={`w-full h-[280px] bg-gray-800 rounded-lg flex flex-col items-center justify-center ${className}`}>
            {/* 红色音频波形图标 */}
            <div className="mb-4 flex items-center justify-center space-x-1">
                <div className="w-1 bg-red-500 rounded-full animate-bounce" style={{ height: '16px', animationDelay: '0ms' }}></div>
                <div className="w-1 bg-red-500 rounded-full animate-bounce" style={{ height: '24px', animationDelay: '100ms' }}></div>
                <div className="w-1 bg-red-500 rounded-full animate-bounce" style={{ height: '20px', animationDelay: '200ms' }}></div>
                <div className="w-1 bg-red-500 rounded-full animate-bounce" style={{ height: '28px', animationDelay: '300ms' }}></div>
                <div className="w-1 bg-red-500 rounded-full animate-bounce" style={{ height: '16px', animationDelay: '400ms' }}></div>
                <div className="w-1 bg-red-500 rounded-full animate-bounce" style={{ height: '22px', animationDelay: '500ms' }}></div>
            </div>
            
            {/* 提交文本 */}
            <div className="text-white text-center">
                <p className="text-lg font-medium">Submitting your task...</p>
            </div>
        </div>
    );
}