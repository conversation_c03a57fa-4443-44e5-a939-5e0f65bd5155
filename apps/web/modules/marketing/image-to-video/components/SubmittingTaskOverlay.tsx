"use client";

export function SubmittingTaskOverlay() {
  return (
    <div className="absolute inset-0 z-10 bg-slate-800/90 backdrop-blur-sm flex items-center justify-center">
      <div className="text-center text-white">
        {/* 蓝色音频波形图标 */}
        <div className="mb-4 flex items-center justify-center space-x-1">
          <div className="w-1 bg-blue-500 rounded-full animate-bounce" style={{ height: '16px', animationDelay: '0ms' }}></div>
          <div className="w-1 bg-blue-500 rounded-full animate-bounce" style={{ height: '24px', animationDelay: '100ms' }}></div>
          <div className="w-1 bg-blue-500 rounded-full animate-bounce" style={{ height: '20px', animationDelay: '200ms' }}></div>
          <div className="w-1 bg-blue-500 rounded-full animate-bounce" style={{ height: '28px', animationDelay: '300ms' }}></div>
          <div className="w-1 bg-blue-500 rounded-full animate-bounce" style={{ height: '16px', animationDelay: '400ms' }}></div>
          <div className="w-1 bg-blue-500 rounded-full animate-bounce" style={{ height: '22px', animationDelay: '500ms' }}></div>
        </div>
        
        {/* 提交文本 */}
        <div className="text-white text-center">
          <p className="text-lg font-medium">Submitting your task...</p>
        </div>
      </div>
    </div>
  );
}