"use client";

import Image from "next/image";

interface VideoProps {
  src?: string;
  isVideo?: boolean;
  alt?: string;
  className?: string;
  generationId?: string; // 新增：generation ID
}

export function Video({ 
  src, 
  isVideo = false, 
  alt = "Media content", 
  className = "",
  generationId // 新增参数
}: VideoProps) {
  // 计算视频容器ID
  const videoContainerId = generationId ? `gm-${generationId}` : undefined;
  if (!src) {
    return (
      <div 
        id={videoContainerId} // 添加ID
        className={`aspect-video bg-slate-800 flex items-center justify-center ${className}`}
      >
        <p className="text-gray-500">No media available</p>
      </div>
    );
  }

  if (!isVideo) {
    return (
      <div 
        id={videoContainerId} // 添加ID
        className={`relative aspect-video ${className}`}
      >
        <Image
          src={src}
          alt={alt}
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
      </div>
    );
  }

  // 直接克隆SampleVideoCard的视频实现
  return (
    <div 
      id={videoContainerId} // 添加ID
      className={`relative aspect-video ${className}`}
    >
      <video
        className="w-full h-full object-cover"
        controls
        preload="metadata"
        autoPlay={true}
        muted={true}
      >
        <source src={src} type="video/mp4" />
        <p className="text-gray-600 p-4 text-center">
          Your browser does not support the video tag.
        </p>
      </video>
    </div>
  );
}