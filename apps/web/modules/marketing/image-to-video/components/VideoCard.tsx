"use client";

import { But<PERSON> } from "@ui/components/button";
import { Progress } from "@ui/components/progress";
import { Edit, RefreshCw } from "lucide-react";
import { useState, useEffect } from "react";
import { calculateTimeBasedProgress, shouldShowProgress, getProgressStatusText } from "../lib/progress-utils";

interface VideoCardProps {
  task: {
    id: string;
    jobId?: string;
    status: 'waiting' | 'processing' | 'completed' | 'failed';
    progress?: number;
    imageUrl?: string; // 用户上传的图片
    videoUrl?: string; // 生成的视频
    coverUrl?: string; // 视频封面
    prompt?: string;
    model?: string;
    timestamp?: string;
    generations?: any[];
    // 新增时间相关字段
    startTime?: number; // 任务开始时间戳
    estimatedTime?: number; // 预估时间（秒）
    estimatedTimeMs?: number; // 预估时间（毫秒）
  };
}

export function VideoCard({ task }: VideoCardProps) {
  // 实时计算基于时间的进度
  const [timeBasedProgress, setTimeBasedProgress] = useState(0);

  useEffect(() => {
    if (!shouldShowProgress(task.status) || !task.startTime || !task.estimatedTime) {
      return;
    }

    // 立即计算一次进度
    const updateProgress = () => {
      const progress = calculateTimeBasedProgress(
        task.startTime!,
        task.estimatedTime!
      );
      setTimeBasedProgress(progress);
    };

    updateProgress();

    // 每秒更新进度
    const interval = setInterval(updateProgress, 1000);

    return () => clearInterval(interval);
  }, [task.status, task.startTime, task.estimatedTime]);

  const renderContent = () => {
    switch (task.status) {
      case 'waiting':
        const waitingProgress = Math.min(timeBasedProgress, 10); // waiting状态最多10%
        const waitingStatusText = getProgressStatusText(waitingProgress, task.status);

        return (
          <div className="relative flex size-full flex-col items-center justify-center gap-4 p-8 text-center">
            {/* 用户图片背景，低透明度 */}
            {task.imageUrl && (
              <div
                className="pointer-events-none absolute inset-0 bg-contain bg-center bg-no-repeat opacity-20"
                style={{ backgroundImage: `url('${task.imageUrl}')` }}
              />
            )}

            {/* 等待中也显示进度条 */}
            <div className="relative w-[90%] max-w-[600px] space-y-4">
              <div className="space-y-2">
                <Progress value={waitingProgress} className="h-2" />
                <div className="text-sm font-medium text-primary">
                  {waitingProgress}%
                </div>
              </div>

              <div className="space-y-2">
                <p className="text-sm font-medium text-foreground">
                  {waitingStatusText}
                </p>
                <p className="text-sm text-muted-foreground">
                  Your task is in the queue and will start shortly.
                </p>
              </div>
            </div>
          </div>
        );

      case 'processing':
        const currentProgress = timeBasedProgress;
        const statusText = getProgressStatusText(currentProgress, task.status);

        return (
          <div className="relative flex size-full flex-col items-center justify-center gap-4 p-8 text-center">
            {/* 用户图片背景，低透明度 - 关键！*/}
            {task.imageUrl && (
              <div
                className="pointer-events-none absolute inset-0 bg-contain bg-center bg-no-repeat opacity-20"
                style={{ backgroundImage: `url('${task.imageUrl}')` }}
              />
            )}

            {/* 进度条区域 */}
            <div className="relative w-[90%] max-w-[600px] space-y-4">
              {/* 进度条 */}
              <div className="space-y-2">
                <Progress value={currentProgress} className="h-2" />
                <div className="text-sm font-medium text-primary">
                  {currentProgress}%
                </div>
              </div>

              {/* 动态状态文字 */}
              <div className="space-y-2">
                <p className="text-sm font-medium text-foreground">
                  {statusText}
                </p>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  Pollo AI is generating your video, which may take {task.estimatedTime || 60} seconds.
                  Once finished, the video will be saved in{" "}
                  <span className="text-primary font-medium">My Creations</span>.
                </p>
              </div>
            </div>
          </div>
        );

      case 'completed':
        // 获取完成的生成结果
        const completedGenerations = task.generations?.filter((gen: any) => gen.status === "succeeded") || [];

        if (completedGenerations.length > 0) {
          const generation = completedGenerations[0];
          return (
            <video
              className="w-full h-auto shadow-sm rounded-lg"
              controls
              preload="metadata"
              poster={generation.cover || generation.thumbnail}
              style={{ maxHeight: '400px' }}
            >
              <source src={generation.videoUrl || generation.mediaUrl} type="video/mp4" />
              <p className="text-gray-600 p-4 text-center">
                Your browser does not support the video tag.
              </p>
            </video>
          );
        } else {
          return (
            <div className="relative flex size-full flex-col items-center justify-center gap-4 p-8 text-center text-muted-foreground">
              <div className="space-y-2">
                <div className="text-sm">Video generation completed</div>
                <div className="text-xs opacity-70">
                  No video results available
                </div>
              </div>
            </div>
          );
        }

      case 'failed':
        return (
          <div className="relative flex size-full flex-col items-center justify-center gap-4 p-8 text-center">
            {task.imageUrl && (
              <div
                className="pointer-events-none absolute inset-0 bg-contain bg-center bg-no-repeat opacity-10"
                style={{ backgroundImage: `url('${task.imageUrl}')` }}
              />
            )}

            <div className="relative space-y-2 text-destructive">
              <div className="text-sm font-medium">Generation failed</div>
              <div className="text-xs opacity-70">
                Please check your parameters and try again
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="bg-card rounded-lg p-5 shadow-sm border">
      {/* 头部信息 - 始终显示，保持布局一致性 */}
      <div className="flex flex-col gap-2 pb-4">
        {/* 品牌和标签行 */}
        <div className="flex max-w-full flex-wrap items-center gap-2">
          <div className="flex flex-wrap items-center gap-2">
            <span className="text-sm font-semibold">Pollo.ai</span>
            <span className="h-3 w-0.5 bg-border"></span>
            <span className="text-xs text-muted-foreground border border-border rounded px-2 py-1">
              Image to Video
            </span>
            <span className="text-xs text-muted-foreground border border-border rounded px-2 py-1">
              {task.model || 'Pollo 1.6'}
            </span>
          </div>
          <div className="flex items-center gap-2 ml-auto">
            <span className="text-xs text-muted-foreground">
              {task.timestamp ? new Date(task.timestamp).toLocaleString() : ''}
            </span>
          </div>
        </div>

        {/* 图片和提示词行 */}
        <div className="flex w-full max-w-full items-center gap-2">
          <div className="flex max-w-screen-sm shrink-0 flex-wrap items-center gap-2">
            {task.imageUrl && (
              <img
                src={task.imageUrl}
                alt=""
                className="size-7 rounded object-cover bg-muted"
                loading="lazy"
              />
            )}
          </div>
          <div className="flex items-center gap-1">
            <div className="text-sm font-medium text-muted-foreground line-clamp-2">
              {task.prompt || 'Video generation task'}
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 - 根据状态决定是否需要背景 */}
      <div className={`${task.status === 'completed' ? '' : 'bg-muted'} rounded-lg overflow-hidden min-h-[240px] md:h-[400px]`}>
        {renderContent()}
      </div>

      {/* 工具栏 - 始终显示 */}
      <div className="flex items-center gap-2 mt-3">
        <Button variant="ghost" size="sm" className="size-7">
          <Edit className="size-4" />
        </Button>
        <Button variant="ghost" size="sm" className="size-7">
          <RefreshCw className="size-4" />
        </Button>
      </div>
    </div>
  );
}