// apps/web/modules/marketing/image-to-video/components/VideoGenerationContainer.tsx
"use client";

import { useState, useCallback } from "react";
import { VideoGenerationForm } from "./VideoGenerationForm";
import { VideoPreviewSection } from "./VideoPreviewSection";
import { createVideoJob, getJobDetails, convertFormDataToJobRequest } from "@shared/lib/jobs-api";
import { useToast } from "@ui/components/use-toast";
import { getGenerationTimeByModelCode } from "../lib/modelConfig";
// 临时使用Date.now生成ID，后续可替换为更好的UUID库
const generateTempId = () => `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

interface Task {
	id: string;
	jobId: string;
	status: string;
	progress: number;
	// 其他任务字段...
}

interface ModelConfig {
	// 模型配置字段...
}

interface VideoGenerationContainerProps {
	initialTasks?: Task[];
	initialConfig?: ModelConfig;
}

export function VideoGenerationContainer({ 
	initialTasks = [],
	initialConfig 
}: VideoGenerationContainerProps = {}) {
	// 管理任务状态 - 使用初始数据
	const [tasks, setTasks] = useState<any[]>(initialTasks);
	const [isProcessing, setIsProcessing] = useState(false);
	const { toast } = useToast();

	// 轮询任务状态
	const pollJobStatus = useCallback(async (jobId: string) => {
		const pollInterval = setInterval(async () => {
			try {
				const response = await getJobDetails(jobId);

				if (response.success && response.data) {
					const { job, generations, progress } = response.data;

					// 更新任务状态
					setTasks((prev) => {
						return prev.map((task) =>
							task.jobId === jobId
								? {
									...task,
									status: job.status,
									progress: progress.total > 0 ? (progress.completed / progress.total) * 100 : 0,
									generations: generations.map(gen => ({
										...gen,
										inputImage: task.inputImage,
										estimatedDuration: task.estimatedTime || getGenerationTimeByModelCode(task.model || "")
									})),
									updatedAt: job.updatedAt,
								}
								: task
						);
					});

					// 如果任务完成或失败，停止轮询
					if (job.status === "succeeded" || job.status === "failed") {
						clearInterval(pollInterval);
						setIsProcessing(false);

						if (job.status === "succeeded") {
							toast.success("视频生成完成", {
								description: "您的视频已成功生成！",
							});
						} else {
							toast.error("视频生成失败", {
								description: "请检查参数并重试。",
							});
						}
					}
				}
			} catch (error) {
				console.error("Error polling job status:", error);
			}
		}, 3000); // 每3秒轮询一次

		// 设置最大轮询时间（15分钟）
		setTimeout(() => {
			clearInterval(pollInterval);
			setIsProcessing(false);
		}, 15 * 60 * 1000);

		return pollInterval;
	}, [toast]);

	// 处理创建任务
	const handleCreateTask = async (formData: any) => {
		console.log("🎬 handleCreateTask called with formData:", formData);
		setIsProcessing(true);
		
		// 第1步：立即显示creating状态
		const tempId = generateTempId();
		
		const inputImageUrl = formData.image || formData.imageFile;
		
		// 根据outputNumber生成多个generation
		const outputNumber = formData.outputNumber || 1;
		const modelGenerationTime = getGenerationTimeByModelCode(formData.model?.code || "");
		const generations = Array.from({ length: outputNumber }, (_, index) => ({
			id: `temp-gen-${tempId}-${index}`,
			tempId: tempId, // 新增：存储关联的临时ID
			status: "creating" as const,
			inputImage: inputImageUrl, // 兼容处理：优先使用accessURL，回退到imageFile
			createdAt: new Date().toISOString(),
			estimatedDuration: modelGenerationTime
		}));

		const creatingTask = {
			id: `creating-${tempId}`,
			tempId: tempId, // 新增：存储纯净的临时ID
			status: "creating",
			timestamp: new Date().toISOString(),
			inputImage: inputImageUrl, // 兼容处理：优先使用accessURL，回退到imageFile
			prompt: formData.prompt,
			outputNumber: outputNumber, // 新增：记录输出数量
			generations: generations
		};
		setTasks((prev) => [creatingTask, ...prev]);

		try {
			// 转换表单数据为API请求格式（包含图片上传）
			console.log("📝 Converting form data to job request...");
			const jobRequest = await convertFormDataToJobRequest(formData);
			console.log("📋 Job request prepared:", jobRequest);

			// 调用Job API创建任务
			console.log("🚀 Calling createVideoJob API...");
			const response = await createVideoJob(jobRequest);
			console.log("📥 API Response:", response);

			if (response.success && response.data) {
				// 第3步：使用API返回的数据替换creating状态
				const { job, generations, user } = response.data;
				const waitingTask = {
					id: job.id,
					jobId: job.id,
					tempId: null, // 清除临时ID
					prompt: job.prompt,
					model: job.modelCode,
					status: "waiting",
					progress: 0,
					timestamp: job.createdAt,
					estimatedTime: getGenerationTimeByModelCode(job.modelCode),
					credit: job.credit,
					numOutputs: job.numOutputs,
					generations: generations.map(gen => ({
						...gen,
						tempId: null, // 清除临时ID
						generationId: gen.id, // 新增：明确的generation ID
						status: gen.status === "waiting" ? "waiting" : gen.status,
						inputImage: job.image || formData.image || formData.imageFile,
						estimatedDuration: getGenerationTimeByModelCode(job.modelCode)
					})),
					inputImage: job.image || formData.imageFile,
					user: user
				};

				// 替换临时任务
				setTasks((prev) => prev.map(t => t.id === creatingTask.id ? waitingTask : t));

				// 显示成功提示
				toast.success("任务创建成功", {
					description: `已消耗 ${job.credit} 积分，正在生成视频...`,
				});

				// 第4步：开始轮询任务状态
				pollJobStatus(job.id);
			} else {
				// ✅ 增强错误处理
				setIsProcessing(false);
				// 移除临时任务
				setTasks((prev) => prev.filter(t => t.id !== creatingTask.id));
				
				const errorMessage = response.error || "未知错误";
				
				if (errorMessage.includes('Rate limit exceeded')) {
					toast.error("请求过于频繁", {
						description: "请稍后再试，或考虑升级账户获得更高限额",
					});
				} else if (errorMessage.includes('Insufficient credits')) {
					toast.error("积分不足", {
						description: "请充值积分后重试",
					});
				} else if (errorMessage.includes('Invalid')) {
					toast.error("参数错误", {
						description: "请检查您的输入参数",
					});
				} else {
					toast.error("任务创建失败", {
						description: errorMessage,
					});
				}
			}
		} catch (error) {
			console.error("Error creating video job:", error);
			setIsProcessing(false);
			// 移除临时任务
			setTasks((prev) => prev.filter(t => t.id !== creatingTask.id));
			
			// ✅ 网络错误处理
			if (error instanceof Error && error.name === 'AbortError') {
				toast.error("请求超时", {
					description: "网络连接超时，请重试",
				});
			} else {
				toast.error("任务创建失败", {
					description: error instanceof Error ? error.message : "网络错误",
				});
			}
		}
	};

	return (
		<div
			id="gen-tool"
			className="flex flex-col lg:flex-row gap-3 md:gap-4 lg:gap-6 w-full p-3 md:p-4 lg:p-6"
		>
			{/* 左侧表单区域 - 调宽 */}
			<div
				id="tool-form"
				className="w-full lg:w-5/12 xl:w-4/12 2xl:w-4/12 bg-card rounded-lg shadow-sm border border-gray-200 flex flex-col"
			>
				{/* 标题 - 在form父div里面 */}
				<div className="p-6 border-b border-gray-200">
					<h1 className="text-2xl font-bold text-gray-800">
						Image To Video AI Generator
					</h1>
				</div>

				<VideoGenerationForm
					onSubmit={handleCreateTask}
					isProcessing={isProcessing}
				/>
			</div>

			{/* 右侧预览/任务区域 - 调窄 */}
			<div
				id="tool-preview"
				className="bg-card shadow-sm border border-gray-200 flex flex-col rounded-lg pb-8 px-8 w-full lg:w-7/12 xl:w-8/12 2xl:w-8/12 relative"
			>
				<VideoPreviewSection tasks={tasks} />
			</div>
		</div>
	);
}
