// apps/web/modules/marketing/image-to-video/components/VideoGenerationForm.tsx
"use client";

import { AspectRatioSelector } from "@marketing/image-to-video/components/form/AspectRatioSelector";
// import { CopyProtectionOption } from "@marketing/image-to-video/components/form/CopyProtectionOption";
import { EndFrameImageUploader } from "@marketing/image-to-video/components/form/EndFrameImageUploader";
import { ModeSelector } from "@marketing/image-to-video/components/form/ModeSelector";
import { ModelSelector } from "@marketing/image-to-video/components/form/ModelSelector";
import { MotionRangeSelector } from "@marketing/image-to-video/components/form/MotionRangeSelector";
import { NegativePromptInput } from "@marketing/image-to-video/components/form/NegativePromptInput";
import { PromptInputSection } from "@marketing/image-to-video/components/form/PromptInputSection";
import { PromptStrengthSlider } from "@marketing/image-to-video/components/form/PromptStrengthSlider";
// import { PublicVisibilityOption } from "@marketing/image-to-video/components/form/PublicVisibilityOption";
import { VideoLengthSelector } from "@marketing/image-to-video/components/form/VideoLengthSelector";
import { VideoOutputOptions } from "@marketing/image-to-video/components/form/VideoOutputOptions";
import { Button } from "@ui/components/button";
import { Skeleton } from "@ui/components/skeleton";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { ChevronDown } from "lucide-react";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@ui/components/collapsible";
import { useCreditCalculation } from "../hooks/useCreditCalculation";
import { useModelOptions } from "../providers/ModelOptionsProvider";
import { CreditDisplay } from "./CreditDisplay";
import { ResolutionSelector } from "./form/ResolutionSelector";
import { SeedInput } from "./form/SeedInput";
import { StyleSelector } from "./form/StyleSelector";
import { ImageUploaderWithEditor } from "./image-editor/ImageUploaderWithEditor";

interface VideoGenerationFormProps {
	onSubmit: (data: any) => void;
	isProcessing: boolean;
}

export function VideoGenerationForm({
	onSubmit,
	isProcessing,
}: VideoGenerationFormProps) {
	// 使用 next-intl 的 useTranslations 加载翻译
	const t = useTranslations("imageToVideo");
	// 在客户端组件中不需要手动管理加载状态
	const isLoading = false;
	const {
		selectedModel,
		selectedMode,
		videoLength,
		aspectRatio,
		style,
		resolution,
	} = useModelOptions();

	// 折叠状态
	const [isAdvancedOpen, setIsAdvancedOpen] = useState(false);

	// 表单状态
	const [formData, setFormData] = useState({
		model: selectedModel,
		mode: selectedMode?.code || "", // 使用mode的code而不是模式对象
		image: null,
		prompt: "",
		aspectRatio: aspectRatio || "16:9",
		videoLength: videoLength || 5,
		style: style || "",
		resolution: resolution || "",
		endFrameImage: null,
		promptStrength: 50,
		negativePrompt: "",
		outputNumber: 1,
		publicVisibility: true,
		copyProtection: true,
		seed: null as number | null,
		seedLocked: false,
		motionRange: "auto",
	});

	// 动态计算 Credits
	const creditCalculation = useCreditCalculation({
		selectedModel,
		selectedMode,
		videoLength: formData.videoLength,
		aspectRatio: formData.aspectRatio,
		outputNumber: formData.outputNumber,
		resolution:
			formData.resolution ||
			selectedModel?.resolutionOptions?.[0] ||
			null,
		style: formData.style || selectedModel?.styleOptions?.[0]?.code || null,
	});

	// 同步状态变化
	useEffect(() => {
		setFormData((prev) => ({
			...prev,
			model: selectedModel,
			mode: selectedMode?.code || "", // 使用code而不是对象
			// 重置与模型相关的选项
			aspectRatio:
				selectedModel?.aspectRatioOptions?.[0] || prev.aspectRatio,
			videoLength:
				selectedModel?.videoLengthOptions?.[0] || prev.videoLength,
			style: selectedModel?.styleOptions?.[0]?.code || prev.style,
			resolution: selectedModel?.resolutionOptions?.[0] || "",
			outputNumber: 1, // 重置输出数量为1
			// 如果新模型不支持seed，清除seed值
			seed: selectedModel?.supportsSeed ? prev.seed : null,
			seedLocked: selectedModel?.supportsSeed ? prev.seedLocked : false,
			// 如果新模型支持motion range，设置默认值，否则保持原值
			motionRange:
				selectedModel?.motionRangeOptions?.[0]?.code ||
				prev.motionRange,
		}));
	}, [selectedModel, selectedMode]);

	// 表单更新处理
	const updateFormData = (field: string, value: any) => {
		setFormData((prev) => ({ ...prev, [field]: value }));
	};

	// 表单验证逻辑
	const isFormValid = formData.image && (formData.model?.code || formData.model?.value);

	// 表单提交处理
	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		
		// 统一验证逻辑
		if (!isFormValid) {
			return;
		}
		
		onSubmit(formData);
	};

	// 加载中显示骨架屏
	if (isLoading) {
		return (
			<div className="space-y-4">
				<Skeleton className="h-8 w-64 mb-2" />
				<Skeleton className="h-[400px] w-full" />
			</div>
		);
	}

	return (
		<form onSubmit={handleSubmit} className="flex flex-col">
			{/* 表单内容区域 */}
			<div className="px-8 pt-2 pb-8 space-y-6">
				{/* 1. 模型选择区 */}
				<div className="space-y-2">
					<label className="text-sm font-medium text-gray-700">
						Model
					</label>
					<ModelSelector />
				</div>

				{/* 2. 核心输入参数 */}
				<div className="space-y-6">
					{/* 图片上传区域 - 移除内嵌的EndFrame */}
					<div className="space-y-2">
						<div className="text-gray-800 font-medium">
							{t("form.imageUpload")}
						</div>
						<ImageUploaderWithEditor
							onChange={(file, uploadedUrl) => {
								// 如果有上传的URL，优先使用URL；否则使用文件
								if (uploadedUrl) {
									updateFormData("image", uploadedUrl);
								} else {
									updateFormData("image", file);
								}
							}}
							maxSize={10}
							acceptedFormats={[
								"image/jpeg",
								"image/png",
								"image/webp",
							]}
							aspectRatioOptions={
								selectedModel?.aspectRatioOptions
							}
						/>
					</div>

					{/* 提示词输入区域 */}
					<PromptInputSection
						value={formData.prompt}
						onChange={(value) => updateFormData("prompt", value)}
					/>
				</div>

				{/* 3. Advanced Settings 折叠面板 */}
				<Collapsible open={isAdvancedOpen} onOpenChange={setIsAdvancedOpen}>
					<CollapsibleTrigger className="flex items-center gap-2 w-full p-0 bg-transparent transition-colors mt-6 mb-4">
						<span className="text-sm font-medium text-gray-700">Advanced Settings</span>
						<ChevronDown 
							className={`h-4 w-4 text-gray-500 transition-transform duration-200 ${
								isAdvancedOpen ? "rotate-180" : ""
							}`}
						/>
					</CollapsibleTrigger>
					<CollapsibleContent className="space-y-6">
						{/* End Frame Image Uploader */}
						{selectedModel?.supportsEndFrame && (
							<div className="space-y-2">
								<div className="text-gray-800 font-medium">
									End Frame Image
								</div>
								<EndFrameImageUploader
									onChange={(file, uploadedUrl) => {
										// 与主图逻辑保持一致
										if (uploadedUrl) {
											updateFormData("endFrameImage", uploadedUrl);
										} else {
											updateFormData("endFrameImage", file);
										}
									}}
									maxSize={10}
									acceptedFormats={[
										"image/jpeg",
										"image/png",
										"image/webp",
									]}
									aspectRatioOptions={
										selectedModel?.aspectRatioOptions
									}
								/>
							</div>
						)}

						{/* 模式选择 */}
						<ModeSelector
							selectedMode={formData.mode || ""}
							onChange={(value) => updateFormData("mode", value)}
						/>

						{/* 风格选择 */}
						<StyleSelector
							selectedStyle={formData.style}
							onChange={(value) => updateFormData("style", value)}
						/>

						{/* 分辨率选择 */}
						<ResolutionSelector
							selectedResolution={formData.resolution}
							onChange={(value) =>
								updateFormData("resolution", value)
							}
						/>

						{/* 长宽比选择 */}
						<AspectRatioSelector
							selectedRatio={formData.aspectRatio}
							onChange={(value) =>
								updateFormData("aspectRatio", value)
							}
						/>

						{/* 视频长度选择 */}
						<VideoLengthSelector
							selectedLength={formData.videoLength}
							onChange={(value) =>
								updateFormData("videoLength", value)
							}
						/>

						{/* 提示词强度 */}
						<PromptStrengthSlider
							value={formData.promptStrength}
							onChange={(value) =>
								updateFormData("promptStrength", value)
							}
						/>

						{/* 反向提示词 */}
						<NegativePromptInput
							value={formData.negativePrompt}
							onChange={(value) =>
								updateFormData("negativePrompt", value)
							}
						/>

						{/* Seed输入 */}
						<SeedInput
							value={formData.seed}
							onChange={(value) => updateFormData("seed", value)}
							isLocked={formData.seedLocked}
							onLockToggle={(locked) =>
								updateFormData("seedLocked", locked)
							}
						/>

						{/* Motion Range选择 */}
						<MotionRangeSelector
							selectedMotionRange={formData.motionRange}
							onChange={(value) =>
								updateFormData("motionRange", value)
							}
						/>

						{/* 视频输出选项 */}
						<VideoOutputOptions
							formData={formData}
							onChange={updateFormData}
						/>
					</CollapsibleContent>
				</Collapsible>

				{/* 4. 底部区域 - 移入space-y-6容器内 */}
				<div className="space-y-2">
					{/* Credits 信息 - 使用动态计算 */}
					{selectedModel && (
						<CreditDisplay
							calculation={creditCalculation}
							showDetails={false}
							modelName={selectedModel.name}
							modeName={selectedMode?.name}
							videoLength={formData.videoLength}
							resolution={
								selectedModel.resolutionOptions &&
								selectedModel.resolutionOptions.length > 0 &&
								formData.resolution
									? formData.resolution
									: undefined
							}
						/>
					)}

					{/* 生成按钮 */}
					<Button
						type="submit"
						variant="gray"
						size="lg"
						className="w-full font-medium"
						loading={isProcessing}
						disabled={!isFormValid}
						aria-disabled={!isFormValid}
					>
						{isProcessing ? `${t("form.processing")}...` : t("form.generateWithAI")}
					</Button>
					{!isFormValid && (
						<p className="text-xs text-red-500 mt-2 text-center">
							{!formData.image && t("errors.validationErrors.imageRequired")}
							{formData.image && (!formData.model?.code && !formData.model?.value) && "Model selection required"}
						</p>
					)}
				</div>
			</div>
		</form>
	);
}
