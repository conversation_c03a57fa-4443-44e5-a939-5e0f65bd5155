"use client";

import type { VideoType } from "@marketing/image-to-video/types";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { cn } from "@ui/lib";
import { formatDistanceToNow } from "date-fns";
import {
	Download,
	Loader2,
	MoreHorizontal,
	Pause,
	Play,
	Share2,
	Star,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";

interface VideoItemProps {
	video: VideoType;
}

export function VideoItem({ video }: VideoItemProps) {
	const t = useTranslations("imageToVideo.videoItem");
	const [isPlaying, setIsPlaying] = useState(false);
	const [isFavorite, setIsFavorite] = useState(video.isFavorite);

	// 处理播放/暂停
	const handlePlayToggle = () => {
		if (video.status === "completed") {
			setIsPlaying(!isPlaying);
		}
	};

	// 处理收藏
	const handleFavoriteToggle = () => {
		setIsFavorite(!isFavorite);
		// 这里可以添加API调用来更新收藏状态
	};

	// 处理下载
	const handleDownload = () => {
		if (video.status === "completed" && video.url) {
			window.open(video.url, "_blank");
		}
	};

	// 处理分享
	const handleShare = () => {
		if (video.status === "completed" && video.url) {
			navigator.clipboard.writeText(video.url);
			// 这里可以添加一个提示，表示链接已复制
		}
	};

	// 渲染视频状态标签
	const renderStatusBadge = () => {
		switch (video.status) {
			case "waiting":
				return (
					<div className="absolute top-2 right-2 bg-gray-500/80 text-white px-2 py-1 rounded-md text-xs font-medium backdrop-blur-sm">
						{t("status.waiting")}
					</div>
				);
			case "processing":
				return (
					<div className="absolute top-2 right-2 bg-blue-500/80 text-white px-2 py-1 rounded-md text-xs font-medium backdrop-blur-sm flex items-center">
						<Loader2 className="h-3 w-3 animate-spin mr-1" />
						{t("status.processing")}
					</div>
				);
			case "failed":
				return (
					<div className="absolute top-2 right-2 bg-red-500/80 text-white px-2 py-1 rounded-md text-xs font-medium backdrop-blur-sm">
						{t("status.failed")}
					</div>
				);
			default:
				return null;
		}
	};

	return (
		<div className="rounded-lg overflow-hidden border bg-card">
			{/* 视频预览 */}
			<div className="relative aspect-video bg-black">
				{video.status === "completed" ? (
					<video
						src={video.url}
						poster={video.thumbnailUrl}
						className="w-full h-full object-contain"
						loop
						playsInline
						ref={(el) => {
							if (el) {
								isPlaying ? el.play() : el.pause();
							}
						}}
					/>
				) : (
					<div className="w-full h-full flex items-center justify-center bg-muted">
						{video.thumbnailUrl ? (
							<img
								src={video.thumbnailUrl}
								alt={video.prompt}
								className="w-full h-full object-contain opacity-50"
							/>
						) : (
							<div className="text-muted-foreground">
								{t("preview.unavailable")}
							</div>
						)}
					</div>
				)}

				{/* 状态标签 */}
				{renderStatusBadge()}

				{/* 播放控制 */}
				{video.status === "completed" && (
					<Button
						variant="ghost"
						size="icon"
						className="absolute bottom-2 left-2 bg-black/50 hover:bg-black/70 text-white rounded-full h-8 w-8"
						onClick={handlePlayToggle}
					>
						{isPlaying ? (
							<Pause className="h-4 w-4" />
						) : (
							<Play className="h-4 w-4" />
						)}
					</Button>
				)}
			</div>

			{/* 视频信息 */}
			<div className="p-3">
				<div className="flex items-start justify-between">
					<div>
						<h3 className="font-medium line-clamp-1">
							{video.prompt}
						</h3>
						<div className="flex items-center text-xs text-muted-foreground mt-1">
							<span>
								{t("createdAt")}:{" "}
								{formatDistanceToNow(
									new Date(video.createdAt),
									{ addSuffix: true },
								)}
							</span>
							<span className="mx-1">•</span>
							<span>
								{video.mode === "professional"
									? t("mode.professional")
									: t("mode.standard")}
							</span>
						</div>
					</div>

					<div className="flex items-center gap-1">
						<Button
							variant="ghost"
							size="icon"
							className={cn(
								"h-8 w-8 rounded-full",
								isFavorite && "text-yellow-500",
							)}
							onClick={handleFavoriteToggle}
							disabled={video.status !== "completed"}
						>
							<Star
								className="h-4 w-4"
								fill={isFavorite ? "currentColor" : "none"}
							/>
						</Button>

						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button
									variant="ghost"
									size="icon"
									className="h-8 w-8 rounded-full"
									disabled={video.status !== "completed"}
								>
									<MoreHorizontal className="h-4 w-4" />
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent align="end">
								<DropdownMenuItem onClick={handleDownload}>
									<Download className="h-4 w-4 mr-2" />
									{t("actions.download")}
								</DropdownMenuItem>
								<DropdownMenuItem onClick={handleShare}>
									<Share2 className="h-4 w-4 mr-2" />
									{t("actions.share")}
								</DropdownMenuItem>
							</DropdownMenuContent>
						</DropdownMenu>
					</div>
				</div>
			</div>
		</div>
	);
}
