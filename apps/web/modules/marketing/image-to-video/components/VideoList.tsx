"use client";

import { useTranslations } from "next-intl";
import { VideoItem } from "./VideoItem";
import { Loader2 } from "lucide-react";
import { ScrollArea } from "@ui/components/scroll-area";
import type { VideoType } from "@marketing/image-to-video/types";

interface VideoListProps {
  videos: VideoType[];
  isLoading: boolean;
  filter: "all" | "favorites";
}

export function VideoList({ videos, isLoading, filter }: VideoListProps) {
  const t = useTranslations("imageToVideo.videoList");
  
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <p className="mt-4 text-sm text-muted-foreground">{t("loading")}</p>
      </div>
    );
  }
  
  if (videos.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <p className="text-lg font-medium">{t("empty.title")}</p>
        <p className="mt-1 text-sm text-muted-foreground">
          {filter === "all" 
            ? t("empty.descriptionAll") 
            : t("empty.descriptionFavorites")}
        </p>
      </div>
    );
  }
  
  return (
    <ScrollArea className="h-[600px] pr-4">
      <div className="space-y-4">
        {videos.map((video) => (
          <VideoItem key={video.id} video={video} />
        ))}
      </div>
    </ScrollArea>
  );
}
