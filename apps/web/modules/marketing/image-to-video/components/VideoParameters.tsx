"use client";

import { useTranslations } from "next-intl";
import { cn } from "@ui/lib";
import { <PERSON>lider } from "@ui/components/slider";
import { RadioGroup, RadioGroupItem } from "@ui/components/radio-group";
import { Label } from "@ui/components/label";
import { Switch } from "@ui/components/switch";
import { Badge } from "@ui/components/badge";
import { Card } from "@ui/components/card";

interface VideoParametersProps {
  mode: "standard" | "professional";
  setMode: (mode: "standard" | "professional") => void;
  promptStrength: number;
  setPromptStrength: (strength: number) => void;
  videoLength: number;
  setVideoLength: (length: number) => void;
  outputCount: number;
  setOutputCount: (count: number) => void;
  isPublic: boolean;
  setIsPublic: (isPublic: boolean) => void;
  hasCopyProtection: boolean;
  setHasCopyProtection: (hasCopyProtection: boolean) => void;
}

export function VideoParameters({
  mode,
  setMode,
  promptStrength,
  setPromptStrength,
  videoLength,
  setVideoLength,
  outputCount,
  setOutputCount,
  isPublic,
  setIsPublic,
  hasCopyProtection,
  setHasCopyProtection,
}: VideoParametersProps) {
  const t = useTranslations("imageToVideo.videoParameters");
  
  return (
    <div className="space-y-6">
      {/* Mode Selection */}
      <div className="space-y-2">
        <h3 className="text-lg font-medium">{t("mode.title")}</h3>
        <RadioGroup 
          value={mode} 
          onValueChange={(value) => setMode(value as "standard" | "professional")}
          className="grid grid-cols-1 md:grid-cols-2 gap-2"
        >
          <Label
            htmlFor="standard-mode"
            className={cn(
              "flex flex-col items-start cursor-pointer rounded-lg border p-4 hover:bg-accent transition-colors",
              mode === "standard" && "border-primary bg-accent"
            )}
          >
            <div className="flex items-center justify-between w-full">
              <div className="font-medium">{t("mode.standard.title")}</div>
              <RadioGroupItem value="standard" id="standard-mode" className="sr-only" />
            </div>
            <p className="text-sm text-muted-foreground mt-1">{t("mode.standard.description")}</p>
          </Label>
          
          <Label
            htmlFor="professional-mode"
            className={cn(
              "flex flex-col items-start cursor-pointer rounded-lg border p-4 hover:bg-accent transition-colors",
              mode === "professional" && "border-primary bg-accent"
            )}
          >
            <div className="flex items-center justify-between w-full">
              <div className="font-medium flex items-center">
                {t("mode.professional.title")}
                <Badge className="ml-2 bg-red-500 text-white">
                  PRO
                </Badge>
              </div>
              <RadioGroupItem value="professional" id="professional-mode" className="sr-only" />
            </div>
            <p className="text-sm text-muted-foreground mt-1">{t("mode.professional.description")}</p>
          </Label>
        </RadioGroup>
      </div>
      
      {/* Prompt Strength */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">{t("promptStrength.title")}</h3>
          <span className="text-sm font-medium">{promptStrength.toFixed(1)}</span>
        </div>
        <Slider
          value={[promptStrength]}
          min={0}
          max={10}
          step={0.1}
          onValueChange={(values) => setPromptStrength(values[0])}
          className="py-2"
        />
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>{t("promptStrength.more")}</span>
          <span>{t("promptStrength.follow")}</span>
        </div>
      </div>
      
      {/* Video Length */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">{t("videoLength.title")}</h3>
          <Badge className="border border-gray-200 dark:border-gray-800">
            {videoLength}s
          </Badge>
        </div>
        <div className="flex gap-2">
          {[5, 10, 15].map((length) => (
            <Card 
              key={length}
              className={cn(
                "flex-1 p-2 text-center cursor-pointer hover:bg-accent transition-colors",
                videoLength === length && "border-primary bg-accent"
              )}
              onClick={() => setVideoLength(length)}
            >
              {length}s
            </Card>
          ))}
        </div>
      </div>
      
      {/* Output Video Number */}
      <div className="space-y-2">
        <h3 className="text-lg font-medium">{t("outputCount.title")}</h3>
        <div className="grid grid-cols-4 gap-2">
          {[1, 2, 3, 4].map((count) => (
            <Card 
              key={count}
              className={cn(
                "p-2 text-center cursor-pointer hover:bg-accent transition-colors",
                outputCount === count && "border-primary bg-accent"
              )}
              onClick={() => setOutputCount(count)}
            >
              {count}
            </Card>
          ))}
        </div>
      </div>
      
      {/* Public Visibility */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">{t("public.title")}</h3>
          <p className="text-sm text-muted-foreground">{t("public.description")}</p>
        </div>
        <Switch
          checked={isPublic}
          onCheckedChange={setIsPublic}
        />
      </div>
      
      {/* Copy Protection */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">{t("copyProtection.title")}</h3>
          <p className="text-sm text-muted-foreground">{t("copyProtection.description")}</p>
        </div>
        <Switch
          checked={hasCopyProtection}
          onCheckedChange={setHasCopyProtection}
        />
      </div>
    </div>
  );
}
