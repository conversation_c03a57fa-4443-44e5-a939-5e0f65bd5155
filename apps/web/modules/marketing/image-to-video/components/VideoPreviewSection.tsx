// apps/web/modules/tooling/image-to-video/components/VideoPreviewSection.tsx
"use client";

import { useState } from "react";
import { SampleVideoCard } from "./SampleVideoCard";
import { MediaPreviewCard } from "./MediaPreviewCard";
import { useSession } from "@saas/auth/hooks/use-session";
import { UserRoundPlus } from "lucide-react";
import Link from "next/link";

interface VideoPreviewSectionProps {
	tasks: any[];
}

export function VideoPreviewSection({ tasks }: VideoPreviewSectionProps) {
	const [isLoadingMore, setIsLoadingMore] = useState(false);
	const { user } = useSession();

	const handleLoadMore = () => {
		setIsLoadingMore(true);
		// 模拟加载更多数据
		setTimeout(() => {
			setIsLoadingMore(false);
		}, 1500);
	};

	return (
		<>
			{/* Header Information Area */}
			<div className="pt-6 mb-6">
				<div className="flex items-center justify-between mb-3">
					<h2 className="text-2xl font-bold text-gray-900">
						Image To Video AI Generator Output Preview
					</h2>
					{user && (
						<Link
							href="/my-generations"
							className="flex items-center justify-center w-10 h-10 text-gray-700 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
							title="My Generations"
						>
							<UserRoundPlus className="h-5 w-5" />
						</Link>
					)}
				</div>
				<p className="text-gray-600 text-base">
					AI Video generation usually takes 1 to 5 minutes, depending on the selected model and parameters.
				</p>
			</div>

			{/* Records Container - 显示实际任务 */}
			<div
				className="relative mb-10 flex min-h-[280px] max-w-full flex-1 flex-col gap-4 md:gap-5"
				id="records-container"
			>
				{/* 显示用户的任务 */}
				{tasks.length > 0
					? tasks.map((task) => {
						// 如果任务已经有generation数据，使用generations数组
						const generations = task.generations;
						
						if (generations && generations.length > 0) {
							// 计算MediaPreviewCard的容器ID（使用第一个generation的ID）
							const firstGeneration = generations[0];
							const containerIdBase = firstGeneration?.tempId || firstGeneration?.generationId || firstGeneration?.id || task.id;
							const containerId = `item-${containerIdBase}`;
							
							return (
								<MediaPreviewCard
									key={task.id}
									containerId={containerId}
									generations={generations.map((gen: any) => ({
										id: gen.id,
										status: gen.status,
										inputImage: gen.inputImage || task.inputImage,
										outputVideo: gen.videoUrl || gen.mediaUrl,
										videoUrl: gen.videoUrl || gen.mediaUrl,
										error: gen.error || task.error,
										createdAt: gen.createdAt || task.timestamp,
										estimatedDuration: gen.estimatedDuration || task.estimatedTime || 60,
										prompt: gen.prompt || task.prompt
									}))}
									appName="Pollo.ai"
									appType="Image to Video"
									modelName={task.model || "Unknown Model"}
									prompt={task.prompt} // 作业级别提示词
									onRefresh={() => console.log("Refresh task", task.id)}
									onEdit={() => console.log("Edit task", task.id)}
								/>
							);
						}
						
						// 如果没有generation数据，使用兼容模式（单个generation）
						const containerIdBase = task.tempId || task.id;
						const containerId = `item-${containerIdBase}`;
						
						return (
							<MediaPreviewCard
								key={task.id}
								containerId={containerId}
								generation={{
									id: task.id,
									status: task.status,
									inputImage: task.inputImage,
									createdAt: task.timestamp || new Date().toISOString(),
									estimatedDuration: task.estimatedTime || 60,
									error: task.error,
									prompt: task.prompt
								}}
								appName="Pollo.ai"
								appType="Image to Video"
								modelName={task.model || "Unknown Model"}
								onRefresh={() => console.log("Refresh task", task.id)}
								onEdit={() => console.log("Edit task", task.id)}
							/>
						);
					})
					: // 显示样本视频
					<SampleVideoCard videoSrc="/videos/banner-video2.mp4" />}
			</div>

			{/* Load More Button */}
			<div className="flex items-center justify-center pb-4 md:pb-1">
				<button
					type="button"
					onClick={handleLoadMore}
					disabled={isLoadingMore}
					className="coco-btn coco-btn-default coco-btn-color-default coco-btn-variant-outlined md:hidden inline-flex items-center justify-center gap-2 rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
				>
					<span
						className="coco-btn-icon coco-btn-loading-icon"
						style={{
							width: isLoadingMore ? "1em" : "0px",
							opacity: isLoadingMore ? 1 : 0,
							transform: isLoadingMore ? "scale(1)" : "scale(0)",
							transition: "all 0.2s ease",
						}}
					>
						<span
							role="img"
							aria-label="loading"
							className="coco-icon coco-icon-loading coco-icon-spin"
						>
							<svg
								viewBox="0 0 1024 1024"
								focusable="false"
								data-icon="loading"
								width="1em"
								height="1em"
								fill="currentColor"
								aria-hidden="true"
								className={isLoadingMore ? "animate-spin" : ""}
							>
								<path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z" />
							</svg>
						</span>
					</span>
					<span>Load More</span>
				</button>
			</div>
		</>
	);
}