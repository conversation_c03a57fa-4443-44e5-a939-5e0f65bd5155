// apps/web/modules/tooling/image-to-video/components/preview/VideoTaskItem.tsx
"use client";

import { Button } from "@ui/components/button";
import { Progress } from "@ui/components/progress";
import { Edit, Play, RefreshCw } from "lucide-react";
import { useTranslations } from "next-intl";

interface VideoTaskItemProps {
	task: any;
}

export function VideoTaskItem({ task }: VideoTaskItemProps) {
	const t = useTranslations("imageToVideo");

	const renderTaskContent = () => {
		switch (task.status) {
			case "waiting":
				return (
					<div className="flex flex-col justify-center items-center h-full p-6">
						<div className="mb-3 text-white text-center">
							<p>{t("preview.queue")}</p>
							<p className="mt-2 font-semibold">
								等待处理中...
							</p>
							{task.estimatedTime && (
								<p className="text-sm text-gray-400 mt-1">
									预计等待时间: {Math.round(task.estimatedTime / 60)} 分钟
								</p>
							)}
						</div>
						<div className="text-xs text-gray-400">
							积分: {task.credit} | 输出数量: {task.numOutputs}
						</div>
					</div>
				);

			case "processing":
				return (
					<div className="flex flex-col justify-center items-center h-full p-6">
						<div className="mb-4 text-white text-center">
							<p className="mb-2">
								{t("preview.generating", { minutes: 4 })}
							</p>
							<p className="text-sm text-gray-400">
								{task.progress}%
							</p>
						</div>
						<Progress value={task.progress} className="w-full" />
					</div>
				);

			case "completed":
				// 完成后显示生成的视频
				const completedGenerations = task.generations?.filter((gen: any) => gen.status === "succeed") || [];
				
				if (completedGenerations.length > 0) {
					return (
						<div className="space-y-2">
							{completedGenerations.map((generation: any, index: number) => (
								<div key={generation.id} className="relative">
									<video
										className="w-full h-auto aspect-video bg-black"
										controls
										poster={generation.thumbnail || generation.cover}
									>
										<source
											src={generation.videoUrl || generation.mediaUrl}
											type="video/mp4"
										/>
									</video>
									<div className="absolute top-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
										视频 {index + 1}
									</div>
									<div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/70 to-transparent">
										<div className="flex items-center justify-between">
											<button className="w-8 h-8 flex items-center justify-center bg-white/20 rounded-full hover:bg-white/30">
												<Play size={16} />
											</button>
											<div className="flex gap-2">
												<button className="w-8 h-8 flex items-center justify-center bg-white/20 rounded-full hover:bg-white/30">
													<RefreshCw size={16} />
												</button>
												<button className="w-8 h-8 flex items-center justify-center bg-white/20 rounded-full hover:bg-white/30">
													<Edit size={16} />
												</button>
											</div>
										</div>
									</div>
								</div>
							))}
						</div>
					);
				} else {
					return (
						<div className="flex flex-col justify-center items-center h-full p-6">
							<div className="text-white text-center">
								<p>视频生成完成，但没有可用的结果</p>
							</div>
						</div>
					);
				}

			case "failed":
				return (
					<div className="flex flex-col justify-center items-center h-full p-6">
						<div className="text-white text-center">
							<p className="text-red-400">生成失败</p>
							<p className="text-sm text-gray-400 mt-2">
								请检查参数并重试
							</p>
						</div>
					</div>
				);

			default:
				return null;
		}
	};

	return (
		<div className="border-b border-gray-700">
			<div className="p-3">
				<div className="flex items-center gap-2 mb-2">
					<img
						src="/images/logo-small.png"
						alt="Pollo.ai"
						className="w-5 h-5"
					/>
					<span className="text-gray-300 text-sm">
						{t("preview.imageToVideo")} | {task.model}
					</span>
					<span className="text-gray-400 text-xs ml-auto">
						{new Date(task.timestamp).toLocaleTimeString()}
					</span>
				</div>

				<p className="text-white text-sm mb-3">{task.prompt}</p>
			</div>

			<div className="h-[240px] bg-gray-800">{renderTaskContent()}</div>

			<div className="p-2 flex">
				<button className="p-1.5 hover:bg-gray-700 rounded">
					<Edit size={18} />
				</button>
				<button className="p-1.5 hover:bg-gray-700 rounded ml-1">
					<RefreshCw size={18} />
				</button>
			</div>
		</div>
	);
}
