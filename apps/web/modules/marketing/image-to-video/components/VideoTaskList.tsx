// apps/web/modules/tooling/image-to-video/components/VideoTaskList.tsx
"use client";

import { useTranslations } from "next-intl";
import { VideoTaskItem } from "./VideoTaskItem";

interface VideoTaskListProps {
	tasks: any[];
}

export function VideoTaskList({ tasks }: VideoTaskListProps) {
	const t = useTranslations("imageToVideo");

	return (
		<div className="max-h-[600px] overflow-y-auto">
			{tasks.map((task) => (
				<VideoTaskItem key={task.id} task={task} />
			))}
		</div>
	);
}
