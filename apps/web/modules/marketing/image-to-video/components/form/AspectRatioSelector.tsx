"use client";

import { cn } from "@ui/lib";
import { useTranslations } from "next-intl";
import { useModelOptions } from "../../providers/ModelOptionsProvider";

interface AspectRatioSelectorProps {
	selectedRatio: string;
	onChange: (value: string) => void;
	className?: string;
}

export function AspectRatioSelector({
	selectedRatio,
	onChange,
	className,
}: AspectRatioSelectorProps) {
	const t = useTranslations("imageToVideo.form");
	const { selectedModel } = useModelOptions();

	if (
		!selectedModel?.aspectRatioOptions ||
		selectedModel.aspectRatioOptions.length === 0
	) {
		return null;
	}

	// 解析宽高比字符串为数值
	const parseAspectRatio = (ratio: string) => {
		const [width, height] = ratio.split(":").map(Number);
		return { width, height };
	};

	// 为不同的宽高比生成视觉样式
	const getVisualStyle = (ratio: string) => {
		const { width, height } = parseAspectRatio(ratio);
		const aspectRatio = width / height;

		// 根据宽高比调整显示大小
		if (aspectRatio > 1) {
			// 横向的比例，固定宽度
			return {
				aspectRatio: `${width} / ${height}`,
				width: "32px",
			};
		}
		// 纵向或正方形的比例，固定高度
		return {
			aspectRatio: `${width} / ${height}`,
			height: "32px",
		};
	};

	return (
		<div className={cn("space-y-3", className)}>
			{/* 标签部分 */}
			<div className="flex flex-col">
				<div className="flex items-center">
					<div className="inline-flex items-center text-gray-700 text-sm font-semibold leading-5">
						{t("aspectRatio.label")}
					</div>
				</div>
				<span className="text-gray-500 text-xs">
					{t("aspectRatio.description")}
				</span>
			</div>

			{/* 宽高比选择器 */}
			<div className="relative flex flex-col gap-1">
				<div className="flex gap-1 overflow-y-auto sm:gap-2 no-scrollbar">
					{selectedModel.aspectRatioOptions.map((ratio) => {
						const isSelected = selectedRatio === ratio;
						const visualStyle = getVisualStyle(ratio);

						return (
							<button
								key={ratio}
								type="button"
								onClick={() => onChange(ratio)}
								className={cn(
									"group flex flex-1 cursor-pointer flex-col items-center justify-center gap-2 rounded-md pb-2 pt-4 text-sm duration-300 border border-transparent",
									"hover:bg-gray-600",
									isSelected
										? "bg-gray-600"
										: "bg-gray-200",
								)}
							>
								{/* 可视化矩形 */}
								<div
									className="flex items-center justify-center"
									style={{ height: "32px" }}
								>
									<span
										className={cn(
											"flex rounded-[2px] border border-transparent duration-300",
											isSelected
												? "bg-blue-400"
												: "bg-gray-400 group-hover:bg-blue-400",
										)}
										style={visualStyle}
									/>
								</div>

								{/* 比例文字 */}
								<span
									className={cn(
										"font-semibold duration-300",
										isSelected
											? "text-blue-400"
											: "text-muted-foreground group-hover:text-blue-400",
									)}
								>
									{ratio}
								</span>
							</button>
						);
					})}
				</div>
			</div>
		</div>
	);
}
