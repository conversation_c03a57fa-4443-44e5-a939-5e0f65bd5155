"use client";

import { Switch } from "@ui/components/switch";
import { useTranslations } from "next-intl";

interface CopyProtectionOptionProps {
	isProtected: boolean;
	onChange: (value: boolean) => void;
}

export function CopyProtectionOption({
	isProtected,
	onChange,
}: CopyProtectionOptionProps) {
	const t = useTranslations("imageToVideo.form");

	return (
		<div className="flex items-center justify-between p-1">
			<div>
				<label htmlFor="copy-protection" className="block text-sm font-medium text-gray-700">
					{t("copyProtection")}
				</label>
				<p className="text-xs text-gray-500 mt-1">
					{t("copyProtectionDescription")}
				</p>
			</div>
			<Switch
				id="copy-protection"
				checked={isProtected}
				onCheckedChange={onChange}
				aria-label={t("copyProtection")}
			/>
		</div>
	);
}
