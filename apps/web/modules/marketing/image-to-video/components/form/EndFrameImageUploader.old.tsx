"use client";

import { Button } from "@ui/components/button";
import { Card } from "@ui/components/card";
import { Upload, X, Edit } from "lucide-react";
import Image from "next/image";
import { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { useTranslations } from "next-intl";

interface EndFrameImageUploaderProps {
	value: File | string | null;
	onChange: (file: File | string | null) => void;
	maxSize?: number;
	acceptedFormats?: string[];
	className?: string;
}

export function EndFrameImageUploader({
	value,
	onChange,
	maxSize = 10,
	acceptedFormats = ["image/jpeg", "image/png", "image/webp"],
	className = "",
}: EndFrameImageUploaderProps) {
	const t = useTranslations("imageToVideo.form");
	const [preview, setPreview] = useState<string | null>(null);
	const [error, setError] = useState<string | null>(null);

	const validateFile = (file: File): string | null => {
		if (!acceptedFormats.includes(file.type)) {
			return `Unsupported format. Please use: ${acceptedFormats
				.map((format) => format.split("/")[1].toUpperCase())
				.join(", ")}`;
		}

		if (file.size > maxSize * 1024 * 1024) {
			return `File too large. Maximum size: ${maxSize}MB`;
		}

		return null;
	};

	const createPreview = (file: File) => {
		const reader = new FileReader();
		reader.onload = (e) => {
			setPreview(e.target?.result as string);
		};
		reader.readAsDataURL(file);
	};

	const onDrop = useCallback(
		(acceptedFiles: File[]) => {
			if (acceptedFiles.length === 0) {
				setError("No valid files selected");
				return;
			}

			const file = acceptedFiles[0];
			const validationError = validateFile(file);

			if (validationError) {
				setError(validationError);
				return;
			}

			setError(null);
			createPreview(file);
			onChange(file);
		},
		[onChange, maxSize, acceptedFormats],
	);

	const { getRootProps, getInputProps, isDragActive } = useDropzone({
		onDrop,
		accept: acceptedFormats.reduce(
			(acc, format) => ({ ...acc, [format]: [] }),
			{},
		),
		multiple: false,
		maxSize: maxSize * 1024 * 1024,
	});

	const clearImage = () => {
		setPreview(null);
		setError(null);
		onChange(null);
	};

	// 如果有值，显示预览界面
	if (value || preview) {
		const imageSrc = preview || (typeof value === "string" ? value : "");

		return (
			<Card className={`relative overflow-hidden ${className}`}>
				<div className="aspect-[16/9] relative bg-gray-100">
					{imageSrc && (
						<Image
							src={imageSrc}
							alt="End frame preview"
							fill
							className="object-cover"
						/>
					)}
					<div className="absolute top-2 right-2 flex gap-2">
						<Button
							type="button"
							variant="gray"
							size="sm"
							onClick={clearImage}
							className="bg-black/50 hover:bg-black/70 text-white border-0"
						>
							<X className="h-4 w-4" />
						</Button>
					</div>
				</div>
				<div className="p-3">
					<p className="text-sm text-gray-600">End frame image uploaded</p>
					<Button
						type="button"
						variant="outline"
						size="sm"
						onClick={clearImage}
						className="mt-2"
					>
						Remove
					</Button>
				</div>
			</Card>
		);
	}

	// 显示上传界面
	return (
		<div className={className}>
			<Card
				{...getRootProps()}
				className={`cursor-pointer transition-colors border-2 border-dashed ${
					isDragActive
						? "border-blue-400 bg-blue-50"
						: "border-gray-300 hover:border-gray-400"
				}`}
			>
				<input {...getInputProps()} />
				<div className="p-8 text-center">
					<Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
					<div className="space-y-2">
						<p className="text-sm font-medium text-gray-700">
							{isDragActive
								? "Drop the end frame image here"
								: "Add End Frame Image"}
						</p>
						<p className="text-xs text-gray-500">
							Drag & drop or click to browse
						</p>
						<p className="text-xs text-gray-400">
							Supported formats: {acceptedFormats
								.map((format) => format.split("/")[1].toUpperCase())
								.join(", ")}{" "}
							(Max {maxSize}MB), with a minimum width/height of 300px.
						</p>
					</div>
				</div>
			</Card>
			{error && (
				<p className="text-sm text-red-500 mt-2">{error}</p>
			)}
		</div>
	);
}