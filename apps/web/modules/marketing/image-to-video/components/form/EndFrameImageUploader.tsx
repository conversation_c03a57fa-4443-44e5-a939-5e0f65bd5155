"use client";

import { But<PERSON> } from "@ui/components/button";
import { Card } from "@ui/components/card";
import { Upload, X, Edit } from "lucide-react";
import Image from "next/image";
import { useTranslations } from "next-intl";
import { useImageUploader } from "../../hooks/useImageUploader";
import { ImageEditDialog } from "../image-editor/ImageEditDialog";

interface EndFrameImageUploaderProps {
	value?: File | string | null;
	onChange: (file: File | Blob | null, uploadedUrl?: string) => void;
	maxSize?: number;
	acceptedFormats?: string[];
	className?: string;
	aspectRatioOptions?: string[];
}

export function EndFrameImageUploader({
	onChange,
	maxSize = 10,
	acceptedFormats = ["image/jpeg", "image/png", "image/webp"],
	className = "",
	aspectRatioOptions,
}: EndFrameImageUploaderProps) {
	const t = useTranslations("imageUploader");
	
	// 使用共享Hook获取所有编辑功能
	const {
		originalFile,
		preview,
		editDialogOpen,
		error,
		handleEdit,
		handleEditSave,
		handleRemove,
		setEditDialogOpen,
		getRootProps,
		getInputProps,
		isDragActive,
		currentFile,
	} = useImageUploader({
		maxSize,
		acceptedFormats,
		onChange,
	});

	// 如果有预览，显示预览界面（与主图组件样式一致）
	if (preview && currentFile) {
		return (
			<div className={`w-full ${className}`}>
				<Card className="relative overflow-hidden border border-gray-200 shadow-sm group">
					<div className="aspect-video bg-gray-50 relative">
						<Image
							src={preview}
							alt={t("endFrame.imageAlt")}
							fill
							className="object-contain"
							sizes="(max-width: 768px) 100vw, 50vw"
						/>
						{/* 悬停覆盖层 - 与主图组件完全相同的样式 */}
						<div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center gap-3">
							<Button
								type="button"
								variant="outline"
								size="sm"
								onClick={handleEdit}
								className="h-9 px-4 text-sm bg-white/90 hover:bg-white border-white/20 backdrop-blur-sm"
							>
								<Edit className="h-4 w-4 mr-2" />
								{t("shared.edit")}
							</Button>
							<Button
								type="button"
								variant="outline"
								size="sm"
								onClick={handleRemove}
								className="h-9 px-4 text-sm bg-white/90 hover:bg-white border-white/20 backdrop-blur-sm text-red-600 hover:text-red-700"
							>
								<X className="h-4 w-4 mr-2" />
								{t("shared.remove")}
							</Button>
						</div>
					</div>
				</Card>

				{/* 编辑对话框 */}
				<ImageEditDialog
					image={originalFile}
					open={editDialogOpen}
					onOpenChange={setEditDialogOpen}
					onSave={handleEditSave}
					aspectRatioOptions={aspectRatioOptions}
				/>

				{error && (
					<p className="text-sm text-red-500 mt-2">{error}</p>
				)}
			</div>
		);
	}

	// 显示上传界面
	return (
		<div className={`w-full ${className}`}>
			<Card
				{...getRootProps()}
				className={`cursor-pointer transition-colors border-2 border-dashed ${
					isDragActive
						? "border-blue-400 bg-blue-50"
						: "border-gray-300 hover:border-gray-400"
				}`}
			>
				<input {...getInputProps()} />
				<div className="p-8 text-center">
					<div className="mb-3 p-3 bg-gray-100 rounded-full mx-auto w-fit">
						{isDragActive ? (
							<Upload size={24} className="text-blue-500" />
						) : (
							<Upload size={24} className="text-gray-500" />
						)}
					</div>
					<div className="space-y-2">
						<p className="text-sm font-medium text-gray-700">
							{isDragActive
								? t("endFrame.dragActive")
								: t("endFrame.dragInactive")}
						</p>
						<p className="text-xs text-gray-500">
							{t("endFrame.dragHelp")}
						</p>
						<p className="text-xs text-gray-400">
							{t("endFrame.formatInfo", { 
								maxSize,
								formats: acceptedFormats
									.map((format) => format.split("/")[1].toUpperCase())
									.join(", ")
							})}
						</p>
					</div>
				</div>
			</Card>

			{/* 编辑对话框 */}
			<ImageEditDialog
				image={originalFile}
				open={editDialogOpen}
				onOpenChange={setEditDialogOpen}
				onSave={handleEditSave}
				aspectRatioOptions={aspectRatioOptions}
			/>

			{error && (
				<p className="text-sm text-red-500 mt-2">{error}</p>
			)}
		</div>
	);
}