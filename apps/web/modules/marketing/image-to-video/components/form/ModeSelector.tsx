"use client";

import { RadioGroup, RadioGroupItem } from "@ui/components/radio-group";
import { useTranslations } from "next-intl";
import { useModelOptions } from "../../providers/ModelOptionsProvider";

interface ModeSelectorProps {
	selectedMode: string; // 使用code识别选中的模式
	onChange: (value: string) => void; // 保持向上传递code，方便表单状态管理
}

export function ModeSelector({ selectedMode, onChange }: ModeSelectorProps) {
	const t = useTranslations("imageToVideo.form");
	const { selectedModel, setSelectedMode } = useModelOptions();

	if (!selectedModel.modes || selectedModel.modes.length === 0) {
		return null;
	}

	return (
		<div className="space-y-3">
			<div
				id="mode-label"
				className="block text-sm font-medium text-gray-700"
			>
				{t("mode.label")}
			</div>
			<RadioGroup
				aria-labelledby="mode-label"
				value={selectedMode || ""}
				onValueChange={(value) => {
					// 确保modes存在并查找匹配code的模式对象
					const modeObject = selectedModel.modes?.find(
						(mode) => mode.code === value,
					);
					if (modeObject) {
						setSelectedMode(modeObject);
						onChange(value);
					}
				}}
				className="flex flex-col space-y-2"
			>
				{selectedModel.modes.map((mode) => (
					<label
						htmlFor={`mode-${mode.code}`}
						key={mode.code}
						className={`p-3 rounded-lg border transition-colors cursor-pointer ${
							selectedMode === mode.code
								? "border-gray-300 bg-red-50/10"
								: "border-gray-300 hover:border-gray-400"
						}`}
					>
						<div className="flex justify-between">
							<div className="flex">
								<div className="mr-3 h-20 w-24 overflow-hidden rounded-md self-center">
									<img
										src={
											mode.code === "std"
												? "/images/standard.jpg"
												: "/images/professional.jpg"
										}
										alt={`${mode.name} illustration`}
										className="h-full w-full object-cover"
									/>
								</div>
								<div className="flex flex-col justify-center">
									<div className="flex items-center">
										<span className="font-medium text-gray-800">
											{mode.name}
										</span>
										{mode.code === "pro" && (
											<span className="ml-2 text-xs bg-red-500 text-white px-2 py-0.5 rounded-full font-medium">
												PRO
											</span>
										)}
									</div>
									<p className="text-sm text-gray-500">
										{(() => {
											switch (mode.code) {
												case 'std':
													return t('mode.stdDescription');
												case 'pro':
													return t('mode.proDescription');
												case 'normal':
													return t('mode.normalDescription');
												case 'smooth':
													return t('mode.smoothDescription');
												default:
													return mode.description || '';
											}
										})()}
									</p>
								</div>
							</div>
							<label
								htmlFor={`mode-${mode.code}`}
								className="cursor-pointer flex items-center"
							>
								<div
									className={`relative flex h-5 w-5 items-center justify-center rounded-full ${selectedMode === mode.code ? "bg-red-500" : "border-[3px] border-gray-400"}`}
								>
									<RadioGroupItem
										value={mode.code}
										id={`mode-${mode.code}`}
										className="sr-only"
									/>
									{selectedMode === mode.code && (
										<div className="h-2 w-2 rounded-full bg-white" />
									)}
								</div>
							</label>
						</div>
					</label>
				))}
			</RadioGroup>
		</div>
	);
}
