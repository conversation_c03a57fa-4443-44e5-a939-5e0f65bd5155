// apps/web/modules/tooling/image-to-video/components/form/ModelSelector.tsx
"use client";

import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandItem,
	CommandList,
} from "@ui/components/command";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { Skeleton } from "@ui/components/skeleton";
import { cn } from "@ui/lib";
import { ChevronDown, Search } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useModelOptions } from "../../providers/ModelOptionsProvider";
// VideoModel类型在useModelOptions hook中被隐式使用

// 模型图标组件
function ModelIcon({
	modelId,
	className,
}: { modelId: string; className?: string }) {
	// 根据模型ID返回对应的颜色和字母
	const getIconConfig = (id: string): { color: string; letter: string } => {
		const config: Record<string, { color: string; letter: string }> = {
			kling1_6: { color: "bg-black", letter: "K" },
			kling2_0: { color: "bg-black", letter: "K" },
			fluxfly1_5: { color: "bg-yellow-500", letter: "P" },
			hailuo: { color: "bg-red-500", letter: "H" },
			runway3: { color: "bg-green-500", letter: "R" },
			runway4: { color: "bg-green-500", letter: "R" },
			"pixverse-v4-5": { color: "bg-purple-500", letter: "P" },
			"pixverse-v4": { color: "bg-purple-500", letter: "P" },
			"pixverse-v3-5": { color: "bg-purple-500", letter: "P" },
		};

		return config[id] || { color: "bg-gray-500", letter: "?" };
	};

	const { color, letter } = getIconConfig(modelId);

	return (
		<span
			className={cn(
				"text-[10px] font-bold text-white h-4 w-4 flex items-center justify-center rounded-full",
				color,
				className,
			)}
		>
			{letter}
		</span>
	);
}

interface ModelSelectorProps {
	className?: string;
}

export function ModelSelector({ className }: ModelSelectorProps) {
	const t = useTranslations("imageToVideo");
	const [open, setOpen] = useState(false);
	const [searchValue, setSearchValue] = useState("");
	const inputRef = useRef<HTMLInputElement>(null);
	const commandRef = useRef<any>(null);
	const commandListRef = useRef<HTMLDivElement>(null);
	const selectedItemRef = useRef<HTMLDivElement>(null);

	// 从上下文获取模型数据
	const { models, selectedModel, setSelectedModel } = useModelOptions();
	const isLoading = models.length === 0;

	// 过滤模型列表
	const filteredModels =
		searchValue.trim() !== ""
			? models.filter(
					(model) =>
						model.name
							.toLowerCase()
							.includes(searchValue.toLowerCase()) ||
						model.description
							?.toLowerCase()
							.includes(searchValue.toLowerCase()),
				)
			: models;

	// 滚动到选中模型的函数
	const scrollToSelectedModel = useCallback(() => {
		if (open && selectedModel) {
			// 增加延迟并添加重试机制，确保DOM完全渲染
			const attemptScroll = (retries = 3) => {
				setTimeout(() => {
					if (selectedItemRef.current && commandListRef.current) {
						// 检查选中的元素是否在过滤后的列表中可见
						const isSelectedModelVisible = filteredModels.some(
							(model) => model.code === selectedModel.code,
						);

						if (isSelectedModelVisible && selectedItemRef.current) {
							selectedItemRef.current.scrollIntoView({
								behavior: "instant",
								block: "center",
								inline: "nearest",
							});
						}
					} else if (retries > 0) {
						attemptScroll(retries - 1);
					}
				}, 100);
			};

			attemptScroll();
		}
	}, [open, selectedModel, filteredModels]);

	// 当 Popover 打开时滚动到选中项
	useEffect(() => {
		if (open && searchValue === "") {
			// 只在没有搜索时自动滚动到选中项
			scrollToSelectedModel();
		}
	}, [open, searchValue, scrollToSelectedModel]);

	// 计算响应式高度
	const getDropdownHeight = () => {
		// 移动端使用较小的高度，桌面端使用较大的高度
		if (typeof window !== "undefined") {
			const isMobile = window.innerWidth < 768;
			const viewportHeight = window.innerHeight;

			if (isMobile) {
				// 移动端：最大高度为视口高度的 40%，但不少于 300px，不超过 400px
				return Math.min(Math.max(viewportHeight * 0.4, 300), 400);
			}
			// 桌面端：最大高度为视口高度的 45%，但不少于 380px，不超过 600px
			return Math.min(Math.max(viewportHeight * 0.45, 380), 600);
		}
		return 380; // 默认高度
	};

	// 处理搜索输入变化
	function handleSearchChange(e: React.ChangeEvent<HTMLInputElement>) {
		const value = e.target.value;
		setSearchValue(value);

		// 当用户输入字符时，自动打开下拉菜单
		if (value.trim() !== "" && !open) {
			setOpen(true);
		}

		// 手动调用Command组件的过滤函数
		if (commandRef.current) {
			commandRef.current.filter(value);
		}
	}

	// 处理模型选择
	function handleSelect(value: string) {
		const model = models.find((m) => m.code === value);
		if (model) {
			setSelectedModel(model);
			setSearchValue(""); // 清空搜索值
			setOpen(false); // 关闭下拉菜单
		}
	}

	// 处理键盘事件
	function handleKeyDown(e: React.KeyboardEvent<HTMLInputElement>) {
		// 当按下回车键时
		if (e.key === "Enter") {
			e.preventDefault();

			// 如果下拉框弹出且有筛选结果
			if (open && filteredModels.length > 0) {
				// 获取当前选中模型（默认第一个）
				const selectedValue = filteredModels[0].code;
				handleSelect(selectedValue);
			}
		} else if (e.key === "Escape") {
			// 当按下退出键时
			setOpen(false);
		} else if (e.key === "ArrowDown" || e.key === "ArrowUp") {
			// 箭头键导航
			if (!open) {
				setOpen(true);
				e.preventDefault();
			} else {
				// 如果下拉框已经打开，转移焦点到Command组件让它处理箭头键
				e.preventDefault();
				const commandElement = commandRef.current;
				if (commandElement) {
					commandElement.focus();
					// 再次触发箭头键事件
					setTimeout(() => {
						const keyEvent = new KeyboardEvent('keydown', {
							key: e.key,
							bubbles: true,
							cancelable: true
						});
						commandElement.dispatchEvent(keyEvent);
					}, 10);
				}
			}
		}
	}

	// 在Popover打开时管理焦点
	useEffect(() => {
		if (open) {
			// 打开时聚焦输入框，但允许Command组件接管箭头键导航
			setTimeout(() => {
				if (inputRef.current) {
					inputRef.current.focus();
				}
			}, 50);
		}
	}, [open]);

	// 当Popover关闭时重置搜索值
	useEffect(() => {
		if (!open) {
			setSearchValue("");
		}
	}, [open]);

	// 简化版本不需要复杂的宽度计算
	// 使用 w-full 和 CSS 变量来处理宽度

	// 简单骨架屏加载状态
	if (!models.length) {
		return <Skeleton className="h-10 w-full" />;
	}

	return (
		<Popover open={open} onOpenChange={setOpen}>
			<PopoverTrigger asChild>
				<div className={cn("relative group w-full", className)}>
					<div
						className="flex items-center justify-between w-full h-10 px-3 py-2 rounded-md border border-input bg-gray-50 shadow-sm cursor-pointer relative"
						style={{
							backgroundColor: "#f9fafb",
						}}
						onClick={() => {
							setOpen(true);
						}}
					>
						{/* 左侧内容区域 */}
						<div className="flex items-center flex-1 min-w-0 relative">
							{/* 显示选中模型的覆盖层 */}
							{searchValue === "" && selectedModel && (
								<div className="absolute left-0 top-0 bottom-0 flex items-center pointer-events-none">
									<ModelIcon
										modelId={selectedModel.code}
										className="mr-2 flex-shrink-0"
									/>
									<span className="text-sm truncate">
										{selectedModel.name}
									</span>
								</div>
							)}
							
							{/* 输入框 - 始终存在但可能透明 */}
							<input
								ref={inputRef}
								className="w-full border-0 bg-transparent focus:ring-0 focus:outline-none text-sm relative z-10"
								style={{
									boxShadow: "none",
									caretColor: "black",
									color: searchValue === "" && selectedModel ? "transparent" : "black",
								}}
								value={searchValue}
								onChange={handleSearchChange}
								onKeyDown={handleKeyDown}
								placeholder={!selectedModel ? t("form.selectModel") : ""}
								onClick={(e) => {
									e.stopPropagation();
									setOpen(true);
									e.currentTarget.focus();
								}}
							/>
						</div>

						{/* 右侧图标 */}
						<div className="flex-shrink-0 ml-2">
							{open ? (
								<Search className="h-5 w-5 text-gray-500" />
							) : (
								<ChevronDown className="h-5 w-5 text-gray-500" />
							)}
						</div>
					</div>
				</div>
			</PopoverTrigger>

			<PopoverContent
				className="overflow-hidden p-0"
				style={{
					width: "var(--radix-popover-trigger-width)",
					minWidth: "var(--radix-popover-trigger-width)",
					backgroundColor: "#f9fafb",
				}}
				align="start"
				sideOffset={5}
			>
				<Command
					ref={commandRef}
					className="rounded-md border-0 shadow-none bg-gray-50 overflow-hidden"
					value={searchValue}
					filter={(value, search) => {
						if (search.trim() === "") return 1;
						const model = models.find(m => m.code === value);
						if (!model) return 0;
						return (
							model.name.toLowerCase().includes(search.toLowerCase()) ||
							(model.description && model.description.toLowerCase().includes(search.toLowerCase()))
						) ? 1 : 0;
					}}
					tabIndex={0}
					onKeyDown={(e) => {
						if (e.key === "Enter") {
							e.preventDefault();
							// 获取当前选中的项目
							const selectedItem = document.querySelector('[data-selected="true"]');
							if (selectedItem) {
								selectedItem.click();
							}
						}
					}}
				>
					{/* 不需要额外的隐藏输入框 */}
					<CommandList
						ref={commandListRef}
						className="overflow-auto"
						style={{ maxHeight: `${getDropdownHeight()}px` }}
					>
						<CommandEmpty className="py-6 text-sm text-center text-gray-500">
							{t("form.noModelsFound")}
						</CommandEmpty>

						<CommandGroup>
							{filteredModels.map((model) => (
								<CommandItem
									key={model.code}
									value={model.code}
									onSelect={handleSelect}
									ref={
										selectedModel?.code === model.code
											? selectedItemRef
											: undefined
									}
									className="px-2 py-1.5 cursor-pointer hover:bg-gray-100 data-[selected=true]:bg-gray-200 w-full"
									style={{ minWidth: "100%" }}
								>
									<div className="flex items-start gap-3 w-full">
										{/* 选中状态指示器 */}
										<div className="flex-shrink-0 mt-1">
											<div
												className={cn(
													"h-5 w-5 rounded-full flex items-center justify-center",
													selectedModel?.code ===
														model.code
														? "bg-red-500"
														: "border-[3px] border-gray-400",
												)}
											>
												{selectedModel?.code ===
													model.code && (
													<div className="h-2 w-2 rounded-full bg-white" />
												)}
											</div>
										</div>

										{/* 模型信息 */}
										<div className="flex-1 min-w-0">
											<div className="flex items-center justify-between w-full">
												<span className="font-medium flex items-center">
													<ModelIcon
														modelId={model.code}
														className="mr-2"
													/>
													<span>{model.name}</span>
												</span>

												{/* PRO 标签 */}
												{"isPro" in model &&
													model.isPro === true && (
														<span className="ml-2 text-xs bg-red-500 text-white px-1.5 py-0.5 rounded-full font-medium">
															PRO
														</span>
													)}
											</div>

											{/* 模型描述 */}
											{(model.description ||
												model.descriptionKey) && (
												<p className="text-xs text-gray-500 mt-1 whitespace-nowrap overflow-hidden text-ellipsis pr-1">
													{model.description ||
														model.descriptionKey}
												</p>
											)}

											{/* 模型特性标签 */}
											<div className="flex flex-wrap gap-2 mt-1.5">
												<span className="text-[10px] px-2 h-5 inline-flex items-center justify-center bg-gray-100 rounded text-gray-600">
													{model
														.videoLengthOptions?.[0] ??
														5}{" "}
													min
												</span>

												{model.supportsEndFrame && (
													<span className="text-[10px] px-2 h-5 inline-flex items-center justify-center bg-gray-100 rounded text-gray-600">
														End frame
													</span>
												)}

												<span className="text-[10px] px-2 h-5 inline-flex items-center justify-center bg-gray-100 rounded text-gray-600">
													{model.fps ?? 30} FPS
												</span>
											</div>
										</div>
									</div>
								</CommandItem>
							))}
						</CommandGroup>
					</CommandList>
				</Command>
			</PopoverContent>
		</Popover>
	);
}
