"use client";

import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON><PERSON>rovider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import { Info } from "lucide-react";
import { useTranslations } from "next-intl";
import { useModelOptions } from "../../providers/ModelOptionsProvider";

interface MotionRangeSelectorProps {
	selectedMotionRange: string;
	onChange: (value: string) => void;
}

export function MotionRangeSelector({
	selectedMotionRange,
	onChange,
}: MotionRangeSelectorProps) {
	const t = useTranslations("imageToVideo.form");
	const { selectedModel } = useModelOptions();

	// 如果模型不存在或不支持motion range，不显示组件
	if (
		!selectedModel ||
		!selectedModel.motionRangeOptions ||
		selectedModel.motionRangeOptions.length === 0
	) {
		return null;
	}

	return (
		<div className="space-y-2">
			{/* 标签行 */}
			<div className="flex items-center gap-2">
				<label
					htmlFor="motion-range"
					className="text-sm font-semibold text-gray-700"
				>
					Motion Range
				</label>
				<TooltipProvider delayDuration={0}>
					<Tooltip>
						<TooltipTrigger asChild>
							<Info className="size-4 text-gray-400 cursor-help" />
						</TooltipTrigger>
						<TooltipContent side="top" className="max-w-xs">
							<p className="text-sm">
								Controls the amount of motion in the generated
								video. Auto lets the AI decide, while
								Small/Medium/Large give you precise control.
							</p>
						</TooltipContent>
					</Tooltip>
				</TooltipProvider>
			</div>

			{/* 分段控件 */}
			<div aria-label="Motion Range selection" className="flex gap-2">
				{selectedModel.motionRangeOptions.map((option) => (
					<button
						key={option.code}
						type="button"
						onClick={() => onChange(option.code)}
						className={cn(
							"flex-1 h-9 flex items-center justify-center rounded-md text-sm font-medium transition-colors",
							selectedMotionRange === option.code
								? "bg-red-500 text-white"
								: "bg-gray-50 text-gray-700 border border-gray-200 hover:border-gray-300 hover:bg-gray-100",
						)}
					>
						{option.name}
					</button>
				))}
			</div>
		</div>
	);
}
