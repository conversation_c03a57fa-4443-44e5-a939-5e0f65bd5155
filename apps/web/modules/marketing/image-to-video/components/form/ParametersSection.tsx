// apps/web/modules/tooling/image-to-video/components/form/ParametersSection.tsx
"use client";

import { useTranslations } from "next-intl";
import { useModelOptions } from "../../providers/ModelOptionsProvider";
import { AspectRatioSelector } from "./AspectRatioSelector";
import { ModeSelector } from "./ModeSelector";
import { NegativePromptInput } from "./NegativePromptInput";
import { PromptStrengthSlider } from "./PromptStrengthSlider";
import { VideoLengthSelector } from "./VideoLengthSelector";

interface ParametersSectionProps {
	formData: any;
	onChange: (field: string, value: any) => void;
}

export function ParametersSection({
	formData,
	onChange,
}: ParametersSectionProps) {
	const t = useTranslations("imageToVideo.form");
	const { selectedModel } = useModelOptions();

	if (!selectedModel) {
		return null;
	}

	return (
		<div className="space-y-6">
			{/* 模式选择 */}
			<ModeSelector
				selectedMode={formData.mode}
				onChange={(value) => onChange("mode", value)}
			/>

			{/* 长宽比和视频长度选择 - 放在同一行 */}
			<div className="grid grid-cols-1 md:grid-cols-2 gap-5">
				{/* 长宽比选择 */}
				<AspectRatioSelector
					selectedRatio={formData.aspectRatio}
					onChange={(value) => onChange("aspectRatio", value)}
				/>

				{/* 视频长度选择 */}
				<VideoLengthSelector
					selectedLength={formData.videoLength}
					onChange={(value) => onChange("videoLength", value)}
				/>
			</div>

			{/* 提示词强度 */}
			<PromptStrengthSlider
				value={formData.promptStrength}
				onChange={(value) => onChange("promptStrength", value)}
			/>

			{/* 反向提示词 */}
			<NegativePromptInput
				value={formData.negativePrompt}
				onChange={(value) => onChange("negativePrompt", value)}
			/>

		</div>
	);
}
