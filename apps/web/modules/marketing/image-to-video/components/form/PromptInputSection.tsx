// apps/web/modules/tooling/image-to-video/components/form/PromptInputSection.tsx
"use client";

import { useTranslations } from "next-intl";
import { useEffect, useRef, useState } from "react";
import { Sparkles } from "lucide-react";
import { useToast } from "@ui/components/use-toast";
import { PromptGeneratorModal } from "@marketing/shared/components/prompt-generator";

interface PromptInputSectionProps {
	value: string;
	onChange: (value: string) => void;
}

export function PromptInputSection({
	value,
	onChange,
}: PromptInputSectionProps) {
	const t = useTranslations("imageToVideo.form");
	const textareaRef = useRef<HTMLTextAreaElement>(null);
	const resizeIconRef = useRef<HTMLDivElement>(null);
	const [isDragging, setIsDragging] = useState(false);
	const [startY, setStartY] = useState(0);
	const [startHeight, setStartHeight] = useState(0);

	// 最大字符数限制
	const MAX_CHARS = 2000;
	const charCount = value.length;

	// 处理输入变化
	const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
		onChange(e.target.value);
	};

	// Prompt Generator 状态管理
	const [isGeneratorModalOpen, setIsGeneratorModalOpen] = useState(false);
	const { toast } = useToast();

	// 处理Generate按钮点击
	const handleGenerateClick = () => {
		setIsGeneratorModalOpen(true);
	};

	// 处理提示词选择
	const handlePromptSelect = (prompt: string) => {
		onChange(prompt);
		setIsGeneratorModalOpen(false);
		toast.success("The selected prompt has been added to your input.");
	};

	// 处理模态框关闭
	const handleModalClose = () => {
		setIsGeneratorModalOpen(false);
	};

	// 处理删除按钮点击
	const handleClear = () => {
		onChange("");
	};

	// 处理调整大小
	const handleResizeStart = (e: React.MouseEvent) => {
		e.preventDefault();
		if (textareaRef.current) {
			setIsDragging(true);
			setStartY(e.clientY);
			setStartHeight(textareaRef.current.offsetHeight);
		}
	};

	useEffect(() => {
		const handleResizeMove = (e: MouseEvent) => {
			if (isDragging && textareaRef.current) {
				const newHeight = startHeight + (e.clientY - startY);
				textareaRef.current.style.height = `${Math.max(60, newHeight)}px`;
			}
		};

		const handleResizeEnd = () => {
			setIsDragging(false);
		};

		if (isDragging) {
			document.addEventListener("mousemove", handleResizeMove);
			document.addEventListener("mouseup", handleResizeEnd);
		}

		return () => {
			document.removeEventListener("mousemove", handleResizeMove);
			document.removeEventListener("mouseup", handleResizeEnd);
		};
	}, [isDragging, startHeight, startY]);

	return (
		<div className="space-y-2">
			<div className="text-gray-800 font-medium pl-1">
				{t("promptLabel")}
			</div>
			<div className="relative bg-white rounded-lg shadow-sm border border-gray-100 hover:border-gray-400 focus-within:border-indigo-300 transition-colors overflow-hidden">
				<div className="relative">
					<textarea
						ref={textareaRef}
						className="w-full min-h-[60x] p-4 outline-none resize-none border-0 focus:ring-0"
						style={{ height: "100px" }}
						value={value}
						onChange={handleChange}
						maxLength={MAX_CHARS}
						placeholder={t("promptPlaceholder")}
					/>
					{/* Resize icon in the bottom-right corner of textarea */}
					<div
						ref={resizeIconRef}
						className="absolute right-3 bottom-3 text-gray-300 cursor-ns-resize w-4 h-4 flex items-center justify-center z-10"
						onMouseDown={handleResizeStart}
						title="Resize textarea"
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							viewBox="0 0 24 24"
							width="16"
							height="16"
						>
							<path
								fill="currentColor"
								fillRule="evenodd"
								d="M22.707 7.293a1 1 0 010 1.414l-14 14a1 1 0 11-1.414-1.414l14-14a1 1 0 011.414 0m0 6a1 1 0 010 1.414l-8 8a1 1 0 11-1.414-1.414l8-8a1 1 0 011.414 0m0 6a1 1 0 010 1.414l-2 2a1 1 0 11-1.414-1.414l2-2a1 1 0 011.414 0"
								clipRule="evenodd"
							/>
						</svg>
					</div>
				</div>
				<div className="p-2 bg-white flex items-center justify-between">
					<button
						onClick={handleGenerateClick}
						className="flex items-center gap-1.5 px-3 py-1.5 text-sm text-gray-500 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
						aria-label="Generate AI prompt"
						type="button"
					>
						<Sparkles className="w-4 h-4 text-gray-500" />
						Generate
					</button>
					<div className="flex items-center gap-2">
						<span className="text-sm text-gray-500">
							{charCount}/{MAX_CHARS}
						</span>
						<button
							className="w-5 h-5 text-gray-400 hover:text-gray-600 cursor-pointer border-0 bg-transparent p-0"
							onClick={handleClear}
							aria-label={"Clear prompt"}
							type="button"
						>
							<svg
								className="w-5 h-5"
								viewBox="0 0 20 20"
								fill="none"
								xmlns="http://www.w3.org/2000/svg"
								aria-hidden="true"
							>
								<path
									fillRule="evenodd"
									clipRule="evenodd"
									d="M7 3.25C7 2.83579 7.33579 2.5 7.75 2.5H12.25C12.6642 2.5 13 2.83579 13 3.25C13 3.66421 12.6642 4 12.25 4H7.75C7.33579 4 7 3.66421 7 3.25ZM4.83464 14.6811L4.13636 7H3.75C3.33579 7 3 6.66421 3 6.25C3 5.83579 3.33579 5.5 3.75 5.5H4H16H16.25C16.6642 5.5 17 5.83579 17 6.25C17 6.66421 16.6642 7 16.25 7H15.8636L15.1654 14.6811C15.0717 15.7112 14.208 16.5 13.1736 16.5H6.82643C5.79202 16.5 4.92829 15.7112 4.83464 14.6811ZM7 9.3C7 8.85817 7.35817 8.5 7.8 8.5H8.2C8.64183 8.5 9 8.85817 9 9.3V11.7C9 12.1418 8.64183 12.5 8.2 12.5H7.8C7.35817 12.5 7 12.1418 7 11.7V9.3ZM11.8 8.5C11.3582 8.5 11 8.85817 11 9.3V11.7C11 12.1418 11.3582 12.5 11.8 12.5H12.2C12.6418 12.5 13 12.1418 13 11.7V9.3C13 8.85817 12.6418 8.5 12.2 8.5H11.8Z"
									fill="currentColor"
								/>
							</svg>
						</button>
					</div>
				</div>
			</div>

			{/* Prompt Generator Modal */}
			<PromptGeneratorModal
				open={isGeneratorModalOpen}
				onClose={handleModalClose}
				onSelect={handlePromptSelect}
				generationType="video"
			/>
		</div>
	);
}
