"use client";

import { Slider } from "@ui/components/slider";
import { useTranslations } from "next-intl";
import { useModelOptions } from "../../providers/ModelOptionsProvider";

interface PromptStrengthSliderProps {
  value: number;
  onChange: (value: number) => void;
}

export function PromptStrengthSlider({ 
  value, 
  onChange 
}: PromptStrengthSliderProps) {
  const t = useTranslations("imageToVideo.form");
  const { selectedModel } = useModelOptions();

  if (!selectedModel?.supportsPromptStrength) {
    return null;
  }

  return (
    <div className="space-y-3">
      <div className="flex justify-between items-center">
        <label htmlFor="prompt-strength" className="text-sm font-medium text-gray-700">
          {t("promptStrength")}
        </label>
        <span className="text-sm font-medium text-gray-600">
          {value}%
        </span>
      </div>
      <Slider
        id="prompt-strength"
        value={[value]}
        min={0}
        max={100}
        step={1}
        onValueChange={(value) => onChange(value[0])}
        className="cursor-pointer"
      />
      <div className="flex justify-between text-xs text-gray-500">
        <span>{t("moreCreative")}</span>
        <span>{t("followPrompt")}</span>
      </div>
    </div>
  );
}
