"use client";

import { Switch } from "@ui/components/switch";
import {
	<PERSON><PERSON><PERSON>,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { AlertCircle } from "lucide-react";
import { useTranslations } from "next-intl";

interface PublicVisibilityOptionProps {
	isPublic: boolean;
	onChange: (value: boolean) => void;
}

export function PublicVisibilityOption({
	isPublic,
	onChange,
}: PublicVisibilityOptionProps) {
	const t = useTranslations("imageToVideo.form");

	return (
		<div className="flex items-center justify-between p-1">
			<div>
				<div className="flex items-center">
					<label
						htmlFor="public-visibility"
						className="block text-sm font-medium text-gray-700"
					>
						{t("visibility")}
					</label>
					<TooltipProvider>
						<Tooltip>
							<TooltipTrigger asChild>
								<div className="ml-1 cursor-help">
									<AlertCircle className="h-4 w-4 text-gray-500" />
								</div>
							</TooltipTrigger>
							<TooltipContent>
								<p className="max-w-xs">
									{"When this option is enabled, the output video may be selected by Media AI and published to the Explore."}
								</p>
							</TooltipContent>
						</Tooltip>
					</TooltipProvider>
				</div>
				<p className="text-xs text-gray-500 mt-1">
					{t("visibilityDescription")}
				</p>
			</div>
			<Switch
				id="public-visibility"
				checked={isPublic}
				onCheckedChange={onChange}
				aria-label={t("visibility")}
			/>
		</div>
	);
}
