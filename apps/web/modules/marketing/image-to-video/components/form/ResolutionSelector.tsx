"use client";

import { cn } from "@ui/lib";
import { useTranslations } from "next-intl";
import { useModelOptions } from "../../providers/ModelOptionsProvider";

interface ResolutionSelectorProps {
	selectedResolution: string;
	onChange: (value: string) => void;
	className?: string;
}

export function ResolutionSelector({
	selectedResolution,
	onChange,
	className,
}: ResolutionSelectorProps) {
	const t = useTranslations("imageToVideo.form");
	const { selectedModel } = useModelOptions();

	// 如果模型不支持分辨率选择，不显示组件
	if (
		!selectedModel?.resolutionOptions ||
		selectedModel.resolutionOptions.length === 0
	) {
		return null;
	}

	// We no longer need different colors for resolutions

	return (
		<div className={cn("space-y-3", className)}>
			<div className="block text-sm font-medium text-gray-700">
				{t("resolution.label")}
			</div>
			<div className="flex gap-2">
				{selectedModel.resolutionOptions.map((resolution) => (
					<button
						key={resolution}
						type="button"
						onClick={() => onChange(resolution)}
						className={cn(
							"flex-1 py-2 rounded-md text-sm font-medium transition-colors h-9 flex items-center justify-center",
							selectedResolution === resolution
								? "bg-gray-600 text-white border-gray-600"
								: "bg-gray-50 text-gray-700 border border-gray-200 hover:border-gray-300 hover:bg-gray-100",
						)}
					>
						{resolution}
					</button>
				))}
			</div>
		</div>
	);
}
