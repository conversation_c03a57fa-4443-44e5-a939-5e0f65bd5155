"use client";
import {
	Toolt<PERSON>,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import { Info, Lock, LockOpen, RefreshCw } from "lucide-react";
import { useModelOptions } from "../../providers/ModelOptionsProvider";

interface SeedInputProps {
	value: number | null;
	onChange: (value: number | null) => void;
	isLocked?: boolean;
	onLockToggle?: (locked: boolean) => void;
}

export function SeedInput({
	value,
	onChange,
	isLocked = false,
	onLockToggle,
}: SeedInputProps) {
	const { selectedModel } = useModelOptions();

	// 如果模型不存在或不支持seed，不显示组件
	if (!selectedModel || !selectedModel.supportsSeed) {
		return null;
	}

	// 生成随机seed值
	const generateRandomSeed = () => {
		if (isLocked) return; // 锁定时不允许生成随机数
		// 生成8位数的随机数 (10000000 - 99999999)
		const randomSeed = Math.floor(Math.random() * 90000000) + 10000000;
		onChange(randomSeed);
	};

	// 处理输入值变化
	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		if (isLocked) return; // 锁定时不允许修改
		const newValue = e.target.value;
		if (newValue === "") {
			onChange(null);
		} else {
			const numValue = Number.parseInt(newValue, 10);
			if (
				!Number.isNaN(numValue) &&
				numValue >= 1 &&
				numValue <= **********
			) {
				onChange(numValue);
			}
		}
	};

	// 处理锁定状态切换
	const handleLockToggle = () => {
		if (onLockToggle) {
			onLockToggle(!isLocked);
		}
	};

	return (
		<div className="space-y-2">
			{/* 标签行 */}
			<div className="flex items-center gap-2">
				<label
					htmlFor="seed-input"
					className="text-sm font-semibold text-gray-700"
				>
					Seed
				</label>
				<TooltipProvider delayDuration={0}>
					<Tooltip>
						<TooltipTrigger asChild>
							<Info className="size-4 text-gray-400 cursor-help" />
						</TooltipTrigger>
						<TooltipContent side="top" className="max-w-xs">
							<p className="text-sm">
								Seed controls the randomness of generation. Use
								the same seed to reproduce similar results, or
								leave empty for random generation.
							</p>
						</TooltipContent>
					</Tooltip>
				</TooltipProvider>
			</div>

			{/* 输入控件行 */}
			<div className="flex items-center w-full">
				{/* 输入框组合容器 */}
				<div className="flex w-full border border-gray-200 rounded-lg bg-gray-50 overflow-hidden focus-within:border-gray-300 transition-colors">
					{/* 前缀锁定图标 - addon */}
					<div className="flex items-center justify-center px-3 border-r border-gray-200">
						<button
							type="button"
							onClick={handleLockToggle}
							className="flex items-center justify-center size-5 text-gray-500 hover:text-gray-700 transition-colors"
							aria-label={isLocked ? "Unlock seed" : "Lock seed"}
						>
							{isLocked ? (
								<Lock className="size-4" />
							) : (
								<LockOpen className="size-4" />
							)}
						</button>
					</div>

					{/* 输入框容器 */}
					<div className="relative flex-1">
						<input
							id="seed-input"
							type="number"
							min="1"
							max="**********"
							value={value || ""}
							onChange={handleInputChange}
							placeholder="Enter seed value"
							disabled={isLocked}
							className={cn(
								"w-full h-9 px-3 text-sm bg-transparent border-0 outline-none",
								"[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none",
								isLocked && "text-gray-500 cursor-not-allowed",
							)}
							aria-describedby="seed-description"
						/>
						{/* 后缀随机化图标 - suffix */}
						<div className="absolute right-0 top-0 h-full flex items-center justify-center px-3">
							<button
								type="button"
								onClick={generateRandomSeed}
								disabled={isLocked}
								className={cn(
									"flex items-center justify-center size-5 transition-colors",
									isLocked
										? "text-gray-300 cursor-not-allowed"
										: "text-gray-400 hover:text-red-500",
								)}
								aria-label="Generate random seed"
							>
								<RefreshCw className="size-4" />
							</button>
						</div>
					</div>
				</div>
			</div>

			{/* 隐藏的描述文本，用于屏幕阅读器 */}
			<p id="seed-description" className="sr-only">
				Seed value between 1 and **********. Controls the randomness of
				AI generation.
			</p>
		</div>
	);
}
