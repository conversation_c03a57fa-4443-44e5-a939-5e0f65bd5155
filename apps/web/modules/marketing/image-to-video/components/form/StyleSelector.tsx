"use client";

import { cn } from "@ui/lib";
import { Info } from "lucide-react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useCallback, useEffect, useRef, useState } from "react";
import { useModelOptions } from "../../providers/ModelOptionsProvider";

interface StyleSelectorProps {
	selectedStyle: string;
	onChange: (value: string) => void;
	className?: string;
}

// 风格预览配置 - 使用静态占位图片
function getStylePreview(styleCode: string) {
	const previews: Record<string, { image: string; alt: string }> = {
		auto: {
			image: "/images/style-previews/auto-placeholder.jpg",
			alt: "Auto style preview",
		},
		anime: {
			image: "/images/style-previews/anime-placeholder.jpg",
			alt: "Anime style preview",
		},
		"3d_animation": {
			image: "/images/style-previews/3d-animation-placeholder.jpg",
			alt: "3D Animation style preview",
		},
		comic: {
			image: "/images/style-previews/comic-placeholder.jpg",
			alt: "Comic style preview",
		},
		clay: {
			image: "/images/style-previews/clay-placeholder.jpg",
			alt: "Clay style preview",
		},
		cyberpunk: {
			image: "/images/style-previews/cyberpunk-placeholder.jpg",
			alt: "Cyberpunk style preview",
		},
	};

	return (
		previews[styleCode] || {
			image: "/images/style-previews/default-placeholder.jpg",
			alt: "Style preview",
		}
	);
}

// 风格预览组件
function StylePreview({ styleCode }: { styleCode: string }) {
	const preview = getStylePreview(styleCode);

	return (
		<div className="relative aspect-video w-full min-w-36">
			<Image
				src={preview.image}
				alt={preview.alt}
				fill
				className="rounded-lg object-cover"
				sizes="(max-width: 768px) 144px, 144px"
				// 使用占位图片，如果加载失败显示渐变背景
				onError={(e) => {
					const target = e.target as HTMLImageElement;
					target.style.display = "none";
					const parent = target.parentElement;
					if (parent) {
						parent.style.background = getStyleGradient(styleCode);
					}
				}}
			/>
		</div>
	);
}

// 备用渐变背景
function getStyleGradient(styleCode: string): string {
	const gradients: Record<string, string> = {
		auto: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
		anime: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
		"3d_animation": "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
		comic: "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
		clay: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
		cyberpunk: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
	};

	return (
		gradients[styleCode] ||
		"linear-gradient(135deg, #9ca3af 0%, #6b7280 100%)"
	);
}

// 拖拽滚动 Hook
function useDragScroll() {
	const scrollRef = useRef<HTMLDivElement>(null);
	const [isDragging, setIsDragging] = useState(false);
	const [startX, setStartX] = useState(0);
	const [scrollLeft, setScrollLeft] = useState(0);

	const handleMouseDown = useCallback((e: React.MouseEvent) => {
		if (!scrollRef.current) return;

		setIsDragging(true);
		setStartX(e.pageX - scrollRef.current.offsetLeft);
		setScrollLeft(scrollRef.current.scrollLeft);

		// 防止文本选择
		e.preventDefault();
	}, []);

	const handleMouseMove = useCallback(
		(e: MouseEvent) => {
			if (!isDragging || !scrollRef.current) return;

			e.preventDefault();
			const x = e.pageX - scrollRef.current.offsetLeft;
			const walk = (x - startX) * 2; // 滚动速度倍数
			scrollRef.current.scrollLeft = scrollLeft - walk;
		},
		[isDragging, startX, scrollLeft],
	);

	const handleMouseUp = useCallback(() => {
		setIsDragging(false);
	}, []);

	const handleMouseLeave = useCallback(() => {
		setIsDragging(false);
	}, []);

	useEffect(() => {
		if (isDragging) {
			document.addEventListener("mousemove", handleMouseMove);
			document.addEventListener("mouseup", handleMouseUp);

			return () => {
				document.removeEventListener("mousemove", handleMouseMove);
				document.removeEventListener("mouseup", handleMouseUp);
			};
		}
	}, [isDragging, handleMouseMove, handleMouseUp]);

	return {
		scrollRef,
		isDragging,
		handleMouseDown,
		handleMouseLeave,
	};
}

export function StyleSelector({
	selectedStyle,
	onChange,
	className,
}: StyleSelectorProps) {
	const t = useTranslations("imageToVideo.form");
	const { selectedModel } = useModelOptions();
	const { scrollRef, isDragging, handleMouseDown, handleMouseLeave } =
		useDragScroll();

	// 如果模型不支持风格选择，不显示组件
	if (
		!selectedModel?.styleOptions ||
		selectedModel.styleOptions.length === 0
	) {
		return null;
	}

	return (
		<div className={cn("space-y-3", className)}>
			{/* 标签区域 - 参考竞品设计 */}
			<div className="flex items-center gap-2">
				<div className="text-sm font-semibold text-gray-700">
					{t("style.label")}
				</div>
				<Info className="h-4 w-4 text-gray-400 cursor-help" />
			</div>

			{/* 滚动容器 - 参考竞品结构 */}
			<div className="relative overflow-hidden rounded-lg">
				<div
					ref={scrollRef}
					className={cn(
						"flex gap-2 overflow-x-auto scrollbar-hide scroll-smooth pb-2",
						isDragging
							? "cursor-grabbing select-none"
							: "cursor-grab",
					)}
					onMouseDown={handleMouseDown}
					onMouseLeave={handleMouseLeave}
				>
					{selectedModel.styleOptions.map((style) => (
						<button
							key={style.code}
							type="button"
							onClick={(e) => {
								// 防止拖拽时触发点击
								if (!isDragging) {
									onChange(style.code);
								}
							}}
							className={cn(
								// 基础样式 - 完全按照竞品设计，统一边框和背景
								"aspect-video flex-1 shrink-0 cursor-pointer select-none rounded-lg border border-gray-200 bg-white p-2 transition-colors duration-200 hover:bg-gray-50 touch-manipulation",
							)}
							// 防止拖拽时的默认行为
							onDragStart={(e) => e.preventDefault()}
						>
							{/* 预览图片区域 */}
							<StylePreview styleCode={style.code} />

							{/* 风格名称 - 按竞品设计，只改变文字颜色 */}
							<div
								className={cn(
									"text-center text-sm font-semibold mb-1 mt-2 transition-colors duration-200",
									selectedStyle === style.code
										? "text-red-600" // 选中时的主色调
										: "text-gray-500", // 未选中时的次要文字色
								)}
							>
								{style.name}
							</div>
						</button>
					))}
				</div>

				{/* 渐变遮罩 - 按竞品使用黑色半透明 */}
				{selectedModel.styleOptions.length > 3 && (
					<div className="pointer-events-none absolute inset-y-0 right-0 w-8 bg-gradient-to-l from-black/50 to-transparent" />
				)}
			</div>
		</div>
	);
}
