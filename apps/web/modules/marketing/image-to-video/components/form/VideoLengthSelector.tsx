"use client";

import { RadioGroup, RadioGroupItem } from "@ui/components/radio-group";
import { useTranslations } from "next-intl";
import { useModelOptions } from "../../providers/ModelOptionsProvider";

interface VideoLengthSelectorProps {
	selectedLength: number;
	onChange: (value: number) => void;
}

export function VideoLengthSelector({
	selectedLength,
	onChange,
}: VideoLengthSelectorProps) {
	const t = useTranslations("imageToVideo.form");
	const { selectedModel } = useModelOptions();

	if (
		!selectedModel?.videoLengthOptions ||
		selectedModel.videoLengthOptions.length === 0
	) {
		return null;
	}

	return (
		<div>
			<div className="flex items-center">
				<h3 className="text-sm font-medium text-gray-700 mr-6">
					{t("videoLength")}
				</h3>
				<RadioGroup
					value={String(selectedLength)}
					onValueChange={(value) => onChange(Number(value))}
					className="flex items-center gap-4"
				>
					{selectedModel.videoLengthOptions.map((length) => (
						<label
							key={length}
							htmlFor={`video-length-${length}`}
							className="flex items-center space-x-2 cursor-pointer"
						>
							<div
								className={`relative flex h-5 w-5 items-center justify-center rounded-full ${selectedLength === length ? "bg-gray-600" : "border-[3px] border-gray-400"}`}
							>
								<RadioGroupItem
									value={String(length)}
									id={`video-length-${length}`}
									className="sr-only"
								/>
								{selectedLength === length && (
									<div className="h-2 w-2 rounded-full bg-white" />
								)}
							</div>
							<span className="text-sm font-medium text-gray-700">
								{`${length}s`}
							</span>
						</label>
					))}
				</RadioGroup>
			</div>
		</div>
	);
}
