// apps/web/modules/tooling/image-to-video/components/form/VideoOutputOptions.tsx
"use client";

import { useTranslations } from "next-intl";
import { useModelOptions } from "../../providers/ModelOptionsProvider";

interface VideoOutputOptionsProps {
	formData: any;
	onChange: (field: string, value: any) => void;
}

export function VideoOutputOptions({
	formData,
	onChange,
}: VideoOutputOptionsProps) {
	const t = useTranslations("imageToVideo.form");
	const { selectedModel } = useModelOptions();

	if (!selectedModel) {
		return null;
	}

	return (
		<div>
			{/* 输出视频数量 */}
			<div>
				<div
					className="block text-sm font-medium mb-2.5"
					id="output-number-label"
				>
					{t("outputNumber")}
				</div>
				<div
					className="flex gap-2"
					aria-labelledby="output-number-label"
				>
					{[1, 2, 3, 4].map((number) => (
						<button
							key={number}
							type="button"
							aria-label={`${t("outputNumber")} ${number}`}
							aria-pressed={formData.outputNumber === number}
							className={`flex-1 h-9 flex items-center justify-center rounded-md text-sm font-medium transition-colors ${
								formData.outputNumber === number
									? "bg-gray-600 text-white border-gray-600"
									: "bg-gray-50 text-gray-700 border border-gray-200 hover:border-gray-300 hover:bg-gray-100"
							}`}
							onClick={() => onChange("outputNumber", number)}
						>
							{number}
						</button>
					))}
				</div>
			</div>
		</div>
	);
}
