"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>ooter,
	<PERSON><PERSON>Header,
	DialogTitle,
} from "@ui/components/dialog";
import { Slider } from "@ui/components/slider";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@ui/components/tabs";
import { cn } from "@ui/lib";
import { useMemo, useRef, useState } from "react";
import type { ReactCropperElement } from "react-cropper";
import <PERSON>ropper from "react-cropper";
import "cropperjs/dist/cropper.css";
import { uploadImage as uploadImageFile } from "../../lib/upload-utils";

interface AspectRatio {
	label: string;
	value: number | null;
	icon: string;
}

// Default aspect ratios as shown in the image
const DEFAULT_ASPECT_RATIOS = ["16:9", "9:16", "1:1", "4:3", "3:4"];

interface ImageEditDialogProps {
	image: File | null;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSave: (uploadedImageUrl: string | null) => void;
	aspectRatioOptions?: string[]; // Optional aspect ratio options from parent model
}

// Upload state hook
const useUploadState = () => {
	const [uploading, setUploading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const uploadWithState = async (imageBlob: Blob, originalFilename: string = 'image.png') => {
		setUploading(true);
		setError(null);

		try {
			const accessURL = await uploadImageFile(imageBlob, originalFilename);
			return accessURL;
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : 'Upload failed';
			setError(errorMessage);
			throw err;
		} finally {
			setUploading(false);
		}
	};

	return { uploadWithState, uploading, error };
};

export function ImageEditDialog({
	image,
	open,
	onOpenChange,
	onSave,
	aspectRatioOptions,
}: ImageEditDialogProps) {
	const cropperRef = useRef<ReactCropperElement>(null);
	const [selectedAspectRatio, setSelectedAspectRatio] = useState<
		number | null
	>(null);
	const [brightness, setBrightness] = useState(100);
	const [contrast, setContrast] = useState(100);
	const [saturation, setSaturation] = useState(100);
	const [zoom, setZoom] = useState(1);
	const [activeTab, setActiveTab] = useState("crop");
	const [isDraggingZoom, setIsDraggingZoom] = useState(false);
	const [showToast, setShowToast] = useState(false);
	const [toastMessage, setToastMessage] = useState("");

	// Use the upload state hook
	const { uploadWithState, uploading, error: uploadError } = useUploadState();

	// Toast function
	const showErrorToast = (message: string) => {
		setToastMessage(message);
		setShowToast(true);
		// Auto-hide after 4 seconds
		setTimeout(() => {
			setShowToast(false);
		}, 4000);
	};

	// Generate aspect ratios based on props or default
	const aspectRatios: AspectRatio[] = useMemo(() => {
		const ratioStrings =
			aspectRatioOptions && aspectRatioOptions.length > 0
				? aspectRatioOptions
				: DEFAULT_ASPECT_RATIOS;

		const ratios: AspectRatio[] = ratioStrings.map((ratioStr) => {
			const [width, height] = ratioStr.split(":").map(Number);
			return {
				label: ratioStr,
				value: width / height,
				icon: "",
			};
		});

		// Always add Free option at the end
		ratios.push({
			label: "Free",
			value: null,
			icon: "",
		});

		return ratios;
	}, [aspectRatioOptions]);

	const imageSrc = useMemo(
		() => image && URL.createObjectURL(image),
		[image],
	);

	const getCroppedImage = async () => {
		const cropper = cropperRef.current?.cropper;

		const canvas = cropper?.getCroppedCanvas({
			// Remove size limitations to preserve original resolution
			// maxWidth: 2048,
			// maxHeight: 2048,
		});

		if (!canvas) return null;

		// Apply filters
		const ctx = canvas.getContext("2d");
		if (ctx) {
			ctx.filter = `brightness(${brightness}%) contrast(${contrast}%) saturate(${saturation}%)`;
			ctx.drawImage(canvas, 0, 0);
		}

		const imageBlob = await new Promise<Blob | null>((resolve) => {
			// Use JPEG quality 0.5 for maximum compression (similar to competitors)
			canvas.toBlob(resolve, "image/jpeg", 0.5);
		});

		return imageBlob;
	};

	const handleAspectRatioChange = (ratio: number | null) => {
		setSelectedAspectRatio(ratio);
		const cropper = cropperRef.current?.cropper;
		if (cropper) {
			cropper.setAspectRatio(ratio || Number.NaN);
		}
	};

	const handleZoomChange = (zoomValue: number) => {
		setZoom(zoomValue);
		const cropper = cropperRef.current?.cropper;
		if (cropper) {
			cropper.zoomTo(zoomValue);
		}
	};

	const handleReset = () => {
		setBrightness(100);
		setContrast(100);
		setSaturation(100);
		setZoom(1);
		setSelectedAspectRatio(null);
		const cropper = cropperRef.current?.cropper;
		if (cropper) {
			cropper.reset();
			cropper.setAspectRatio(Number.NaN);
		}
	};

	const handleSave = async () => {
		try {
			const editedImage = await getCroppedImage();
			if (!editedImage) {
				onSave(null);
				return;
			}

			// Check edited image size (after compression)
			if (editedImage.size > 10 * 1024 * 1024) {
				showErrorToast("Edited image too large. Maximum size: 10MB");
				onSave(null);
				onOpenChange(false);
				return;
			}

			// Validate image dimensions
			const canvas = cropperRef.current?.cropper?.getCroppedCanvas({
				// Use same settings as getCroppedImage - no size limits
			});
			
			if (!canvas) {
				showErrorToast("Failed to process the image.");
				onSave(null);
				onOpenChange(false);
				return;
			}

			const { width, height } = canvas;
			const minDimension = Math.min(width, height);
			
			if (minDimension < 300) {
				showErrorToast("Failed to upload the image. The shorter side must exceed 300 pixels.");
				onSave(null);
				onOpenChange(false);
				return;
			}

			// Upload the image and get the access URL
			const originalFilename = image?.name.replace(/\.(png|jpeg|webp)$/i, '.jpg') || 'edited-image.jpg';
			const accessURL = await uploadWithState(editedImage, originalFilename);
			
			// Pass the uploaded image URL to parent
			onSave(accessURL);
			onOpenChange(false);
		} catch (error) {
			console.error('Failed to save and upload image:', error);
			showErrorToast("Failed to process the image.");
			onSave(null);
			onOpenChange(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
				<DialogHeader>
					<DialogTitle>Edit uploaded images</DialogTitle>
				</DialogHeader>

				<div className="flex flex-col h-[70vh]">
					{/* Image preview area */}
					<div className="flex-1 bg-gray-50 rounded-lg overflow-hidden mb-4">
						{imageSrc && (
							<Cropper
								src={imageSrc}
								style={{
									width: "100%",
									height: "100%",
									filter: `brightness(${brightness}%) contrast(${contrast}%) saturate(${saturation}%)`,
									touchAction: "none",
								}}
								aspectRatio={selectedAspectRatio || Number.NaN}
								guides={true}
								ref={cropperRef}
								background={false}
								responsive={true}
								autoCropArea={1}
								checkOrientation={false}
								viewMode={1}
								dragMode="move"
								movable={true}
								zoomable={true}
								zoomOnTouch={true}
								zoomOnWheel={true}
								cropBoxMovable={false}
								cropBoxResizable={true}
								toggleDragModeOnDblclick={true}
								wheelZoomRatio={0.1}
								minCanvasWidth={0}
								minCanvasHeight={0}
							/>
						)}
					</div>

					{/* Controls */}
					<Tabs
						value={activeTab}
						onValueChange={setActiveTab}
						className="w-full"
					>
						<TabsList className="grid w-full grid-cols-2">
							<TabsTrigger value="crop">
								Crop & Aspect Ratio
							</TabsTrigger>
							<TabsTrigger value="adjust">
								Adjustments
							</TabsTrigger>
						</TabsList>

						<TabsContent
							value="crop"
							className="space-y-4 min-h-[180px] md:min-h-[180px]"
						>
							<div>
								<div className="w-full text-left text-sm font-medium mb-3">
									Aspect Ratio
								</div>
								<div className="flex gap-1 overflow-y-auto sm:gap-2 no-scrollbar">
									{aspectRatios.map((ratio) => {
										const isSelected =
											selectedAspectRatio === ratio.value;

										// 为不同的宽高比生成视觉样式
										const getVisualStyle = () => {
											if (!ratio.value) return null;
											const aspectRatio = ratio.value;

											// 根据宽高比调整显示大小
											if (aspectRatio > 1) {
												// 横向的比例，固定宽度
												return {
													aspectRatio: `${aspectRatio}`,
													width: "32px",
												};
											}
											// 纵向或正方形的比例，固定高度
											return {
												aspectRatio: `${aspectRatio}`,
												height: "32px",
											};
										};

										const visualStyle = getVisualStyle();

										return (
											<button
												key={ratio.label}
												type="button"
												onClick={() =>
													handleAspectRatioChange(
														ratio.value,
													)
												}
												className={cn(
													"group flex flex-1 cursor-pointer flex-col items-center justify-center gap-2 rounded-md pb-2 pt-4 text-sm duration-300 border",
													"hover:bg-gray-300",
													isSelected
														? "bg-gray-300 border-blue-500"
														: "bg-gray-200 border-transparent",
												)}
												aria-label={`Set aspect ratio to ${ratio.label}`}
												aria-pressed={isSelected}
											>
												{/* 可视化矩形 */}
												<div
													className="flex items-center justify-center"
													style={{ height: "32px" }}
												>
													{ratio.value ? (
														<span
															className={cn(
																"flex rounded-[2px] border border-transparent duration-300",
																isSelected
																	? "bg-blue-500"
																	: "bg-gray-400 group-hover:bg-blue-500",
															)}
															style={
																visualStyle ||
																undefined
															}
														/>
													) : (
														<span className="text-lg">
															🔓
														</span>
													)}
												</div>

												{/* 比例文字 */}
												<span
													className={cn(
														"font-semibold duration-300 text-xs",
														isSelected
															? "text-blue-500"
															: "text-gray-600 group-hover:text-blue-500",
													)}
												>
													{ratio.label}
												</span>
											</button>
										);
									})}
								</div>
							</div>
							<div>
								<h4 className="text-sm font-medium mb-3">
									Zoom
								</h4>
								<section className="img-crop-control img-crop-control-zoom flex items-center w-full mx-auto rounded-[6px] bg-gray-100">
									<button
										className="flex items-center justify-center h-[32px] w-[32px] bg-transparent border-0 font-inherit text-[18px] cursor-pointer disabled:opacity-20 disabled:cursor-default text-gray-700 hover:bg-gray-200 transition-colors"
										onClick={() =>
											handleZoomChange(
												Math.max(1, zoom - 0.1),
											)
										}
										disabled={zoom <= 1}
										type="button"
										title={`Zoom out to ${Math.max(1, zoom - 0.1).toFixed(1)}x`}
									>
										－
									</button>
									<div className="flex-1 px-2 relative">
										{/* Custom Slider with Thumb Tooltip */}
										<div
											className="relative w-full h-6 flex items-center"
											style={
												{
													"--value": zoom,
													"--min": 1,
													"--max": 3,
												} as React.CSSProperties
											}
										>
											{/* Track */}
											<div className="absolute w-full h-1.5 bg-gray-300 rounded-full" />

											{/* Range */}
											<div
												className="absolute h-1.5 bg-blue-500 rounded-full"
												style={{
													width: `${((zoom - 1) / (3 - 1)) * 100}%`,
												}}
											/>

											{/* Thumb with Tooltip */}
											<div
												className="absolute w-4 h-4 bg-white border-2 border-blue-500 rounded-full shadow-sm cursor-pointer transition-all group focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
												style={{
													left: `calc(${((zoom - 1) / (3 - 1)) * 100}% - 8px)`,
												}}
												onMouseDown={(e) => {
													setIsDraggingZoom(true);
													const rect =
														e.currentTarget.parentElement!.getBoundingClientRect();
													const handleMouseMove = (
														e: MouseEvent,
													) => {
														const x =
															e.clientX -
															rect.left;
														const percentage =
															Math.max(
																0,
																Math.min(
																	1,
																	x /
																		rect.width,
																),
															);
														const newValue =
															1 +
															percentage *
																(3 - 1);
														handleZoomChange(
															Math.round(
																newValue * 10,
															) / 10,
														);
													};
													const handleMouseUp =
														() => {
															setIsDraggingZoom(
																false,
															);
															document.removeEventListener(
																"mousemove",
																handleMouseMove,
															);
															document.removeEventListener(
																"mouseup",
																handleMouseUp,
															);
														};
													document.addEventListener(
														"mousemove",
														handleMouseMove,
													);
													document.addEventListener(
														"mouseup",
														handleMouseUp,
													);
												}}
												onTouchStart={(e) => {
													setIsDraggingZoom(true);
													const rect =
														e.currentTarget.parentElement!.getBoundingClientRect();
													const handleTouchMove = (
														e: TouchEvent,
													) => {
														const x =
															e.touches[0]
																.clientX -
															rect.left;
														const percentage =
															Math.max(
																0,
																Math.min(
																	1,
																	x /
																		rect.width,
																),
															);
														const newValue =
															1 +
															percentage *
																(3 - 1);
														handleZoomChange(
															Math.round(
																newValue * 10,
															) / 10,
														);
													};
													const handleTouchEnd =
														() => {
															setIsDraggingZoom(
																false,
															);
															document.removeEventListener(
																"touchmove",
																handleTouchMove,
															);
															document.removeEventListener(
																"touchend",
																handleTouchEnd,
															);
														};
													document.addEventListener(
														"touchmove",
														handleTouchMove,
													);
													document.addEventListener(
														"touchend",
														handleTouchEnd,
													);
												}}
												onKeyDown={(e) => {
													if (e.key === "ArrowLeft") {
														e.preventDefault();
														handleZoomChange(
															Math.max(
																1,
																zoom - 0.1,
															),
														);
													} else if (
														e.key === "ArrowRight"
													) {
														e.preventDefault();
														handleZoomChange(
															Math.min(
																3,
																zoom + 0.1,
															),
														);
													}
												}}
												tabIndex={0}
												role="slider"
												aria-valuemin={1}
												aria-valuemax={3}
												aria-valuenow={zoom}
												aria-label="Zoom level"
											>
												{/* Tooltip */}
												<div
													className={`absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded pointer-events-none transition-opacity z-10 ${isDraggingZoom ? "opacity-100" : "opacity-0 group-hover:opacity-100"}`}
												>
													{zoom.toFixed(1)}x
													{/* Arrow */}
													<div className="absolute top-full left-1/2 transform -translate-x-1/2 w-2 h-2 bg-black rotate-45 -mt-1" />
												</div>
											</div>
										</div>
									</div>
									<button
										className="flex items-center justify-center h-[32px] w-[32px] bg-transparent border-0 font-inherit text-[18px] cursor-pointer disabled:opacity-20 disabled:cursor-default text-gray-700 hover:bg-gray-200 transition-colors"
										onClick={() =>
											handleZoomChange(
												Math.min(3, zoom + 0.1),
											)
										}
										disabled={zoom >= 3}
										type="button"
										title={`Zoom in to ${Math.min(3, zoom + 0.1).toFixed(1)}x`}
									>
										＋
									</button>
								</section>
							</div>
						</TabsContent>

						<TabsContent
							value="adjust"
							className="space-y-4 min-h-[180px] md:min-h-[180px]"
						>
							<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
								<div>
									<label
										htmlFor="brightness-slider"
										className="text-sm font-medium mb-2 block"
									>
										Brightness: {brightness}%
									</label>
									<Slider
										id="brightness-slider"
										value={[brightness]}
										onValueChange={(value: number[]) =>
											setBrightness(value[0])
										}
										min={0}
										max={200}
										step={1}
										className="w-full"
									/>
								</div>
								<div>
									<label
										htmlFor="contrast-slider"
										className="text-sm font-medium mb-2 block"
									>
										Contrast: {contrast}%
									</label>
									<Slider
										id="contrast-slider"
										value={[contrast]}
										onValueChange={(value: number[]) =>
											setContrast(value[0])
										}
										min={0}
										max={200}
										step={1}
										className="w-full"
									/>
								</div>
								<div>
									<label
										htmlFor="saturation-slider"
										className="text-sm font-medium mb-2 block"
									>
										Saturation: {saturation}%
									</label>
									<Slider
										id="saturation-slider"
										value={[saturation]}
										onValueChange={(value: number[]) =>
											setSaturation(value[0])
										}
										min={0}
										max={200}
										step={1}
										className="w-full"
									/>
								</div>
							</div>
						</TabsContent>
					</Tabs>
				</div>

				<DialogFooter className="!flex !justify-end !items-end !text-right">
					<div className="flex flex-col space-y-2 w-full">
						{/* Error message */}
						{uploadError && (
							<div className="text-sm text-red-500 bg-red-50 p-2 rounded">
								{uploadError}
							</div>
						)}
						
						{/* Buttons */}
						<div className="flex space-x-3 justify-end">
							<Button 
								variant="outline" 
								onClick={handleReset}
								disabled={uploading}
							>
								Reset
							</Button>
							<Button
								variant="outline"
								onClick={() => onOpenChange(false)}
								disabled={uploading}
							>
								Cancel
							</Button>
							<Button
								onClick={handleSave}
								className="bg-red-500 hover:bg-red-600"
								disabled={uploading}
							>
								{uploading ? (
									<>
										<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
										Uploading...
									</>
								) : (
									"OK"
								)}
							</Button>
						</div>
					</div>
				</DialogFooter>
			</DialogContent>

			{/* Toast Notification */}
			{showToast && (
				<div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-[9999] pointer-events-none">
					<div className="text-white px-4 py-3 rounded-lg shadow-xl flex items-center gap-3 max-w-md opacity-100" style={{ backgroundColor: 'rgb(17, 24, 39)' }}>
						<div className="flex-shrink-0 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
							<span className="text-white text-sm font-bold">✕</span>
						</div>
						<span className="text-sm font-medium">{toastMessage}</span>
					</div>
				</div>
			)}
		</Dialog>
	);
}
