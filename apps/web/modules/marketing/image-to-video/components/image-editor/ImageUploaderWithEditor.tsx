"use client";

import { But<PERSON> } from "@ui/components/button";
import { Card } from "@ui/components/card";
import { Edit, Upload, X } from "lucide-react";
import Image from "next/image";
import { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { ImageEditDialog } from "./ImageEditDialog";

interface ImageUploaderWithEditorProps {
	onChange: (file: File | Blob | null, uploadedUrl?: string) => void;
	maxSize?: number; // in MB
	acceptedFormats?: string[];
	className?: string;
	aspectRatioOptions?: string[]; // Optional aspect ratio options from parent model
}

export function ImageUploaderWithEditor({
	onChange,
	maxSize = 10,
	acceptedFormats = ["image/jpeg", "image/png", "image/webp"],
	className = "",
	aspectRatioOptions,
}: ImageUploaderWithEditorProps) {
	const [originalFile, setOriginalFile] = useState<File | null>(null);
	const [editedFile, setEditedFile] = useState<Blob | null>(null);
	const [uploadedUrl, setUploadedUrl] = useState<string | null>(null);
	const [preview, setPreview] = useState<string | null>(null);
	const [editDialogOpen, setEditDialogOpen] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const validateFile = (file: File): string | null => {
		if (!acceptedFormats.includes(file.type)) {
			return `Unsupported format. Please use: ${acceptedFormats
				.map((format) => format.split("/")[1].toUpperCase())
				.join(", ")}`;
		}

		if (file.size > maxSize * 1024 * 1024) {
			return `File too large. Maximum size: ${maxSize}MB`;
		}

		return null;
	};

	const createPreview = (file: File | Blob) => {
		const reader = new FileReader();
		reader.onload = (e) => {
			setPreview(e.target?.result as string);
		};
		reader.readAsDataURL(file);
	};

	const handleFile = (file: File) => {
		// Only check format here, allow large files to open dialog
		if (!acceptedFormats.includes(file.type)) {
			const formatError = `Unsupported format. Please use: ${acceptedFormats
				.map((format) => format.split("/")[1].toUpperCase())
				.join(", ")}`;
			setError(formatError);
			setPreview(null);
			setOriginalFile(null);
			setEditedFile(null);
			onChange(null);
			return;
		}

		setError(null);
		setOriginalFile(file);
		setEditedFile(null);
		// Don't create preview or call onChange yet - wait for edit completion
		setPreview(null);

		setEditDialogOpen(true);
	};

	const onDrop = useCallback((acceptedFiles: File[]) => {
		if (acceptedFiles.length > 0) {
			handleFile(acceptedFiles[0]);
		}
	}, []);

	const { getRootProps, getInputProps, isDragActive } = useDropzone({
		onDrop,
		accept: acceptedFormats.reduce(
			(acc, format) => {
				acc[format] = [];
				return acc;
			},
			{} as Record<string, string[]>,
		),
		// Remove maxSize limit from dropzone - handle it manually in validateFile
		maxFiles: 1,
	});

	const handleEdit = () => {
		if (originalFile) {
			setEditDialogOpen(true);
		}
	};

	const handleEditSave = (uploadedImageUrl: string | null) => {
		if (uploadedImageUrl) {
			// Store the uploaded image URL
			setUploadedUrl(uploadedImageUrl);
			
			// ✅ 直接传递 uploadedImageUrl 给父组件
			onChange(null, uploadedImageUrl);
			
			// 异步加载图片用于预览
			fetch(uploadedImageUrl)
				.then(response => response.blob())
				.then(blob => {
					setEditedFile(blob);
					createPreview(blob);
				})
				.catch(error => {
					console.error('Error loading uploaded image for preview:', error);
					// 如果预览失败，直接使用URL作为预览
					setPreview(uploadedImageUrl);
				});
		} else {
			// Validation failed - clear all state to ensure preview area remains empty
			setPreview(null);
			setError(null);
			setOriginalFile(null);
			setEditedFile(null);
			setUploadedUrl(null);
			onChange(null);
		}
	};

	const handleRemove = () => {
		setPreview(null);
		setError(null);
		setOriginalFile(null);
		setEditedFile(null);
		setUploadedUrl(null);
		onChange(null);
	};

	const currentFile = editedFile || originalFile;
	const formatList = acceptedFormats
		.map((format) => format.split("/")[1].toUpperCase())
		.join(", ");

	return (
		<div className={`w-full ${className}`}>
			{preview && currentFile ? (
				<Card className="relative overflow-hidden border border-gray-200 shadow-sm group">
					<div className="aspect-video bg-gray-50 relative">
						<Image
							src={preview}
							alt="Uploaded image"
							fill
							className="object-contain"
							sizes="(max-width: 768px) 100vw, 50vw"
						/>
						{/* Hover overlay with buttons */}
						<div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center gap-3">
							<Button
								type="button"
								variant="outline"
								size="sm"
								onClick={handleEdit}
								className="h-9 px-4 text-sm bg-white/90 hover:bg-white border-white/20 backdrop-blur-sm"
							>
								<Edit className="h-4 w-4 mr-2" />
								Edit
							</Button>
							<Button
								type="button"
								variant="outline"
								size="sm"
								onClick={handleRemove}
								className="h-9 px-4 text-sm bg-white/90 hover:bg-white border-white/20 backdrop-blur-sm text-red-600 hover:text-red-700"
							>
								<X className="h-4 w-4 mr-2" />
								Remove
							</Button>
						</div>
					</div>
				</Card>
			) : (
				<div
					{...getRootProps()}
					className={`w-full border-2 border-dashed rounded-lg p-6 transition-colors cursor-pointer ${
						isDragActive
							? "border-blue-400 bg-blue-50/10"
							: error
								? "border-red-500 bg-red-50/5"
								: "border-gray-300 hover:border-gray-400 hover:bg-gray-50/50"
					}`}
				>
					<input {...getInputProps()} />
					<div className="flex flex-col items-center justify-center text-center">
						<div className="mb-3 p-3 bg-gray-100 rounded-full">
							{isDragActive ? (
								<Upload size={24} className="text-blue-500" />
							) : (
								<Upload size={24} className="text-gray-500" />
							)}
						</div>
						<p className="mb-2 text-sm font-medium text-gray-700">
							{isDragActive
								? "Drop the image here"
								: "Drag & drop an image here, or click to browse"}
						</p>
						<p className="text-xs text-gray-500 mb-4">
							Supported formats: {formatList} • Max size:{" "}
							{maxSize}MB, with a minimum width/height of 300px.
						</p>
						{error && (
							<p className="mt-3 text-sm text-red-500 font-medium">
								{error}
							</p>
						)}
					</div>
				</div>
			)}

			<ImageEditDialog
				image={originalFile}
				open={editDialogOpen}
				onOpenChange={setEditDialogOpen}
				onSave={handleEditSave}
				aspectRatioOptions={aspectRatioOptions}
			/>
		</div>
	);
}
