# Image Editor Components

这是一套完整的图片编辑组件，基于您的项目技术栈构建，包含图片上传、裁剪、调整等功能。

## 组件结构

```
apps/web/modules/marketing/image-to-video/components/image-editor/
├── ImageEditDialog.tsx          # 图片编辑对话框（核心编辑功能）
├── ImageUploaderWithEditor.tsx  # 集成上传和编辑的组件
└── README.md                    # 说明文档
```

## 主要功能

### 🎨 图片编辑功能
- **裁剪工具**: 支持自由裁剪和多种预设比例
- **比例选择**: 1:1, 16:9, 9:16, 4:3, 3:4, 自由比例
- **图像调整**: 亮度、对比度、饱和度调节
- **实时预览**: 所有调整都有实时预览效果

### 📤 上传功能
- **拖拽上传**: 支持拖拽文件到指定区域
- **格式验证**: 支持 JPEG, PNG, WebP 格式
- **大小限制**: 可配置最大文件大小
- **错误处理**: 完善的错误提示和处理

## 技术实现

### 核心技术栈
- **React Cropper**: 基于 cropperjs 的 React 封装
- **React Dropzone**: 文件拖拽上传
- **Shadcn UI**: UI 组件库
- **Tailwind CSS**: 样式框架
- **TypeScript**: 类型安全

### 依赖包
```json
{
  "react-cropper": "^2.3.3",
  "cropperjs": "^1.6.1", 
  "react-dropzone": "^14.2.3"
}
```

## 使用方法

### 基础使用

```tsx
import { ImageUploaderWithEditor } from "@marketing/image-to-video/components/image-editor/ImageUploaderWithEditor";

function MyComponent() {
  const [file, setFile] = useState<File | Blob | null>(null);

  return (
    <ImageUploaderWithEditor
      onChange={setFile}
      maxSize={10} // 10MB
      acceptedFormats={["image/jpeg", "image/png", "image/webp"]}
    />
  );
}
```

### 高级配置

```tsx
<ImageUploaderWithEditor
  onChange={handleFileChange}
  maxSize={5}
  acceptedFormats={["image/jpeg", "image/png"]}
  className="custom-uploader"
/>
```

### 仅使用编辑对话框

```tsx
import { ImageEditDialog } from "@marketing/image-to-video/components/image-editor/ImageEditDialog";

function MyComponent() {
  const [image, setImage] = useState<File | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  return (
    <ImageEditDialog
      image={image}
      open={dialogOpen}
      onOpenChange={setDialogOpen}
      onSave={(editedImage) => {
        console.log("Edited image:", editedImage);
      }}
    />
  );
}
```

### 集成到现有的 ImageUploader 组件

您可以替换现有的 `ImageUploader.tsx` 或者与其并存使用：

```tsx
// 在 VideoGenerationForm.tsx 中使用
import { ImageUploaderWithEditor } from "./image-editor/ImageUploaderWithEditor";

// 替换原有的 ImageUploader
<ImageUploaderWithEditor
  onChange={(file) => updateFormData("image", file)}
  maxSize={10}
  acceptedFormats={["image/jpeg", "image/png", "image/webp"]}
/>
```

## API 参考

### ImageUploaderWithEditor Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `onChange` | `(file: File \| Blob \| null) => void` | - | 文件变化回调 |
| `maxSize` | `number` | `10` | 最大文件大小(MB) |
| `acceptedFormats` | `string[]` | `["image/jpeg", "image/png", "image/webp"]` | 支持的文件格式 |
| `className` | `string` | `""` | 自定义样式类 |

### ImageEditDialog Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `image` | `File \| null` | - | 要编辑的图片文件 |
| `open` | `boolean` | - | 对话框开启状态 |
| `onOpenChange` | `(open: boolean) => void` | - | 对话框状态变化回调 |
| `onSave` | `(editedImage: Blob \| null) => void` | - | 保存编辑结果回调 |

## 与现有组件的集成

### 替换现有的 ImageUploader

如果您想要完全替换现有的 `ImageUploader.tsx`，可以这样做：

1. **备份现有组件**（如果需要）
2. **更新 VideoGenerationForm.tsx**：

```tsx
// 将这行
import { ImageUploader } from "@marketing/shared/components/ImageUploader";

// 替换为
import { ImageUploaderWithEditor } from "./image-editor/ImageUploaderWithEditor";
```

### 与现有组件并存

如果您想保留现有组件，可以添加一个开关来选择使用哪个组件：

```tsx
const [useAdvancedEditor, setUseAdvancedEditor] = useState(false);

// 在表单中添加切换选项
{useAdvancedEditor ? (
  <ImageUploaderWithEditor
    onChange={(file) => updateFormData("image", file)}
    maxSize={10}
    acceptedFormats={["image/jpeg", "image/png", "image/webp"]}
  />
) : (
  <ImageUploader
    onChange={(file) => updateFormData("image", file)}
    maxSize={10}
    acceptedFormats={["image/jpeg", "image/png", "image/webp"]}
  />
)}
```

## 样式定制

组件使用 Tailwind CSS 构建，您可以通过以下方式定制样式：

1. **传入 className**: 覆盖默认样式
2. **修改 Tailwind 配置**: 调整主题色彩
3. **CSS 变量**: 使用 CSS 变量定制颜色

## 最佳实践

### 1. 文件大小优化
```tsx
// 推荐设置合理的文件大小限制
<ImageUploaderWithEditor maxSize={5} />
```

### 2. 错误处理
```tsx
const handleFileChange = (file: File | Blob | null) => {
  if (file) {
    // 成功处理
    console.log("File uploaded:", file);
  } else {
    // 错误处理
    console.log("Upload failed or cancelled");
  }
};
```

### 3. 性能优化
- 使用 `useCallback` 包装回调函数
- 适当设置图片最大尺寸
- 考虑使用 WebP 格式以减小文件大小

## 扩展功能

### 可以添加的功能
1. **更多滤镜**: 模糊、锐化、复古等
2. **旋转功能**: 90度旋转、自由旋转
3. **文字水印**: 添加文字或图片水印
4. **批量处理**: 支持多张图片同时编辑
5. **撤销重做**: 编辑历史记录功能

### 集成建议
- 与您现有的文件上传系统集成
- 添加进度条显示上传状态
- 集成云存储服务（如 AWS S3）
- 添加图片压缩功能

## 故障排除

### 常见问题

1. **Cropper 不显示**
   - 确保已安装 `cropperjs` 和 `react-cropper`
   - 检查 CSS 是否正确导入

2. **拖拽不工作**
   - 确保 `react-dropzone` 版本兼容
   - 检查浏览器兼容性

3. **图片质量问题**
   - 调整 `getCroppedCanvas` 的参数
   - 使用合适的图片格式和质量设置

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 许可证

遵循项目主许可证。 