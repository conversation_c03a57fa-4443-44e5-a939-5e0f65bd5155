"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { ImageUploaderWithEditor } from "./ImageUploaderWithEditor";
import { ImageUploader } from "../../../shared/components/ImageUploader";
// EndFrameToggle has been removed in favor of direct EndFrameImageUploader
import { useModelOptions } from "../../providers/ModelOptionsProvider";

interface VideoGenerationFormProps {
	onSubmit: () => void;
	isProcessing: boolean;
}

interface FormData {
	image: File | null;
	endFrame: boolean;
}

export function VideoGenerationFormWithEditor({
	onSubmit,
	isProcessing,
}: VideoGenerationFormProps) {
	const t = useTranslations("imageToVideo");
	const { selectedModel } = useModelOptions();
	const [useAdvancedEditor, setUseAdvancedEditor] = useState(false);
	const [formData, setFormData] = useState<FormData>({
		image: null,
		endFrame: false,
	});

	const updateFormData = (key: keyof FormData, value: any) => {
		setFormData(prev => ({ ...prev, [key]: value }));
	};

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		onSubmit();
	};

	return (
		<form onSubmit={handleSubmit} className="flex flex-col h-full">
			<div className="flex-1 overflow-y-auto p-5 space-y-6 custom-scrollbar">
				<div className="space-y-2.5">
					<div className="flex items-center justify-between">
						<h3 className="text-sm font-medium text-gray-700">
							{t("form.imageUpload")}
						</h3>
						<div className="flex items-center gap-3">
							<label className="flex items-center gap-2 text-xs text-gray-600">
								<input
									type="checkbox"
									checked={useAdvancedEditor}
									onChange={(e) => setUseAdvancedEditor(e.target.checked)}
									className="rounded"
								/>
								Advanced Editor
							</label>

							{/* EndFrameToggle removed - now using direct EndFrameImageUploader in form */}
						</div>
					</div>

					{useAdvancedEditor ? (
						<ImageUploaderWithEditor
							onChange={(file) => updateFormData("image", file)}
							maxSize={10}
							acceptedFormats={["image/jpeg", "image/png", "image/webp"]}
						/>
					) : (
						<ImageUploader
							onChange={(file) => updateFormData("image", file)}
							maxSize={10}
							acceptedFormats={["image/jpeg", "image/png", "image/webp"]}
						/>
					)}
				</div>
			</div>
		</form>
	);
}

// Alternative simple integration example
export function SimpleImageEditorExample() {
	const t = useTranslations("imageToVideo");
	const [image, setImage] = useState<File | null>(null);

	return (
		<div className="space-y-2.5">
			<div className="flex items-center justify-between">
				<h3 className="text-sm font-medium text-gray-700">
					{t("form.imageUpload")}
				</h3>
				{/* EndFrameToggle removed - now using direct EndFrameImageUploader in form */}
			</div>
			<ImageUploaderWithEditor
				onChange={(file) => setImage(file)}
				maxSize={10}
				acceptedFormats={["image/jpeg", "image/png", "image/webp"]}
			/>
		</div>
	);
}