import { getTranslations } from "next-intl/server";
import type { Locale } from "@repo/i18n";
import Image from "next/image";

// 需要客户端组件处理视频轮播
import { AnimateImagesShowcaseClient } from "./AnimateImagesShowcaseClient";

interface AnimateImagesShowcaseProps {
  locale: Locale;
}

interface VideoExample {
  id: string;
  originalImage: string;
  videoSrc: string;
  prompt: string;
}

export async function AnimateImagesShowcase({ locale }: AnimateImagesShowcaseProps) {
  const t = await getTranslations({ locale, namespace: "imageToVideo" });

  // 视频示例数据
  const videoExamples: VideoExample[] = [
    {
      id: "1",
      originalImage: "/images/image-to-video/image-01.jpg",
      videoSrc: "/videos/image-to-video/video-01.mp4",
      prompt: "A serene landscape with gentle wind blowing through trees",
    },
    {
      id: "2",
      originalImage: "/images/image-to-video/image-02.jpg",
      videoSrc: "/videos/image-to-video/video-02.mp4",
      prompt: "Ocean waves crashing against rocks with dramatic lighting",
    },
    {
      id: "3",
      originalImage: "/images/image-to-video/image-03.jpg",
      videoSrc: "/videos/image-to-video/video-03.mp4",
      prompt: "City lights twinkling at night with passing cars",
    },
    {
      id: "4",
      originalImage: "/images/image-to-video/image-04.jpg",
      videoSrc: "/videos/image-to-video/video-04.mp4",
      prompt: "Fire dancing with sparks flying in the darkness",
    },
    {
      id: "5",
      originalImage: "/images/image-to-video/image-05.jpg",
      videoSrc: "/videos/image-to-video/video-05.mp4",
      prompt: "Clouds moving across a vibrant sunset sky",
    },
  ];

  return (
    <div className="px-4 pt-12 md:pt-24 lg:pt-[160px]">
      <div className="mx-auto max-w-[980px] text-center">
        <h2 className="text-xl font-bold text-white lg:text-6xl">
          Animate Your Images
        </h2>
        <p className="mt-2 text-sm text-gray-300 lg:mt-4 lg:text-base">
          Transform static images into dynamic videos with AI-powered animation
        </p>
      </div>
      
      {/* 客户端组件处理轮播和视频播放功能 */}
      <AnimateImagesShowcaseClient 
        examples={videoExamples} 
        labels={{
          originalImage: "Original Image",
          prompt: "Prompt",
          video: "Generated Video",
        }} 
      />
    </div>
  );
}
