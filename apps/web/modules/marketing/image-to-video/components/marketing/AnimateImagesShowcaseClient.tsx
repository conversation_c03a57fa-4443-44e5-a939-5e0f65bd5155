"use client";

import { useState } from "react";
import Image from "next/image";

interface VideoExample {
  id: string;
  originalImage: string;
  videoSrc: string;
  prompt: string;
}

interface AnimateImagesShowcaseClientProps {
  examples: VideoExample[];
  labels: {
    originalImage: string;
    prompt: string;
    video: string;
  };
}

export function AnimateImagesShowcaseClient({ 
  examples, 
  labels 
}: AnimateImagesShowcaseClientProps) {
  const [currentIndex, setCurrentIndex] = useState(0);

  const handlePrev = () => {
    setCurrentIndex((prev) => (prev === 0 ? examples.length - 1 : prev - 1));
  };

  const handleNext = () => {
    setCurrentIndex((prev) => (prev === examples.length - 1 ? 0 : prev + 1));
  };

  const currentExample = examples[currentIndex];

  return (
    <div className="relative mx-auto mt-3 flex h-auto max-w-[1320px] items-center justify-center gap-6 lg:mt-14">
      {/* 左侧导航按钮 */}
      <button
        type="button"
        onClick={handlePrev}
        className="absolute start-0.5 z-[2] flex size-10 items-center justify-center rounded-full bg-[#1d1d1d] text-white text-opacity-70 transition-all duration-500 hover:scale-110 hover:bg-rose-500 lg:size-14"
        aria-label="Previous example"
      >
        <span className="i-com--left size-8" />
      </button>

      <div className="w-full xl:max-w-[1160px]">
        <div className="w-full overflow-hidden text-white">
          <div className="flex min-w-0 flex-col items-center gap-5 overflow-hidden text-white xl:flex-row">
            <div className="relative flex w-full flex-row gap-2 md:flex-col md:gap-0 lg:w-[300px] xl:w-[460px]">
              <span className="i-cus--pol-double-arrow absolute -bottom-6 start-32 z-[2] block size-8 rotate-90 text-rose-500 md:hidden md:rotate-0 lg:-end-8 lg:bottom-auto lg:start-auto lg:top-1/2 lg:size-12 lg:-translate-y-1/2 xl:block" />
              
              <div className="border-gray-700 bg-gray-800 w-[156px] rounded-xl border p-1 md:w-full md:p-1.5 md:pb-[42px]">
                <span className="relative z-[1] block w-fit rounded-full bg-[#2d2d2d] px-[14px] py-[4px] text-xs text-white">
                  {labels.originalImage}
                </span>
                <figure className="relative mx-auto -mt-2 h-[150px] w-[133px] object-cover md:mt-3 lg:h-[380px] lg:w-[285px]">
                  <Image 
                    src={currentExample.originalImage}
                    alt="image to video"
                    fill
                    className="object-cover"
                  />
                </figure>
              </div>

              <div className="border-gray-700 bg-gray-800 relative mt-5 w-[300px] flex-1 rounded-xl border p-3 text-sm md:w-full md:px-[30px] md:py-5">
                <p className="font-semibold text-white">{labels.prompt}</p>
                <p className="text-gray-300 mt-2.5">{currentExample.prompt}</p>
              </div>
            </div>

            <div className="bg-gray-800 border-gray-700 size-full overflow-hidden rounded-xl border p-1.5 lg:flex-1">
              <span className="relative z-[2] block w-fit rounded-full bg-[#2d2d2d] px-[14px] py-[4px] text-xs text-white backdrop-blur-3xl">
                {labels.video}
              </span>
              <div className="mx-auto -mt-2 size-full md:mt-3 lg:h-[518px] lg:w-[460px]">
                <video 
                  className="size-full object-contain" 
                  poster={currentExample.originalImage}
                  src={currentExample.videoSrc} 
                  playsInline
                  webkit-playsinline="true"
                  controlsList="nodownload"
                  loop
                  preload="none"
                  controls
                />
              </div>
            </div>
          </div>
        </div>

        <div className="mx-auto mt-4 flex w-fit items-center gap-3">
          {examples.map((_, idx) => (
            <span 
              key={idx}
              onClick={() => setCurrentIndex(idx)}
              className={`size-2 cursor-pointer rounded-full ${
                idx === currentIndex ? "bg-[#d9d9d9]" : "bg-gray-500"
              }`}
              role="button"
              aria-label={`Go to example ${idx + 1}`}
              tabIndex={0}
            />
          ))}
        </div>
      </div>

      {/* 右侧导航按钮 */}
      <button
        type="button"
        onClick={handleNext}
        className="absolute end-0.5 flex size-10 items-center justify-center rounded-full bg-[#1d1d1d] text-white text-opacity-70 transition-all duration-500 hover:scale-110 hover:bg-rose-500 lg:size-14"
        aria-label="Next example"
      >
        <span className="i-com--right size-8" />
      </button>
    </div>
  );
}
