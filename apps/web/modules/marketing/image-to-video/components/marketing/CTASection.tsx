import { getTranslations } from "next-intl/server";
import Link from "next/link";
import Image from "next/image";
import type { Locale } from "@repo/i18n";

interface CTASectionProps {
  locale: Locale;
}

export async function CTASection({ locale }: CTASectionProps) {
  const t = await getTranslations({ locale, namespace: "imageToVideo" });
  
  return (
    <div className="relative overflow-hidden bg-black px-4 py-12 before:absolute before:-top-4 before:left-1/2 before:block before:size-[1000px] before:-translate-x-1/2 before:-translate-y-full before:scale-x-[2] before:rounded-full before:bg-[conic-gradient(from_-90deg_at_50%_50%,#DCFF63_0deg,#7BF4E2_91.8deg,#3A6DF2_185.4deg,#FF5C2E_253.8deg,#FFBE26_302.4deg,#DCFF63_360deg)] before:blur-[100px] after:absolute after:-bottom-6 after:left-1/2 after:block after:size-[1000px] after:-translate-x-1/2 after:translate-y-full after:scale-x-[2] after:rounded-full after:bg-[conic-gradient(from_-90deg_at_50%_50%,#DCFF63_0deg,#7BF4E2_91.8deg,#3A6DF2_185.4deg,#FF5C2E_253.8deg,#FFBE26_302.4deg,#DCFF63_360deg)] after:blur-[100px] md:py-20 lg:py-[160px]">
      <div className="relative z-[2] mx-auto max-w-[900px]">
        <Image
          src="/images/logo-icon.png"
          alt="FluxFly Logo"
          width={96}
          height={96}
          className="mx-auto size-12 lg:size-24"
        />
        <h2 className="mt-3 text-center text-2xl font-bold text-white lg:text-7xl">
          Start Creating Videos Today
        </h2>
        <div className="flex w-fit rounded-full before:rounded-full after:rounded-full p-0.5 relative z-[2] mx-auto mt-6 bg-gradient-to-r from-rose-500 to-purple-500">
          <Link
            href={`/${locale}/image-to-video`}
            className="relative z-[1] px-8 py-4 text-center md:px-12 rounded-full text-white bg-black flex items-center justify-center"
          >
            Get Started Now
          </Link>
        </div>
      </div>
    </div>
  );
}
