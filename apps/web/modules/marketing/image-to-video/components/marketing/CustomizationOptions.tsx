import { getTranslations } from "next-intl/server";
import Link from "next/link";
import Image from "next/image";
import type { Locale } from "@repo/i18n";

interface CustomizationOptionsProps {
  locale: Locale;
}

export async function CustomizationOptions({ locale }: CustomizationOptionsProps) {
  const t = await getTranslations({ locale, namespace: "imageToVideo" });
  
  return (
    <div className="bg-gray-900 px-4 py-12 lg:py-[160px]">
      <div className="mx-auto flex max-w-[1204px] flex-col-reverse items-center gap-8 lg:flex-row lg:gap-16">
        <Image
          src="/images/image-to-video/freely-customizable.jpg"
          alt="Customization interface showing various video generation options"
          width={600}
          height={592}
          className="rounded-lg"
        />
        <div>
          <span className="i-cus--pol-my-images size-9 md:size-14" />
          <h2 className="mt-2 text-xl font-bold text-white md:mt-3 lg:text-6xl">
            Freely Customizable
          </h2>
          <p className="mt-2 text-sm sm:mt-3 md:text-base">
            Take full control of your video generation with advanced customization options. Adjust aspect ratios, motion strength, and styling to create exactly what you envision.
          </p>
          <div className="flex w-fit rounded-full before:rounded-full after:rounded-full p-0.5 relative z-[2] mt-6 bg-gradient-to-r from-rose-500 via-indigo-500 to-amber-500">
            <Link
              href={`/${locale}/image-to-video`}
              className="relative z-[1] px-8 text-center md:px-12 rounded-full text-white bg-black flex items-center justify-center py-3 md:py-4"
            >
              Try It Now
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
