import { getTranslations } from "next-intl/server";
import type { Locale } from "@repo/i18n";

interface FAQSectionProps {
  locale: Locale;
}

interface FAQItem {
  question: string;
  answer: string | React.ReactNode;
}

export async function FAQSection({ locale }: FAQSectionProps) {
  const t = await getTranslations({ locale, namespace: "imageToVideo" });
  
  // FAQ项目列表
  const faqItems: FAQItem[] = [
    {
      question: "What formats are supported for image uploads?",
      answer: "We support JPEG, PNG, and WebP image formats. The maximum file size is 10MB per image.",
    },
    {
      question: "How long does it take to generate a video?",
      answer: "Video generation typically takes 1-3 minutes depending on the complexity and length of your video. You'll receive a notification when your video is ready.",
    },
    {
      question: "What video lengths are available?",
      answer: "You can generate videos ranging from 2 seconds to 10 seconds in length, depending on your selected model and plan.",
    },
    {
      question: "Can I customize the aspect ratio of my videos?",
      answer: "Yes, we offer multiple aspect ratios including 16:9 (landscape), 9:16 (portrait), and 1:1 (square) to suit different platforms and use cases.",
    },
    {
      question: "How many credits does video generation cost?",
      answer: "Credit costs vary based on video length, quality, and model selection. Basic videos start at 1 credit, while premium features may require more credits.",
    },
  ];
  
  // 结构化数据
  const faqStructuredData = {
    "@context": "http://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqItems.map((item) => ({
      "@type": "Question",
      "name": [item.question],
      "acceptedAnswer": {
        "@type": "Answer",
        "text": item.answer as string,
      },
    })),
  };

  return (
    <div className="bg-gray-900 mt-12 py-12 md:mt-20 md:py-20 lg:mt-[160px] lg:py-[160px]">
      {/* 结构化数据 */}
      <script
        id="FAQPage"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqStructuredData) }}
      />
      
      <div className="mx-auto flex max-w-[1198px] px-4 flex-col gap-3 md:items-start md:gap-x-[140px] !max-w-[920px] [&>div]:max-w-full md:[&>h2]:text-center">
        <h2 className="text-white text-center text-xl font-bold md:text-start lg:text-7xl !md:text-center w-full">
          Frequently Asked Questions
        </h2>
        
        <div className="w-full max-w-[700px] space-y-4 md:space-y-9">
          {faqItems.map((item, index) => (
            <details
              key={index}
              className="text-white border-gray-700 before:i-com--right group relative space-y-3 border-b pb-4 pl-9 transition-all before:absolute before:left-0 before:top-0 before:size-6 before:transition-transform before:duration-300 open:before:rotate-90 md:pb-9"
            >
              <summary className="cursor-pointer">
                <h3 className="text-base font-medium md:text-lg">{item.question}</h3>
              </summary>
              <div className="grid grid-rows-[0fr] overflow-hidden opacity-0 transition-[grid-template-rows,opacity] duration-300 ease-in-out group-open:grid-rows-[1fr] group-open:opacity-100">
                <p className="text-gray-300 text-sm md:text-base min-h-0">{item.answer}</p>
              </div>
            </details>
          ))}
        </div>
      </div>
    </div>
  );
}
