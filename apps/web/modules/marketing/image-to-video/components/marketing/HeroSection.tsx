import { getTranslations } from "next-intl/server";
import Link from "next/link";
import type { Locale } from "@repo/i18n";

interface HeroSectionProps {
  locale: Locale;
}

export async function HeroSection({ locale }: HeroSectionProps) {
  const t = await getTranslations({ locale, namespace: "imageToVideo" });

  const modelOptions = [
    { icon: "kling", name: "Kling AI", href: "/m/kling-ai" },
    { icon: "runway", name: "Runway", href: "/m/runway-ai" },
    { icon: "hailuo", name: "Hailuo AI", href: "/m/hailuo-ai" },
    { icon: "vidu", name: "Vidu AI", href: "/m/vidu-ai" },
    { icon: "luma", name: "Luma AI", href: "/m/luma-ai" },
    { icon: "pixverse", name: "Pix<PERSON><PERSON>", href: "/m/pixverse-ai" },
    { icon: "pika", name: "<PERSON><PERSON> AI", href: "/m/pika-ai" },
    { icon: "wanx", name: "Wanx AI", href: "/m/wanx-ai" },
    { icon: "hunyuan", name: "Hunyuan", href: "/m/hunyuan" },
    { icon: "veo", name: "Veo 2", href: "/m/veo/veo-2" },
    { icon: "seaweed", name: "Seaweed", href: "/m/seaweed" },
  ];

  return (
    <div className="mx-auto max-w-[1232px] px-4 mt-2 md:mt-24 lg:mt-[160px]">
      <div className="mx-auto max-w-[880px] text-center">
        <h2 className="text-xl font-bold text-white md:text-6xl">
          Transform Images into Dynamic Videos
        </h2>
        <p className="mt-2 text-sm text-gray-300 md:text-lg">
          Bring your static images to life with AI-powered video generation. Choose from multiple advanced models to create stunning animations.
        </p>
      </div>
      <div className="bg-gray-900 mt-3 rounded-xl px-4 py-8 md:mt-12">
        <div className="flex flex-wrap justify-center gap-4 md:gap-x-8 md:gap-y-4">
          {modelOptions.map((model) => (
            <div key={model.name} className="relative">
              <span className="mx-auto flex w-fit items-center justify-center rounded-md bg-black p-2.5">
                <span className={`size-8 i-com--logo-${model.icon}`} />
              </span>
              <Link 
                href={`/${locale}${model.href}`} 
                className="block pt-2 text-sm text-gray-300 hover:text-rose-500 after:absolute after:inset-0 md:text-lg"
              >
                {model.name}
              </Link>
            </div>
          ))}
        </div>
        <Link 
          href={`/${locale}/m`}
          className="mx-auto mt-4 block w-fit text-lg text-rose-500 hover:text-rose-400 md:text-xl"
        >
          Explore All Models →
        </Link>
      </div>
    </div>
  );
}
