import { getTranslations } from "next-intl/server";
import Link from "next/link";
import type { Locale } from "@repo/i18n";

interface ResourcesSectionProps {
  locale: Locale;
}

interface ResourceLink {
  href: string;
  label: string;
}

export async function ResourcesSection({ locale }: ResourcesSectionProps) {
  const t = await getTranslations({ locale, namespace: "imageToVideo" });
  
  // 资源链接配置
  const resourceLinks: { left: ResourceLink[]; right: ResourceLink[] } = {
    left: [
      {
        href: "/hub/best-ai-image-to-video-generators",
        label: "Best AI Image to Video Generators",
      },
      {
        href: "/m/runway-ai/image-to-video",
        label: "Runway AI Image to Video",
      },
      {
        href: "/m/kling-ai/image-to-video",
        label: "Kling AI Image to Video",
      },
      {
        href: "/hub/how-to-use-kling-ai-image-to-video",
        label: "How to Use Kling AI Image to Video",
      },
      {
        href: "/hub/kling-ai-image-to-video-not-working",
        label: "Kling AI Image to Video Troubleshooting",
      },
    ],
    right: [
      {
        href: "/hub/how-to-use-runway-ai-image-to-video",
        label: "How to Use Runway AI Image to Video",
      },
      {
        href: "/hub/how-to-use-motion-brush",
        label: "How to Use Motion Brush",
      },
      {
        href: "/m/kling-ai/motion-brush",
        label: "Kling AI Motion Brush",
      },
      {
        href: "/m/runway-ai/motion-brush",
        label: "Runway AI Motion Brush",
      },
      {
        href: "/hub/how-to-use-kling-ai-motion-brush",
        label: "How to Use Kling AI Motion Brush",
      },
    ],
  };
  
  return (
    <div className="mx-auto max-w-[1112px] px-4 py-16 lg:py-24">
      <div className="mx-auto max-w-[880px] text-center">
        <h2 className="text-xl font-bold text-white md:text-6xl">
          {t("resources.title")}
        </h2>
        <p className="text-gray-300 text-sm md:pt-3 md:text-lg">
          {t("resources.description")}
        </p>
      </div>
      
      <div className="flex flex-col gap-4 pt-3 md:flex-row md:pt-10">
        {/* Left column */}
        <div className="bg-gray-900 flex w-full flex-col gap-3 rounded-lg p-4 md:p-6">
          {resourceLinks.left.map((link) => (
            <Link
              key={link.href}
              href={`/${locale}${link.href}`}
              className="text-gray-300 hover:text-white group flex items-center gap-3 text-sm md:text-base"
            >
              <span className="i-com--right-arrow text-emerald-500 size-4 shrink-0 md:size-5" />
              <span className="group-hover:underline">{link.label}</span>
            </Link>
          ))}
        </div>
        
        {/* Right column */}
        <div className="bg-gray-900 flex w-full flex-col gap-3 rounded-lg p-4 md:p-6">
          {resourceLinks.right.map((link) => (
            <Link
              key={link.href}
              href={`/${locale}${link.href}`}
              className="text-gray-300 hover:text-white group flex w-fit items-center gap-3 text-sm md:text-base"
            >
              <span className="i-com--right-arrow text-emerald-500 size-4 shrink-0 md:size-5" />
              <span className="group-hover:underline">{link.label}</span>
            </Link>
          ))}
        </div>
      </div>
      
      <Link
        href={`/${locale}/hub`}
        className="text-rose-500 group flex items-center justify-center gap-[2px] pt-3"
      >
        <span className="text-sm font-medium md:text-base">
          {t("resources.moreResourcesLink")}
        </span>
        <span className="i-com--right size-[22px] duration-300 group-hover:translate-x-3" />
      </Link>
    </div>
  );
}
