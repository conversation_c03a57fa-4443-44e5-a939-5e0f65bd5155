import { getTranslations } from "next-intl/server";
import Link from "next/link";
import type { Locale } from "@repo/i18n";

interface TutorialSectionProps {
  locale: Locale;
}

export async function TutorialSection({ locale }: TutorialSectionProps) {
  const t = await getTranslations({ locale, namespace: "imageToVideo" });
  
  // YouTube视频ID
  const videoId = "ZWM-esKmBc0";
  
  // 结构化数据
  const videoStructuredData = {
    "@context": "http://schema.org",
    "@type": "VideoObject",
    "name": t("tutorial.videoTitle"),
    "uploadDate": "2025-03-13T06:33:16.596Z",
    "thumbnailUrl": `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`,
    "description": t("tutorial.videoDescription"),
    "contentUrl": `https://www.youtube.com/embed/${videoId}`,
    "embedUrl": `https://www.youtube.com/embed/${videoId}`
  };
  
  return (
    <div className="bg-gray-800 px-4 py-12 lg:py-[160px]">
      <div className="mx-auto max-w-[1196px] text-center">
        <h2 className="mx-auto max-w-[800px] text-xl font-bold text-white lg:text-6xl">
          {t("tutorial.title")}
        </h2>
        <p className="mt-3 text-sm text-gray-300 md:text-lg">
          {t("tutorial.description")}
        </p>
        
        <div className="mt-6 lg:mt-10 flex flex-col-reverse items-center gap-6 xl:flex-row xl:gap-24">
          {/* 视频结构化数据 */}
          <script
            id="VideoObject"
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: JSON.stringify(videoStructuredData) }}
          />
          
          <iframe
            className="shrink-0 w-full md:max-w-[720px]"
            src={`https://www.youtube.com/embed/${videoId}`}
            width="720"
            height="400"
            title={t("tutorial.videoTitle")}
            allowFullScreen
          />
          
          <div className="flex flex-col gap-4 md:gap-5">
            {[1, 2, 3].map((step) => (
              <div 
                key={step}
                className="bg-gray-900 border-gray-700 flex-1 rounded-xl border p-4 text-start md:p-6"
              >
                <p className="font-semibold text-sm lg:text-xl">
                  {t("tutorial.steps.stepLabel", { number: step })}
                </p>
                <p className="text-gray-300 mt-2 text-sm lg:text-base">
                  {(() => {
                    switch (step) {
                      case 1:
                        return t('tutorial.steps.1');
                      case 2:
                        return t('tutorial.steps.2');
                      case 3:
                        return t('tutorial.steps.3');
                      default:
                        return '';
                    }
                  })()}
                </p>
              </div>
            ))}
          </div>
        </div>
        
        <div className="flex w-fit rounded-full before:rounded-full after:rounded-full p-0.5 relative z-[2] mx-auto mt-10 bg-gradient-to-r from-rose-500 via-indigo-500 to-amber-500">
          <Link
            href={`/${locale}/image-to-video`}
            className="relative z-[1] px-8 text-center md:px-12 rounded-full text-white bg-black flex items-center justify-center py-3 md:py-4"
          >
            {t("tutorial.ctaButton")}
          </Link>
        </div>
      </div>
    </div>
  );
}
