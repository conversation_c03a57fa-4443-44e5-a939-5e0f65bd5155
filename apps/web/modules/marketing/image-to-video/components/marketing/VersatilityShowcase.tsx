import { getTranslations } from "next-intl/server";
import Link from "next/link";
import type { Locale } from "@repo/i18n";

interface VersatilityShowcaseProps {
  locale: Locale;
}

export async function VersatilityShowcase({ locale }: VersatilityShowcaseProps) {
  const t = await getTranslations({ locale, namespace: "imageToVideo" });
  
  return (
    <div className="px-4 py-12 lg:py-[160px]">
      <div className="mx-auto max-w-[880px] text-center">
        <h2 className="text-xl font-bold text-white lg:text-6xl">
          {t("versatility.title")}
        </h2>
        <p className="mt-2 text-sm md:mt-3 md:text-base">
          {t("versatility.description")}
        </p>
      </div>
      
      <div className="flex w-fit rounded-full before:rounded-full after:rounded-full relative z-[2] mx-auto mt-6 p-0.5 bg-gradient-to-r from-rose-500 via-indigo-500 to-amber-500">
        <Link 
          href={`/${locale}/image-to-video`}
          className="relative z-[1] px-8 text-center md:px-12 rounded-full text-white bg-black flex items-center justify-center py-3 md:py-4"
        >
          {t("versatility.ctaButton")}
        </Link>
      </div>
      
      <div className="mx-auto mt-12 aspect-[3/2] size-full overflow-hidden rounded-xl lg:h-[498px] lg:w-[900px] lg:rounded-3xl">
        <video 
          className="size-full object-cover" 
          poster="/images/image-to-video/create-any-video.png" 
          src="/videos/image-to-video/create-any.mp4" 
          playsInline
          webkit-playsinline="true"
          controlsList="nodownload"
          loop
          preload="none"
          controls
        />
      </div>
    </div>
  );
}
