// apps/web/modules/tooling/image-to-video/components/modals/PromptIdeaInputModal.tsx
"use client";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@ui/components/dialog";
import { Button } from "@ui/components/button";
import { Textarea } from "@ui/components/textarea";
import { useTranslations } from "next-intl";
import { useState } from "react";

interface PromptIdeaInputModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (prompt: string) => void;
}

export function PromptIdeaInputModal({
  open,
  onClose,
  onSubmit,
}: PromptIdeaInputModalProps) {
  const t = useTranslations("imageToVideo.prompt");
  const [promptText, setPromptText] = useState("");

  const handleSubmit = () => {
    if (promptText.trim()) {
      onSubmit(promptText.trim());
      setPromptText("");
      onClose();
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{t("ideaTitle")}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <Textarea
            value={promptText}
            onChange={(e) => setPromptText(e.target.value)}
            placeholder={t("ideaPlaceholder")}
            className="min-h-[100px]"
          />
        </div>
        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onClose}>
            {t("cancel")}
          </Button>
          <Button onClick={handleSubmit}>
            {t("submit")}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}