// apps/web/modules/tooling/image-to-video/components/modals/PromptSelectionModal.tsx (continued)
"use client";

import { But<PERSON> } from "@ui/components/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogFooter,
	<PERSON><PERSON>Header,
	DialogTitle,
} from "@ui/components/dialog";
import { Copy } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";

interface PromptSelectionModalProps {
	open: boolean;
	prompts: string[];
	onClose: () => void;
	onSelect: (prompt: string) => void;
}

export function PromptSelectionModal({
	open,
	prompts,
	onClose,
	onSelect,
}: PromptSelectionModalProps) {
	const t = useTranslations("imageToVideo.modal.promptSelection");
	const [selectedPrompt, setSelectedPrompt] = useState<string | null>(null);

	const handleSelect = (prompt: string) => {
		setSelectedPrompt(prompt);
	};

	const handleConfirm = () => {
		if (selectedPrompt) {
			onSelect(selectedPrompt);
			setSelectedPrompt(null);
		}
	};

	const handleCopy = (prompt: string) => {
		navigator.clipboard.writeText(prompt);
	};

	const handleGenerateMore = () => {
		// 这里可以添加生成更多提示词的逻辑
		// 简单模拟更新列表
		const morePrompts = [...prompts];
		morePrompts.push(
			"Two playful groundhogs chasing each other through tunnels, popping their heads up from different holes in the ground.",
		);
		morePrompts.push(
			"A close-up of groundhogs playing with blades of grass, their whiskers twitching as they explore their surroundings.",
		);

		// 这里可以调用API生成更多提示词
	};

	const handleClose = () => {
		setSelectedPrompt(null);
		onClose();
	};

	return (
		<Dialog open={open} onOpenChange={handleClose}>
			<DialogContent className="sm:max-w-[600px]">
				<DialogHeader>
					<DialogTitle>{t("title")}</DialogTitle>
				</DialogHeader>

				<div className="py-4 max-h-[400px] overflow-y-auto">
					<div className="space-y-2">
						{prompts.map((prompt, index) => (
							<div
								key={index}
								className={`p-3 border rounded-lg relative ${
									selectedPrompt === prompt
										? "border-red-500 bg-red-50/10"
										: "border-gray-200"
								}`}
							>
								<div className="flex items-start">
									<input
										type="radio"
										name="promptSelection"
										checked={selectedPrompt === prompt}
										onChange={() => handleSelect(prompt)}
										className="mt-1 mr-3"
									/>
									<p className="text-sm flex-1 pr-8">
										{prompt}
									</p>
									<button
										type="button"
										className="absolute right-3 top-3 hover:bg-gray-100 p-1 rounded"
										onClick={() => handleCopy(prompt)}
									>
										<Copy size={16} />
									</button>
								</div>
							</div>
						))}
					</div>
				</div>

				<div className="flex justify-center mb-2">
					<Button variant="ghost" onClick={handleGenerateMore}>
						{t("generateMore")}
					</Button>
				</div>

				<DialogFooter>
					<Button variant="outline" onClick={handleClose}>
						{t("cancel")}
					</Button>
					<Button onClick={handleConfirm} disabled={!selectedPrompt}>
						{t("confirm")}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
