import { useMemo } from "react";
import { 
  calculateCredits, 
  type CreditCalculationInput,
  CreditCalculationError
} from "@repo/utils";
import type { ModelMode, VideoModel } from "@repo/config/vmodel/types";

interface CreditCalculationParams {
	selectedModel: VideoModel | null;
	selectedMode: ModelMode | null;
	videoLength: number | null;
	aspectRatio: string | null;
	outputNumber: number;
	resolution?: string | null;
	style?: string | null;
}

export interface CreditCalculationResult {
	totalCredits: number;
	singleCredits: number;
	pricingKey: string | null;
	calculationMethod: "exact" | "partial" | "fallback";
	details?: string;
}

export function useCreditCalculation({
	selectedModel,
	selectedMode,
	videoLength,
	aspectRatio,
	outputNumber = 1,
	resolution,
	style,
}: CreditCalculationParams): CreditCalculationResult {
	return useMemo(() => {
		// 如果没有选择模型，返回默认值
		if (!selectedModel) {
			return {
				totalCredits: 0,
				singleCredits: 0,
				pricingKey: null,
				calculationMethod: "fallback",
			};
		}

		// 从新配置中获取定价信息
		const pricingConfig = selectedModel.pricingConfig;
		if (!pricingConfig) {
			// 如果没有定价配置，返回默认值
			return {
				totalCredits: 5 * outputNumber,
				singleCredits: 5,
				pricingKey: null,
				calculationMethod: "fallback",
				details: `No pricing config found, using fallback: 5 × ${outputNumber}`,
			};
		}

		// 使用共享的积分计算函数
		try {
			const calculationInput: CreditCalculationInput = {
				modelConfig: selectedModel,
				params: {
					duration: videoLength || undefined,
					modeCode: selectedMode?.code,
					resolution: resolution || undefined,
					style: style || undefined,
				}
			};

			const result = calculateCredits(calculationInput);
			const singleCredits = result.totalCredits;
			const totalCredits = singleCredits * outputNumber;

			// 构建计算详情
			const calculationDetails = [`Base credits: ${pricingConfig.baseCredits}`];
			result.appliedMultipliers.forEach(multiplier => {
				calculationDetails.push(`${multiplier.type} (${multiplier.value}): ×${multiplier.multiplier}`);
			});

		// 构建定价键用于调试
		const pricingKeyParts = ['image-to-video', selectedModel.code];
		if (selectedMode) pricingKeyParts.push(selectedMode.code);
		if (resolution) pricingKeyParts.push(resolution);
		if (videoLength) pricingKeyParts.push(videoLength.toString());
		if (style) pricingKeyParts.push(style);
		const pricingKey = pricingKeyParts.join('_');

			return {
				totalCredits,
				singleCredits,
				pricingKey,
				calculationMethod: "exact" as const,
				details: `${calculationDetails.join(' → ')} = ${singleCredits} × ${outputNumber} = ${totalCredits}`,
			};
		} catch (error) {
			console.error('Credit calculation error:', error);
			return {
				totalCredits: 5 * outputNumber,
				singleCredits: 5,
				pricingKey: null,
				calculationMethod: "fallback" as const,
				details: `Calculation error, using fallback: 5 × ${outputNumber}`,
			};
		}
	}, [
		selectedModel,
		selectedMode,
		videoLength,
		aspectRatio,
		outputNumber,
		resolution,
		style,
	]);
}
