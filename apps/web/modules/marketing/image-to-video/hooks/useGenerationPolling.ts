import { useEffect, useCallback } from "react";

interface UseGenerationPollingOptions {
  jobId: string;
  enabled?: boolean;
  interval?: number; // 轮询间隔（毫秒）
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
}

export function useGenerationPolling({
  jobId,
  enabled = true,
  interval = 3000, // 默认3秒
  onSuccess,
  onError,
}: UseGenerationPollingOptions) {
  const fetchJobStatus = useCallback(async () => {
    try {
      const response = await fetch(`/api/jobs/${jobId}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch job status: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // 检查是否有完成的generation
      const generation = data.generations?.[0];
      if (generation) {
        if (generation.status === "completed" || generation.status === "failed") {
          onSuccess?.(data);
          return true; // 停止轮询
        }
      }
      
      return false; // 继续轮询
    } catch (error) {
      onError?.(error as Error);
      return true; // 出错时停止轮询
    }
  }, [jobId, onSuccess, onError]);

  useEffect(() => {
    if (!enabled || !jobId) return;

    let timeoutId: NodeJS.Timeout;
    let isActive = true;

    const poll = async () => {
      if (!isActive) return;
      
      const shouldStop = await fetchJobStatus();
      
      if (!shouldStop && isActive) {
        timeoutId = setTimeout(poll, interval);
      }
    };

    // 立即执行第一次
    poll();

    return () => {
      isActive = false;
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [enabled, jobId, interval, fetchJobStatus]);
}