"use client";

import { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";

interface UseImageUploaderOptions {
	maxSize: number;
	acceptedFormats: string[];
	onChange: (file: File | Blob | null, uploadedUrl?: string) => void;
}

export function useImageUploader({
	maxSize,
	acceptedFormats,
	onChange,
}: UseImageUploaderOptions) {
	// 所有编辑相关的状态（从 ImageUploaderWithEditor 提取）
	const [originalFile, setOriginalFile] = useState<File | null>(null);
	const [editedFile, setEditedFile] = useState<Blob | null>(null);
	const [uploadedUrl, setUploadedUrl] = useState<string | null>(null);
	const [preview, setPreview] = useState<string | null>(null);
	const [editDialogOpen, setEditDialogOpen] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// 文件验证（与 ImageUploaderWithEditor 保持一致）
	const validateFile = (file: File): string | null => {
		if (!acceptedFormats.includes(file.type)) {
			return `Unsupported format. Please use: ${acceptedFormats
				.map((format) => format.split("/")[1].toUpperCase())
				.join(", ")}`;
		}

		if (file.size > maxSize * 1024 * 1024) {
			return `File too large. Maximum size: ${maxSize}MB`;
		}

		return null;
	};

	// 创建预览（与 ImageUploaderWithEditor 保持一致）
	const createPreview = (file: File | Blob) => {
		const reader = new FileReader();
		reader.onload = (e) => {
			setPreview(e.target?.result as string);
		};
		reader.readAsDataURL(file);
	};

	// 处理文件上传（从 ImageUploaderWithEditor 复制）
	const handleFile = (file: File) => {
		// 只检查格式，允许大文件进入编辑对话框
		if (!acceptedFormats.includes(file.type)) {
			const formatError = `Unsupported format. Please use: ${acceptedFormats
				.map((format) => format.split("/")[1].toUpperCase())
				.join(", ")}`;
			setError(formatError);
			setPreview(null);
			setOriginalFile(null);
			setEditedFile(null);
			onChange(null);
			return;
		}

		setError(null);
		setOriginalFile(file);
		setEditedFile(null);
		// 不立即创建预览，等待编辑完成
		setPreview(null);

		// 强制打开编辑对话框
		setEditDialogOpen(true);
	};

	// Dropzone 回调
	const onDrop = useCallback((acceptedFiles: File[]) => {
		if (acceptedFiles.length > 0) {
			handleFile(acceptedFiles[0]);
		}
	}, [acceptedFormats, maxSize]);

	// Dropzone 配置（与 ImageUploaderWithEditor 保持一致）
	const { getRootProps, getInputProps, isDragActive } = useDropzone({
		onDrop,
		accept: acceptedFormats.reduce(
			(acc, format) => {
				acc[format] = [];
				return acc;
			},
			{} as Record<string, string[]>,
		),
		// 移除 maxSize 限制，在 validateFile 中手动处理
		maxFiles: 1,
	});

	// 编辑按钮点击
	const handleEdit = () => {
		if (originalFile) {
			setEditDialogOpen(true);
		}
	};

	// 编辑保存回调（从 ImageUploaderWithEditor 复制的关键逻辑）
	const handleEditSave = (uploadedImageUrl: string | null) => {
		if (uploadedImageUrl) {
			// 存储上传的URL
			setUploadedUrl(uploadedImageUrl);
			
			// ✅ 直接传递 uploadedImageUrl 给父组件
			onChange(null, uploadedImageUrl);
			
			// 异步加载图片用于预览
			fetch(uploadedImageUrl)
				.then(response => response.blob())
				.then(blob => {
					setEditedFile(blob);
					createPreview(blob);
				})
				.catch(error => {
					console.error('Error loading uploaded image for preview:', error);
					// 如果预览失败，直接使用URL作为预览
					setPreview(uploadedImageUrl);
				});
		} else {
			// 验证失败 - 清除所有状态，确保预览区域保持空白
			setPreview(null);
			setError(null);
			setOriginalFile(null);
			setEditedFile(null);
			setUploadedUrl(null);
			onChange(null);
		}
	};

	// 删除图片
	const handleRemove = () => {
		setPreview(null);
		setError(null);
		setOriginalFile(null);
		setEditedFile(null);
		setUploadedUrl(null);
		onChange(null);
	};

	return {
		// 状态
		originalFile,
		editedFile,
		uploadedUrl,
		preview,
		editDialogOpen,
		error,
		
		// 方法
		handleFile,
		handleEdit,
		handleEditSave,
		handleRemove,
		onDrop,
		setEditDialogOpen,
		
		// Dropzone 属性
		getRootProps,
		getInputProps,
		isDragActive,
		
		// 计算属性
		currentFile: editedFile || originalFile,
	};
}