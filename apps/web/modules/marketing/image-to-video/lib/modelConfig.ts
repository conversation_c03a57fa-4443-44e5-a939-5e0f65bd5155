// 从主配置导入并重新导出视频模型配置
// 这是一个适配器模式，使我们能够在应用中使用配置中心的数据

import { videoModelConfig } from "@repo/config/vmodel";
import type {
	ModelMode,
	SupportedFeature,
	VideoModel,
} from "@repo/config/vmodel/types";

// 重新导出类型和配置
export { videoModelConfig };

// 提供辅助函数获取默认模型
export function getDefaultModel(): VideoModel {
	const activeModels = videoModelConfig.models.filter(
		(model) => model.isActive,
	);
	return activeModels[0]; // 选择第一个激活的模型作为默认模型
}

// 提供辅助函数获取默认模型（根据功能过滤）
export function getDefaultModelForFeature(
	feature: SupportedFeature,
): VideoModel {
	const filteredModels = getModelsForFeature(feature);
	return filteredModels[0]; // 选择第一个模型作为默认模型
}

// 提供辅助函数根据功能类型过滤模型
export function getModelsForFeature(feature: SupportedFeature): VideoModel[] {
	return videoModelConfig.models.filter(
		(model: VideoModel) =>
			model.isActive && model.supportedFeatures.includes(feature),
	);
}

// 提供辅助函数获取默认模式
export function getDefaultMode(model: VideoModel): ModelMode | null {
	if (!model?.modes?.length) {
		return null;
	}
	// 选择第一个模式作为默认模式，或者选择标记为默认的模式
	return (
		model.modes.find((mode: ModelMode) => mode.isDefault) || model.modes[0]
	);
}

// 检查选项是否支持结束帧
export function checkCanUseEndFrame(
	model: VideoModel,
	mode: ModelMode | null,
): boolean {
	if (!model) {
		return false;
	}

	// 如果模型有模式
	if (model.modes?.length) {
		// 如果有选择模式，检查该模式是否支持
		if (mode) {
			return mode.supportsEndFrame ?? model.supportsEndFrame;
		}
		// 检查是否有任何模式支持
		return !!model.modes.some((m: ModelMode) => m.supportsEndFrame);
	}

	// 没有模式，直接检查模型
	return model.supportsEndFrame;
}

// 获取模型的预估生成时间（秒）
export function getModelGenerationTime(model: VideoModel | null): number {
	if (!model) {
		return 60; // 默认1分钟
	}
	
	return model.generationTimeSeconds || 60;
}

// 根据模型代码获取预估生成时间
export function getGenerationTimeByModelCode(modelCode: string): number {
	const model = videoModelConfig.models.find(m => m.code === modelCode);
	return getModelGenerationTime(model || null);
}
