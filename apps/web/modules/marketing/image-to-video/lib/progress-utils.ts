/**
 * 基于时间估算计算假进度条
 * 模拟竞争对手的进度条体验
 */
export function calculateTimeBasedProgress(
  startTime: number, 
  estimatedTimeSeconds: number,
  currentTime: number = Date.now()
): number {
  if (!startTime || estimatedTimeSeconds <= 0) {
    return 0;
  }

  // 计算已流逝的时间（秒）
  const elapsedSeconds = (currentTime - startTime) / 1000;
  
  // 计算原始进度百分比
  let rawProgress = (elapsedSeconds / estimatedTimeSeconds) * 100;
  
  // 添加非线性减速效果，模拟真实AI处理
  let adjustedProgress;
  
  if (rawProgress < 10) {
    // 前10%快速启动
    adjustedProgress = rawProgress * 1.2;
  } else if (rawProgress < 60) {
    // 10-60%正常速度  
    adjustedProgress = 12 + (rawProgress - 10) * 1.0;
  } else if (rawProgress < 85) {
    // 60-85%轻微减速
    adjustedProgress = 62 + (rawProgress - 60) * 0.8;
  } else {
    // 85%+大幅减速，最多到95%
    adjustedProgress = Math.min(82 + (rawProgress - 85) * 0.2, 95);
  }
  
  // 添加轻微随机波动增加真实感
  const randomVariation = (Math.random() - 0.5) * 1; // ±0.5%
  adjustedProgress = Math.max(0, Math.min(95, adjustedProgress + randomVariation));
  
  return Math.round(adjustedProgress);
}

/**
 * 检查任务是否应该显示进度条
 */
export function shouldShowProgress(status: string): boolean {
  return status === 'waiting' || status === 'processing';
}

/**
 * 获取进度条对应的状态文本
 */
export function getProgressStatusText(progress: number, status: string): string {
  if (status === 'waiting') {
    return 'Queued for processing...';
  }
  
  if (progress < 10) {
    return 'Initializing AI model...';
  } else if (progress < 30) {
    return 'Analyzing image...';
  } else if (progress < 60) {
    return 'Generating keyframes...';
  } else if (progress < 85) {
    return 'Rendering video...';
  } else {
    return 'Finalizing output...';
  }
}