/**
 * Frontend upload utilities for image-to-video functionality
 */

export interface UploadResponse {
	sign: string;
	expired: number;
	accessURL: string;
}

export interface UploadError extends Error {
	code?: string;
	status?: number;
}

/**
 * Upload an image or video file to R2 storage
 * @param file - File or Blob to upload
 * @param originalFilename - Original filename (used for type detection)
 * @param type - File type ('image' or 'video')
 * @returns Promise<string> - Access URL of uploaded file
 */
export const uploadFile = async (
	file: File | Blob,
	originalFilename: string,
	type: 'image' | 'video'
): Promise<string> => {
	try {
		// 1. Get signed upload URL
		const requestBody = {
			filename: originalFilename,
			type: type,
		};
		
		console.log('Upload request:', requestBody);
		
		const response = await fetch('/api/upload/sign', {
			method: 'POST',
			credentials: 'include',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify(requestBody),
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error('Upload sign error:', response.status, errorText);
			const error = new Error(`Failed to get upload URL: ${response.status} ${errorText}`) as UploadError;
			error.status = response.status;
			throw error;
		}

		const { sign, accessURL }: UploadResponse = await response.json();

		// 2. Upload file directly to R2
		const uploadResponse = await fetch(sign, {
			method: 'PUT',
			body: file,
			headers: {
				'Content-Type': file.type || (type === 'image' ? 'image/png' : 'video/mp4'),
			},
		});

		if (!uploadResponse.ok) {
			const error = new Error('Failed to upload file') as UploadError;
			error.status = uploadResponse.status;
			throw error;
		}

		return accessURL;
	} catch (error) {
		if (error instanceof Error) {
			throw error;
		}
		throw new Error('Unknown upload error');
	}
};

/**
 * Upload an image file
 * @param file - Image file or blob
 * @param originalFilename - Original filename
 * @returns Promise<string> - Access URL of uploaded image
 */
export const uploadImage = async (
	file: File | Blob,
	originalFilename: string = 'image.png'
): Promise<string> => {
	return uploadFile(file, originalFilename, 'image');
};

/**
 * Upload a video file
 * @param file - Video file or blob
 * @param originalFilename - Original filename
 * @returns Promise<string> - Access URL of uploaded video
 */
export const uploadVideo = async (
	file: File | Blob,
	originalFilename: string = 'video.mp4'
): Promise<string> => {
	return uploadFile(file, originalFilename, 'video');
}; 