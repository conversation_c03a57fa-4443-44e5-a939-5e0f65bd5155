// apps/web/modules/tooling/image-to-video/providers/ModelOptionsProvider.tsx
"use client";

import type { ModelMode, VideoModel } from "@repo/config/vmodel/types";
import {
	type ReactNode,
	createContext,
	useContext,
	useEffect,
	useMemo,
	useState,
} from "react";
import {
	getDefaultMode,
	getDefaultModelForFeature,
	getModelsForFeature,
} from "../lib/modelConfig";

interface ModelOptionsContextType {
	models: VideoModel[];
	selectedModel: VideoModel;
	selectedMode: ModelMode | null;
	setSelectedModel: (model: VideoModel) => void;
	setSelectedMode: (mode: ModelMode | null) => void;
	videoLength: number | null;
	setVideoLength: (length: number | null) => void;
	aspectRatio: string | null;
	setAspectRatio: (ratio: string | null) => void;
	style: string | null;
	setStyle: (style: string | null) => void;
	resolution: string | null;
	setResolution: (resolution: string | null) => void;
}

const ModelOptionsContext = createContext<ModelOptionsContextType | undefined>(
	undefined,
);

interface ModelOptionsProviderProps {
	children: ReactNode;
	feature?: "image-to-video" | "text-to-video"; // 添加功能类型参数
}

export function ModelOptionsProvider({
	children,
	feature = "image-to-video",
}: ModelOptionsProviderProps) {
	// 根据功能类型获取过滤后的模型列表
	const models = useMemo(() => getModelsForFeature(feature), [feature]);

	// 获取默认模型和模式
	const defaultModel = useMemo(
		() => getDefaultModelForFeature(feature),
		[feature],
	);
	const defaultMode = useMemo(
		() => getDefaultMode(defaultModel),
		[defaultModel],
	);

	// 状态管理
	const [selectedModel, setSelectedModel] =
		useState<VideoModel>(defaultModel);
	const [selectedMode, setSelectedMode] = useState<ModelMode | null>(
		defaultMode,
	);
	const [videoLength, setVideoLength] = useState<number | null>(
		selectedModel.videoLengthOptions?.length
			? selectedModel.videoLengthOptions[0]
			: null,
	);
	const [aspectRatio, setAspectRatio] = useState<string | null>(
		selectedModel.aspectRatioOptions?.length
			? selectedModel.aspectRatioOptions[0]
			: null,
	);
	const [style, setStyle] = useState<string | null>(
		selectedModel.styleOptions?.length
			? selectedModel.styleOptions[0].code
			: null,
	);
	const [resolution, setResolution] = useState<string | null>(
		selectedModel.resolutionOptions?.length
			? selectedModel.resolutionOptions[0]
			: null,
	);

	// 当功能类型改变时，重新设置默认模型
	useEffect(() => {
		const newDefaultModel = getDefaultModelForFeature(feature);
		setSelectedModel(newDefaultModel);
	}, [feature]);

	// 当模型改变时更新模式和其他选项
	useEffect(() => {
		// 重置模式选择 - 使用适配器函数
		const newMode = getDefaultMode(selectedModel);
		setSelectedMode(newMode);


		// 更新视频长度
		if (selectedModel.videoLengthOptions?.length) {
			setVideoLength(selectedModel.videoLengthOptions[0]);
		} else {
			setVideoLength(null);
		}

		// 更新宽高比
		if (selectedModel.aspectRatioOptions?.length) {
			setAspectRatio(selectedModel.aspectRatioOptions[0]);
		} else {
			setAspectRatio(null);
		}

		// 更新风格
		if (selectedModel.styleOptions?.length) {
			setStyle(selectedModel.styleOptions[0].code);
		} else {
			setStyle(null);
		}

		// 更新分辨率
		if (selectedModel.resolutionOptions?.length) {
			setResolution(selectedModel.resolutionOptions[0]);
		} else {
			setResolution(null);
		}
	}, [selectedModel]);


	const value: ModelOptionsContextType = {
		models,
		selectedModel,
		selectedMode,
		setSelectedModel,
		setSelectedMode,
		videoLength,
		setVideoLength,
		aspectRatio,
		setAspectRatio,
		style,
		setStyle,
		resolution,
		setResolution,
	};

	return (
		<ModelOptionsContext.Provider value={value}>
			{children}
		</ModelOptionsContext.Provider>
	);
}

// 自定义钩子
export function useModelOptions() {
	const context = useContext(ModelOptionsContext);
	if (context === undefined) {
		throw new Error(
			"useModelOptions must be used within a ModelOptionsProvider",
		);
	}
	return context;
}
