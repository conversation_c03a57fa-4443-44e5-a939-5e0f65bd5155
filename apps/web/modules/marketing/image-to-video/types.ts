// 从配置中心导入视频模型类型
import type { VideoModel, ModelMode } from "@repo/config/vmodel/types";

// 重导出这些类型，使其在应用中可用
export type { VideoModel, ModelMode };

// 应用特定类型
export type VideoStatus = "waiting" | "queued" | "processing" | "completed" | "failed";
export type VideoMode = "standard" | "professional";

export interface VideoType {
  id: string;
  prompt: string;
  thumbnailUrl?: string;
  url?: string;
  status: VideoStatus;
  mode: VideoMode;
  createdAt: string;
  updatedAt: string;
  isFavorite: boolean;
  isPublic: boolean;
  hasCopyProtection: boolean;
  userId: string;
}

export interface VideoGenerationParams {
  image: File;
  prompt: string;
  mode: VideoMode;
  promptStrength: number;
  videoLength: number;
  outputCount: number;
  isPublic: boolean;
  hasCopyProtection: boolean;
}
