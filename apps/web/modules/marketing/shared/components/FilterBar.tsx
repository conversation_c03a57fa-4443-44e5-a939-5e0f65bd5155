'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { Checkbox } from '@ui/components/checkbox';
import { Button } from '@ui/components/button';
import { ChevronDown, ChevronUp, CopyCheck, X, Star, Download, Trash2, LogOut } from 'lucide-react';

interface FilterBarProps {
  onFiltersChange: (filters: FilterState) => void;
  onSelectModeToggle: (enabled: boolean) => void;
  selectedCount: number;
  totalCount?: number;
  onSelectAll?: () => void;
  // 新增批量操作props
  onBulkFavorite?: () => void;
  onBulkDelete?: () => void;
  onBulkDownload?: () => void;
  bulkOperationLoading?: boolean;
}

interface FilterState {
  favorites: boolean;
  publishStatus?: 'reviewing' | 'published' | 'rejected';
}

export function FilterBar({ 
  onFiltersChange, 
  onSelectModeToggle, 
  selectedCount, 
  totalCount = 0, 
  onSelectAll,
  onBulkFavorite,
  onBulkDelete,
  onBulkDownload,
  bulkOperationLoading = false
}: FilterBarProps) {
  const t = useTranslations('myMedia');
  const [filters, setFilters] = useState<FilterState>({ favorites: false });
  const [selectMode, setSelectMode] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const [targetWidth, setTargetWidth] = useState(120);

  const handleFavoritesChange = (checked: boolean) => {
    const newFilters = { ...filters, favorites: checked };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handlePublishStatusChange = (value: string) => {
    const newPublishStatus = value === 'all' ? undefined : value as FilterState['publishStatus'];
    
    // 如果选择的值与当前值相同，不进行任何操作
    if (filters.publishStatus === newPublishStatus) {
      setIsDropdownOpen(false);
      return;
    }
    
    // 预先计算新宽度并设置
    const newLabel = value === 'all' ? 'All' : 
                    value === 'reviewing' ? 'Reviewing' : 
                    value === 'published' ? 'Published' : 'Rejected';
    const publishPrefix = "Publish: ";
    const totalText = publishPrefix + newLabel;
    const charWidth = 7;
    const arrowSpace = 16;
    const padding = 24;
    const minWidth = 120;
    const calculatedWidth = (totalText.length * charWidth) + arrowSpace + padding;
    const newWidth = Math.max(minWidth, calculatedWidth);
    
    setTargetWidth(newWidth);
    
    const newFilters = {
      ...filters,
      publishStatus: newPublishStatus
    };
    setFilters(newFilters);
    onFiltersChange(newFilters);
    setIsDropdownOpen(false);
  };

  const handleMouseEnter = () => {
    setIsHovering(true);
    setIsDropdownOpen(true);
  };

  const handleMouseLeave = (e: React.MouseEvent) => {
    // 检查鼠标是否移动到了下拉菜单区域
    const currentTarget = e.currentTarget as HTMLElement;
    const relatedTarget = e.relatedTarget as HTMLElement;
    
    if (relatedTarget && currentTarget.contains(relatedTarget)) {
      return; // 鼠标还在组件内，不关闭
    }
    
    setIsHovering(false);
    setIsDropdownOpen(false);
  };

  const getCurrentLabel = () => {
    if (filters.publishStatus === 'reviewing') return 'Reviewing';
    if (filters.publishStatus === 'published') return 'Published'; 
    if (filters.publishStatus === 'rejected') return 'Rejected';
    return 'All';
  };

  // 初始化目标宽度
  useEffect(() => {
    const currentLabel = getCurrentLabel();
    const publishPrefix = "Publish: ";
    const totalText = publishPrefix + currentLabel;
    const charWidth = 7;
    const arrowSpace = 16;
    const padding = 24;
    const minWidth = 120;
    const calculatedWidth = (totalText.length * charWidth) + arrowSpace + padding;
    setTargetWidth(Math.max(minWidth, calculatedWidth));
  }, [filters.publishStatus]);

  const handleSelectModeToggle = () => {
    const newMode = !selectMode;
    setSelectMode(newMode);
    onSelectModeToggle(newMode);
  };

  return (
    <div className="flex items-center justify-between h-12">
      <div className="flex items-center space-x-6">
        {/* Publish Status Filter */}
        <div 
          className="relative"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <div 
            className="h-8 bg-background text-foreground rounded-md px-3 py-1 flex items-center justify-between cursor-pointer transition-all duration-300 hover:bg-muted"
            style={{ width: `${targetWidth}px` }}
          >
            <span className="text-sm whitespace-nowrap">Publish: {getCurrentLabel()}</span>
            <div className={`transition-transform duration-200 ease-in-out ${isHovering ? 'rotate-180' : 'rotate-0'}`}>
              <ChevronDown className="h-4 w-4" />
            </div>
          </div>
          
          {isDropdownOpen && (
            <div className="absolute top-full left-0 w-full bg-background rounded-md shadow-lg z-50 overflow-hidden">
              <div 
                className="px-3 py-2 text-sm text-foreground hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors mx-1 my-0.5 first:mt-1 last:mb-1 rounded-sm"
                onClick={() => handlePublishStatusChange('all')}
              >
                All
              </div>
              <div 
                className="px-3 py-2 text-sm text-foreground hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors mx-1 my-0.5 rounded-sm"
                onClick={() => handlePublishStatusChange('reviewing')}
              >
                Reviewing
              </div>
              <div 
                className="px-3 py-2 text-sm text-foreground hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors mx-1 my-0.5 rounded-sm"
                onClick={() => handlePublishStatusChange('published')}
              >
                Published
              </div>
              <div 
                className="px-3 py-2 text-sm text-foreground hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors mx-1 my-0.5 last:mb-1 rounded-sm"
                onClick={() => handlePublishStatusChange('rejected')}
              >
                Rejected
              </div>
            </div>
          )}
        </div>

        {/* Favorites Filter */}
        <div className="flex items-center space-x-2">
          <Checkbox
            id="favorites"
            checked={filters.favorites}
            onCheckedChange={handleFavoritesChange}
          />
          <label htmlFor="favorites" className="text-sm font-medium">
            {t('favorites')}
          </label>
        </div>
      </div>

      <div className="flex items-center space-x-4">
        {/* 选择模式工具栏 */}
        {selectMode && (
          <div className="flex items-center space-x-2 bg-background rounded-lg px-3 h-8">
            <Checkbox
              id="selectAll"
              checked={selectedCount > 0 && selectedCount === totalCount}
              onCheckedChange={onSelectAll}
              className="h-4 w-4"
            />
            <span className="text-sm font-medium whitespace-nowrap text-foreground">
              Select All ({selectedCount} selected)
            </span>
            
            <div className="flex items-center space-x-1">
              <Button 
                size="sm" 
                variant="ghost" 
                onClick={onBulkFavorite}
                disabled={selectedCount === 0 || bulkOperationLoading}
                className="text-foreground hover:bg-accent hover:text-accent-foreground p-1.5 disabled:opacity-50"
                title="批量收藏"
              >
                <Star className="w-4 h-4" />
              </Button>

              <Button 
                size="sm" 
                variant="ghost" 
                onClick={onBulkDownload}
                disabled={selectedCount === 0 || bulkOperationLoading}
                className="text-foreground hover:bg-accent hover:text-accent-foreground p-1.5 disabled:opacity-50"
                title="批量下载"
              >
                <Download className="w-4 h-4" />
              </Button>

              <Button 
                size="sm" 
                variant="ghost" 
                onClick={onBulkDelete}
                disabled={selectedCount === 0 || bulkOperationLoading}
                className="text-foreground hover:bg-accent hover:text-accent-foreground p-1.5 disabled:opacity-50"
                title="批量删除"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
            
            {/* 加载状态指示 */}
            {bulkOperationLoading && (
              <div className="flex items-center space-x-2 text-sm text-muted-foreground ml-2">
                <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                <span>处理中...</span>
              </div>
            )}
          </div>
        )}

        <Button
          variant="ghost"
          size="sm"
          onClick={handleSelectModeToggle}
          className="flex items-center space-x-2 text-foreground hover:bg-accent hover:text-accent-foreground h-8 min-w-fit"
        >
          {selectMode ? (
            <>
              <LogOut className="h-4 w-4" />
              <span>Exit Batch Selection</span>
            </>
          ) : (
            <>
              <CopyCheck className="h-4 w-4" />
              <span>Select</span>
            </>
          )}
        </Button>

        {selectedCount > 0 && !selectMode && (
          <span className="text-sm text-muted-foreground">
            {t('itemsSelected', { count: selectedCount })}
          </span>
        )}
      </div>
    </div>
  );
}