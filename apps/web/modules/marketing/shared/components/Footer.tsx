import { LocaleLink } from "@i18n/routing";
import { config } from "@repo/config";
import { Logo } from "@shared/components/Logo";
import { cn } from "@ui/lib";
import {
	Facebook,
	Instagram,
	MessageSquare,
	Play,
	Twitter,
} from "lucide-react";
import { getTranslations } from "next-intl/server";

// 社交媒体图标映射
const SocialIcons: Record<string, React.ReactNode> = {
	Twitter: <Twitter className="w-5 h-5" />,
	Facebook: <Facebook className="w-5 h-5" />,
	Instagram: <Instagram className="w-5 h-5" />,
	Play: <Play className="w-5 h-5" />,
	MessageSquare: <MessageSquare className="w-5 h-5" />,
};

interface FooterProps {
	className?: string;
}

export async function Footer({ className }: FooterProps = {}) {
	const t = await getTranslations("common");

	return (
		<footer
			className={cn(
				"border-t py-8 text-foreground/60 text-sm",
				className,
			)}
		>
			<div className="container">
				<div className="flex justify-center mb-8">
					<div className="grid grid-cols-2 md:grid-cols-3 gap-16 md:gap-24 lg:gap-32">
						{config.footer.sections.map((section) => (
							<dl key={section.id}>
								<dt className="text-sm font-semibold mb-4 text-foreground/80">
									{t(section.titleKey as any)}
								</dt>
								<dd className="flex flex-col gap-2">
									{section.links.map((link) => (
										<LocaleLink
											key={link.href}
											href={link.href}
											className="text-foreground/60 hover:text-foreground transition-colors font-normal text-sm"
										>
											{t(link.labelKey as any)}
										</LocaleLink>
									))}
								</dd>
							</dl>
						))}

						{/* 第三列：Logo和社交媒体 */}
						<div className="hidden md:flex md:flex-col md:gap-4">
							<Logo className="opacity-70 grayscale" />

							<div className="flex space-x-3">
								{config.footer.socialLinks.map((link) => (
									<a
										key={link.href}
										href={link.href}
										className="text-foreground/60 hover:text-foreground transition-colors"
										target="_blank"
										rel="noopener noreferrer"
									>
										{SocialIcons[link.icon]}
									</a>
								))}
							</div>

							<p className="text-sm text-foreground/60">
								{t(config.footer.copyrightKey as any)}
							</p>
						</div>
					</div>
				</div>

				<div className="flex flex-col md:flex-row items-center justify-center border-t pt-8">
					<div className="mb-4 md:mb-0 md:hidden">
						<Logo className="opacity-70 grayscale" />
					</div>

					{/* 移动端显示社交媒体链接 */}
					<div className="md:hidden flex flex-col items-center gap-4 mt-4">
						<div className="flex space-x-4">
							{config.footer.socialLinks.map((link) => (
								<a
									key={link.href}
									href={link.href}
									className="text-foreground/60 hover:text-foreground transition-colors"
									target="_blank"
									rel="noopener noreferrer"
								>
									{SocialIcons[link.icon]}
								</a>
							))}
						</div>

						<p className="text-sm text-foreground/60 text-center">
							{t(config.footer.copyrightKey as any)}
						</p>
					</div>
				</div>
			</div>
		</footer>
	);
}
