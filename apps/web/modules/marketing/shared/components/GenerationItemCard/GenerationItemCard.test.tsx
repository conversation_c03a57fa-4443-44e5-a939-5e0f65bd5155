/**
 * @jest-environment jsdom
 */

import { render, screen, fireEvent } from '@testing-library/react';
import { GenerationItemCard } from './GenerationItemCard';
import type { GenerationData } from './types';

// Mock data for testing
const mockGeneration: GenerationData = {
	id: 'test-gen-1',
	userId: 'test-user-1',
	cover: 'https://example.com/cover.jpg',
	thumbnail: 'https://example.com/thumb.jpg',
	videoUrl: 'https://example.com/video.mp4',
	mediaUrl: 'https://example.com/video.mp4',
	publishStatus: undefined,
	favorite: false,
	status: 'succeeded',
	canPublish: true,
	canDelete: true,
	canCreateSimilar: true,
	createdAt: new Date('2024-01-01'),
	starNum: 5,
	isLiked: false,
	user: {
		id: 'test-user-1',
		name: 'Test User',
		image: 'https://example.com/avatar.jpg',
	},
};

// Mock handlers
const mockHandlers = {
	onPublish: jest.fn(),
	onFavorite: jest.fn(),
	onDownload: jest.fn(),
	onShare: jest.fn(),
	onCopyLink: jest.fn(),
	onDelete: jest.fn(),
	onCreateSimilar: jest.fn(),
};

describe('GenerationItemCard', () => {
	beforeEach(() => {
		// Reset all mocks before each test
		Object.values(mockHandlers).forEach(mock => mock.mockClear());
	});

	it('renders the component with basic props', () => {
		render(
			<GenerationItemCard
				generation={mockGeneration}
				{...mockHandlers}
			/>
		);

		expect(screen.getByText('Test User')).toBeInTheDocument();
	});

	it('displays unpublished status by default', () => {
		render(
			<GenerationItemCard
				generation={mockGeneration}
				{...mockHandlers}
			/>
		);

		expect(screen.getByText('Unpublished')).toBeInTheDocument();
	});

	it('displays published status when publishStatus is "published"', () => {
		const publishedGeneration = {
			...mockGeneration,
			publishStatus: 'published' as const,
		};

		render(
			<GenerationItemCard
				generation={publishedGeneration}
				{...mockHandlers}
			/>
		);

		expect(screen.getByText('Published')).toBeInTheDocument();
	});

	it('calls onFavorite when favorite button is clicked', () => {
		render(
			<GenerationItemCard
				generation={mockGeneration}
				{...mockHandlers}
			/>
		);

		// Find the favorite button in the bottom area (should be visible)
		const favoriteButtons = screen.getAllByRole('button');
		const favoriteButton = favoriteButtons.find(button =>
			button.querySelector('svg')?.classList.contains('lucide-heart')
		);

		expect(favoriteButton).toBeInTheDocument();
		
		if (favoriteButton) {
			fireEvent.click(favoriteButton);
			expect(mockHandlers.onFavorite).toHaveBeenCalledWith('test-gen-1');
		}
	});

	it('shows favorited state when favorite is true', () => {
		const favoritedGeneration = {
			...mockGeneration,
			favorite: true,
		};

		render(
			<GenerationItemCard
				generation={favoritedGeneration}
				{...mockHandlers}
			/>
		);

		// Check for heart icons that should be filled (favorited state)
		const heartIcons = screen.container.querySelectorAll('.lucide-heart');
		const filledHearts = Array.from(heartIcons).filter(heart =>
			heart.classList.contains('fill-red-500')
		);
		
		expect(filledHearts.length).toBeGreaterThan(0);
	});

	it('displays video element when videoUrl is provided', () => {
		render(
			<GenerationItemCard
				generation={mockGeneration}
				{...mockHandlers}
			/>
		);

		const video = screen.getByRole('application') || screen.container.querySelector('video');
		expect(video).toBeInTheDocument();
	});

	it('shows "No preview available" when no video or poster is provided', () => {
		const noMediaGeneration = {
			...mockGeneration,
			cover: undefined,
			thumbnail: undefined,
			videoUrl: undefined,
			mediaUrl: undefined,
		};

		render(
			<GenerationItemCard
				generation={noMediaGeneration}
				{...mockHandlers}
			/>
		);

		expect(screen.getByText('No preview available')).toBeInTheDocument();
	});

	it('handles mouse hover events', () => {
		render(
			<GenerationItemCard
				generation={mockGeneration}
				{...mockHandlers}
			/>
		);

		const card = screen.container.firstChild as HTMLElement;
		
		// Simulate mouse enter
		fireEvent.mouseEnter(card);
		
		// The "Create Similar Video" button should become visible on hover
		// But since it's controlled by CSS classes, we check for the presence of the button
		expect(screen.getByText('Create Similar Video')).toBeInTheDocument();

		// Simulate mouse leave
		fireEvent.mouseLeave(card);
	});

	it('renders action menu trigger', () => {
		render(
			<GenerationItemCard
				generation={mockGeneration}
				{...mockHandlers}
			/>
		);

		// Look for the more horizontal icon (three dots menu)
		const menuTrigger = screen.container.querySelector('.lucide-more-horizontal');
		expect(menuTrigger).toBeInTheDocument();
	});

	it('applies custom className', () => {
		const customClass = 'custom-test-class';
		
		render(
			<GenerationItemCard
				generation={mockGeneration}
				className={customClass}
				{...mockHandlers}
			/>
		);

		const card = screen.container.firstChild as HTMLElement;
		expect(card).toHaveClass(customClass);
	});

	it('respects canPublish permission', () => {
		const noPublishGeneration = {
			...mockGeneration,
			canPublish: false,
		};

		render(
			<GenerationItemCard
				generation={noPublishGeneration}
				{...mockHandlers}
			/>
		);

		// The publish option should not be available in the menu
		// This would require opening the dropdown menu to test properly
		// For now, we just verify the component renders without crashing
		expect(screen.getByText('Test User')).toBeInTheDocument();
	});

	it('handles different status states', () => {
		const statuses: Array<GenerationData['status']> = ['waiting', 'processing', 'succeeded', 'failed'];

		statuses.forEach(status => {
			const generationWithStatus = {
				...mockGeneration,
				status,
			};

			const { unmount } = render(
				<GenerationItemCard
					generation={generationWithStatus}
					{...mockHandlers}
				/>
			);

			expect(screen.getByText('Test User')).toBeInTheDocument();
			unmount();
		});
	});
});