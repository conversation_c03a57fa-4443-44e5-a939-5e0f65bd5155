"use client";

import { cn } from "@ui/lib";
import { Heart, Star } from "lucide-react";
import { useState } from "react";
import { Checkbox } from "@ui/components/checkbox";
import Link from "next/link";
import { useParams } from "next/navigation";
import { buildVideoPath } from "@shared/utils/buildLocalePath";
import { ActionMenu } from "./components/ActionMenu";
import { StatusBadge } from "./components/StatusBadge";
import { UserInfo } from "./components/UserInfo";
import { VideoPreview } from "./components/VideoPreview";
import { useCardActions } from "./hooks/useCardActions";
import { useVideoPlay } from "./hooks/useVideoPlay";
import type { GenerationItemCardProps } from "./types";

export function GenerationItemCard({
	generation,
	onPublish,
	onFavorite,
	onLike,
	onDownload,
	onDownloadWatermark,
	onDownloadNoWatermark,
	onShare,
	onShareX,
	onShareFacebook,
	onCopyLink,
	onDelete,
	onCreateSimilar,
	onClick,
	selectMode = false,
	selected = false,
	onSelect,
	className,
}: GenerationItemCardProps) {
	const [isHovered, setIsHovered] = useState(false);
	const params = useParams();
	const locale = params.locale as string;

	const actions = {
		onPublish,
		onFavorite,
		onLike,
		onDownload,
		onDownloadWatermark,
		onDownloadNoWatermark,
		onShare,
		onShareX,
		onShareFacebook,
		onCopyLink,
		onDelete,
		onCreateSimilar,
	};

	const { handleFavorite, handleCreateSimilar, handleLike } = useCardActions(
		generation,
		actions,
	);

	const {
		videoRef,
		handlePlay,
		handlePause,
		handleVideoPlay,
		handleVideoPause,
	} = useVideoPlay();

	const handleMouseEnter = () => {
		setIsHovered(true);
		handlePlay();
	};

	const handleMouseLeave = () => {
		setIsHovered(false);
		handlePause();
	};

	const handleInteractiveClick = (e: React.MouseEvent) => {
		// 阻止 Link 导航，用于交互元素
		e.preventDefault();
		e.stopPropagation();
	};

	const handleCardClick = (e: React.MouseEvent) => {
		// 在选择模式下阻止导航
		if (selectMode) {
			e.preventDefault();
			return;
		}
		
		// 检查是否点击了交互元素（按钮、菜单等）
		const target = e.target as HTMLElement;
		if (target.closest('button') || target.closest('[role="menuitem"]') || target.closest('[data-interactive]')) {
			e.preventDefault();
			return;
		}
		
		// Link 组件会处理导航，不需要手动调用 onClick
	};

	const cardContent = (
		<div
			className={cn(
				"bg-card group relative w-full cursor-pointer rounded-lg md:rounded-xl",
				"aspect-[360/270] max-w-[360px] min-h-[250px] md:min-h-[270px]",
				className,
			)}
			onMouseEnter={handleMouseEnter}
			onMouseLeave={handleMouseLeave}
			onClick={handleCardClick}
		>
			<div className="text-foreground relative size-full">
				<div className="relative size-full rounded-lg md:rounded-xl">
					{/* 视频预览区域 */}
					<VideoPreview
						ref={videoRef}
						src={generation.videoUrl || generation.mediaUrl}
						poster={generation.cover || generation.thumbnail}
						className="size-full cursor-pointer rounded-lg md:rounded-xl object-cover"
						onPlay={handleVideoPlay}
						onPause={handleVideoPause}
					/>

					{/* 渐变遮罩和底部内容 */}
					<div className="absolute bottom-0 left-0 flex w-full justify-between items-end bg-gradient-to-b from-black/0 to-black/80 p-2 rounded-b-lg md:rounded-b-xl">
						{/* 左侧：用户信息 + Create Similar Video按钮 */}
						<UserInfo
							user={generation.user}
							isHovered={isHovered}
							onCreateSimilar={handleCreateSimilar}
						/>

						{/* 右侧：三个点菜单 + 心形图标并排 */}
						<div className="flex items-center gap-x-2">
							<div onClick={handleInteractiveClick}>
								<ActionMenu
									generation={generation}
									actions={actions}
									visible={isHovered}
								/>
							</div>

							{/* 心形点赞按钮 */}
							<button
								type="button"
								className="flex cursor-pointer items-center gap-x-1 p-1 transition-all hover:bg-white/20 rounded"
								onClick={(e) => {
									handleInteractiveClick(e);
									handleLike();
								}}
								aria-label={generation.isLiked ? "Unlike" : "Like"}
							>
								<Heart
									className={cn(
										"size-5 transition-colors stroke-2",
										generation.isLiked
											? "fill-red-500 text-red-500"
											: "text-white fill-none",
									)}
								/>
								{generation.starNum > 0 && (
									<span className="text-white text-sm font-medium">
										{generation.starNum}
									</span>
								)}
							</button>
						</div>
					</div>
				</div>
			</div>

			{/* 左上角状态标签 - 暂时注释掉 */}
			{/* <StatusBadge
				status={generation.publishStatus}
				className="absolute left-3 top-3"
			/> */}

			{/* 左上角选择复选框 */}
			{selectMode && (
				<div className="absolute left-2 top-2 z-10" onClick={handleInteractiveClick}>
					<Checkbox
						checked={selected}
						onCheckedChange={onSelect}
						className={`h-4 w-4 border-2 border-white data-[state=checked]:bg-primary data-[state=checked]:border-primary ${
							selected ? 'bg-white/90' : 'bg-white/30'
						}`}
					/>
				</div>
			)}

			{/* 右上角收藏按钮 - 更新后的实现 */}
			<button
				type="button"
				className={cn(
					"cursor-pointer items-center gap-x-1 rounded p-1 transition-all hover:bg-white/20 absolute right-2 top-2",
					// 新逻辑：已收藏时始终显示，未收藏时hover显示
					generation.favorite ? "flex" : (isHovered ? "flex" : "hidden"),
				)}
				onClick={(e) => {
					handleInteractiveClick(e);
					handleFavorite();
				}}
				aria-label={generation.favorite ? "Remove from favorites" : "Add to favorites"}
			>
				<Star
					className={cn(
						"size-4 transition-colors stroke-2",
						generation.favorite
							? "fill-yellow-500 text-yellow-500"  // 已收藏：实心黄色
							: "text-white fill-none",            // 未收藏hover：空心白色
					)}
				/>
			</button>
		</div>
	);

	// 在选择模式下，不使用 Link 包装
	if (selectMode) {
		return cardContent;
	}

	// 正常模式下，使用 Link 包装实现拦截路由
	return (
		<Link 
			href={buildVideoPath(locale, generation.id, 'my-generations')}
			className="block"
		>
			{cardContent}
		</Link>
	);
}