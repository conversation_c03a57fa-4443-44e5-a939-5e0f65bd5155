# GenerationItemCard Component

一个用于展示视频生成记录的交互式卡片组件，支持悬停播放、收藏、分享等多种操作。

## 功能特性

- 🎥 **视频预览**: 悬停时自动播放视频
- ❤️ **收藏功能**: 支持收藏/取消收藏操作
- 📤 **分享操作**: 支持发布、分享、下载等多种操作
- 🎨 **响应式设计**: 适配不同屏幕尺寸
- 🔄 **状态管理**: 支持多种发布状态显示
- 📱 **触摸友好**: 优化移动端交互体验

## 安装和使用

### 基本使用

```tsx
import { GenerationItemCard } from '@marketing/shared/components/GenerationItemCard';

function MyGenerationsPage() {
  const generation = {
    id: "gen_123",
    userId: "user_456",
    cover: "https://example.com/cover.jpg",
    videoUrl: "https://example.com/video.mp4",
    publishStatus: undefined,
    favorite: false,
    status: 'succeeded',
    canPublish: true,
    canDelete: true,
    canCreateSimilar: true,
    createdAt: new Date(),
    user: {
      id: "user_456", 
      name: "<PERSON>",
      image: "https://example.com/avatar.jpg"
    }
  };

  return (
    <GenerationItemCard
      generation={generation}
      onFavorite={(id) => console.log('Toggle favorite:', id)}
      onPublish={(id) => console.log('Publish:', id)}
      onDownload={(id) => console.log('Download:', id)}
      onShare={(id) => console.log('Share:', id)}
      onCopyLink={(id) => console.log('Copy link:', id)}
      onDelete={(id) => console.log('Delete:', id)}
      onCreateSimilar={(id) => console.log('Create similar:', id)}
    />
  );
}
```

### 网格布局使用

```tsx
function VideoGallery({ generations }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {generations.map((generation) => (
        <GenerationItemCard
          key={generation.id}
          generation={generation}
          onFavorite={handleFavorite}
          onShare={handleShare}
          onCreateSimilar={handleCreateSimilar}
        />
      ))}
    </div>
  );
}
```

## API 接口

### GenerationItemCardProps

| 属性 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| `generation` | `GenerationData` | ✅ | - | 生成记录数据 |
| `onPublish` | `(id: string) => void` | ❌ | - | 发布回调 |
| `onFavorite` | `(id: string) => void` | ❌ | - | 收藏回调 |
| `onDownload` | `(id: string) => void` | ❌ | - | 下载回调 |
| `onShare` | `(id: string) => void` | ❌ | - | 分享回调 |
| `onCopyLink` | `(id: string) => void` | ❌ | - | 复制链接回调 |
| `onDelete` | `(id: string) => void` | ❌ | - | 删除回调 |
| `onCreateSimilar` | `(id: string) => void` | ❌ | - | 创建相似视频回调 |
| `className` | `string` | ❌ | - | 自定义CSS类名 |

### GenerationData

| 属性 | 类型 | 必填 | 描述 |
|------|------|------|------|
| `id` | `string` | ✅ | 生成记录ID |
| `userId` | `string` | ✅ | 用户ID |
| `cover` | `string` | ❌ | 视频封面URL |
| `thumbnail` | `string` | ❌ | 缩略图URL |
| `videoUrl` | `string` | ❌ | 视频URL |
| `mediaUrl` | `string` | ❌ | 媒体URL |
| `publishStatus` | `'reviewing' \\| 'published' \\| 'rejected'` | ❌ | 发布状态 |
| `favorite` | `boolean` | ✅ | 是否收藏 |
| `status` | `'waiting' \\| 'processing' \\| 'succeeded' \\| 'failed'` | ✅ | 生成状态 |
| `canPublish` | `boolean` | ✅ | 是否可发布 |
| `canDelete` | `boolean` | ✅ | 是否可删除 |
| `canCreateSimilar` | `boolean` | ✅ | 是否可创建相似视频 |
| `createdAt` | `Date` | ✅ | 创建时间 |
| `user` | `UserData` | ✅ | 用户信息 |

## 交互行为

### 悬停效果
- 鼠标悬停时自动播放视频
- 显示"Create Similar Video"按钮
- 显示操作菜单图标
- 离开时暂停视频播放

### 操作菜单
点击右下角的三点图标显示下拉菜单，包含：
- **Publish**: 发布视频（仅未发布时显示）
- **Add to favorite / Remove from favorite**: 收藏切换
- **Download**: 下载视频（仅成功生成时可用）
- **Share**: 分享视频
- **Copy Link**: 复制视频链接
- **Delete**: 删除视频（仅可删除时显示）

### 状态显示
- **Unpublished**: 灰色标签（默认）
- **Published**: 绿色标签
- **Reviewing**: 黄色标签
- **Rejected**: 红色标签

## 样式定制

### 自定义尺寸

```tsx
<GenerationItemCard
  generation={generation}
  className="w-[320px] h-[240px]"  // 自定义尺寸
/>
```

### 响应式网格

```css
.grid-responsive {
  @apply grid gap-4;
  grid-template-columns: repeat(auto-fill, minmax(279px, 1fr));
}
```

## 无障碍性

- 支持键盘导航
- 提供适当的ARIA标签
- 屏幕阅读器友好
- 良好的颜色对比度

## 浏览器兼容性

- Chrome >= 88
- Firefox >= 85  
- Safari >= 14
- Edge >= 88

## 故障排除

### 视频不播放
- 检查视频URL是否有效
- 确保视频格式为浏览器支持的格式（MP4推荐）
- 检查CORS设置

### 样式问题
- 确保已正确导入Tailwind CSS
- 检查shadcn/ui组件是否正确安装
- 验证自定义CSS类名是否冲突

### 回调不触发
- 检查回调函数是否正确传递
- 验证生成记录的权限字段设置
- 确保组件状态正确

## 更新日志

### v1.0.0
- 初始版本
- 支持基本的视频预览和操作功能
- 响应式设计
- 完整的TypeScript类型支持