"use client";

import * as PopoverPrimitive from "@radix-ui/react-popover";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { cn } from "@ui/lib";
import {
	Download,
	ExternalLink,
	Link,
	MoreHorizontal,
	Share2,
	Star,
	Tag,
	Trash2,
	Upload,
} from "lucide-react";
import { useCallback, useRef, useState } from "react";
import type { CardActions, GenerationData } from "../types";

interface ActionMenuProps {
	generation: GenerationData;
	actions: CardActions;
	visible: boolean;
	className?: string;
}

export function ActionMenu({
	generation,
	actions,
	visible,
	className,
}: ActionMenuProps) {
	const [isOpen, setIsOpen] = useState(false);
	const [closeTimer, setCloseTimer] = useState<NodeJS.Timeout | null>(null);
	const [downloadSubmenuOpen, setDownloadSubmenuOpen] = useState(false);
	const [downloadSubmenuTimer, setDownloadSubmenuTimer] = useState<NodeJS.Timeout | null>(null);
	const [shareSubmenuOpen, setShareSubmenuOpen] = useState(false);
	const [shareSubmenuTimer, setShareSubmenuTimer] = useState<NodeJS.Timeout | null>(null);
	const [submenuDirection, setSubmenuDirection] = useState<'left' | 'right'>('right');
	const [shareSubmenuDirection, setShareSubmenuDirection] = useState<'left' | 'right'>('right');
	const downloadButtonRef = useRef<HTMLButtonElement>(null);
	const shareButtonRef = useRef<HTMLButtonElement>(null);

	const handleMouseEnter = () => {
		// 清除所有相关的关闭定时器
		if (closeTimer) {
			clearTimeout(closeTimer);
			setCloseTimer(null);
		}
		if (downloadSubmenuTimer) {
			clearTimeout(downloadSubmenuTimer);
			setDownloadSubmenuTimer(null);
		}
		if (shareSubmenuTimer) {
			clearTimeout(shareSubmenuTimer);
			setShareSubmenuTimer(null);
		}
		setIsOpen(true);
	};

	const handleMouseLeave = useCallback(() => {
		const timer = setTimeout(() => {
			// 使用函数式更新来获取最新的状态
			setDownloadSubmenuOpen(currentDownloadSubmenuOpen => {
				setShareSubmenuOpen(currentShareSubmenuOpen => {
					// 检查任何子菜单是否还在打开状态，如果是则不关闭主菜单
					if (!currentDownloadSubmenuOpen && !currentShareSubmenuOpen) {
						setIsOpen(false);
					}
					return currentShareSubmenuOpen; // 返回当前状态，不改变
				});
				return currentDownloadSubmenuOpen; // 返回当前状态，不改变
			});
		}, 200);
		setCloseTimer(timer);
	}, [downloadSubmenuOpen, shareSubmenuOpen]);

	const handleDownloadMouseEnter = () => {
		if (downloadSubmenuTimer) {
			clearTimeout(downloadSubmenuTimer);
			setDownloadSubmenuTimer(null);
		}
		
		// 边缘检测：检查下载按钮是否靠近屏幕右边缘
		if (downloadButtonRef.current) {
			const rect = downloadButtonRef.current.getBoundingClientRect();
			const viewportWidth = window.innerWidth;
			const submenuWidth = 220; // 调整为min-w-55对应的宽度(220px)
			const threshold = 50; // 50px阈值
			
			// 如果按钮右边缘 + 子菜单宽度 + 阈值 > 屏幕宽度，则向左弹出
			if (rect.right + submenuWidth + threshold > viewportWidth) {
				setSubmenuDirection('left');
			} else {
				setSubmenuDirection('right');
			}
		}
		
		setDownloadSubmenuOpen(true);
	};

	const handleDownloadMouseLeave = () => {
		const timer = setTimeout(() => {
			setDownloadSubmenuOpen(false);
		}, 300); // 增加到300ms延迟，给用户更多时间移动鼠标
		setDownloadSubmenuTimer(timer);
	};

	const handleSubmenuMouseEnter = () => {
		// 鼠标进入子菜单时，清除所有关闭定时器
		if (downloadSubmenuTimer) {
			clearTimeout(downloadSubmenuTimer);
			setDownloadSubmenuTimer(null);
		}
		// 同时清除父级的关闭定时器，保持主菜单打开
		if (closeTimer) {
			clearTimeout(closeTimer);
			setCloseTimer(null);
		}
		// 确保主菜单和子菜单都保持打开
		setIsOpen(true);
		setDownloadSubmenuOpen(true);
	};

	const handleSubmenuMouseLeave = () => {
		// 鼠标离开子菜单时，只关闭子菜单，不关闭主菜单
		// 让主菜单的mouseLeave逻辑来处理主菜单的关闭
		const timer = setTimeout(() => {
			setDownloadSubmenuOpen(false);
		}, 200);
		setDownloadSubmenuTimer(timer);
	};

	const handleShareMouseEnter = () => {
		if (shareSubmenuTimer) {
			clearTimeout(shareSubmenuTimer);
			setShareSubmenuTimer(null);
		}
		
		// 边缘检测：检查分享按钮是否靠近屏幕右边缘
		if (shareButtonRef.current) {
			const rect = shareButtonRef.current.getBoundingClientRect();
			const viewportWidth = window.innerWidth;
			const submenuWidth = 220; // 调整为min-w-55对应的宽度(220px)
			const threshold = 50; // 50px阈值
			
			// 如果按钮右边缘 + 子菜单宽度 + 阈值 > 屏幕宽度，则向左弹出
			if (rect.right + submenuWidth + threshold > viewportWidth) {
				setShareSubmenuDirection('left');
			} else {
				setShareSubmenuDirection('right');
			}
		}
		
		setShareSubmenuOpen(true);
	};

	const handleShareMouseLeave = () => {
		const timer = setTimeout(() => {
			setShareSubmenuOpen(false);
		}, 300); // 增加到300ms延迟，给用户更多时间移动鼠标
		setShareSubmenuTimer(timer);
	};

	const handleShareSubmenuMouseEnter = () => {
		// 鼠标进入子菜单时，清除所有关闭定时器
		if (shareSubmenuTimer) {
			clearTimeout(shareSubmenuTimer);
			setShareSubmenuTimer(null);
		}
		// 同时清除父级的关闭定时器，保持主菜单打开
		if (closeTimer) {
			clearTimeout(closeTimer);
			setCloseTimer(null);
		}
		// 确保主菜单和子菜单都保持打开
		setIsOpen(true);
		setShareSubmenuOpen(true);
	};

	const handleShareSubmenuMouseLeave = () => {
		// 鼠标离开子菜单时，只关闭子菜单，不关闭主菜单
		// 让主菜单的mouseLeave逻辑来处理主菜单的关闭
		const timer = setTimeout(() => {
			setShareSubmenuOpen(false);
		}, 200);
		setShareSubmenuTimer(timer);
	};
	const menuItems = [
		{
			icon: <Upload className="size-4" />,
			label: "Publish",
			action: () => actions.onPublish?.(generation.id),
			show:
				generation.canPublish &&
				generation.publishStatus !== "published",
			variant: "default" as const,
		},
		{
			icon: generation.favorite ? (
				<Star className="size-4 fill-current" />
			) : (
				<Star className="size-4" />
			),
			label: generation.favorite
				? "Remove from favorite"
				: "Favorite",
			action: () => actions.onFavorite?.(generation.id),
			show: true,
			variant: "default" as const,
		},
		{
			icon: <Download className="size-4" />,
			label: "Download",
			action: () => actions.onDownload?.(generation.id),
			show: generation.status === "succeeded",
			variant: "default" as const,
		},
		{
			icon: <Share2 className="size-4" />,
			label: "Share",
			action: () => actions.onShare?.(generation.id),
			show: true,
			variant: "default" as const,
		},
		{
			icon: <Link className="size-4" />,
			label: "Copy Link",
			action: () => actions.onCopyLink?.(generation.id),
			show: true,
			variant: "default" as const,
		},
		{
			icon: <Trash2 className="size-4" />,
			label: "Delete",
			action: () => actions.onDelete?.(generation.id),
			show: generation.canDelete,
			variant: "destructive" as const,
		},
	];

	const visibleItems = menuItems.filter((item) => item.show);

	// 调试日志：检查删除菜单项是否显示
	console.log('ActionMenu debug:', {
		generationId: generation.id,
		canDelete: generation.canDelete,
		deleteMenuItem: menuItems.find(item => item.label === 'Delete'),
		visibleItemsCount: visibleItems.length,
		visibleItems: visibleItems.map(item => item.label)
	});

	if (visibleItems.length === 0) {
		return null;
	}

	return (
		<div
			className={cn(
				"transition-all duration-300",
				visible ? "opacity-100" : "opacity-0",
				className,
			)}
		>
			<Popover open={isOpen} onOpenChange={setIsOpen}>
				<PopoverTrigger asChild>
					<button 
						type="button"
						className={cn(
							"text-white hover:bg-white/20 flex cursor-pointer items-center justify-center rounded p-1 transition-all",
							"border-none outline-none focus:outline-none focus:ring-0",
							visible ? "opacity-100" : "opacity-0"
						)}
						onMouseEnter={handleMouseEnter}
						onMouseLeave={handleMouseLeave}
					>
						<MoreHorizontal className="size-5" />
					</button>
				</PopoverTrigger>
				<PopoverContent 
					side="top" 
					align="end" 
					alignOffset={-10}
					className="w-auto p-1 bg-gray-800/95 border-gray-700 backdrop-blur-sm"
					sideOffset={6}
					onMouseEnter={handleMouseEnter}
					onMouseLeave={handleMouseLeave}
				>
					<PopoverPrimitive.Arrow 
						className="fill-gray-800 drop-shadow-sm" 
						width={16} 
						height={8} 
					/>
					<div className="space-y-0.5 relative">
						{visibleItems.map((item, index) => (
							<div key={index} className="relative">
								{/* 在删除项前添加分隔线 */}
								{item.variant === "destructive" && index > 0 && (
									<hr className="border-gray-600/50 my-0.5" />
								)}
								
								{/* Download菜单项特殊处理 */}
								{item.label === "Download" ? (
									<div className="relative" onMouseLeave={handleDownloadMouseLeave}>
										<button
											ref={downloadButtonRef}
											type="button"
											className={cn(
												"flex items-center gap-2 w-full px-2.5 py-1.5 text-xs rounded cursor-pointer transition-colors text-left whitespace-nowrap",
												"border-none outline-none focus:outline-none focus:ring-0",
												"text-white hover:bg-gray-700/80"
											)}
											onMouseEnter={handleDownloadMouseEnter}
										>
											<span className="w-3.5 h-3.5 flex items-center justify-center">
												{item.icon}
											</span>
											<span>{item.label}</span>
										</button>
										
										{/* 下载子菜单 */}
										{downloadSubmenuOpen && (
											<div 
												role="menu"
												className={cn(
													"absolute top-0 w-auto min-w-55 p-1 bg-gray-800/95 border border-gray-700 rounded-md backdrop-blur-sm shadow-lg z-50",
													submenuDirection === 'right' 
														? "left-full ml-1" 
														: "right-full mr-1"
												)}
												onMouseEnter={handleSubmenuMouseEnter}
												onMouseLeave={handleSubmenuMouseLeave}
											>
												{/* 无形的桥接区域 */}
												<div 
													role="presentation"
													aria-hidden="true"
													className={cn(
														"absolute top-0 h-full w-1 bg-transparent",
														submenuDirection === 'right' 
															? "-left-1" 
															: "-right-1"
													)}
													onMouseEnter={handleSubmenuMouseEnter}
												/>
												<button
													type="button"
													onClick={() => actions.onDownloadWatermark?.(generation.id)}
													className="flex items-center gap-2 w-full px-3 py-1.5 text-xs rounded cursor-pointer transition-colors text-left text-white hover:bg-gray-700/80 border-none outline-none focus:outline-none focus:ring-0"
												>
													<div className="relative w-3.5 h-3.5 flex items-center justify-center">
														<Download className="w-3 h-3" />
														<Tag className="w-1.5 h-1.5 absolute -top-0.5 -right-0.5 text-yellow-400" />
													</div>
													<span>Download with watermark</span>
												</button>
												<button
													type="button"
													onClick={() => actions.onDownloadNoWatermark?.(generation.id)}
													className="flex items-center gap-2 w-full px-3 py-1.5 text-xs rounded cursor-pointer transition-colors text-left text-white hover:bg-gray-700/80 border-none outline-none focus:outline-none focus:ring-0"
												>
													<div className="relative w-3.5 h-3.5 flex items-center justify-center">
														<Download className="w-3.5 h-3.5 text-green-400" />
													</div>
													<span>Download without watermark</span>
												</button>
											</div>
										)}
									</div>
								) : item.label === "Share" ? (
									/* Share菜单项特殊处理 */
									<div className="relative" onMouseLeave={handleShareMouseLeave}>
										<button
											ref={shareButtonRef}
											type="button"
											className={cn(
												"flex items-center gap-2 w-full px-2.5 py-1.5 text-xs rounded cursor-pointer transition-colors text-left whitespace-nowrap",
												"border-none outline-none focus:outline-none focus:ring-0",
												"text-white hover:bg-gray-700/80"
											)}
											onMouseEnter={handleShareMouseEnter}
										>
											<span className="w-3.5 h-3.5 flex items-center justify-center">
												{item.icon}
											</span>
											<span>{item.label}</span>
										</button>
										
										{/* 分享子菜单 */}
										{shareSubmenuOpen && (
											<div 
												role="menu"
												className={cn(
													"absolute top-0 w-auto p-1 bg-gray-800/95 border border-gray-700 rounded-md backdrop-blur-sm shadow-lg z-50",
													shareSubmenuDirection === 'right' 
														? "left-full ml-1" 
														: "right-full mr-1"
												)}
												onMouseEnter={handleShareSubmenuMouseEnter}
												onMouseLeave={handleShareSubmenuMouseLeave}
											>
												{/* 无形的桥接区域 */}
												<div 
													role="presentation"
													aria-hidden="true"
													className={cn(
														"absolute top-0 h-full w-1 bg-transparent",
														shareSubmenuDirection === 'right' 
															? "-left-1" 
															: "-right-1"
													)}
													onMouseEnter={handleShareSubmenuMouseEnter}
												/>
												<button
													type="button"
													onClick={() => actions.onShareX?.(generation.id)}
													className="flex items-center gap-2 w-full px-3 py-1.5 text-xs rounded cursor-pointer transition-colors text-left text-white hover:bg-gray-700/80 border-none outline-none focus:outline-none focus:ring-0 whitespace-nowrap"
												>
													<div className="relative w-3.5 h-3.5 flex items-center justify-center">
														{/* X (Twitter) Logo SVG */}
														<svg className="w-3.5 h-3.5" viewBox="0 0 24 24" fill="currentColor">
															<path d="M18.901 1.153h3.68l-8.04 9.19L24 22.846h-7.406l-5.8-7.584-6.638 7.584H.474l8.6-9.83L0 1.154h7.594l5.243 6.932ZM17.61 20.644h2.039L6.486 3.24H4.298Z"/>
														</svg>
													</div>
													<span>Share on X</span>
												</button>
												<button
													type="button"
													onClick={() => actions.onShareFacebook?.(generation.id)}
													className="flex items-center gap-2 w-full px-3 py-1.5 text-xs rounded cursor-pointer transition-colors text-left text-white hover:bg-gray-700/80 border-none outline-none focus:outline-none focus:ring-0 whitespace-nowrap"
												>
													<div className="relative w-3.5 h-3.5 flex items-center justify-center">
														{/* Facebook "f" Letter Bold SVG */}
														<svg className="w-3.5 h-3.5" viewBox="0 0 320 512" fill="currentColor">
															<path d="M279.14 288l14.22-92.66h-88.91v-60.13c0-25.35 12.42-50.06 52.24-50.06h40.42V6.26S260.43 0 225.36 0c-73.22 0-121.08 44.38-121.08 124.72v70.62H22.89V288h81.39v224h100.17V288z"/>
														</svg>
													</div>
													<span>Share on Facebook</span>
												</button>
											</div>
										)}
									</div>
								) : (
									/* 其他普通菜单项 */
									<button
										type="button"
										onClick={item.action}
										className={cn(
											"flex items-center gap-2 w-full px-2.5 py-1.5 text-xs rounded cursor-pointer transition-colors text-left whitespace-nowrap",
											"border-none outline-none focus:outline-none focus:ring-0",
											item.variant === "destructive"
												? "text-red-400 hover:bg-red-900/20"
												: "text-white hover:bg-gray-700/80",
										)}
									>
										<span className="w-3.5 h-3.5 flex items-center justify-center">
											{item.icon}
										</span>
										<span>{item.label}</span>
									</button>
								)}
							</div>
						))}
					</div>
				</PopoverContent>
			</Popover>
		</div>
	);
}
