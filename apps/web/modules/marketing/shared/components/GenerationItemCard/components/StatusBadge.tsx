"use client";

import { cn } from "@ui/lib";

interface StatusBadgeProps {
	status?: "reviewing" | "published" | "rejected";
	className?: string;
}

export function StatusBadge({ status, className }: StatusBadgeProps) {
	const getStatusConfig = (status?: string) => {
		switch (status) {
			case "published":
				return {
					label: "Published",
					className: "bg-green-500/80 text-white",
				};
			case "reviewing":
				return {
					label: "Reviewing",
					className: "bg-yellow-500/80 text-white",
				};
			case "rejected":
				return {
					label: "Rejected",
					className: "bg-red-500/80 text-white",
				};
			default:
				return {
					label: "Unpublished",
					className: "bg-black/30 text-white",
				};
		}
	};

	const config = getStatusConfig(status);

	return (
		<button
			type="button"
			className={cn(
				"flex w-fit items-center justify-center gap-x-1 rounded-full border-none px-[6px] py-[2px] text-xs leading-4 hover:text-f-text",
				config.className,
				className,
			)}
		>
			<span className="scale-90">{config.label}</span>
		</button>
	);
}