"use client";

import { UserAvatar } from "@shared/components/UserAvatar";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";

interface UserInfoProps {
	user?: {
		id?: string;
		name?: string;
		image?: string;
	};
	isHovered: boolean;
	onCreateSimilar?: () => void;
	className?: string;
}

export function UserInfo({
	user,
	isHovered,
	onCreateSimilar,
	className,
}: UserInfoProps) {
	return (
		<div className={cn("flex flex-col space-y-2", className)}>
			{/* 用户信息 - 始终显示 */}
			<div className="hover:text-primary inline-flex cursor-pointer items-center gap-x-2">
				<div className="bg-orange-500 relative flex size-6 items-center justify-center rounded-full text-white hover:cursor-pointer">
					<UserAvatar
						name={user?.name || 'Unknown User'}
						avatarUrl={user?.image}
						className="size-full rounded-full"
					/>
				</div>
				<span className="line-clamp-1 max-w-[150px] text-sm text-white">
					{user?.name || 'Unknown User'}
				</span>
			</div>

			{/* Hover时显示的Create Similar Video按钮 */}
			<div
				className={cn(
					"transition-all duration-300",
					isHovered ? "flex opacity-100" : "hidden opacity-0",
				)}
			>
				<Button
					type="button"
					className="h-8 items-center justify-center gap-x-1 rounded-full px-3 text-sm font-semibold hover:opacity-90 md:text-xs text-white"
					style={{
						background: "linear-gradient(85.93deg, rgb(139, 69, 255) 0%, rgb(59, 130, 246) 100%)",
					}}
					onClick={onCreateSimilar}
				>
					<span>Remix it now</span>
				</Button>
			</div>
		</div>
	);
}
