"use client";

import { cn } from "@ui/lib";
import React, { forwardRef } from "react";

interface VideoPreviewProps {
	src?: string;
	poster?: string;
	className?: string;
	onPlay?: () => void;
	onPause?: () => void;
}

export const VideoPreview = forwardRef<HTMLVideoElement, VideoPreviewProps>(
	({ src, poster, className, onPlay, onPause }, ref) => {
		if (!src && !poster) {
			return (
				<div
					className={cn(
						"flex items-center justify-center bg-muted text-muted-foreground",
						className,
					)}
				>
					<div className="text-center">
						<div className="text-sm">No preview available</div>
					</div>
				</div>
			);
		}

		if (src) {
			return (
				<video
					ref={ref}
					className={cn(
						"bg-black bg-cover bg-center cursor-pointer object-cover w-full h-full",
						className,
					)}
					poster={poster}
					src={src}
					playsInline
					webkit-playsinline="true"
					controlsList="nodownload"
					loop
					preload="none"
					muted
					onPlay={onPlay}
					onPause={onPause}
					style={{
						backgroundImage: poster
							? `url("${poster}")`
							: undefined,
					}}
				/>
			);
		}

		// Fallback to poster image
		return (
			<div
				className={cn(
					"bg-cover bg-center bg-no-repeat w-full h-full",
					className,
				)}
				style={{
					backgroundImage: poster ? `url("${poster}")` : undefined,
				}}
			/>
		);
	},
);

VideoPreview.displayName = "VideoPreview";
