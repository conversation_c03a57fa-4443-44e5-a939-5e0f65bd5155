"use client";

import { useState } from "react";
import { GenerationItemCard } from "./GenerationItemCard";
import type { GenerationData } from "./types";

// 模拟数据
const createMockGeneration = (id: string, overrides: Partial<GenerationData> = {}): GenerationData => ({
	id,
	userId: "user_456",
	cover: "/api/placeholder/279/207", // 使用占位图片
	thumbnail: "/api/placeholder/279/207",
	videoUrl: undefined, // 实际使用时替换为真实视频URL
	mediaUrl: undefined,
	publishStatus: undefined,
	favorite: false,
	status: "succeeded",
	canPublish: true,
	canDelete: true,
	canCreateSimilar: true,
	createdAt: new Date(),
	user: {
		id: "user_456",
		name: "<PERSON> (<PERSON>)",
		image: "/api/placeholder/32/32",
	},
	...overrides,
});

const mockGenerations: GenerationData[] = [
	createMockGeneration("gen_1", {
		publishStatus: undefined,
		favorite: false,
	}),
	createMockGeneration("gen_2", {
		publishStatus: "published",
		favorite: true,
	}),
	createMockGeneration("gen_3", {
		publishStatus: "reviewing",
		favorite: false,
		canPublish: false,
	}),
	createMockGeneration("gen_4", {
		publishStatus: "rejected",
		favorite: true,
		canDelete: true,
	}),
	createMockGeneration("gen_5", {
		status: "processing",
		canPublish: false,
		canDelete: false,
	}),
	createMockGeneration("gen_6", {
		status: "failed",
		canPublish: false,
		canCreateSimilar: false,
	}),
];

export function GenerationItemCardDemo() {
	const [generations, setGenerations] = useState(mockGenerations);
	const [logs, setLogs] = useState<string[]>([]);

	const addLog = (message: string) => {
		setLogs(prev => [`${new Date().toLocaleTimeString()}: ${message}`, ...prev.slice(0, 9)]);
	};

	const updateGeneration = (id: string, updates: Partial<GenerationData>) => {
		setGenerations(prev =>
			prev.map(gen => (gen.id === id ? { ...gen, ...updates } : gen))
		);
	};

	const handlePublish = (id: string) => {
		addLog(`Publishing generation ${id}`);
		updateGeneration(id, { publishStatus: "reviewing" });
	};

	const handleFavorite = (id: string) => {
		const generation = generations.find(g => g.id === id);
		const newFavoriteState = !generation?.favorite;
		addLog(`${newFavoriteState ? "Adding to" : "Removing from"} favorites: ${id}`);
		updateGeneration(id, { favorite: newFavoriteState });
	};

	const handleDownload = (id: string) => {
		addLog(`Downloading generation ${id}`);
		// 模拟下载
		setTimeout(() => {
			addLog(`Download completed for ${id}`);
		}, 1000);
	};

	const handleShare = (id: string) => {
		addLog(`Sharing generation ${id}`);
		// 模拟分享对话框
		if (navigator.share) {
			navigator.share({
				title: "Check out my AI generated video!",
				url: `https://example.com/generations/${id}`,
			});
		}
	};

	const handleCopyLink = (id: string) => {
		const url = `https://example.com/generations/${id}`;
		navigator.clipboard.writeText(url).then(() => {
			addLog(`Copied link for generation ${id}`);
		});
	};

	const handleDelete = (id: string) => {
		if (confirm("Are you sure you want to delete this generation?")) {
			addLog(`Deleting generation ${id}`);
			setGenerations(prev => prev.filter(g => g.id !== id));
		}
	};

	const handleCreateSimilar = (id: string) => {
		addLog(`Creating similar video for generation ${id}`);
		// 模拟导航到创建页面
		setTimeout(() => {
			addLog(`Navigating to create similar video based on ${id}`);
		}, 500);
	};

	const resetDemo = () => {
		setGenerations(mockGenerations);
		setLogs([]);
		addLog("Demo reset");
	};

	return (
		<div className="min-h-screen bg-background p-8">
			<div className="max-w-7xl mx-auto space-y-8">
				{/* Header */}
				<div className="text-center space-y-4">
					<h1 className="text-4xl font-bold">GenerationItemCard Demo</h1>
					<p className="text-muted-foreground max-w-2xl mx-auto">
						Interactive demonstration of the GenerationItemCard component with various states and actions.
						Hover over cards to see the interactive effects.
					</p>
					<button
						onClick={resetDemo}
						className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
					>
						Reset Demo
					</button>
				</div>

				{/* Grid Layout */}
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
					{generations.map((generation) => (
						<div key={generation.id} className="flex justify-center">
							<GenerationItemCard
								generation={generation}
								onPublish={handlePublish}
								onFavorite={handleFavorite}
								onDownload={handleDownload}
								onShare={handleShare}
								onCopyLink={handleCopyLink}
								onDelete={handleDelete}
								onCreateSimilar={handleCreateSimilar}
							/>
						</div>
					))}
				</div>

				{/* Action Log */}
				<div className="bg-card rounded-lg p-6 border">
					<h2 className="text-2xl font-semibold mb-4">Action Log</h2>
					<div className="space-y-2 max-h-64 overflow-y-auto">
						{logs.length === 0 ? (
							<p className="text-muted-foreground italic">No actions yet. Try interacting with the cards above!</p>
						) : (
							logs.map((log, index) => (
								<div
									key={index}
									className="font-mono text-sm p-2 bg-muted rounded"
								>
									{log}
								</div>
							))
						)}
					</div>
				</div>

				{/* Feature Guide */}
				<div className="bg-card rounded-lg p-6 border">
					<h2 className="text-2xl font-semibold mb-4">Interactive Features</h2>
					<div className="grid md:grid-cols-2 gap-6">
						<div>
							<h3 className="text-lg font-medium mb-2">Hover Effects</h3>
							<ul className="space-y-1 text-sm text-muted-foreground">
								<li>• Video auto-play on hover</li>
								<li>• Show "Create Similar Video" button</li>
								<li>• Display action menu (three dots)</li>
								<li>• Show favorite button in top-right</li>
							</ul>
						</div>
						<div>
							<h3 className="text-lg font-medium mb-2">Action Menu</h3>
							<ul className="space-y-1 text-sm text-muted-foreground">
								<li>• Publish (when unpublished)</li>
								<li>• Add/Remove favorite</li>
								<li>• Download (when succeeded)</li>
								<li>• Share and Copy Link</li>
								<li>• Delete (when allowed)</li>
							</ul>
						</div>
					</div>
				</div>

				{/* Status Legend */}
				<div className="bg-card rounded-lg p-6 border">
					<h2 className="text-2xl font-semibold mb-4">Status Legend</h2>
					<div className="flex flex-wrap gap-4">
						<div className="flex items-center gap-2">
							<div className="w-4 h-4 rounded-full bg-gray-500/80"></div>
							<span className="text-sm">Unpublished</span>
						</div>
						<div className="flex items-center gap-2">
							<div className="w-4 h-4 rounded-full bg-green-500/80"></div>
							<span className="text-sm">Published</span>
						</div>
						<div className="flex items-center gap-2">
							<div className="w-4 h-4 rounded-full bg-yellow-500/80"></div>
							<span className="text-sm">Reviewing</span>
						</div>
						<div className="flex items-center gap-2">
							<div className="w-4 h-4 rounded-full bg-red-500/80"></div>
							<span className="text-sm">Rejected</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}