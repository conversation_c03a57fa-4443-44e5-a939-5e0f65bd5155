"use client";

import { useCallback } from "react";
import type { CardActions, GenerationData } from "../types";

export function useCardActions(
	generation: GenerationData,
	actions: CardActions,
) {
	const handleFavorite = useCallback(async () => {
		await actions.onFavorite?.(generation.id);
	}, [actions.onFavorite, generation.id]);

	const handlePublish = useCallback(() => {
		if (generation.canPublish && generation.publishStatus !== "published") {
			actions.onPublish?.(generation.id);
		}
	}, [
		actions.onPublish,
		generation.id,
		generation.canPublish,
		generation.publishStatus,
	]);

	const handleDownload = useCallback(() => {
		if (generation.status === "succeeded") {
			actions.onDownload?.(generation.id);
		}
	}, [actions.onDownload, generation.id, generation.status]);

	const handleShare = useCallback(() => {
		actions.onShare?.(generation.id);
	}, [actions.onShare, generation.id]);

	const handleCopyLink = useCallback(() => {
		actions.onCopyLink?.(generation.id);
	}, [actions.onCopyLink, generation.id]);

	const handleDelete = useCallback(() => {
		if (generation.canDelete) {
			actions.onDelete?.(generation.id);
		}
	}, [actions.onDelete, generation.id, generation.canDelete]);

	const handleCreateSimilar = useCallback(() => {
		if (generation.canCreateSimilar) {
			actions.onCreateSimilar?.(generation.id);
		}
	}, [actions.onCreateSimilar, generation.id, generation.canCreateSimilar]);

	const handleLike = useCallback(async () => {
		await actions.onLike?.(generation.id);
	}, [actions.onLike, generation.id]);

	return {
		handleFavorite,
		handlePublish,
		handleDownload,
		handleShare,
		handleCopyLink,
		handleDelete,
		handleCreateSimilar,
		handleLike,
	};
}
