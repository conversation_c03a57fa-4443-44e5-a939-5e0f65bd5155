"use client";

import { useRef, useState } from "react";

export function useVideoPlay() {
	const videoRef = useRef<HTMLVideoElement>(null);
	const [isPlaying, setIsPlaying] = useState(false);

	const handlePlay = () => {
		if (videoRef.current) {
			videoRef.current.play().catch(console.error);
			setIsPlaying(true);
		}
	};

	const handlePause = () => {
		if (videoRef.current) {
			videoRef.current.pause();
			setIsPlaying(false);
		}
	};

	const handleVideoPlay = () => {
		setIsPlaying(true);
	};

	const handleVideoPause = () => {
		setIsPlaying(false);
	};

	return {
		videoRef,
		isPlaying,
		handlePlay,
		handlePause,
		handleVideoPlay,
		handleVideoPause,
	};
}
