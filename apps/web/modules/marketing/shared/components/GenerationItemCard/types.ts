export interface GenerationData {
	id: string;
	userId: string;
	cover?: string;
	thumbnail?: string;
	videoUrl?: string;
	mediaUrl?: string;
	publishStatus?: "reviewing" | "published" | "rejected";
	favorite: boolean;
	status: "waiting" | "processing" | "succeeded" | "failed";
	canPublish: boolean;
	canDelete: boolean;
	canCreateSimilar: boolean;
	createdAt: Date;
	// 点赞相关字段
	starNum: number;
	isLiked?: boolean; // 当前用户是否已点赞
	user: {
		id: string;
		name: string;
		image?: string;
	};
}

export interface GenerationItemCardProps {
	generation: GenerationData;
	onPublish?: (id: string) => void;
	onFavorite?: (id: string) => Promise<void>; // 更新为异步类型
	onLike?: (id: string) => Promise<void>; // 点赞/取消点赞回调
	onDownload?: (id: string) => void;
	onDownloadWatermark?: (id: string) => void;
	onDownloadNoWatermark?: (id: string) => void;
	onShare?: (id: string) => void;
	onShareX?: (id: string) => void;
	onShareFacebook?: (id: string) => void;
	onCopyLink?: (id: string) => void;
	onDelete?: (id: string) => void;
	onCreateSimilar?: (id: string) => void;
	onClick?: (id: string) => void; // 卡片点击回调
	selectMode?: boolean;
	selected?: boolean;
	onSelect?: (selected: boolean) => void;
	className?: string;
}

export interface CardActions {
	onPublish?: (id: string) => void;
	onFavorite?: (id: string) => Promise<void>; // 更新为异步类型
	onLike?: (id: string) => Promise<void>; // 点赞/取消点赞回调
	onDownload?: (id: string) => void;
	onDownloadWatermark?: (id: string) => void;
	onDownloadNoWatermark?: (id: string) => void;
	onShare?: (id: string) => void;
	onShareX?: (id: string) => void;
	onShareFacebook?: (id: string) => void;
	onCopyLink?: (id: string) => void;
	onDelete?: (id: string) => void;
	onCreateSimilar?: (id: string) => void;
}
