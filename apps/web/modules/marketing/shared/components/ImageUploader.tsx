// apps/web/modules/marketing/shared/components/ImageUploader.tsx
"use client";

import { Button } from "@ui/components/button";
import { Image as ImageIcon, Upload, X } from "lucide-react";
import { useTranslations } from "next-intl";
import { useRef, useState } from "react";

interface ImageUploaderProps {
	onChange: (file: File | null) => void;
	maxSize?: number; // in MB
	acceptedFormats?: string[];
}

export function ImageUploader({
	onChange,
	maxSize = 10,
	acceptedFormats = ["image/jpeg", "image/png", "image/webp"],
}: ImageUploaderProps) {
	const t = useTranslations("imageToVideo");
	const [dragActive, setDragActive] = useState(false);
	const [preview, setPreview] = useState<string | null>(null);
	const [error, setError] = useState<string | null>(null);
	const [file, setFile] = useState<File | null>(null);
	const fileInputRef = useRef<HTMLInputElement>(null);

	const handleDrag = (e: React.DragEvent) => {
		e.preventDefault();
		e.stopPropagation();

		if (e.type === "dragenter" || e.type === "dragover") {
			setDragActive(true);
		} else if (e.type === "dragleave") {
			setDragActive(false);
		}
	};

	const validateFile = (file: File): string | null => {
		if (!acceptedFormats.includes(file.type)) {
			return t("shared.imageUploader.formatError", {
				formats: acceptedFormats
					.map((format) => format.split("/")[1].toUpperCase())
					.join(", "),
			});
		}

		if (file.size > maxSize * 1024 * 1024) {
			return t("shared.imageUploader.sizeError", { size: maxSize });
		}

		return null;
	};

	const handleFile = (file: File) => {
		const validationError = validateFile(file);

		if (validationError) {
			setError(validationError);
			setPreview(null);
			setFile(null);
			onChange(null);
			return;
		}

		setError(null);
		setFile(file);

		// Create preview
		const reader = new FileReader();
		reader.onload = (e) => {
			setPreview(e.target?.result as string);
		};
		reader.readAsDataURL(file);

		onChange(file);
	};

	const handleDrop = (e: React.DragEvent) => {
		e.preventDefault();
		e.stopPropagation();
		setDragActive(false);

		if (e.dataTransfer?.files?.[0]) {
			handleFile(e.dataTransfer.files[0]);
		}
	};

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		if (e.target?.files?.[0]) {
			handleFile(e.target.files[0]);
		}
	};

	const handleClick = () => {
		fileInputRef.current?.click();
	};

	const handleRemove = () => {
		setPreview(null);
		setError(null);
		setFile(null);
		if (fileInputRef.current) {
			fileInputRef.current.value = "";
		}
		onChange(null);
	};

	const formatList = acceptedFormats
		.map((format) => format.split("/")[1].toUpperCase())
		.join(", ");

	return (
		<div className="w-full">
			{preview ? (
				<div className="relative border border-gray-200 rounded-lg overflow-hidden shadow-sm transition-all hover:shadow-md">
					<div className="aspect-video bg-gray-50 relative">
						<img
							src={preview}
							alt={t("shared.imageUploader.uploadedImage")}
							className="w-full h-full object-contain"
						/>
					</div>
					<div className="p-3 flex items-center justify-between bg-gray-50 border-t border-gray-200">
						<div className="text-sm text-gray-600">
							{file?.name}{" "}
							<span className="text-gray-400">
								({Math.round((file?.size || 0) / 1024)} KB)
							</span>
						</div>
						<Button
							type="button"
							variant="outline"
							size="sm"
							onClick={handleRemove}
							className="h-8 px-3 text-xs bg-white hover:bg-gray-50 border border-gray-200 hover:border-gray-300 text-gray-700 hover:text-red-600 transition-colors"
							aria-label={t("shared.imageUploader.removeImage")}
						>
							<X className="h-3.5 w-3.5 mr-1" /> {t("shared.imageUploader.remove")}
						</Button>
					</div>
				</div>
			) : (
				<button
					type="button"
					className={`w-full border-2 border-dashed rounded-lg p-6 transition-colors cursor-pointer ${
						dragActive
							? "border-red-400 bg-red-50/10"
							: error
								? "border-red-500 bg-red-50/5"
								: "border-gray-300 hover:border-gray-400 hover:bg-gray-50/50"
					}`}
					onDragEnter={handleDrag}
					onDragLeave={handleDrag}
					onDragOver={handleDrag}
					onDrop={handleDrop}
					onClick={handleClick}
					aria-label={t("shared.imageUploader.dragOrClick")}
				>
					<div className="flex flex-col items-center justify-center text-center">
						<div className="mb-3 p-3 bg-gray-100 rounded-full">
							{dragActive ? (
								<Upload size={24} className="text-red-500" />
							) : (
								<ImageIcon
									size={24}
									className="text-gray-500"
								/>
							)}
						</div>
						<p className="mb-2 text-sm font-medium text-gray-700">
							{dragActive ? t("shared.imageUploader.dropHere") : t("shared.imageUploader.dragOrClick")}
						</p>
						<p className="text-xs text-gray-500">
							{t("shared.imageUploader.supportedFormats", { formats: formatList })}
						</p>
						<Button
							type="button"
							variant="outline"
							size="sm"
							className="mt-4 h-8 px-3 text-xs bg-white hover:bg-gray-50 border border-gray-200 hover:border-gray-300 shadow-sm rounded-md transition-colors"
						>
							{t("shared.imageUploader.browseFiles")}
						</Button>
						{error && (
							<p className="mt-3 text-sm text-red-500 font-medium">
								{error}
							</p>
						)}
					</div>
					<input
						ref={fileInputRef}
						type="file"
						accept={acceptedFormats.join(",")}
						onChange={handleChange}
						className="hidden"
						aria-label={t("shared.imageUploader.browseFiles")}
					/>
				</button>
			)}
		</div>
	);
}
