"use client";

import { useTranslations } from "next-intl";
import { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "@saas/auth/hooks/use-session";
import { useInfiniteGenerations } from "../hooks/useInfiniteGenerations";
import { useDownload } from "../hooks/useDownload";
import { useDelete } from "../hooks/useDelete";
import { useBulkOperations } from "../hooks/useBulkOperations";
import { FilterBar } from "./FilterBar";
import { GenerationItemCard } from "./GenerationItemCard";
import { LoadingSpinner } from "./LoadingSpinner";
import { LoginPrompt } from "./LoginPrompt";
import { useVideoStore } from "@shared/stores";

interface Props {
  type: 'video' | 'image';
  locale: string;
}

export function InfiniteMediaContent({ type, locale }: Props) {
  const t = useTranslations('myMedia');
  const router = useRouter();
  const { user, loaded } = useSession();
  const { setVideos } = useVideoStore();
  const [filters, setFilters] = useState({ favorites: false });
  const [selectMode, setSelectMode] = useState(false);
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());
  
  
  // 跟踪单个卡片的点赞状态更新
  const [likeUpdates, setLikeUpdates] = useState<Record<string, { isLiked: boolean; starNum: number }>>({});
  
  // 跟踪单个卡片的收藏状态更新
  const [favoriteUpdates, setFavoriteUpdates] = useState<Record<string, boolean>>({});
  
  // 无限滚动相关状态
  const loadMoreRef = useRef<HTMLDivElement>(null);
  
  const { 
    data: generations, 
    isLoading, 
    isLoadingMore,
    error, 
    hasNextPage,
    loadMore,
    refetch,
    // ✨ 新增的本地操作方法
    removeItems,
    updateItemsFavorite
  } = useInfiniteGenerations({ type, filters });


  const { 
    downloadWithWatermark, 
    downloadWithoutWatermark, 
    loading: downloadLoading, 
    error: downloadError 
  } = useDownload();

  const { 
    deleteGeneration,
    batchDeleteGenerations,
    loading: deleteLoading, 
    error: deleteError 
  } = useDelete();

  const { 
    bulkFavorite, 
    bulkDelete,
    bulkDownload,
    // ✨ 新增优化版本
    bulkDeleteOptimized,
    bulkFavoriteOptimized,
    bulkDownloadOptimized,
    loading: bulkOperationLoading, 
    error: bulkOperationError 
  } = useBulkOperations();

  // Intersection Observer 用于检测滚动到底部
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting && hasNextPage && !isLoadingMore) {
          loadMore();
        }
      },
      { threshold: 0.1, rootMargin: '100px' } // 提前 100px 开始加载
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current);
      }
    };
  }, [hasNextPage, isLoadingMore, loadMore]);

  // 筛选条件变化时重置列表
  useEffect(() => {
    setSelectedIds(new Set());
    setLikeUpdates({}); // 重置点赞状态
    setFavoriteUpdates({}); // 重置收藏状态
    // 注意：refetch不需要在这里调用，useInfiniteGenerations hook会自动处理filters变化
  }, [filters]);


  // 将API数据同步到video store，以便模态框可以访问
  useEffect(() => {
    if (generations.length > 0) {
      // 转换数据格式以匹配VideoData类型
      const videoData = generations.map(gen => {
        return {
        id: gen.id,
        title: gen.job?.prompt || 'Untitled',
        videoUrl: gen.videoUrl || gen.mediaUrl || '',
        posterUrl: gen.cover || gen.thumbnail || '',
        duration: gen.job?.duration || 5, // 使用Job表的duration
        author: {
          id: gen.user.id,
          name: gen.user.name,
          avatar: gen.user.image || ''
        },
        createdAt: gen.createdAt.toISOString(),
        status: gen.publishStatus || 'unpublished',
        type: 'image-to-video',
        originalImage: typeof gen.job?.image === 'string' ? gen.job.image : '', // ✅ 修复：使用Job表的原始输入图片
        prompt: typeof gen.job?.prompt === 'string' ? gen.job.prompt : '',       // ✅ 修复：使用Job表的提示词
        model: gen.job?.modelCode || 'Pollo 1.6',
        resolution: gen.job?.resolution || '480P',
        seed: gen.job?.seed?.toString() || '140269222',
        outputDimension: gen.job?.aspectRatio || '736 x 544',
        project: 'Default Project',
        likes: gen.starNum,
        isLiked: gen.isLiked || false,
        isFavorite: gen.favorite
        }
      });
      
      setVideos(videoData);
    }
  }, [generations, setVideos]);

  const handleCardSelect = (id: string, selected: boolean) => {
    const newSelected = new Set(selectedIds);
    if (selected) {
      newSelected.add(id);
    } else {
      newSelected.delete(id);
    }
    setSelectedIds(newSelected);
  };

  

  const handleBulkDownload = async () => {
    if (selectedIds.size === 0) {
      alert('请先选择要下载的项目');
      return;
    }

    console.log('批量下载:', Array.from(selectedIds));
    
    const success = await bulkDownload(Array.from(selectedIds));
    
    if (success) {
      // 更新UI状态
      setSelectedIds(new Set());
      setSelectMode(false);
      alert(`成功下载 ${selectedIds.size} 个项目`);
    } else if (bulkOperationError) {
      alert(`批量下载操作失败: ${bulkOperationError}`);
    }
  };

  // ✨ 新增：优化版批量下载处理
  const handleBulkDownloadOptimized = async () => {
    if (selectedIds.size === 0) {
      alert('请先选择要下载的项目');
      return;
    }

    const idsToDownload = Array.from(selectedIds);
    console.log('📥 [UI] 开始优化版批量下载:', idsToDownload);
    
    const success = await bulkDownloadOptimized(idsToDownload, (downloadUrls) => {
      console.log('✅ [UI] 下载API成功，开始下载文件');
      
      // ✨ 关键：API成功后处理所有下载链接
      downloadUrls.forEach((url, index) => {
        const generationId = idsToDownload[index];
        const filename = `generation_${generationId}.mp4`; // 可以根据实际媒体类型调整扩展名
        
        // 延迟下载避免浏览器阻止多个下载
        setTimeout(() => {
          downloadFile(url, filename);
        }, index * 100); // 每个文件间隔100ms下载
      });
    });
    
    if (success) {
      setSelectedIds(new Set());
      setSelectMode(false);
      alert(`成功下载 ${idsToDownload.length} 个项目`);
    } else if (bulkOperationError) {
      alert(`批量下载操作失败: ${bulkOperationError}`);
    }
  };

  // 工具函数：触发文件下载
  const downloadFile = (url: string, filename: string) => {
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.target = '_blank'; // 如果下载失败则在新标签页打开
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const handleBulkFavorite = async () => {
    if (selectedIds.size === 0) {
      alert('请先选择要收藏的项目');
      return;
    }

    console.log('批量收藏:', Array.from(selectedIds));
    
    // 询问用户操作类型
    const shouldFavorite = confirm(`是否要收藏选中的 ${selectedIds.size} 个项目？\n\n点击确定收藏，点击取消取消收藏`);
    
    const success = await bulkFavorite(Array.from(selectedIds), shouldFavorite);
    
    if (success) {
      // 更新UI状态
      setSelectedIds(new Set());
      setSelectMode(false);
      await refetch();
      alert(`成功${shouldFavorite ? '收藏' : '取消收藏'} ${selectedIds.size} 个项目`);
    } else if (bulkOperationError) {
      alert(`批量收藏操作失败: ${bulkOperationError}`);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedIds.size === 0) {
      alert('请先选择要删除的项目');
      return;
    }

    if (!confirm(`确定要删除选中的 ${selectedIds.size} 个项目吗？此操作不可撤销。`)) {
      return;
    }

    console.log('批量删除:', Array.from(selectedIds));
    
    console.log('🔥 [Debug] 开始批量删除，当前数据量:', generations.length);
    const success = await bulkDelete(Array.from(selectedIds));
    
    if (success) {
      console.log('✅ [Debug] 批量删除成功，准备刷新数据...');
      // 更新UI状态
      setSelectedIds(new Set());
      setSelectMode(false);
      
      console.log('🔄 [Debug] 调用 refetch() 重新获取数据...');
      console.time('refetch-duration');
      await refetch();
      console.timeEnd('refetch-duration');
      console.log('📊 [Debug] refetch 完成，新的数据量:', generations.length);
      
      alert(`成功删除 ${selectedIds.size} 个项目`);
    } else if (bulkOperationError) {
      console.error('❌ [Debug] 批量删除失败:', bulkOperationError);
      alert(`批量删除操作失败: ${bulkOperationError}`);
    }
  };

  // ✨ 新增：优化版批量收藏处理
  const handleBulkFavoriteOptimized = async () => {
    if (selectedIds.size === 0) {
      alert('请先选择要收藏的项目');
      return;
    }

    const shouldFavorite = confirm(`是否要收藏选中的 ${selectedIds.size} 个项目？\n\n点击确定收藏，点击取消取消收藏`);
    
    const idsToUpdate = Array.from(selectedIds);
    console.log('⭐ [UI] 开始优化版批量收藏:', idsToUpdate, shouldFavorite);
    
    const success = await bulkFavoriteOptimized(idsToUpdate, shouldFavorite, (updatedIds, favorite) => {
      console.log('✅ [UI] 收藏API成功，更新本地状态');
      
      // ✨ 关键：API成功后立即更新本地状态，不调用 refetch()
      updateItemsFavorite(updatedIds, favorite);
      
      // 清理收藏状态缓存
      setFavoriteUpdates(prev => {
        const newUpdates = { ...prev };
        updatedIds.forEach(id => delete newUpdates[id]);
        return newUpdates;
      });
    });
    
    if (success) {
      setSelectedIds(new Set());
      setSelectMode(false);
      alert(`成功${shouldFavorite ? '收藏' : '取消收藏'} ${idsToUpdate.length} 个项目`);
    } else if (bulkOperationError) {
      alert(`批量收藏操作失败: ${bulkOperationError}`);
    }
  };

  // ✨ 新增：优化版批量删除处理
  const handleBulkDeleteOptimized = async () => {
    if (selectedIds.size === 0) {
      alert('请先选择要删除的项目');
      return;
    }

    if (!confirm(`确定要删除选中的 ${selectedIds.size} 个项目吗？此操作不可撤销。`)) {
      return;
    }

    const idsToDelete = Array.from(selectedIds);
    console.log('🗑️ [UI] 开始优化版批量删除:', idsToDelete);
    
    const success = await bulkDeleteOptimized(idsToDelete, (deletedIds) => {
      console.log('✅ [UI] 删除API成功，更新本地状态');
      
      // ✨ 关键：API成功后立即更新本地状态，不调用 refetch()
      removeItems(deletedIds);
      
      // 清理相关本地缓存
      setLikeUpdates(prev => {
        const newUpdates = { ...prev };
        deletedIds.forEach(id => delete newUpdates[id]);
        return newUpdates;
      });
      
      setFavoriteUpdates(prev => {
        const newUpdates = { ...prev };
        deletedIds.forEach(id => delete newUpdates[id]);
        return newUpdates;
      });
    });
    
    if (success) {
      setSelectedIds(new Set());
      setSelectMode(false);
      alert(`成功删除 ${idsToDelete.length} 个项目`);
    } else if (bulkOperationError) {
      alert(`批量删除操作失败: ${bulkOperationError}`);
    }
  };

  const handleCancelSelection = () => {
    setSelectedIds(new Set());
    setSelectMode(false);
  };

  const handleSelectModeToggle = (enabled: boolean) => {
    setSelectMode(enabled);
    if (!enabled) {
      // 退出选择模式时清空选中项
      setSelectedIds(new Set());
    }
  };

  const handleSelectAll = () => {
    if (selectedIds.size === generations.length) {
      // 如果已全选，则取消全选
      setSelectedIds(new Set());
    } else {
      // 否则全选
      setSelectedIds(new Set(generations.map(g => g.id)));
    }
  };

  // 渲染内容区域
  const renderContent = () => {
    // 如果session还未加载完成，或者数据正在加载且没有数据，显示加载状态
    if ((!loaded || isLoading) && generations.length === 0) {
      return (
        <div className="flex justify-center items-center py-12">
          <LoadingSpinner />
          <span className="ml-2 text-muted-foreground">{t('loading')}</span>
        </div>
      );
    }

    if (error && generations.length === 0) {
      return (
        <div className="text-center py-8">
          <p className="text-red-500 mb-4">{t('errorLoading')}</p>
          <button 
            type="button"
            onClick={() => refetch()} 
            className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
          >
            {t('retry')}
          </button>
        </div>
      );
    }

    // 由于页面在marketing路由组下（公开页面），显示空状态
    // 引导用户创建内容而不是强制登录
    // 只有session完全加载后才显示空状态提示
    if (loaded && !isLoading && generations.length === 0 && !error) {
      return <LoginPrompt locale={locale} type={type} />;
    }

    return (
      <>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {generations.map((generation) => {
            // 合并原始数据和状态更新
            const updatedGeneration = {
              ...generation,
              ...(likeUpdates[generation.id] && {
                isLiked: likeUpdates[generation.id].isLiked,
                starNum: likeUpdates[generation.id].starNum
              }),
              // 合并收藏状态更新
              ...(favoriteUpdates[generation.id] !== undefined && {
                favorite: favoriteUpdates[generation.id]
              })
            };
            
            return (
              <GenerationItemCard
                key={generation.id}
                generation={updatedGeneration}
              selectMode={selectMode}
              selected={selectedIds.has(generation.id)}
              onSelect={(selected) => handleCardSelect(generation.id, selected)}
              onPublish={() => {/* TODO: 实现发布逻辑 */}}
              onLike={async (id) => {
                const startTime = Date.now();
                console.log('Like clicked for generation:', id);
                try {
                  const response = await fetch(`/api/generations/${id}/likes/toggle`, {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                  });

                  if (!response.ok) {
                    const errorData = await response.json();
                    console.error('Error toggling like:', errorData.error);
                    return;
                  }

                  const result = await response.json();
                  const clientResponseTime = Date.now() - startTime;
                  
                  console.log(`🚀 Like toggled successfully in ${clientResponseTime}ms (Server: ${result.responseTime}ms):`, result);
                  
                  // 只更新这个卡片的状态，不重新获取所有数据
                  setLikeUpdates(prev => ({
                    ...prev,
                    [id]: {
                      isLiked: result.isLiked,
                      starNum: result.likeCount
                    }
                  }));
                } catch (error) {
                  console.error('Error toggling like:', error);
                }
              }}
              onFavorite={async (id) => {
                console.log('Favorite clicked for generation:', id);
                try {
                  const response = await fetch(`/api/generations/${id}/favorite/toggle`, {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                  });

                  if (!response.ok) {
                    const errorData = await response.json();
                    console.error('Error toggling favorite:', errorData.error);
                    
                    // 处理不同类型的错误
                    if (response.status === 403) {
                      alert('You can only favorite your own videos');
                    } else if (response.status === 404) {
                      alert('Video not found');
                    } else {
                      alert('Failed to update favorite status');
                    }
                    return;
                  }

                  const result = await response.json(); // { favorite: true|false }
                  console.log('Favorite toggled successfully:', result);
                  
                  // 更新单个卡片的收藏状态，无需重新获取所有数据
                  setFavoriteUpdates(prev => ({
                    ...prev,
                    [id]: result.favorite
                  }));
                } catch (error) {
                  console.error('Error toggling favorite:', error);
                  alert('Network error occurred');
                }
              }}
              onDownload={() => {/* TODO: 实现下载逻辑 */}}
              onDownloadWatermark={downloadWithWatermark}
              onDownloadNoWatermark={downloadWithoutWatermark}
              onShare={() => {/* TODO: 实现分享逻辑 */}}
              onCopyLink={() => {/* TODO: 实现复制链接逻辑 */}}
              onDelete={async (id) => {
                console.log('Delete clicked for generation:', id);
                if (confirm('Are you sure you want to delete this generation?')) {
                  console.log('User confirmed deletion, calling deleteGeneration...');
                  const success = await deleteGeneration(id);
                  console.log('Delete result:', success);
                  if (success) {
                    await refetch();
                  } else if (deleteError) {
                    alert(`Failed to delete generation: ${deleteError}`);
                  }
                }
              }}
              onCreateSimilar={() => {/* TODO: 实现创建相似逻辑 */}}
              />
            );
          })}
        </div>

        {/* 加载更多触发区域 */}
        <div ref={loadMoreRef} className="py-4">
          {isLoadingMore && (
            <div className="flex justify-center items-center">
              <LoadingSpinner />
              <span className="ml-2 text-muted-foreground">{t('loadingMore')}</span>
            </div>
          )}
          
          {!hasNextPage && generations.length > 0 && (
            <div className="text-center py-8">
              <p className="text-muted-foreground">{t('noMoreContent')}</p>
            </div>
          )}
        </div>

        {generations.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">{t('noContent')}</p>
          </div>
        )}
      </>
    );
  };

  return (
    <div className="space-y-6">
      {/* FilterBar 始终显示，确保用户可以切换筛选条件 */}
      <FilterBar
        onFiltersChange={setFilters}
        onSelectModeToggle={handleSelectModeToggle}
        selectedCount={selectedIds.size}
        totalCount={generations.length}
        onSelectAll={handleSelectAll}
        onBulkFavorite={handleBulkFavoriteOptimized} // ✨ 使用优化版本
        onBulkDelete={handleBulkDeleteOptimized}     // ✨ 使用优化版本
        onBulkDownload={handleBulkDownloadOptimized} // ✨ 使用优化版本
        bulkOperationLoading={bulkOperationLoading}
      />

      {/* 渲染主要内容 */}
      {renderContent()}
      
    </div>
  );
}