'use client';

import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { Button } from '@ui/components/button';

interface LoginPromptProps {
  locale: string;
  type: 'video' | 'image';
}

export function LoginPrompt({ locale, type }: LoginPromptProps) {
  const t = useTranslations('myMedia');

  return (
    <div className="flex flex-col items-center justify-center py-16 px-4 text-center min-h-[400px]">
      <div className="max-w-md mx-auto">
        {/* 复古电视机图标 - 使用 SVG */}
        <div className="relative mb-8 mx-auto w-32 h-24">
          <svg
            viewBox="0 0 200 150"
            className="w-full h-full text-muted-foreground"
            fill="currentColor"
          >
            {/* TV 外框 */}
            <rect x="20" y="20" width="160" height="100" rx="8" fill="currentColor" opacity="0.8" />
            <rect x="25" y="25" width="150" height="80" rx="4" fill="background" />
            
            {/* 屏幕 */}
            <rect x="30" y="30" width="140" height="70" rx="2" fill="currentColor" opacity="0.1" />
            
            {/* 小浣熊图标 - 简化版 */}
            <circle cx="100" cy="60" r="12" fill="currentColor" opacity="0.6" />
            <circle cx="95" cy="57" r="2" fill="background" />
            <circle cx="105" cy="57" r="2" fill="background" />
            <path d="M85 50 Q90 45 95 50 Q100 45 105 50 Q110 45 115 50" stroke="currentColor" strokeWidth="1.5" fill="none" opacity="0.6" />
            
            {/* TV 控制旋钮 */}
            <circle cx="175" cy="40" r="6" fill="currentColor" opacity="0.6" />
            <circle cx="175" cy="55" r="6" fill="currentColor" opacity="0.6" />
            
            {/* TV 天线 */}
            <line x1="90" y1="20" x2="80" y2="5" stroke="currentColor" strokeWidth="2" opacity="0.4" />
            <line x1="110" y1="20" x2="120" y2="5" stroke="currentColor" strokeWidth="2" opacity="0.4" />
            
            {/* TV 腿 */}
            <rect x="60" y="120" width="8" height="12" fill="currentColor" opacity="0.6" />
            <rect x="132" y="120" width="8" height="12" fill="currentColor" opacity="0.6" />
          </svg>
        </div>
        
        <h2 className="text-2xl font-semibold mb-4 text-foreground">
          {t('emptyStateTitle')}
        </h2>
        
        <p className="text-muted-foreground mb-8">
          {t('emptyStateDescription')}
        </p>
        
        <Button asChild size="lg" className="bg-pink-600 hover:bg-pink-700">
          <Link href={`/${locale}/image-to-video`}>
            {type === 'video' ? t('createVideoNow') : t('createImageNow')}
          </Link>
        </Button>
      </div>
    </div>
  );
}