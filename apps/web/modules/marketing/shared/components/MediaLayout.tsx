import { useTranslations } from 'next-intl';
import { TabNavigation } from './TabNavigation';

interface MediaLayoutProps {
  children: React.ReactNode;
  currentTab: 'videos' | 'images';
  locale: string;
}

export function MediaLayout({ children, currentTab, locale }: MediaLayoutProps) {
  const t = useTranslations('myMedia');
  
  return (
    <div className="container mx-auto px-4 pt-24 md:pt-32 pb-12 lg:pb-16">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-4">
          {currentTab === 'videos' ? t('myVideos') : t('myImages')}
        </h1>
        <TabNavigation currentTab={currentTab} locale={locale} />
      </div>
      {children}
    </div>
  );
}