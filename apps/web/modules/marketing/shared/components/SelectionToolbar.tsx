'use client';

import { useTranslations } from 'next-intl';
import { Button } from '@ui/components/button';
import { Download, Heart, Trash2, X, Star, Plus } from 'lucide-react';

interface SelectionToolbarProps {
  selectedCount: number;
  selectMode: boolean;
  onBulkDownload: () => void;
  onBulkFavorite: () => void;
  onBulkDelete: () => void;
  onCancel: () => void;
}

export function SelectionToolbar({
  selectedCount,
  selectMode,
  onBulkDownload,
  onBulkFavorite,
  onBulkDelete,
  onCancel
}: SelectionToolbarProps) {
  const t = useTranslations('myMedia');

  if (!selectMode) {
    return null;
  }

  return (
    <div className="fixed left-4 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white rounded-lg shadow-lg px-4 py-3 z-50">
      <div className="flex items-center space-x-4">
        <span className="text-sm font-medium whitespace-nowrap">
          Select All ({selectedCount} selected)
        </span>
        
        <div className="flex items-center space-x-2">
          <Button 
            size="sm" 
            variant="ghost" 
            onClick={onBulkFavorite} 
            className="text-white hover:bg-gray-800 flex items-center space-x-1"
            title="Favorite"
          >
            <Star className="w-4 h-4" />
            <span className="text-sm">Favorite</span>
          </Button>

          <Button 
            size="sm" 
            variant="ghost" 
            onClick={onBulkDownload} 
            className="text-white hover:bg-gray-800 flex items-center space-x-1"
            title="Download"
          >
            <Download className="w-4 h-4" />
            <span className="text-sm">Download</span>
          </Button>

          <Button 
            size="sm" 
            variant="ghost" 
            onClick={onBulkDelete} 
            className="text-white hover:bg-gray-800 flex items-center space-x-1"
            title="Delete"
          >
            <Trash2 className="w-4 h-4" />
            <span className="text-sm">Delete</span>
          </Button>

          <Button 
            size="sm" 
            variant="ghost" 
            className="text-white hover:bg-gray-800 flex items-center space-x-1"
            title="Add to"
          >
            <Plus className="w-4 h-4" />
            <span className="text-sm">Add to</span>
          </Button>
        </div>

        <Button 
          size="sm" 
          variant="ghost" 
          onClick={onCancel} 
          className="text-white hover:bg-gray-800 ml-4"
          title="Close"
        >
          <X className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}