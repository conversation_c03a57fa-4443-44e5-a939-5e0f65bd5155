'use client';

import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { cn } from '@ui/lib';
import { useEffect, useRef, useState } from 'react';

interface TabNavigationProps {
  currentTab: 'videos' | 'images';
  locale: string;
  onTabChange?: (tab: 'videos' | 'images') => void;
}

export function TabNavigation({ currentTab, locale, onTabChange }: TabNavigationProps) {
  const t = useTranslations('myMedia');
  const navRef = useRef<HTMLDivElement>(null);
  const [indicatorStyle, setIndicatorStyle] = useState({ width: 0, left: 0 });
  
  const tabs = [
    { 
      key: 'videos' as const,
      label: t('videos'), 
      href: `/${locale}/my-generations?tab=videos` 
    },
    { 
      key: 'images' as const,
      label: t('images'), 
      href: `/${locale}/my-generations?tab=images` 
    },
  ];

  const activeIndex = tabs.findIndex(tab => tab.key === currentTab);

  useEffect(() => {
    if (navRef.current) {
      const activeButton = navRef.current.children[activeIndex] as HTMLElement;
      if (activeButton) {
        const { offsetLeft, offsetWidth } = activeButton;
        setIndicatorStyle({
          left: offsetLeft,
          width: offsetWidth
        });
      }
    }
  }, [activeIndex]);

  return (
    <div className="relative">
      <nav ref={navRef} className="flex space-x-8">
        {tabs.map((tab) => {
          const isActive = currentTab === tab.key;
          
          // 如果提供了onTabChange回调，使用客户端切换
          if (onTabChange) {
            return (
              <button
                key={tab.key}
                onClick={() => onTabChange(tab.key)}
                className={cn(
                  'py-2 px-1 font-medium text-sm transition-colors duration-300',
                  isActive
                    ? 'text-primary'
                    : 'text-muted-foreground hover:text-foreground'
                )}
              >
                {tab.label}
              </button>
            );
          }
          
          // 否则使用原来的Link方式（向后兼容）
          return (
            <Link
              key={tab.key}
              href={tab.href}
              className={cn(
                'py-2 px-1 font-medium text-sm transition-colors duration-300',
                isActive
                  ? 'text-primary'
                  : 'text-muted-foreground hover:text-foreground'
              )}
            >
              {tab.label}
            </Link>
          );
        })}
      </nav>
      
      {/* 平移的下划线 */}
      <div
        className="absolute bottom-0 h-0.5 bg-gradient-to-r from-violet-500 to-blue-500 rounded-full transition-all duration-500 ease-out"
        style={{
          width: `${indicatorStyle.width}px`,
          transform: `translateX(${indicatorStyle.left}px)`
        }}
      />
    </div>
  );
}