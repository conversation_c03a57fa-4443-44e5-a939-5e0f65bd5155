'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { useRouter, useSearchParams } from 'next/navigation';
import { TabNavigation } from './TabNavigation';
import { InfiniteMediaContent } from './InfiniteMediaContent';

interface UnifiedMediaPageProps {
  locale: string;
  initialTab?: 'videos' | 'images';
}

export function UnifiedMediaPage({ locale, initialTab = 'videos' }: UnifiedMediaPageProps) {
  const t = useTranslations('myMedia');
  const router = useRouter();
  const [currentTab, setCurrentTab] = useState<'videos' | 'images'>(initialTab);

  const handleTabChange = (tab: 'videos' | 'images') => {
    setCurrentTab(tab);
    // 更新URL但不刷新页面
    router.replace(`/${locale}/my-generations?tab=${tab}`, { scroll: false });
  };

  return (
    <div className="container mx-auto px-4 pt-24 md:pt-32 pb-12 lg:pb-16">
      <div className="mb-1">
        <h1 className="text-2xl font-bold mb-4">
          {currentTab === 'videos' ? t('myVideos') : t('myImages')}
        </h1>
        <TabNavigation 
          currentTab={currentTab} 
          locale={locale} 
          onTabChange={handleTabChange}
        />
      </div>
      
      {currentTab === 'videos' && (
        <InfiniteMediaContent type="video" locale={locale} />
      )}
      
      {currentTab === 'images' && (
        <div className="text-center py-12">
          <div className="max-w-md mx-auto">
            <div className="mb-4">
              <svg 
                className="mx-auto h-12 w-12 text-muted-foreground" 
                fill="none" 
                viewBox="0 0 24 24" 
                stroke="currentColor"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" 
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-foreground mb-2">
              {t('imagesComingSoon')}
            </h3>
            <p className="text-muted-foreground">
              {t('imagesFeatureDescription')}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}