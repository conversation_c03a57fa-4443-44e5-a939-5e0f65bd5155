"use client";

import { Crown } from "lucide-react";
import Link from "next/link";
import { cn } from "@ui/lib";

interface UpgradeButtonProps {
  isCollapsed?: boolean;
  className?: string;
}

export function UpgradeButton({ isCollapsed = false, className }: UpgradeButtonProps) {
  if (isCollapsed) {
    return (
      <div className={cn("px-2", className)}>
        <Link 
          href="/pricing" 
          className="flex items-center justify-center w-full h-9 rounded-md bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 transition-colors"
          title="Upgrade Now"
        >
          <Crown size={16} />
        </Link>
      </div>
    );
  }
  
  return (
    <div className={cn("px-2", className)}>
      <Link 
        href="/pricing" 
        className="flex items-center justify-center gap-2 w-full py-2 px-4 rounded-md bg-gradient-to-r from-purple-500 to-pink-500 text-white font-medium hover:from-purple-600 hover:to-pink-600 transition-colors"
      >
        <Crown size={16} />
        <span>Upgrade Now</span>
      </Link>
    </div>
  );
}
