// apps/web/modules/marketing/shared/components/prompt-generator/PromptGeneratorModal.tsx
"use client";

import { But<PERSON> } from "@ui/components/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ui/components/dialog";
import { Textarea } from "@ui/components/textarea";
import { Copy, X, ArrowLeft } from "lucide-react";
import { useState, useCallback } from "react";
import { useToast } from "@ui/components/use-toast";
import { generatePromptsFromIdea } from "../../lib/prompts-api";

interface PromptGeneratorModalProps {
  open: boolean;
  onClose: () => void;
  onSelect: (prompt: string) => void;
  generationType?: 'video' | 'image' | 'text';
}

type ModalStep = 'input' | 'selection';

export function PromptGeneratorModal({
  open,
  onClose,
  onSelect,
  generationType = 'video',
}: PromptGeneratorModalProps) {
  // 状态管理
  const [currentStep, setCurrentStep] = useState<ModalStep>('input');
  const [ideaText, setIdeaText] = useState("");
  const [generatedPrompts, setGeneratedPrompts] = useState<string[]>([]);
  const [selectedPrompt, setSelectedPrompt] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [lastIdea, setLastIdea] = useState<string>("");
  
  const { toast } = useToast();
  const MAX_CHARS = 300;

  // 处理文本输入
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value;
    if (newText.length <= MAX_CHARS) {
      setIdeaText(newText);
    }
  };

  // 处理创意提交
  const handleIdeaSubmit = useCallback(async () => {
    if (!ideaText.trim() || isGenerating) return;

    setIsGenerating(true);
    setLastIdea(ideaText.trim());
    
    try {
      const prompts = await generatePromptsFromIdea(ideaText.trim(), generationType);
      setGeneratedPrompts(prompts);
      setCurrentStep('selection');
    } catch (error) {
      console.error('Failed to generate prompts:', error);
      toast.error(error instanceof Error ? error.message : "Failed to generate prompts. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  }, [ideaText, isGenerating, generationType, toast]);

  // 处理Generate more
  const handleGenerateMore = useCallback(async () => {
    if (!lastIdea) return;

    setIsGenerating(true);
    try {
      const newPrompts = await generatePromptsFromIdea(lastIdea, generationType);
      setGeneratedPrompts(prev => [...prev, ...newPrompts]);
    } catch (error) {
      console.error('Failed to generate more prompts:', error);
      toast.error("Failed to generate more prompts. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  }, [lastIdea, generationType, toast]);

  // 处理提示词选择
  const handlePromptSelect = (prompt: string) => {
    setSelectedPrompt(prompt);
  };

  // 处理确认选择
  const handleConfirm = () => {
    if (selectedPrompt) {
      onSelect(selectedPrompt);
      handleModalClose();
      toast.success("The selected prompt has been added to your input.");
    }
  };

  // 处理复制
  const handleCopy = async (prompt: string) => {
    try {
      await navigator.clipboard.writeText(prompt);
      toast.success("Prompt copied to clipboard");
    } catch (error) {
      console.error('Failed to copy text:', error);
      toast.error("Failed to copy prompt to clipboard");
    }
  };

  // 返回上一步
  const handleBack = () => {
    setCurrentStep('input');
    setSelectedPrompt(null);
  };

  // 处理模态框关闭
  const handleModalClose = () => {
    if (!isGenerating) {
      setCurrentStep('input');
      setIdeaText("");
      setGeneratedPrompts([]);
      setSelectedPrompt(null);
      setLastIdea("");
      onClose();
    }
  };

  // 键盘事件处理
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (currentStep === 'input' && e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleIdeaSubmit();
    }
  };

  const isSubmitDisabled = !ideaText.trim() || isGenerating;
  const isCharLimitReached = ideaText.length >= MAX_CHARS * 0.9;

  return (
    <Dialog open={open} onOpenChange={!isGenerating ? handleModalClose : undefined}>
      <DialogContent 
        className="sm:max-w-[600px] max-h-[80vh] flex flex-col"
        onPointerDownOutside={(e) => isGenerating && e.preventDefault()}
      >
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {currentStep === 'selection' && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBack}
                  disabled={isGenerating}
                  className="h-6 w-6 p-0"
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              )}
              <DialogTitle className="text-lg font-semibold text-gray-900">
                {currentStep === 'input' 
                  ? "Tell us anything about your video"
                  : "Choose a prompt to create your video"
                }
              </DialogTitle>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleModalClose}
              disabled={isGenerating}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        {/* 步骤指示器 */}
        <div className="flex items-center space-x-2 py-2">
          <div className={`w-2 h-2 rounded-full transition-colors ${
            currentStep === 'input' ? 'bg-red-500' : 'bg-gray-300'
          }`} />
          <div className={`flex-1 h-px transition-colors ${
            currentStep === 'selection' ? 'bg-red-500' : 'bg-gray-300'
          }`} />
          <div className={`w-2 h-2 rounded-full transition-colors ${
            currentStep === 'selection' ? 'bg-red-500' : 'bg-gray-300'
          }`} />
        </div>

        {/* 内容区域 */}
        <div className="flex-1 py-4 overflow-hidden">
          {currentStep === 'input' ? (
            // 创意输入步骤
            <div className="space-y-4">
              <div className="relative">
                <Textarea
                  value={ideaText}
                  onChange={handleTextChange}
                  onKeyDown={handleKeyDown}
                  placeholder="Futuristic fantasy video"
                  className="min-h-[120px] resize-none pr-16 text-sm"
                  disabled={isGenerating}
                  autoFocus
                />
                <div className={`absolute bottom-3 right-3 text-xs transition-colors ${
                  isCharLimitReached ? 'text-red-500' : 'text-muted-foreground'
                }`}>
                  {ideaText.length} / {MAX_CHARS}
                </div>
              </div>
              
              <div className="text-xs text-muted-foreground">
                Tip: Press Cmd+Enter (Mac) or Ctrl+Enter (Windows) to continue quickly
              </div>
            </div>
          ) : (
            // 提示词选择步骤
            <div className="max-h-[400px] overflow-y-auto pr-2">
              <div className="space-y-3">
                {generatedPrompts.map((prompt, index) => (
                  <div
                    key={`${index}-${prompt.slice(0, 20)}`}
                    className={`p-4 border rounded-lg relative transition-all cursor-pointer hover:border-gray-300 ${
                      selectedPrompt === prompt
                        ? "border-red-500 bg-red-50/10 shadow-sm"
                        : "border-gray-200"
                    }`}
                    onClick={() => handlePromptSelect(prompt)}
                  >
                    <div className="flex items-start">
                      <input
                        type="radio"
                        name="promptSelection"
                        checked={selectedPrompt === prompt}
                        onChange={() => handlePromptSelect(prompt)}
                        className="mt-1 mr-3 accent-red-500"
                        tabIndex={-1}
                      />
                      <p className="text-sm flex-1 pr-8 leading-relaxed text-gray-700">
                        {prompt}
                      </p>
                      <button
                        type="button"
                        className="absolute right-3 top-3 hover:bg-gray-100 p-1.5 rounded transition-colors"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCopy(prompt);
                        }}
                        title="Copy to clipboard"
                      >
                        <Copy size={14} className="text-gray-500" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Generate more 按钮 (仅在选择步骤显示) */}
        {currentStep === 'selection' && (
          <div className="flex justify-center py-2 border-t border-gray-100">
            <Button 
              variant="ghost" 
              onClick={handleGenerateMore}
              disabled={isGenerating}
              className="text-gray-600 hover:text-gray-800"
            >
              {isGenerating ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
                  <span>Generating...</span>
                </div>
              ) : (
                "Generate more"
              )}
            </Button>
          </div>
        )}

        {/* 底部按钮 */}
        <DialogFooter className="border-t border-gray-100 pt-4">
          <Button 
            variant="outline" 
            onClick={currentStep === 'input' ? handleModalClose : handleBack}
            disabled={isGenerating}
            className="min-w-[80px]"
          >
            {currentStep === 'input' ? 'Cancel' : 'Back'}
          </Button>
          
          {currentStep === 'input' ? (
            <Button 
              onClick={handleIdeaSubmit}
              disabled={isSubmitDisabled}
              className={`min-w-[100px] ${
                isSubmitDisabled 
                  ? "bg-gray-300 hover:bg-gray-300" 
                  : "bg-red-500 hover:bg-red-600"
              }`}
            >
              {isGenerating ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Loading...</span>
                </div>
              ) : (
                "Continue"
              )}
            </Button>
          ) : (
            <Button 
              onClick={handleConfirm} 
              disabled={!selectedPrompt || isGenerating}
              className={`min-w-[100px] ${
                !selectedPrompt || isGenerating
                  ? "bg-gray-300 hover:bg-gray-300" 
                  : "bg-red-500 hover:bg-red-600"
              }`}
            >
              Confirm
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}