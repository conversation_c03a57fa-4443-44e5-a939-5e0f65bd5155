// apps/web/modules/marketing/shared/components/prompt-generator/PromptIdeaInputModal.tsx
"use client";

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@ui/components/dialog";
import { Button } from "@ui/components/button";
import { Textarea } from "@ui/components/textarea";
import { useState } from "react";
import { X } from "lucide-react";

interface PromptIdeaInputModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (idea: string) => Promise<void>;
  isLoading?: boolean;
}

export function PromptIdeaInputModal({
  open,
  onClose,
  onSubmit,
  isLoading = false,
}: PromptIdeaInputModalProps) {
  const [ideaText, setIdeaText] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const MAX_CHARS = 300;

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value;
    if (newText.length <= MAX_CHARS) {
      setIdeaText(newText);
    }
  };

  const handleSubmit = async () => {
    if (ideaText.trim() && !isSubmitting) {
      setIsSubmitting(true);
      try {
        await onSubmit(ideaText.trim());
        setIdeaText("");
        onClose();
      } catch (error) {
        console.error('Error submitting idea:', error);
        // 错误会在父组件处理
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setIdeaText("");
      onClose();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const isSubmitDisabled = !ideaText.trim() || isSubmitting;
  const isCharLimitReached = ideaText.length >= MAX_CHARS * 0.9;

  return (
    <Dialog open={open} onOpenChange={!isSubmitting ? handleClose : undefined}>
      <DialogContent className="sm:max-w-[500px]" onPointerDownOutside={(e) => isSubmitting && e.preventDefault()}>
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-lg font-semibold text-gray-900">
              Tell us anything about your video
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              disabled={isSubmitting}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="relative">
            <Textarea
              value={ideaText}
              onChange={handleTextChange}
              onKeyDown={handleKeyDown}
              placeholder="Futuristic fantasy video"
              className="min-h-[120px] resize-none pr-16 text-sm"
              disabled={isSubmitting}
              autoFocus
            />
            <div className={`absolute bottom-3 right-3 text-xs transition-colors ${
              isCharLimitReached ? 'text-red-500' : 'text-muted-foreground'
            }`}>
              {ideaText.length} / {MAX_CHARS}
            </div>
          </div>
          
          {/* 提示文本 */}
          <div className="text-xs text-muted-foreground">
            Tip: Press Cmd+Enter (Mac) or Ctrl+Enter (Windows) to continue quickly
          </div>
        </div>
        
        <div className="flex justify-end space-x-2">
          <Button 
            variant="outline" 
            onClick={handleClose}
            disabled={isSubmitting}
            className="min-w-[80px]"
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={isSubmitDisabled}
            className={`min-w-[100px] ${
              isSubmitDisabled 
                ? "bg-gray-300 hover:bg-gray-300" 
                : "bg-red-500 hover:bg-red-600"
            }`}
          >
            {isSubmitting ? (
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                <span>Loading...</span>
              </div>
            ) : (
              "Continue"
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}