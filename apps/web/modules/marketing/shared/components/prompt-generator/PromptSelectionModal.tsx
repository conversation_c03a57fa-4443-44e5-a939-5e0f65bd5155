// apps/web/modules/marketing/shared/components/prompt-generator/PromptSelectionModal.tsx
"use client";

import { Button } from "@ui/components/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@ui/components/dialog";
import { Copy, X } from "lucide-react";
import { useState } from "react";
import { useToast } from "@ui/components/use-toast";

interface PromptSelectionModalProps {
  open: boolean;
  prompts: string[];
  onClose: () => void;
  onSelect: (prompt: string) => void;
  onGenerateMore: () => Promise<void>;
  isGeneratingMore?: boolean;
  maxPrompts?: number; // 最大显示提示词数量
}

export function PromptSelectionModal({
  open,
  prompts,
  onClose,
  onSelect,
  onGenerateMore,
  isGeneratingMore = false,
  maxPrompts = 20, // 默认最多显示20条
}: PromptSelectionModalProps) {
  const [selectedPrompt, setSelectedPrompt] = useState<string | null>(null);
  const { toast } = useToast();

  const handleSelect = (prompt: string) => {
    setSelectedPrompt(prompt);
  };

  const handleConfirm = () => {
    if (selectedPrompt) {
      onSelect(selectedPrompt);
      setSelectedPrompt(null);
    }
  };

  const handleCopy = async (prompt: string) => {
    try {
      await navigator.clipboard.writeText(prompt);
      toast.success("Prompt copied to clipboard");
    } catch (error) {
      console.error('Failed to copy text:', error);
      toast.error("Failed to copy prompt to clipboard");
    }
  };

  const handleGenerateMore = async () => {
    try {
      await onGenerateMore();
    } catch (error) {
      console.error('Error generating more prompts:', error);
      toast.error("Failed to generate more prompts. Please try again.");
    }
  };

  const handleClose = () => {
    if (!isGeneratingMore) {
      setSelectedPrompt(null);
      onClose();
    }
  };

  const displayedPrompts = prompts.slice(0, maxPrompts);
  const hasMoreThanMax = prompts.length > maxPrompts;

  return (
    <Dialog open={open} onOpenChange={!isGeneratingMore ? handleClose : undefined}>
      <DialogContent 
        className="sm:max-w-[600px] max-h-[80vh] flex flex-col"
        onPointerDownOutside={(e) => isGeneratingMore && e.preventDefault()}
      >
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-lg font-semibold text-gray-900">
              Choose a prompt to create your video
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              disabled={isGeneratingMore}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="flex-1 py-4 overflow-hidden">
          <div className="max-h-[400px] overflow-y-auto pr-2">
            <div className="space-y-3">
              {displayedPrompts.map((prompt, index) => (
                <div
                  key={`${index}-${prompt.slice(0, 20)}`} // 更安全的key
                  className={`p-4 border rounded-lg relative transition-all cursor-pointer hover:border-gray-300 ${
                    selectedPrompt === prompt
                      ? "border-red-500 bg-red-50/10 shadow-sm"
                      : "border-gray-200"
                  }`}
                  onClick={() => handleSelect(prompt)}
                >
                  <div className="flex items-start">
                    <input
                      type="radio"
                      name="promptSelection"
                      checked={selectedPrompt === prompt}
                      onChange={() => handleSelect(prompt)}
                      className="mt-1 mr-3 accent-red-500"
                      tabIndex={-1} // 防止tab导航到radio button
                    />
                    <p className="text-sm flex-1 pr-8 leading-relaxed text-gray-700">
                      {prompt}
                    </p>
                    <button
                      type="button"
                      className="absolute right-3 top-3 hover:bg-gray-100 p-1.5 rounded transition-colors"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCopy(prompt);
                      }}
                      title="Copy to clipboard"
                    >
                      <Copy size={14} className="text-gray-500" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
            
            {hasMoreThanMax && (
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800">
                  Showing {maxPrompts} of {prompts.length} prompts. 
                  The first {maxPrompts} are usually the best quality.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Generate more button */}
        <div className="flex justify-center py-2 border-t border-gray-100">
          <Button 
            variant="ghost" 
            onClick={handleGenerateMore}
            disabled={isGeneratingMore}
            className="text-gray-600 hover:text-gray-800"
          >
            {isGeneratingMore ? (
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
                <span>Generating...</span>
              </div>
            ) : (
              "Generate more"
            )}
          </Button>
        </div>

        <DialogFooter className="border-t border-gray-100 pt-4">
          <Button 
            variant="outline" 
            onClick={handleClose}
            disabled={isGeneratingMore}
            className="min-w-[80px]"
          >
            Cancel
          </Button>
          <Button 
            onClick={handleConfirm} 
            disabled={!selectedPrompt || isGeneratingMore}
            className={`min-w-[100px] ${
              !selectedPrompt || isGeneratingMore
                ? "bg-gray-300 hover:bg-gray-300" 
                : "bg-red-500 hover:bg-red-600"
            }`}
          >
            Confirm
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}