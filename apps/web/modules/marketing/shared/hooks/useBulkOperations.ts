'use client';

import { useState } from 'react';
import { useSession } from '@saas/auth/hooks/use-session';

interface UseBulkOperationsReturn {
  bulkFavorite: (generationIds: string[], favorite: boolean) => Promise<boolean>;
  bulkDelete: (generationIds: string[]) => Promise<boolean>;
  bulkDownload: (generationIds: string[]) => Promise<boolean>;
  // ✨ 新增优化版本
  bulkDeleteOptimized: (
    generationIds: string[], 
    onSuccess: (deletedIds: string[]) => void
  ) => Promise<boolean>;
  bulkFavoriteOptimized: (
    generationIds: string[], 
    favorite: boolean,
    onSuccess: (updatedIds: string[], favorite: boolean) => void
  ) => Promise<boolean>;
  bulkDownloadOptimized: (
    generationIds: string[],
    onSuccess: (downloadUrls: string[]) => void
  ) => Promise<boolean>;
  loading: boolean;
  error: string | null;
}

export function useBulkOperations(): UseBulkOperationsReturn {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { session } = useSession();

  const bulkFavorite = async (generationIds: string[], favorite: boolean): Promise<boolean> => {
    if (!session?.userId) {
      setError('用户未认证');
      return false;
    }

    if (generationIds.length === 0) {
      setError('没有选择要操作的项目');
      return false;
    }

    if (generationIds.length > 50) {
      setError('一次最多只能操作50个项目');
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      console.log(`[BulkOperations] 开始批量${favorite ? '收藏' : '取消收藏'} ${generationIds.length} 个项目`);
      
      const response = await fetch('/api/bulk/generations/favorite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          generationIds,
          favorite
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP错误! 状态: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.updated && Array.isArray(result.updated)) {
        console.log(`[BulkOperations] 成功更新 ${result.updated.length} 个项目的收藏状态`);
        return true;
      } else {
        throw new Error(result.error || result.message || '收藏失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '发生未知错误';
      console.error('[BulkOperations] 批量收藏操作错误:', errorMessage);
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const bulkDelete = async (generationIds: string[]): Promise<boolean> => {
    if (!session?.userId) {
      setError('用户未认证');
      return false;
    }

    if (generationIds.length === 0) {
      setError('没有选择要删除的项目');
      return false;
    }

    if (generationIds.length > 50) {
      setError('一次最多只能删除50个项目');
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      console.log(`[BulkOperations] 开始批量删除 ${generationIds.length} 个项目`);
      
      const response = await fetch('/api/bulk/generations/delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          generationIds
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP错误! 状态: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.deleted && Array.isArray(result.deleted)) {
        console.log(`[BulkOperations] 成功删除 ${result.deleted.length} 个项目`);
        return true;
      } else {
        throw new Error(result.error || result.message || '删除失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '发生未知错误';
      console.error('[BulkOperations] 批量删除操作错误:', errorMessage);
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // ✨ 新增：优化版批量删除
  const bulkDeleteOptimized = async (
    generationIds: string[], 
    onSuccess: (deletedIds: string[]) => void
  ): Promise<boolean> => {
    if (!session?.userId || generationIds.length === 0) {
      setError('参数错误');
      return false;
    }

    if (generationIds.length > 50) {
      setError('一次最多只能删除50个项目');
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      console.log(`🗑️ [BulkOperations] 开始优化版批量删除 ${generationIds.length} 个项目`);
      console.time('bulk-delete-optimized');
      
      const response = await fetch('/api/bulk/generations/delete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ generationIds }),
      });

      console.timeEnd('bulk-delete-optimized');

      if (!response.ok) {
        throw new Error(`HTTP错误! 状态: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.deleted && Array.isArray(result.deleted)) {
        console.log(`✅ [BulkOperations] API删除成功，返回 ${result.deleted.length} 个ID`);
        // ✨ 关键：API成功后调用回调更新本地状态，使用响应返回的ID列表
        onSuccess(result.deleted);
        return true;
      } else {
        throw new Error(result.error || result.message || '删除失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知错误';
      console.error('[BulkOperations] 优化版批量删除失败:', errorMessage);
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // ✨ 新增：优化版批量收藏
  const bulkFavoriteOptimized = async (
    generationIds: string[], 
    favorite: boolean,
    onSuccess: (updatedIds: string[], favorite: boolean) => void
  ): Promise<boolean> => {
    if (!session?.userId || generationIds.length === 0) {
      setError('参数错误');
      return false;
    }

    if (generationIds.length > 50) {
      setError('一次最多只能操作50个项目');
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      console.log(`⭐ [BulkOperations] 开始优化版批量收藏 ${generationIds.length} 个项目，状态: ${favorite}`);
      console.time('bulk-favorite-optimized');
      
      const response = await fetch('/api/bulk/generations/favorite', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ generationIds, favorite }),
      });

      console.timeEnd('bulk-favorite-optimized');

      if (!response.ok) {
        throw new Error(`HTTP错误! 状态: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.updated && Array.isArray(result.updated)) {
        console.log(`✅ [BulkOperations] API收藏成功，返回 ${result.updated.length} 个ID`);
        // ✨ 关键：API成功后调用回调更新本地状态，使用响应返回的ID列表
        onSuccess(result.updated, favorite);
        return true;
      } else {
        throw new Error(result.error || result.message || '收藏失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知错误';
      console.error('[BulkOperations] 优化版批量收藏失败:', errorMessage);
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const bulkDownload = async (generationIds: string[]): Promise<boolean> => {
    if (!session?.userId) {
      setError('用户未认证');
      return false;
    }

    if (generationIds.length === 0) {
      setError('没有选择要下载的项目');
      return false;
    }

    if (generationIds.length > 50) {
      setError('一次最多只能下载50个项目');
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      console.log(`[BulkOperations] 开始批量下载 ${generationIds.length} 个项目`);
      
      const response = await fetch('/api/bulk/generations/download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          generationIds
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP错误! 状态: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.downloadUrls && Array.isArray(result.downloadUrls)) {
        console.log(`[BulkOperations] 成功生成 ${result.downloadUrls.length} 个下载链接`);
        
        // 触发浏览器下载
        result.downloadUrls.forEach((url: string, index: number) => {
          const filename = `generation_${generationIds[index]}.mp4`;
          downloadFile(url, filename);
        });
        
        return true;
      } else {
        throw new Error(result.error || result.message || '下载失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '发生未知错误';
      console.error('[BulkOperations] 批量下载操作错误:', errorMessage);
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // ✨ 新增：优化版批量下载
  const bulkDownloadOptimized = async (
    generationIds: string[],
    onSuccess: (downloadUrls: string[]) => void
  ): Promise<boolean> => {
    if (!session?.userId || generationIds.length === 0) {
      setError('参数错误');
      return false;
    }

    if (generationIds.length > 50) {
      setError('一次最多只能下载50个项目');
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      console.log(`📥 [BulkOperations] 开始优化版批量下载 ${generationIds.length} 个项目`);
      console.time('bulk-download-optimized');
      
      const response = await fetch('/api/bulk/generations/download', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ generationIds }),
      });

      console.timeEnd('bulk-download-optimized');

      if (!response.ok) {
        throw new Error(`HTTP错误! 状态: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.downloadUrls && Array.isArray(result.downloadUrls)) {
        console.log(`✅ [BulkOperations] API下载成功，返回 ${result.downloadUrls.length} 个URL`);
        // ✨ 关键：API成功后调用回调，让上层处理下载逻辑
        onSuccess(result.downloadUrls);
        return true;
      } else {
        throw new Error(result.error || result.message || '下载失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知错误';
      console.error('[BulkOperations] 优化版批量下载失败:', errorMessage);
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // 工具函数：触发文件下载
  const downloadFile = (url: string, filename: string) => {
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.target = '_blank'; // 如果下载失败则在新标签页打开
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  return {
    bulkFavorite,
    bulkDelete,
    bulkDownload,
    // ✨ 新增优化版本
    bulkDeleteOptimized,
    bulkFavoriteOptimized,
    bulkDownloadOptimized,
    loading,
    error,
  };
}