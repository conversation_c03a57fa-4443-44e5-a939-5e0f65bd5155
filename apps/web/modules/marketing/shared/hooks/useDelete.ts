'use client';

import { useState } from 'react';
import { useSession } from '@saas/auth/hooks/use-session';

interface UseDeleteReturn {
  deleteGeneration: (generationId: string) => Promise<boolean>;
  batchDeleteGenerations: (generationIds: string[]) => Promise<boolean>;
  loading: boolean;
  error: string | null;
}

export function useDelete(): UseDeleteReturn {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { session } = useSession();

  const deleteGeneration = async (generationId: string): Promise<boolean> => {
    console.log('useDelete.deleteGeneration called:', { generationId, hasSession: !!session?.userId });
    
    if (!session?.userId) {
      setError('User not authenticated');
      console.log('Delete failed: User not authenticated');
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/generations/${generationId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.deleted) {
        console.log(`Successfully deleted generation ${generationId}`);
        return true;
      } else if (result.failed) {
        const errorMessage = Object.values(result.failed)[0] as string;
        throw new Error(errorMessage);
      } else {
        throw new Error('Unknown delete response format');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('Delete generation error:', errorMessage);
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const batchDeleteGenerations = async (generationIds: string[]): Promise<boolean> => {
    if (!session?.userId) {
      setError('User not authenticated');
      return false;
    }

    if (generationIds.length === 0) {
      setError('No generations to delete');
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      // 串行删除每个 generation，因为 API 不支持批量删除
      // 直接调用 API 而不是递归调用 deleteGeneration 来避免 loading 状态冲突
      const results = await Promise.allSettled(
        generationIds.map(async (generationId) => {
          try {
            const response = await fetch(`/api/generations/${generationId}`, {
              method: 'DELETE',
              headers: {
                'Content-Type': 'application/json',
              },
            });

            if (!response.ok) {
              const errorData = await response.json().catch(() => ({}));
              throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            
            if (result.deleted) {
              return true;
            } else if (result.failed) {
              const errorMessage = Object.values(result.failed)[0] as string;
              throw new Error(errorMessage);
            } else {
              throw new Error('Unknown delete response format');
            }
          } catch (err) {
            console.error(`Failed to delete generation ${generationId}:`, err);
            throw err;
          }
        })
      );

      const successCount = results.filter(result => 
        result.status === 'fulfilled' && result.value
      ).length;

      if (successCount === generationIds.length) {
        console.log(`Successfully deleted ${successCount} generations`);
        return true;
      } else {
        const failedCount = generationIds.length - successCount;
        throw new Error(`Failed to delete ${failedCount} out of ${generationIds.length} generations`);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('Batch delete generations error:', errorMessage);
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    deleteGeneration,
    batchDeleteGenerations,
    loading,
    error,
  };
}