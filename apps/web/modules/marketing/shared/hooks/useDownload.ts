"use client";

import { useState } from "react";
import { useSession } from "@saas/auth/hooks/use-session";

interface DownloadResult {
  success: boolean;
  error?: string;
}

export function useDownload() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useSession();

  const downloadWithWatermark = async (generationId: string): Promise<DownloadResult> => {
    if (!user) {
      return { success: false, error: "Please login first" };
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/generations/${generationId}/download?media=video&type=watermark`);

      const result = await response.json();

      if (response.ok && result.downloadUrl) {
        // 自动开始下载
        const link = document.createElement('a');
        link.href = result.downloadUrl;
        link.download = `video_${generationId}_watermark.mp4`;
        
        // 添加到DOM并触发点击
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        return { success: true };
      } else {
        const errorMessage = result.error || "Download failed";
        setError(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (err) {
      const errorMessage = "Network error, please try again";
      setError(errorMessage);
      console.error("Download error:", err);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const downloadWithoutWatermark = async (generationId: string): Promise<DownloadResult> => {
    if (!user) {
      return { success: false, error: "Please login first" };
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/generations/${generationId}/download?media=video&type=no-watermark`);

      const result = await response.json();

      if (response.ok && result.downloadUrl) {
        // 自动开始下载
        const link = document.createElement('a');
        link.href = result.downloadUrl;
        link.download = `video_${generationId}_no_watermark.mp4`;
        
        // 添加到DOM并触发点击
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        return { success: true };
      } else {
        const errorMessage = result.error || "Download failed";
        setError(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (err) {
      const errorMessage = "Network error, please try again";
      setError(errorMessage);
      console.error("Download error:", err);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const clearError = () => setError(null);

  return {
    downloadWithWatermark,
    downloadWithoutWatermark,
    loading,
    error,
    clearError,
  };
}