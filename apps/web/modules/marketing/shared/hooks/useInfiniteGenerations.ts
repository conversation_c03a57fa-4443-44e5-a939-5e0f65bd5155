'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSession } from '@saas/auth/hooks/use-session';

interface UseInfiniteGenerationsParams {
  type: 'video' | 'image';
  filters: {
    favorites: boolean;
    publishStatus?: string;
  };
}

interface ApiUser {
  id: string;
  name: string;
  image?: string;
}

interface Generation {
  id: string;
  userId: string;
  jobId: string;
  mediaId: string;
  cover?: string;
  thumbnail?: string;
  videoUrl?: string;
  mediaUrl?: string;
  publishStatus?: 'reviewing' | 'published' | 'rejected';
  favorite: boolean;
  status: 'waiting' | 'processing' | 'succeeded' | 'failed';
  mediaType: 'video' | 'image';
  canPublish: boolean;
  canDelete: boolean;
  canCreateSimilar: boolean;
  createdAt: Date;
  // 点赞相关字段
  starNum: number;
  isLiked?: boolean;
  user: {
    id: string;
    name: string;
    image?: string;
  };
  // Job关联数据
  job?: {
    prompt?: string;
    image?: string;
    duration?: number;
    modelCode?: string;
    resolution?: string;
    seed?: number;
    aspectRatio?: string;
  };
}

interface InfiniteScrollState {
  data: Generation[];
  page: number;
  hasNextPage: boolean;
  isLoading: boolean;
  isLoadingMore: boolean;
  error: string | null;
}

export function useInfiniteGenerations({ type, filters }: UseInfiniteGenerationsParams) {
  const [state, setState] = useState<InfiniteScrollState>({
    data: [],
    page: 0,
    hasNextPage: true,
    isLoading: true,
    isLoadingMore: false,
    error: null,
  });
  
  const { user, loaded } = useSession();

  // 构建基础查询参数
  const buildParams = useCallback((page: number) => {
    const params = new URLSearchParams();
    
    // 根据 tab 类型设置筛选参数
    if (type === 'video') {
      params.append('mediaType', 'video');
      params.append('generationType', 'video');
    } else if (type === 'image') {
      params.append('mediaType', 'image');
      params.append('generationType', 'image');
    }

    // 添加其他筛选条件
    if (filters.favorites) {
      params.append('favorite', 'true');
    }
    
    if (filters.publishStatus) {
      params.append('publishStatus', filters.publishStatus);
    }

    // 分页参数
    params.append('page', page.toString());
    params.append('pageSize', '20'); // 每次加载 20 个

    return params;
  }, [type, filters]);

  // 获取数据的核心函数
  const fetchGenerations = useCallback(async (page: number, isLoadMore = false) => {
    // 添加调试信息
    console.log('fetchGenerations called:', { 
      hasUser: !!user, 
      loaded,
      page, 
      isLoadMore 
    });
    
    // 如果session还未加载完成，保持加载状态
    if (!loaded) {
      console.log('Session not loaded yet, keeping loading state');
      return;
    }
    
    // 如果session已加载但用户未登录，返回空状态
    if (!user) {
      console.log('Session loaded but user not logged in, returning empty state');
      setState(prev => ({
        ...prev,
        data: [],
        page: 0,
        hasNextPage: false,
        isLoading: false,
        isLoadingMore: false,
        error: null,
      }));
      return;
    }

    console.log('User is logged in, calling API...');

    setState(prev => ({
      ...prev,
      isLoading: !isLoadMore,
      isLoadingMore: isLoadMore,
      error: null,
    }));

    try {
      const params = buildParams(page);
      const apiUrl = `/api/generations?${params}`;
      console.log('🌐 [API REQUEST] 发送请求到:', apiUrl);
      console.time('api-request-duration');
      
      const response = await fetch(apiUrl);
      console.timeEnd('api-request-duration');
      console.log('📡 [API RESPONSE] 状态码:', response.status, response.statusText);
      
      if (!response.ok) {
        throw new Error('Failed to fetch generations');
      }

      const result = await response.json();
      console.log('📊 [API RESPONSE] 数据:', {
        success: result.success,
        dataLength: result.data?.length,
        pagination: result.pagination,
        isLoadMore,
        timestamp: new Date().toISOString()
      });
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch generations');
      }

      const rawGenerations = result.data.generations;
      const rawJobs = result.data.jobs || [];
      const users = result.data.users || [];
      const pagination = result.data.pagination;

      // 调试：检查原始数据结构
      console.log('API response data:', {
        rawGenerations: rawGenerations.slice(0, 2),
        rawJobs: rawJobs.slice(0, 2),
        users: users,
        firstGeneration: rawGenerations[0],
        firstJob: rawJobs[0]
      });

      // 将 users 数组转换成 userId -> user 的映射
      const userMap = new Map<string, ApiUser>();
      users.forEach((user: ApiUser) => {
        userMap.set(user.id, user);
      });

      // 将 jobs 数组转换成 jobId -> job 的映射
      const jobMap = new Map<string, any>();
      rawJobs.forEach((job: any) => {
        jobMap.set(job.id, job);
      });

      // 为每个 generation 添加对应的 user 和 job 信息
      const newData = rawGenerations.map((generation: any) => {
        const job = jobMap.get(generation.jobId);
        console.log(`🔍 Generation ${generation.id} -> Job ${generation.jobId}:`, {
          foundJob: !!job,
          jobPrompt: job?.prompt,
          jobImage: job?.image
        });
        
        return {
          ...generation,
          createdAt: new Date(generation.createdAt),
          starNum: generation.starNum || 0,
          isLiked: generation.isLiked || false,
          user: userMap.get(generation.userId) || {
            id: generation.userId,
            name: 'Unknown User',
            image: null
          },
          job: job || null
        };
      });

      console.log('🔄 [STATE UPDATE] 准备更新状态:', {
        isLoadMore,
        newDataLength: newData.length,
        prevDataLength: isLoadMore ? '会保留之前数据' : '会替换数据',
        pagination
      });

      setState(prev => {
        const newState = {
          ...prev,
          data: isLoadMore ? [...prev.data, ...newData] : newData,
          page: pagination.page,
          hasNextPage: pagination.hasNext,
          isLoading: false,
          isLoadingMore: false,
          error: null,
        };
        
        console.log('✅ [STATE UPDATED] 状态已更新:', {
          finalDataLength: newState.data.length,
          page: newState.page,
          hasNextPage: newState.hasNextPage
        });
        
        return newState;
      });
    } catch (err) {
      setState(prev => ({
        ...prev,
        error: err instanceof Error ? err.message : 'Unknown error',
        isLoading: false,
        isLoadingMore: false,
      }));
    }
  }, [user, loaded, buildParams]);

  // 加载更多数据
  const loadMore = useCallback(() => {
    if (!state.hasNextPage || state.isLoadingMore) {
      return;
    }
    fetchGenerations(state.page + 1, true);
  }, [state.hasNextPage, state.isLoadingMore, state.page, fetchGenerations]);

  // 重新获取数据（重置列表）
  const refetch = useCallback(() => {
    console.log('🔄 [useInfiniteGenerations] refetch 被调用，重置状态并重新获取数据');
    setState(prev => ({
      ...prev,
      data: [],
      page: 0,
      hasNextPage: true,
    }));
    console.log('🚀 [useInfiniteGenerations] 调用 fetchGenerations(1, false)');
    fetchGenerations(1, false);
  }, [fetchGenerations]);

  // ✨ 新增：通用本地数据更新方法
  const updateLocalData = useCallback((updater: (prev: Generation[]) => Generation[]) => {
    console.log('🔄 [LocalUpdate] 执行本地数据更新');
    setState(prev => ({
      ...prev,
      data: updater(prev.data)
    }));
  }, []);

  // ✨ 新增：移除指定ID的项目
  const removeItems = useCallback((idsToRemove: string[]) => {
    console.log(`🗑️ [LocalUpdate] 移除 ${idsToRemove.length} 个项目:`, idsToRemove);
    const idsSet = new Set(idsToRemove);
    updateLocalData(prev => {
      const filtered = prev.filter(item => !idsSet.has(item.id));
      console.log(`📊 [LocalUpdate] 数据更新: ${prev.length} -> ${filtered.length}`);
      return filtered;
    });
  }, [updateLocalData]);

  // ✨ 新增：批量更新收藏状态
  const updateItemsFavorite = useCallback((idsToUpdate: string[], favorite: boolean) => {
    console.log(`⭐ [LocalUpdate] 更新 ${idsToUpdate.length} 个项目收藏状态为: ${favorite}`);
    const idsSet = new Set(idsToUpdate);
    updateLocalData(prev => {
      const updated = prev.map(item => 
        idsSet.has(item.id) ? { ...item, favorite } : item
      );
      console.log(`📊 [LocalUpdate] 收藏状态已更新`);
      return updated;
    });
  }, [updateLocalData]);

  // 初始加载和筛选条件变化时重新加载
  useEffect(() => {
    console.log('🔄 [useInfiniteGenerations] useEffect 触发，条件变化', {
      type,
      filters,
      user: user?.id,
      loaded,
      timestamp: new Date().toISOString()
    });
    fetchGenerations(1, false);
  }, [type, filters, user, loaded, fetchGenerations]);

  return {
    data: state.data,
    isLoading: state.isLoading,
    isLoadingMore: state.isLoadingMore,
    error: state.error,
    hasNextPage: state.hasNextPage,
    loadMore,
    refetch,
    // ✨ 新增的本地操作方法
    updateLocalData,
    removeItems,
    updateItemsFavorite,
  };
}