"use client";

import { useState, useEffect } from "react";
import { useSession } from "@saas/auth/hooks/use-session";

export function useUserCredits() {
  const { user } = useSession();
  const [credits, setCredits] = useState<number | undefined>(0);
  const [isLoading, setIsLoading] = useState(true);
  
  const fetchCredits = async () => {
    if (!user) {
      setCredits(undefined);
      setIsLoading(false);
      return;
    }
    
    try {
      setIsLoading(true);
      const response = await fetch("/api/user/credits");
      
      if (!response.ok) {
        throw new Error("Failed to fetch credits");
      }
      
      const data = await response.json();
      setCredits(data.credits);
    } catch (error) {
      console.error("Error fetching credits:", error);
      setCredits(0);
    } finally {
      setIsLoading(false);
    }
  };
  
  useEffect(() => {
    fetchCredits();
  }, [user]);
  
  return {
    credits,
    isLoading,
    refetchCredits: fetchCredits
  };
}
