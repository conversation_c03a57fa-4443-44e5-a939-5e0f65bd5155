"use client";

import { useState, useEffect } from "react";
import { useSession } from "@saas/auth/hooks/use-session";
import type { VideoType } from "@marketing/image-to-video/types";

export function useUserVideos() {
  const { user } = useSession();
  const [videos, setVideos] = useState<VideoType[]>([]);
  const [favoriteVideos, setFavoriteVideos] = useState<VideoType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  const fetchVideos = async () => {
    if (!user) {
      setVideos([]);
      setFavoriteVideos([]);
      setIsLoading(false);
      return;
    }
    
    try {
      setIsLoading(true);
      const response = await fetch("/api/videos");
      
      if (!response.ok) {
        throw new Error("Failed to fetch videos");
      }
      
      const data = await response.json();
      setVideos(data.videos);
      setFavoriteVideos(data.videos.filter((video: VideoType) => video.isFavorite));
    } catch (error) {
      console.error("Error fetching videos:", error);
      setVideos([]);
      setFavoriteVideos([]);
    } finally {
      setIsLoading(false);
    }
  };
  
  const toggleFavorite = async (videoId: string) => {
    try {
      const response = await fetch(`/api/videos/${videoId}/favorite`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });
      
      if (!response.ok) {
        throw new Error("Failed to toggle favorite");
      }
      
      // 更新本地状态
      setVideos(prevVideos => 
        prevVideos.map(video => 
          video.id === videoId 
            ? { ...video, isFavorite: !video.isFavorite } 
            : video
        )
      );
      
      // 更新收藏视频列表
      setFavoriteVideos(prevFavorites => {
        const video = prevFavorites.find(v => v.id === videoId);
        return video 
          ? prevFavorites.filter(v => v.id !== videoId) 
          : [...prevFavorites, videos.find(v => v.id === videoId) as VideoType];
      });
    } catch (error) {
      console.error("Error toggling favorite:", error);
    }
  };
  
  useEffect(() => {
    fetchVideos();
  }, [user]);
  
  return {
    videos,
    favoriteVideos,
    isLoading,
    refetchVideos: fetchVideos,
    toggleFavorite
  };
}
