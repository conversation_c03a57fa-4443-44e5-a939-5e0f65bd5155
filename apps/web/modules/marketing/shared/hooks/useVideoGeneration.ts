"use client";

import { useState } from "react";
import type { VideoGenerationParams } from "@marketing/image-to-video/types";

export function useVideoGeneration() {
  const [isGenerating, setIsGenerating] = useState(false);
  
  const generateVideo = async (params: VideoGenerationParams) => {
    try {
      setIsGenerating(true);
      
      // 创建FormData对象来上传图片和其他参数
      const formData = new FormData();
      formData.append("image", params.image);
      formData.append("prompt", params.prompt);
      formData.append("mode", params.mode);
      formData.append("promptStrength", params.promptStrength.toString());
      formData.append("videoLength", params.videoLength.toString());
      formData.append("outputCount", params.outputCount.toString());
      formData.append("isPublic", params.isPublic.toString());
      formData.append("hasCopyProtection", params.hasCopyProtection.toString());
      
      const response = await fetch("/api/videos/generate", {
        method: "POST",
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error("Failed to generate video");
      }
      
      const data = await response.json();
      return data.videoId;
    } catch (error) {
      console.error("Error generating video:", error);
      throw error;
    } finally {
      setIsGenerating(false);
    }
  };
  
  return {
    isGenerating,
    generateVideo
  };
}
