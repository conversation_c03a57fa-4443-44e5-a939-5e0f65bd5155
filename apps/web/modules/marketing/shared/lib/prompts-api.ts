// apps/web/modules/marketing/shared/lib/prompts-api.ts
import { apiClient } from "@shared/lib/api-client";

// API调用接口类型定义
export interface PromptGenerationRequest {
  idea: string;
  type?: 'video' | 'image' | 'text'; // 生成类型
  style?: string; // 风格偏好
}

export interface PromptGenerationResponse {
  prompts: string[];
}

export interface PromptGenerationError {
  error: string;
  code?: string;
}

/**
 * 调用API生成提示词
 * @param request 生成请求参数
 * @returns 生成的提示词列表
 */
export async function generatePrompts(
  request: PromptGenerationRequest
): Promise<PromptGenerationResponse> {
  try {
    // 使用标准fetch调用，因为prompts路由可能还没有在TypeScript类型中定义
    const response = await fetch('/api/prompts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}: Failed to generate prompts`;
      try {
        const errorData = await response.json() as PromptGenerationError;
        errorMessage = errorData.error || errorMessage;
      } catch {
        // 如果JSON解析失败，使用默认错误消息
      }
      throw new Error(errorMessage);
    }

    return await response.json();
  } catch (error) {
    console.error('Generate prompts API error:', error);
    
    // 处理常见错误类型
    if (error instanceof Error) {
      if (error.message.includes('rate limit')) {
        throw new Error('API调用频率过高，请稍后重试');
      } else if (error.message.includes('auth')) {
        throw new Error('认证失败，请重新登录');
      } else if (error.message.includes('network')) {
        throw new Error('网络连接错误，请检查网络');
      }
    }
    
    throw error;
  }
}

/**
 * 简化的API调用，只传入创意文本
 * @param idea 用户输入的创意描述
 * @param type 生成类型，默认为video
 * @returns 生成的提示词列表
 */
export async function generatePromptsFromIdea(
  idea: string,
  type: 'video' | 'image' | 'text' = 'video'
): Promise<string[]> {
  const response = await generatePrompts({ idea, type });
  return response.prompts;
}