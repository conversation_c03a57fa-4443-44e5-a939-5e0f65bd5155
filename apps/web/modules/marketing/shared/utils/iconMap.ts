// 图标映射文件
import {
  Compass,
  Crown,
  FolderHeart,
  Home,
  Image as ImageIcon,
  Layers,
  Settings,
  Sparkles,
  Stars,
  Type,
  User,
  Video,
  Wand2,
} from "lucide-react";

// 图标名称到组件的映射
export const iconMap: Record<string, React.ComponentType<any>> = {
  Home,
  Compass,
  Image: ImageIcon,
  Type,
  User,
  Video,
  Sparkles,
  Layers,
  Wand2,
  Crown,
  FolderHeart,
  Settings,
  Stars,
};
