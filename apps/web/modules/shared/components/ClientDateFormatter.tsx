"use client";

import dayjs from "dayjs";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";

// 扩展 dayjs 功能
dayjs.extend(utc);
dayjs.extend(timezone);

interface ClientDateFormatterProps {
	date: Date | string;
	format?: string;
}

export function ClientDateFormatter({
	date,
	format = "MMMM D, YYYY",
}: ClientDateFormatterProps) {
	// 直接格式化并返回，不使用状态以减少闪烁
	const userTimezone = dayjs.tz.guess();
	const formattedDate = dayjs(date).tz(userTimezone).format(format);

	return <>{formattedDate}</>;
}
