import { cn } from "@ui/lib";

export function Logo({
	withLabel = false,
	className,
}: {
	className?: string;
	withLabel?: boolean;
}) {
	return (
		<span
			className={cn(
				"flex items-center font-semibold text-foreground leading-none",
				className,
			)}
		>
			<svg width="78" height="23" viewBox="0 0 78 23" className="h-6 w-auto text-foreground" fill="currentColor">
				<title>SUPAPI</title>
				<path d="M9.34 19.32C8.03333 19.32 6.92 19.1133 6 18.7C5.08 18.2867 4.36667 17.7 3.86 16.94C3.35333 16.18 3.06667 15.28 3 14.24L6.9 14.06C6.96667 14.5267 7.1 14.9267 7.3 15.26C7.5 15.58 7.76667 15.8267 8.1 16C8.44667 16.16 8.87333 16.24 9.38 16.24C9.78 16.24 10.12 16.2 10.4 16.12C10.68 16.0267 10.8933 15.8867 11.04 15.7C11.1867 15.5133 11.26 15.28 11.26 15C11.26 14.7467 11.1933 14.5267 11.06 14.34C10.94 14.14 10.6933 13.9667 10.32 13.82C9.96 13.66 9.42 13.5067 8.7 13.36C7.40667 13.0933 6.36 12.7867 5.56 12.44C4.76 12.08 4.17333 11.62 3.8 11.06C3.44 10.5 3.26 9.78667 3.26 8.92C3.26 8.04 3.48667 7.26667 3.94 6.6C4.39333 5.93333 5.04667 5.41333 5.9 5.04C6.75333 4.66667 7.78 4.48 8.98 4.48C10.2467 4.48 11.3067 4.69333 12.16 5.12C13.0133 5.54667 13.6733 6.13333 14.14 6.88C14.62 7.62667 14.9067 8.49333 15 9.48L11.14 9.64C11.1133 9.2 11.0067 8.82667 10.82 8.52C10.6333 8.21333 10.38 7.98 10.06 7.82C9.75333 7.64667 9.38 7.56 8.94 7.56C8.38 7.56 7.94667 7.67333 7.64 7.9C7.33333 8.12667 7.18 8.42667 7.18 8.8C7.18 9.09333 7.25333 9.34 7.4 9.54C7.56 9.74 7.81333 9.90667 8.16 10.04C8.52 10.1733 9.00667 10.2933 9.62 10.4C11.02 10.64 12.1267 10.9667 12.94 11.38C13.7533 11.7933 14.3267 12.2867 14.66 12.86C15.0067 13.4333 15.18 14.0933 15.18 14.84C15.18 15.76 14.9467 16.56 14.48 17.24C14.0267 17.9067 13.36 18.42 12.48 18.78C11.6133 19.14 10.5667 19.32 9.34 19.32ZM23.2997 19.32C22.0464 19.32 20.953 19.0933 20.0197 18.64C19.0997 18.1867 18.3864 17.5467 17.8797 16.72C17.373 15.88 17.1197 14.8933 17.1197 13.76V4.78H21.0397V13.76C21.0397 14.5333 21.233 15.1333 21.6197 15.56C22.0064 15.9733 22.5664 16.18 23.2997 16.18C24.0464 16.18 24.613 15.9733 24.9997 15.56C25.3864 15.1333 25.5797 14.5333 25.5797 13.76V4.78H29.4997V13.76C29.4997 14.8933 29.2464 15.88 28.7397 16.72C28.233 17.5467 27.513 18.1867 26.5797 18.64C25.6597 19.0933 24.5664 19.32 23.2997 19.32ZM31.9705 19V4.8H38.0505C39.8371 4.8 41.2371 5.23333 42.2505 6.1C43.2771 6.96667 43.7905 8.15333 43.7905 9.66C43.7905 10.6467 43.5571 11.5067 43.0905 12.24C42.6371 12.96 41.9771 13.52 41.1105 13.92C40.2571 14.3067 39.2371 14.5 38.0505 14.5H35.8905V19H31.9705ZM35.8905 11.36H37.7505C38.3638 11.36 38.8571 11.22 39.2305 10.94C39.6038 10.6467 39.7905 10.22 39.7905 9.66C39.7905 9.1 39.6038 8.67333 39.2305 8.38C38.8705 8.08667 38.3771 7.94 37.7505 7.94H35.8905V11.36ZM42.9459 19L48.0659 4.8H52.5259L57.6459 19H53.6459L52.8459 16.66H47.7259L46.9259 19H42.9459ZM48.7659 13.62H51.8259L50.3059 9.12L48.7659 13.62ZM58.6697 19V4.8H64.7497C66.5364 4.8 67.9364 5.23333 68.9497 6.1C69.9764 6.96667 70.4897 8.15333 70.4897 9.66C70.4897 10.6467 70.2564 11.5067 69.7897 12.24C69.3364 12.96 68.6764 13.52 67.8097 13.92C66.9564 14.3067 65.9364 14.5 64.7497 14.5H62.5897V19H58.6697ZM62.5897 11.36H64.4497C65.063 11.36 65.5564 11.22 65.9297 10.94C66.303 10.6467 66.4897 10.22 66.4897 9.66C66.4897 9.1 66.303 8.67333 65.9297 8.38C65.5697 8.08667 65.0764 7.94 64.4497 7.94H62.5897V11.36ZM71.99 19V4.8H75.91V19H71.99Z" />
			</svg>
			{withLabel && (
				<span className="ml-3 hidden text-lg md:block">SUPAPI</span>
			)}
		</span>
	);
}
