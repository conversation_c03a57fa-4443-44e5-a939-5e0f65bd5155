"use client";

import { useEffect } from "react";
import { Sparkles } from "lucide-react";
import { useSession } from "@saas/auth/hooks/use-session";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@ui/components/tooltip";
import { useCreditsStore } from "@shared/stores/useCreditsStore";
import { cn } from "@ui/lib";

export function UserCredits({ onDarkBackground = false }: { onDarkBackground?: boolean }) {
  const { user } = useSession();
  const { credits, isLoading, fetchCredits } = useCreditsStore();

  useEffect(() => {
    if (user) {
      fetchCredits();
    }
  }, [user, fetchCredits]);

  if (isLoading) {
    return <div className={cn("flex items-center gap-1", onDarkBackground ? "text-white/70" : "text-muted-foreground")}><Sparkles className="h-4 w-4" /> ...</div>;
  }

  // 根据积分数量确定显示样式
  const isLow = credits !== null && credits <= 3;
  const isCritical = credits !== null && credits <= 1;
  
  // 根据积分状态和背景设置不同的样式类
  const creditTextClass = cn(
    "flex items-center gap-1",
    isCritical 
      ? "text-red-500" 
      : isLow 
        ? "text-amber-500" 
        : onDarkBackground 
          ? "text-white" 
          : "text-muted-foreground"
  );

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={creditTextClass}>
            <Sparkles className="h-4 w-4" /> {credits ?? 0}
          </div>
        </TooltipTrigger>
        <TooltipContent>
          {isCritical ? (
            <p className="text-red-500">Low credits! Click to purchase more</p>
          ) : isLow ? (
            <p className="text-amber-500">Credits running low</p>
          ) : (
            <p>AI Hint Credits Remaining</p>
          )}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
