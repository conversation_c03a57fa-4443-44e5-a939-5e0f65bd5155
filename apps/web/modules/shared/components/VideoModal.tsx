'use client'

import React, { useEffect } from 'react'
import { createPortal } from 'react-dom'
import { VideoDetailView } from '@shared/components/video-detail'
import { useVideoStore } from '@shared/stores'
import type { VideoData } from '@shared/components/video-detail/types'

interface VideoModalProps {
  videoId: string
  isOpen: boolean
  onClose: () => void
  onNavigate?: (videoId: string) => void
}

export function VideoModal({ videoId, isOpen, onClose, onNavigate }: VideoModalProps) {
  const { videos } = useVideoStore()

  // 从 store 中获取视频数据
  const video = videos.find(v => v.id === videoId)
  const currentIndex = videos.findIndex(v => v.id === videoId)
  const hasPrevious = currentIndex > 0
  const hasNext = currentIndex < videos.length - 1

  // ESC 键关闭
  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEsc)
      // 防止背景滚动
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEsc)
      document.body.style.overflow = ''
    }
  }, [isOpen, onClose])

  if (!isOpen || !video) return null

  const handleRegenerate = () => {
    console.log('Regenerate video:', videoId)
  }

  const handleUpscale = () => {
    console.log('Upscale video:', videoId)
  }

  const handleVideoToVideo = () => {
    console.log('Video to Video:', videoId)
  }

  const handleCanvas = () => {
    console.log('Canvas edit:', videoId)
  }

  const handleAddSoundEffects = () => {
    console.log('Add sound effects:', videoId)
  }

  const handleLipSync = () => {
    console.log('Lip sync:', videoId)
  }

  const handleLike = (liked: boolean) => {
    console.log('Like video:', videoId, liked)
  }

  const handleFavorite = (favorited: boolean) => {
    console.log('Favorite video:', videoId, favorited)
  }

  const handleShare = () => {
    console.log('Share video:', videoId)
  }

  const handleCreateSimilar = () => {
    console.log('Create similar video:', videoId)
  }

  const handleNavigatePrevious = () => {
    if (hasPrevious && onNavigate) {
      const previousVideo = videos[currentIndex - 1]
      onNavigate(previousVideo.id)
    }
  }

  const handleNavigateNext = () => {
    if (hasNext && onNavigate) {
      const nextVideo = videos[currentIndex + 1]
      onNavigate(nextVideo.id)
    }
  }

  // 使用 Portal 渲染到 body
  return createPortal(
    <>
      {/* 背景遮罩 */}
      <div
        className="fixed inset-0 z-[9999] bg-background/80 animate-in fade-in-0 duration-200"
        onClick={onClose}
      />

      {/* 模态框内容 */}
      <div className="fixed top-[50%] left-[50%] z-[10000] w-[96vw] max-w-[1600px] h-[88vh] translate-x-[-50%] translate-y-[-50%] bg-background border rounded-lg shadow-2xl animate-in zoom-in-95 fade-in-0 duration-200">
        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute top-1 right-1 z-[10001] inline-flex h-6 w-6 items-center justify-center text-muted-foreground hover:text-foreground hover:bg-accent transition-all duration-200 p-0 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          aria-label="Close"
        >
          <svg className="size-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* 视频详情内容 */}
        <VideoDetailView
          video={video}
          onRegenerate={handleRegenerate}
          onUpscale={handleUpscale}
          onVideoToVideo={handleVideoToVideo}
          onCanvas={handleCanvas}
          onAddSoundEffects={handleAddSoundEffects}
          onLipSync={handleLipSync}
          onLike={handleLike}
          onFavorite={handleFavorite}
          onShare={handleShare}
          onCreateSimilar={handleCreateSimilar}
          onNavigatePrevious={handleNavigatePrevious}
          onNavigateNext={handleNavigateNext}
          hasPrevious={hasPrevious}
          hasNext={hasNext}
          className="h-full"
        />
      </div>
    </>,
    document.body
  )
}