'use client'

import { Star } from 'lucide-react'
import { cn } from '@ui/lib'

interface FavoriteButtonProps {
  isFavorite: boolean
  onToggle: () => void
  className?: string
}

export function FavoriteButton({ isFavorite, onToggle, className }: FavoriteButtonProps) {
  return (
    <button
      type="button"
      className={cn(
        "flex cursor-pointer items-center gap-x-1 rounded p-1 transition-all",
        "hover:bg-white/20 text-white",
        className
      )}
      onClick={onToggle}
      aria-label={isFavorite ? "Remove from favorites" : "Add to favorites"}
    >
      <Star
        className={cn(
          "size-4 transition-colors stroke-2",
          isFavorite
            ? "fill-yellow-500 text-yellow-500"
            : "text-white fill-none",
        )}
      />
    </button>
  )
}