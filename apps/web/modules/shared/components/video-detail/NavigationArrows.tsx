'use client'

import { cn } from '@ui/lib'

interface NavigationArrowsProps {
  onPrevious?: () => void
  onNext?: () => void
  hasPrevious?: boolean
  hasNext?: boolean
  position?: 'left' | 'right'
  className?: string
}

export function NavigationArrows({
  onPrevious,
  onNext,
  hasPrevious = true,
  hasNext = true,
  position = 'left',
  className
}: NavigationArrowsProps) {
  const positionClasses = position === 'right' 
    ? "absolute left-full top-1/2 translate-x-3 -translate-y-1/2 flex-col gap-y-3 sm:flex"
    : "absolute right-full top-1/2 -translate-x-3 -translate-y-1/2 flex-col gap-y-3 sm:flex"

  return (
    <div className={cn(
      "hidden",
      positionClasses,
      className
    )}>
      <button 
        type="button" 
        className={cn(
          "rounded-full bg-card p-2 transition-all shadow-md",
          hasPrevious 
            ? "hover:bg-accent cursor-pointer" 
            : "opacity-50 cursor-not-allowed"
        )}
        onClick={onPrevious}
        disabled={!hasPrevious}
        aria-label="Previous video"
      >
        <svg className="size-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
          <path strokeLinecap="round" strokeLinejoin="round" d="M5 15l7-7 7 7" />
        </svg>
      </button>
      
      <button 
        type="button" 
        className={cn(
          "rounded-full bg-card p-2 transition-all shadow-md",
          hasNext 
            ? "hover:bg-accent cursor-pointer" 
            : "opacity-50 cursor-not-allowed"
        )}
        onClick={onNext}
        disabled={!hasNext}
        aria-label="Next video"
      >
        <svg className="size-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
          <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
        </svg>
      </button>
    </div>
  )
}