# Video Detail View Component

基于竞争对手Pollo.ai设计的视频详情展示组件，采用左右分栏布局，集成Video.js播放器和完整的视频信息展示功能。

## 组件结构

```
video-detail/
├── VideoDetailView.tsx          # 主组件
├── SimpleVideoPlayer.tsx        # Video.js播放器
├── VideoInfoPanel.tsx          # 视频信息面板
├── VideoActionsPanel.tsx       # 操作按钮面板
├── FavoriteButton.tsx          # 收藏按钮
├── components/                 # 子组件
│   ├── VideoHeader.tsx         # 头部信息
│   ├── VideoLabels.tsx         # 状态标签
│   ├── OriginalImageSection.tsx # 原始图片区域
│   ├── PromptSection.tsx       # Prompt区域
│   ├── VideoParameters.tsx     # 参数信息
│   ├── ActionButton.tsx        # 操作按钮
│   └── VideoInteractionBar.tsx # 交互栏
├── types.ts                    # 类型定义
├── index.ts                    # 导出文件
├── example.tsx                 # 使用示例
└── README.md                   # 说明文档
```

## 快速开始

### 基本使用

```tsx
import { VideoDetailView, type VideoData } from '@/modules/shared/components/video-detail'

const videoData: VideoData = {
  id: "video123",
  videoUrl: "https://example.com/video.mp4",
  posterUrl: "https://example.com/poster.jpg",
  duration: 30,
  author: {
    id: "user123",
    name: "John Doe",
    avatar: "https://example.com/avatar.jpg"
  },
  createdAt: "2025-01-01T12:00:00Z",
  status: "published",
  type: "image-to-video",
  prompt: "A beautiful sunset",
  model: "Pollo 1.6",
  resolution: "1080P",
  seed: "123456",
  outputDimension: "1920 x 1080",
  project: "My Project",
  likes: 10,
  isLiked: false,
  isFavorite: false
}

function MyVideoPage() {
  return (
    <VideoDetailView
      video={videoData}
      onLike={(liked) => console.log('Liked:', liked)}
      onCreateSimilar={() => console.log('Create similar')}
      // ... 其他回调函数
    />
  )
}
```

### 主要Props

#### VideoDetailViewProps

| 属性 | 类型 | 必需 | 描述 |
|------|------|------|------|
| video | VideoData | ✓ | 视频数据 |
| onRegenerate | () => void | | 重新生成回调 |
| onUpscale | () => void | | 放大回调 |
| onVideoToVideo | () => void | | 视频转视频回调 |
| onCanvas | () => void | | 画布编辑回调 |
| onAddSoundEffects | () => void | | 添加音效回调 |
| onLipSync | () => void | | 唇形同步回调 |
| onLike | (liked: boolean) => void | | 点赞回调 |
| onFavorite | (favorited: boolean) => void | | 收藏回调 |
| onShare | () => void | | 分享回调 |
| onCreateSimilar | () => void | | 创建相似视频回调 |
| className | string | | 自定义样式类 |

#### VideoData

```tsx
interface VideoData {
  id: string
  title?: string
  videoUrl: string              // 视频URL
  posterUrl?: string           // 海报图URL
  duration: number             // 视频时长(秒)
  author: UserProfile          // 作者信息
  createdAt: string           // 创建时间(ISO字符串)
  status: 'published' | 'unpublished'  // 发布状态
  type: 'image-to-video' | 'text-to-video'  // 视频类型
  originalImage?: string       // 原始图片URL
  prompt: string              // 生成提示词
  model: string               // 使用的模型
  resolution: string          // 分辨率
  seed: string               // 随机种子
  outputDimension: string    // 输出尺寸
  project: string            // 项目名称
  likes: number              // 点赞数
  isLiked: boolean          // 是否已点赞
  isFavorite?: boolean      // 是否已收藏
}
```

## 特性

### 🎬 Video.js 播放器
- 支持播放/暂停、进度控制
- 音量控制、全屏功能
- 倍速播放 (0.5x, 1x, 1.5x, 2x)
- 响应式设计
- 背景模糊效果

### 📋 完整的视频信息展示
- 作者信息和创建时间
- 视频类型和发布状态标签
- 原始图片预览
- Prompt展示和复制功能
- 详细的生成参数

### 🎯 丰富的操作功能
- 6个主要操作按钮：重新生成、放大、视频转视频、画布、音效、唇形同步
- 点赞、收藏、分享功能
- 创建相似视频
- 响应式按钮布局

### 📱 响应式设计
- 桌面端：左右分栏布局
- 移动端：上下堆叠布局
- 自适应尺寸和间距

## 子组件使用

你也可以单独使用各个子组件：

### SimpleVideoPlayer

```tsx
import { SimpleVideoPlayer } from '@/modules/shared/components/video-detail'

<SimpleVideoPlayer
  videoUrl="https://example.com/video.mp4"
  posterUrl="https://example.com/poster.jpg"
  duration={30}
  muted={true}
  loop={true}
/>
```

### VideoInfoPanel

```tsx
import { VideoInfoPanel } from '@/modules/shared/components/video-detail'

<VideoInfoPanel video={videoData} />
```

### VideoActionsPanel

```tsx
import { VideoActionsPanel } from '@/modules/shared/components/video-detail'

<VideoActionsPanel
  video={videoData}
  onRegenerate={() => {}}
  onUpscale={() => {}}
  // ... 其他回调
/>
```

## 样式定制

组件使用Tailwind CSS和项目的设计系统，支持通过className属性进行样式定制：

```tsx
<VideoDetailView
  video={videoData}
  className="custom-video-detail"
  // ...
/>
```

## 依赖要求

- React 19+
- TypeScript
- Tailwind CSS
- Video.js (已安装)
- @types/video.js (已安装)
- Shadcn/ui组件库

## 注意事项

1. **Video.js初始化**: 播放器会自动处理初始化和清理，确保正确的内存管理
2. **响应式布局**: 在lg断点(1024px)以下会自动切换为移动端布局
3. **事件处理**: 所有回调函数都是可选的，未提供时对应功能将不可用
4. **样式兼容**: 组件样式与项目现有设计系统保持一致

## 示例

查看 `example.tsx` 文件获取完整的使用示例。