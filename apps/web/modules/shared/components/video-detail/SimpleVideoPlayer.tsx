'use client'

import { useEffect, useRef } from 'react'
import videojs from 'video.js'
import 'video.js/dist/video-js.css'
import { cn } from '@ui/lib'
import type { SimpleVideoPlayerProps } from './types'

export function SimpleVideoPlayer({ 
  videoUrl, 
  posterUrl, 
  duration,
  autoPlay = false,
  muted = true,
  loop = true,
  onReady,
  className
}: SimpleVideoPlayerProps) {
  const videoRef = useRef<HTMLDivElement>(null)
  const playerRef = useRef<videojs.Player | null>(null)

  useEffect(() => {
    // 确保 Video.js 播放器只初始化一次
    if (!playerRef.current) {
      const videoElement = document.createElement('video-js')
      
      videoElement.classList.add('vjs-big-play-centered')
      videoRef.current?.appendChild(videoElement)

      const player = videojs(videoElement, {
        autoplay: autoPlay,
        controls: true,
        responsive: true,
        fluid: true,
        muted: muted,
        loop: loop,
        poster: posterUrl,
        preload: 'metadata',
        playbackRates: [0.5, 1, 1.5, 2],
        sources: [{
          src: videoUrl,
          type: 'video/mp4'
        }],
        // 自定义样式
        html5: {
          vhs: {
            overrideNative: true
          }
        }
      }, () => {
        onReady?.(player)
      })

      playerRef.current = player
    }

    return () => {
      const player = playerRef.current
      if (player && !player.isDisposed()) {
        player.dispose()
        playerRef.current = null
      }
    }
  }, [videoUrl, posterUrl, autoPlay, muted, loop, onReady])

  return (
    <div className={cn("relative size-full", className)}>
      <div 
        ref={videoRef} 
        className="video-js vjs-default-skin !bg-transparent vjs-big-play-button vjs-fill size-full"
        data-vjs-player
      />
    </div>
  )
}