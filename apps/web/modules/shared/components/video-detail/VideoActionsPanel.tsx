'use client'

import { cn } from '@ui/lib'
import { ActionButton } from './components/ActionButton'
import { VideoInteractionBar } from './components/VideoInteractionBar'
import type { VideoActionsPanelProps } from './types'

export function VideoActionsPanel({ 
  video, 
  onRegenerate,
  onUpscale,
  onVideoToVideo,
  onCanvas,
  onAddSoundEffects,
  onLipSync,
  onLike,
  onFavorite,
  onShare,
  onCreateSimilar,
  className 
}: VideoActionsPanelProps) {
  const actionButtons = [
    { icon: 'regenerate', label: 'Regenerate', action: onRegenerate },
    { icon: 'upscale', label: 'Upscale', action: onUpscale },
    { icon: 'video-to-video', label: 'Video to Video', action: onVideoToVideo },
    { icon: 'canvas', label: 'Canvas', action: onCanvas },
    { icon: 'sound', label: 'Add Sound Effects', action: onAddSoundEffects },
    { icon: 'lip-sync', label: 'Lip Sync', action: onLipSync },
  ]

  return (
    <div className={cn("flex flex-col gap-y-5", className)}>
      {/* 功能按钮网格 */}
      <div className="grid grid-cols-2 gap-2">
        {actionButtons.map((button) => (
          <ActionButton
            key={button.label}
            icon={button.icon}
            label={button.label}
            onClick={button.action}
          />
        ))}
      </div>
      
      {/* 底部交互区域 */}
      <VideoInteractionBar 
        likes={video.likes}
        isLiked={video.isLiked}
        onLike={onLike}
        onShare={onShare}
        onCreateSimilar={onCreateSimilar}
      />
    </div>
  )
}