'use client'

import { cn } from '@ui/lib'
import { VideoPlayer } from './VideoPlayer'
import { VideoInfoPanel } from './VideoInfoPanel'
import { VideoActionsPanel } from './VideoActionsPanel'
import { FavoriteButton } from './FavoriteButton'
import { NavigationArrows } from './NavigationArrows'
import type { VideoDetailViewProps } from './types'

export function VideoDetailView({ 
  video, 
  onRegenerate,
  onUpscale,
  onVideoToVideo,
  onCanvas,
  onAddSoundEffects,
  onLipSync,
  onLike,
  onFavorite,
  onShare,
  onCreateSimilar,
  onNavigatePrevious,
  onNavigateNext,
  hasPrevious = true,
  hasNext = true,
  className 
}: VideoDetailViewProps) {
  const handleFavorite = () => {
    onFavorite?.(!video.isFavorite)
  }

  return (
    <div className={cn("bg-card relative p-0 lg:rounded-lg lg:p-6", className)}>
      <div className="relative flex flex-col lg:flex-row lg:gap-6 lg:rounded-lg">
        {/* 左侧视频播放器 */}
        <div className="flex-1">
          <div className="relative bg-muted size-[100vw] w-full lg:h-[80vh] lg:flex-1">
            <div className="relative size-full">
              {/* 背景模糊层 */}
              {video.posterUrl && (
                <>
                  <div 
                    className="absolute inset-0 rounded-2xl bg-cover bg-center bg-no-repeat" 
                    style={{ backgroundImage: `url(${video.posterUrl})` }} 
                  />
                  <div className="absolute inset-0 backdrop-blur-lg" />
                </>
              )}
              
              {/* Video.js 播放器容器 */}
              <div className="flex size-full flex-col items-center justify-center hover:cursor-pointer [&_.video-js]:size-full">
                <div className="relative w-full h-full max-w-full max-h-full transition-shadow hover:shadow-lg">
                  <VideoPlayer
                    videoUrl={video.videoUrl}
                    posterUrl={video.posterUrl}
                    duration={video.duration}
                    className="size-full"
                  />
                </div>
              </div>
              
              {/* 收藏按钮 */}
              <FavoriteButton 
                isFavorite={video.isFavorite || false}
                onToggle={handleFavorite}
                className="absolute right-2 top-2 lg:right-2" 
              />
            </div>
          </div>
        </div>
        
        {/* 右侧信息面板 */}
        <div className="flex flex-col justify-between gap-y-3 p-6 lg:h-[80vh] lg:w-[408px] lg:p-0">
          <VideoInfoPanel video={video} />
          <VideoActionsPanel 
            video={video}
            onRegenerate={onRegenerate}
            onUpscale={onUpscale}
            onVideoToVideo={onVideoToVideo}
            onCanvas={onCanvas}
            onAddSoundEffects={onAddSoundEffects}
            onLipSync={onLipSync}
            onLike={onLike}
            onFavorite={onFavorite}
            onShare={onShare}
            onCreateSimilar={onCreateSimilar}
          />
        </div>
      </div>
      
      {/* 上下翻页箭头 - 只在模态框模式下显示 */}
      {(onNavigatePrevious || onNavigateNext) && (
        <>
          {/* 左侧导航 */}
          <NavigationArrows
            position="left"
            onPrevious={onNavigatePrevious}
            onNext={onNavigateNext}
            hasPrevious={hasPrevious}
            hasNext={hasNext}
          />
          
          {/* 右侧导航 */}
          <NavigationArrows
            position="right"
            onPrevious={onNavigatePrevious}
            onNext={onNavigateNext}
            hasPrevious={hasPrevious}
            hasNext={hasNext}
          />
        </>
      )}
    </div>
  )
}