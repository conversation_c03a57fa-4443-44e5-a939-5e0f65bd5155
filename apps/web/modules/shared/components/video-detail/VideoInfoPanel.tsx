'use client'

import { cn } from '@ui/lib'
import { VideoHeader } from './components/VideoHeader'
import { VideoLabels } from './components/VideoLabels'
import { OriginalImageSection } from './components/OriginalImageSection'
import { PromptSection } from './components/PromptSection'
import { VideoParameters } from './components/VideoParameters'
import type { VideoInfoPanelProps } from './types'

export function VideoInfoPanel({ video, className }: VideoInfoPanelProps) {
  
  return (
    <div className={cn("flex max-h-fit min-h-0 flex-1 flex-col gap-y-3", className)}>
      {/* 头部信息 */}
      <VideoHeader video={video} />
      
      {/* 内容区域 */}
      <div className="scrollbar bg-background flex-1 overflow-y-auto rounded-md p-3">
        {/* 标签区域 */}
        <VideoLabels type={video.type} status={video.status} />
        
        {/* 原始图片 */}
        {video.originalImage && (
          <OriginalImageSection imageUrl={video.originalImage} />
        )}
        
        {/* Prompt区域 */}
        {video.prompt && (
          <PromptSection prompt={video.prompt} />
        )}
        
        {/* 参数信息 */}
        <VideoParameters video={video} />
      </div>
    </div>
  )
}