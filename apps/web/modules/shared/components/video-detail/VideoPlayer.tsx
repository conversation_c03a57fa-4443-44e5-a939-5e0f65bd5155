"use client";

import { useEffect, useRef, useState } from "react";
import videojs from "video.js";
import "video.js/dist/video-js.css";
import { cn } from "@ui/lib";

// 播放按钮SVG组件
const PlayIcon = ({ className }: { className?: string }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		width="24"
		height="24"
		viewBox="0 0 20 20"
		className={className}
	>
		<path
			fill="currentColor"
			d="M15.544 9.59a1 1 0 0 1-.053 1.728L6.476 16.2A1 1 0 0 1 5 15.321V4.804a1 1 0 0 1 1.53-.848l9.014 5.634Z"
		/>
	</svg>
);

interface VideoPlayerProps {
	videoUrl: string;
	posterUrl?: string;
	duration?: number;
	muted?: boolean;
	loop?: boolean;
	onReady?: (player: any) => void;
	className?: string;
}

export function VideoPlayer({
	videoUrl,
	posterUrl,
	muted = true,
	loop = true,
	onReady,
	className,
}: VideoPlayerProps) {
	const videoRef = useRef<HTMLDivElement>(null);
	const playerRef = useRef<any>(null);
	const [savedTime, setSavedTime] = useState(0);
	const [isInitialized, setIsInitialized] = useState(false);
	const [showPlayButton, setShowPlayButton] = useState(true);

	// 检查浏览器是否支持画中画
	const checkPiPSupport = () => {
		return "pictureInPictureEnabled" in document;
	};

	useEffect(() => {
		// 确保 Video.js 播放器只初始化一次
		if (!playerRef.current && videoRef.current) {
			const videoElement = document.createElement("video-js");

			videoElement.classList.add("vjs-big-play-centered");
			videoRef.current.appendChild(videoElement);

			const player = videojs(
				videoElement,
				{
					autoplay: false, // 不自动播放，通过hover控制
					controls: false, // 初始隐藏控制栏
					responsive: true,
					fluid: true,
					muted: muted,
					loop: loop,
					poster: posterUrl,
					preload: "metadata",
					playbackRates: [0.5, 1, 1.5, 2],
					sources: [
						{
							src: videoUrl,
							type: "video/mp4",
						},
					],
					// 完全按照竞争对手的控制栏配置
					controlBar: {
						children: [
							"playToggle",
							"skipBackward",
							"skipForward",
							"volumePanel",
							"currentTimeDisplay",
							"timeDivider",
							"durationDisplay",
							"progressControl",
							"liveDisplay",
							"seekToLive",
							"remainingTimeDisplay",
							"customControlSpacer",
							"playbackRateMenuButton",
							"chaptersButton",
							"descriptionsButton",
							"subsCapsButton",
							"audioButton",
							...(checkPiPSupport()
								? ["pictureInPictureToggle"]
								: []),
							"fullscreenToggle",
						],
						volumePanel: {
							inline: false, // 竖直音量控制
						},
					},
					html5: {
						vhs: {
							overrideNative: true,
						},
					},
				},
				() => {
					setIsInitialized(true);
					onReady?.(player);

					// 设置画中画事件监听
					if (checkPiPSupport()) {
						player.on("enterpictureinpicture", () => {
							console.log("进入画中画模式");
						});

						player.on("leavepictureinpicture", () => {
							console.log("退出画中画模式");
						});
					}

					// 监听播放状态变化来控制大播放按钮显示/隐藏
					player.on('play', () => {
						setShowPlayButton(false);
					});

					player.on('pause', () => {
						setShowPlayButton(true);
					});

					player.on('ended', () => {
						setShowPlayButton(true);
					});
				},
			);

			playerRef.current = player;
		}

		return () => {
			const player = playerRef.current;
			if (player && !player.isDisposed()) {
				player.dispose();
				playerRef.current = null;
			}
		};
	}, [videoUrl, posterUrl, muted, loop, onReady]);

	const handleMouseEnter = () => {
		const player = playerRef.current;
		if (player && isInitialized) {
			// 首先显示控制栏（此时显示播放按钮）
			player.controls(true);
			// 延迟一小段时间再开始播放，让用户看到播放按钮
			setTimeout(() => {
				// 从保存的位置开始播放
				if (savedTime > 0) {
					player.currentTime(savedTime);
				}
				// 开始播放（按钮变为暂停）
				player.play().catch(console.error);
			}, 300); // 300ms延迟让播放按钮先显示
		}
	};

	const handleMouseLeave = () => {
		const player = playerRef.current;
		if (player && isInitialized) {
			// 保存当前播放位置
			setSavedTime(player.currentTime());
			// 暂停播放
			player.pause();
			// 隐藏控制栏
			player.controls(false);
		}
	};

	const handlePlayButtonClick = () => {
		const player = playerRef.current;
		if (player && isInitialized) {
			player.controls(true);
			if (savedTime > 0) {
				player.currentTime(savedTime);
			}
			player.play().catch(console.error);
		}
	};

	return (
		<div
			className={cn("relative size-full", className)}
			onMouseEnter={handleMouseEnter}
			onMouseLeave={handleMouseLeave}
			role="application"
			aria-label="Video player with hover controls"
		>
			<div
				ref={videoRef}
				className="video-js vjs-default-skin !bg-transparent vjs-big-play-button vjs-fill size-full"
				data-vjs-player
			/>

			{/* 自定义播放按钮覆盖层 */}
			{showPlayButton && (
				<div className="absolute inset-0 flex items-center justify-center pointer-events-none">
					<button
						onClick={handlePlayButtonClick}
						className="w-16 h-16 rounded-full bg-black/70 hover:bg-black/90 flex items-center justify-center transition-all duration-200 hover:scale-110 pointer-events-auto"
						aria-label="播放视频"
					>
						<PlayIcon className="w-6 h-6 text-white ml-0.5" />
					</button>
				</div>
			)}

			{/* 最小化样式 - 只保留必要的 */}
			<style jsx global>{`
				.video-js .vjs-control-bar {
					background: rgba(0, 0, 0, 0.5);
					backdrop-filter: blur(4px);
					transition: opacity 0.3s ease;
					border-radius: 0;
				}

				/* 隐藏Video.js默认的大播放按钮，使用自定义按钮 */
				.video-js .vjs-big-play-button {
					display: none !important;
				}



				/* 控制栏按钮hover效果 */
				.video-js .vjs-control:hover {
					background-color: rgba(255, 255, 255, 0.1);
					border-radius: 4px;
				}
			`}</style>
		</div>
	);
}
