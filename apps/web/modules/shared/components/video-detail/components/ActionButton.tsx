"use client";

import { But<PERSON> } from "@ui/components/button";
import { cn } from "@ui/lib";
import {
	type LucideIcon,
	MessageSquare,
	Palette,
	RefreshCw,
	TrendingUp,
	Video,
	Volume2,
} from "lucide-react";

interface ActionButtonProps {
	icon: string;
	label: string;
	onClick?: () => void;
	className?: string;
}

// 自定义SVG图标组件
const HdIcon = ({ className }: { className?: string }) => (
	<svg 
		xmlns="http://www.w3.org/2000/svg" 
		width="24" 
		height="24" 
		viewBox="0 0 24 24"
		className={className}
	>
		<path 
			fill="currentColor" 
			d="M6 15h1.5v-2h2v2H11V9H9.5v2.5h-2V9H6v6Zm7 0h4q.425 0 .713-.288T18 14v-4q0-.425-.288-.713T17 9h-4v6Zm1.5-1.5v-3h2v3h-2ZM4 20q-.825 0-1.413-.588T2 18V6q0-.825.588-1.413T4 4h16q.825 0 1.413.588T22 6v12q0 .825-.588 1.413T20 20H4Zm0-2h16V6H4v12Zm0 0V6v12Z"
		/>
	</svg>
);

const LipSyncIcon = ({ className }: { className?: string }) => (
	<svg 
		xmlns="http://www.w3.org/2000/svg" 
		width="24" 
		height="24" 
		viewBox="0 0 14 14"
		className={className}
	>
		<path 
			fill="none" 
			stroke="currentColor" 
			strokeLinecap="round" 
			strokeLinejoin="round" 
			d="M1 7.5s1.686 3.486 6 3.486S13 7.5 13 7.5s-1.427-4.875-6-2.312C2.427 2.625 1 7.5 1 7.5m0-.004h12"
		/>
	</svg>
);

const iconMap: Record<string, LucideIcon | React.ComponentType<{ className?: string }>> = {
	regenerate: RefreshCw,
	upscale: HdIcon,
	"video-to-video": Video,
	canvas: Palette,
	sound: Volume2,
	"lip-sync": LipSyncIcon,
};

export function ActionButton({
	icon,
	label,
	onClick,
	className,
}: ActionButtonProps) {
	const IconComponent = iconMap[icon] || Palette; // 使用Palette作为默认图标

	return (
		<Button
			type="button"
			variant="outline"
			size="sm"
			className={cn(
				"!bg-background text-muted-foreground flex items-center gap-x-1 !border-none !px-2 hover:!bg-accent",
				"h-8 justify-start text-xs",
				className,
			)}
			onClick={onClick}
		>
			<IconComponent className="size-6" />
			<div className="flex w-full items-center gap-x-1">
				<span>{label}</span>
			</div>
		</Button>
	);
}
