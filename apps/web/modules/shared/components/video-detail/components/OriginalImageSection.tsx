"use client";

import { cn } from "@ui/lib";
import { Search } from "lucide-react";
import Image from "next/image";
import { useState, useEffect } from "react";

interface OriginalImageSectionProps {
	imageUrl: string;
	className?: string;
}

export function OriginalImageSection({
	imageUrl,
	className,
}: OriginalImageSectionProps) {
	const [isHovered, setIsHovered] = useState(false);
	const [imageLoaded, setImageLoaded] = useState(false);
	const [isLoading, setIsLoading] = useState(true);

	// 当imageUrl变化时重置状态
	useEffect(() => {
		setImageLoaded(false);
		setIsLoading(true);
	}, [imageUrl]);

	const handleImageLoad = () => {
		setImageLoaded(true);
		setIsLoading(false);
	};

	const handleImageError = () => {
		setIsLoading(false);
	};

	// 使用固定的最大容器尺寸避免布局跳跃
	const containerSize = { width: 120, height: 80 };

	return (
		<div className={cn("mb-3", className)}>
			<div className="mb-2">
				<div className="inline-flex items-center text-sm font-semibold leading-5">
					Original image
				</div>
			</div>
			<div className="flex gap-2 mb-3">
				<div 
					className="flex-shrink-0 relative"
					style={{ 
						width: `${containerSize.width}px`, 
						height: `${containerSize.height}px` 
					}}
				>
					<div
						className="bg-background hover:border-border text-muted-foreground flex size-full flex-col items-center justify-center overflow-hidden rounded-lg border border-transparent cursor-pointer relative"
						onMouseEnter={() => setIsHovered(true)}
						onMouseLeave={() => setIsHovered(false)}
					>
						{/* 加载占位符 */}
						{isLoading && (
							<div className="absolute inset-0 flex items-center justify-center bg-muted rounded-lg">
								<div className="w-4 h-4 border-2 border-muted-foreground border-t-transparent rounded-full animate-spin" />
							</div>
						)}
						
						{/* 图片容器 */}
						<div className="group relative inline-flex size-full cursor-pointer">
							<div
								className={cn(
									"flex size-full items-center justify-center transition-all duration-200",
									isHovered && "opacity-20",
									isLoading && "opacity-0",
									imageLoaded && "opacity-100"
								)}
							>
								<Image
									src={imageUrl}
									alt="Original image"
									width={containerSize.width}
									height={containerSize.height}
									className="size-full object-contain rounded-lg transition-opacity duration-200"
									onLoad={handleImageLoad}
									onError={handleImageError}
									priority
								/>
							</div>
							
							{/* hover时的搜索图标 */}
							{isHovered && imageLoaded && (
								<div className="absolute left-1/2 top-1/2 z-[1] flex -translate-x-1/2 -translate-y-1/2 transition-opacity duration-200">
									<span className="inline-flex justify-center items-center hover:text-primary cursor-pointer px-1 py-1">
										<Search className="size-4" />
									</span>
								</div>
							)}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
