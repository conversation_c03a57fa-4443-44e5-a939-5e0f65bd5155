"use client";

import { cn } from "@ui/lib";
import { Copy } from "lucide-react";
import { useState } from "react";

interface PromptSectionProps {
	prompt: string;
	className?: string;
}

export function PromptSection({ prompt, className }: PromptSectionProps) {
	const [copied, setCopied] = useState(false);

	const handleCopy = async () => {
		try {
			await navigator.clipboard.writeText(prompt);
			setCopied(true);
			setTimeout(() => setCopied(false), 2000);
		} catch (err) {
			console.error("Failed to copy text: ", err);
		}
	};

	return (
		<div className={cn("mb-3", className)}>
			<div className="mb-2">
				<div className="flex w-full items-center justify-between">
					<div className="inline-flex items-center text-sm font-semibold leading-5">
						Prompt
					</div>
					<button
						type="button"
						onClick={handleCopy}
						className="inline-flex items-center justify-center hover:text-primary transition-colors p-1 rounded"
						title={copied ? "Copied!" : "Copy prompt"}
					>
						<Copy className="text-muted-foreground size-4 cursor-pointer" />
					</button>
				</div>
			</div>
			<div className="text-muted-foreground text-xs leading-relaxed">
				<div className="scrollbar max-h-[100px] overflow-y-auto pr-2">
					{typeof prompt === 'string' ? prompt : JSON.stringify(prompt)}
				</div>
			</div>
			<div className="border-t my-3" />
		</div>
	);
}
