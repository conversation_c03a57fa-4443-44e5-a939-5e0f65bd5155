"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { cn } from "@ui/lib";
import type { VideoData } from "../types";

interface VideoHeaderProps {
	video: VideoData;
	className?: string;
}

export function VideoHeader({ video, className }: VideoHeaderProps) {
	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleString("en-US", {
			year: "numeric",
			month: "2-digit",
			day: "2-digit",
			hour: "2-digit",
			minute: "2-digit",
		});
	};

	return (
		<div className={cn("flex items-center justify-between", className)}>
			<div className="flex">
				<div className="inline-flex cursor-pointer items-center gap-x-2 hover:text-primary">
					<Avatar className="size-6">
						<AvatarImage
							src={video.author.avatar}
							alt={video.author.name}
						/>
						<AvatarFallback className="bg-orange-600 text-white text-xs">
							{video.author.name.charAt(0).toUpperCase()}
						</AvatarFallback>
					</Avatar>
					<span className="line-clamp-1 max-w-[200px] text-sm">
						{video.author.name}
					</span>
				</div>
			</div>
			<div className="text-muted-foreground text-xs font-normal">
				{formatDate(video.createdAt)}
			</div>
		</div>
	);
}
