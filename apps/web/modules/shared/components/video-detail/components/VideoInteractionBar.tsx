"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { cn } from "@ui/lib";
import { Heart, Link, MoreHorizontal, Share2 } from "lucide-react";

interface VideoInteractionBarProps {
	likes: number;
	isLiked: boolean;
	onLike?: (liked: boolean) => void;
	onShare?: () => void;
	onCreateSimilar?: () => void;
	className?: string;
}

export function VideoInteractionBar({
	likes,
	isLiked,
	onLike,
	onShare,
	onCreateSimilar,
	className,
}: VideoInteractionBarProps) {
	const handleLike = () => {
		onLike?.(!isLiked);
	};

	return (
		<div className={cn("flex w-full items-center gap-2", className)}>
			{/* 点赞按钮 */}
			<div
				className="flex cursor-pointer items-center justify-center gap-x-1 rounded-full bg-opacity-15 px-2 py-1 text-xs hover:bg-opacity-40 bg-background hover:bg-accent text-muted-foreground hover:text-primary group"
				onClick={handleLike}
			>
				<Heart
					className={cn(
						"size-[18px] order-1 group-hover:text-primary transition-colors",
						isLiked ? "text-primary fill-primary" : "",
					)}
				/>
				<span className="order-2">{likes}</span>
			</div>

			{/* 操作按钮组 */}
			<div className="flex flex-wrap items-center gap-2 gap-x-1.5">
				<div className="text-muted-foreground hover:bg-accent hover:text-primary flex cursor-pointer items-center gap-x-1 rounded p-1 transition-all">
					<Link className="size-6 md:size-4 !size-[18px]" />
				</div>
				<div
					className="text-muted-foreground hover:bg-accent hover:text-primary flex cursor-pointer items-center gap-x-1 rounded p-1 transition-all"
					onClick={onShare}
				>
					<Share2 className="size-6 md:size-4 !size-[18px]" />
				</div>
				<div className="text-muted-foreground hover:bg-accent hover:text-primary flex cursor-pointer items-center gap-x-1 rounded p-1 transition-all">
					<MoreHorizontal className="size-6 md:size-4 !size-[18px]" />
				</div>
			</div>

			{/* Create Similar Video 按钮 */}
			<Button
				type="button"
				size="sm"
				className="ms-auto flex h-7 items-center justify-center gap-x-1 px-3 text-sm font-semibold md:px-3"
				onClick={onCreateSimilar}
			>
				Create Similar Video
			</Button>
		</div>
	);
}
