"use client";

import { Badge } from "@ui/components/badge";
import { cn } from "@ui/lib";
import { Eye, EyeOff } from "lucide-react";

interface VideoLabelsProps {
	type: "image-to-video" | "text-to-video";
	status: "published" | "unpublished";
	className?: string;
}

export function VideoLabels({ type, status, className }: VideoLabelsProps) {
	const getTypeLabel = (type: string) => {
		switch (type) {
			case "image-to-video":
				return "Image to Video";
			case "text-to-video":
				return "Text to Video";
			default:
				return type;
		}
	};

	return (
		<div
			className={cn("mb-3 flex items-center justify-between", className)}
		>
			<Badge variant="secondary" className="h-6 px-2 text-xs">
				{getTypeLabel(type)}
			</Badge>
			<div className="flex min-w-[120px] justify-end">
				<Badge
					variant={status === "published" ? "default" : "secondary"}
					className={cn(
						"h-6 px-2 text-xs flex items-center gap-1",
						status === "unpublished" &&
							"bg-muted hover:bg-muted/80",
					)}
				>
					{status === "published" ? (
						<>
							<Eye className="size-3" />
							<span>Published</span>
						</>
					) : (
						<>
							<EyeOff className="size-3" />
							<span>Unpublished</span>
						</>
					)}
				</Badge>
			</div>
		</div>
	);
}
