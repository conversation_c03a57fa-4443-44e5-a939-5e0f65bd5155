"use client";

import { cn } from "@ui/lib";
import type { VideoData } from "../types";

interface VideoParametersProps {
	video: VideoData;
	className?: string;
}

export function VideoParameters({ video, className }: VideoParametersProps) {
	const parameters = [
		{ label: "Model", value: video.model },
		{ label: "Resolution", value: video.resolution },
		{ label: "Duration", value: `${video.duration}s` },
		{ label: "Seed", value: video.seed },
		{ label: "Output Dimension", value: video.outputDimension },
	];

	return (
		<div className={cn("space-y-2", className)}>
			{parameters.map((param, index) => (
				<div key={param.label} className="flex items-center text-xs">
					<div className="w-[35%] min-w-[120px] flex-shrink-0">
						<div className="inline-flex items-center after:content-[':'] font-semibold text-foreground/80 whitespace-nowrap">
							{param.label}
						</div>
					</div>
					<div className="flex-1 flex items-center gap-3">
						<span className="text-muted-foreground text-xs">
							{param.value}
						</span>
					</div>
				</div>
			))}
		</div>
	);
}
