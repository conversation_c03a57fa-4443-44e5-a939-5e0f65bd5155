'use client'

import { VideoDetailView, type VideoData } from './index'

// 示例数据
const exampleVideoData: VideoData = {
  id: "cm84hl47d01qnkvxudh1tv25y",
  title: "开盒视频",
  videoUrl: "https://videocdn.pollo.ai/web-cdn/pollo/production/cm84hl47d01qnkvxudh1tv25y/wm/1753357108102-7d65ca48-f97e-4186-af13-7df65b3ffc9f.mp4",
  posterUrl: "https://videocdn.pollo.ai/web-cdn/pollo/production/cm84hl47d01qnkvxudh1tv25y/cover/1753357108102-e5a0aaa8-ba82-4b95-af16-dba07e82ecd4.webp",
  duration: 5,
  author: {
    id: "user123",
    name: "<PERSON> (<PERSON>)",
    avatar: "https://lh3.googleusercontent.com/a/ACg8ocLJtsM1rekLdP8Z3OD3w79xTtlsftvPqcfQETfgV5VhgLR76rI=s96-c"
  },
  createdAt: "2025-07-24T19:37:00Z",
  status: "unpublished",
  type: "image-to-video",
  originalImage: "https://videocdn.pollo.ai/web-cdn/pollo/production/cm84hl47d01qnkvxudh1tv25y/image/1753356961026-eeeb6c6b-b8b9-4651-ac5b-5736b3ca64f7.jpg",
  prompt: "开盒视频",
  model: "Pollo 1.6",
  resolution: "480P",
  seed: "140269222",
  outputDimension: "736 x 544",
  project: "Default Project",
  likes: 1,
  isLiked: true,
  isFavorite: false
}

export function VideoDetailViewExample() {
  const handleRegenerate = () => {
    console.log('Regenerate clicked')
  }

  const handleUpscale = () => {
    console.log('Upscale clicked')
  }

  const handleVideoToVideo = () => {
    console.log('Video to Video clicked')
  }

  const handleCanvas = () => {
    console.log('Canvas clicked')
  }

  const handleAddSoundEffects = () => {
    console.log('Add Sound Effects clicked')
  }

  const handleLipSync = () => {
    console.log('Lip Sync clicked')
  }

  const handleLike = (liked: boolean) => {
    console.log('Like toggled:', liked)
  }

  const handleFavorite = (favorited: boolean) => {
    console.log('Favorite toggled:', favorited)
  }

  const handleShare = () => {
    console.log('Share clicked')
  }

  const handleCreateSimilar = () => {
    console.log('Create Similar Video clicked')
  }

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="mx-auto max-w-7xl">
        <h1 className="mb-6 text-3xl font-bold">Video Detail View Example</h1>
        
        <VideoDetailView
          video={exampleVideoData}
          onRegenerate={handleRegenerate}
          onUpscale={handleUpscale}
          onVideoToVideo={handleVideoToVideo}
          onCanvas={handleCanvas}
          onAddSoundEffects={handleAddSoundEffects}
          onLipSync={handleLipSync}
          onLike={handleLike}
          onFavorite={handleFavorite}
          onShare={handleShare}
          onCreateSimilar={handleCreateSimilar}
        />
      </div>
    </div>
  )
}

// 使用示例
export default function ExamplePage() {
  return <VideoDetailViewExample />
}