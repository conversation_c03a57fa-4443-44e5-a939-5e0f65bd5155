export interface VideoData {
  id: string
  title?: string
  videoUrl: string
  posterUrl?: string
  duration: number
  author: UserProfile
  createdAt: string
  status: 'published' | 'unpublished'
  type: 'image-to-video' | 'text-to-video'
  originalImage?: string
  prompt: string
  model: string
  resolution: string  
  seed: string
  outputDimension: string
  project: string
  likes: number
  isLiked: boolean
  isFavorite?: boolean
}

export interface UserProfile {
  id: string
  name: string
  avatar?: string
}

export interface VideoDetailViewProps {
  video: VideoData
  onRegenerate?: () => void
  onUpscale?: () => void
  onVideoToVideo?: () => void
  onCanvas?: () => void
  onAddSoundEffects?: () => void
  onLipSync?: () => void
  onLike?: (liked: boolean) => void
  onFavorite?: (favorited: boolean) => void
  onShare?: () => void
  onCreateSimilar?: () => void
  onNavigatePrevious?: () => void
  onNavigateNext?: () => void
  hasPrevious?: boolean
  hasNext?: boolean
  className?: string
}

export interface SimpleVideoPlayerProps {
  videoUrl: string
  posterUrl?: string
  duration?: number
  autoPlay?: boolean
  muted?: boolean
  loop?: boolean
  onReady?: (player: any) => void
  className?: string
}

export interface VideoInfoPanelProps {
  video: VideoData
  className?: string
}

export interface VideoActionsPanelProps {
  video: VideoData
  onRegenerate?: () => void
  onUpscale?: () => void
  onVideoToVideo?: () => void
  onCanvas?: () => void
  onAddSoundEffects?: () => void
  onLipSync?: () => void
  onLike?: (liked: boolean) => void
  onFavorite?: (favorited: boolean) => void
  onShare?: () => void
  onCreateSimilar?: () => void
  className?: string
}