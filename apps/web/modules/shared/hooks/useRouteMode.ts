'use client'

import { usePathname, useSearchParams } from 'next/navigation'

export function useRouteMode() {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  
  // 检测访问来源
  const fromParam = searchParams.get('from')      // from=my-generations
  const sourceParam = searchParams.get('source')  // source=share
  const isIntercepted = pathname.includes('@modal')
  
  return {
    isModal: isIntercepted && (fromParam || sourceParam),
    isDirectAccess: !isIntercepted,
    accessSource: fromParam || sourceParam || 'direct',
    // 用于分析和显示逻辑
    isFromList: fromParam === 'my-generations',
    isFromShare: sourceParam === 'share',
    // 根据来源提供差异化体验
    showManagementOptions: fromParam === 'my-generations',
    showSocialFeatures: sourceParam === 'share',
    trackingContext: fromParam || sourceParam || 'direct'
  }
}