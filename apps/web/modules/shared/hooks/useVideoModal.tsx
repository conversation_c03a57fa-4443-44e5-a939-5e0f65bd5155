'use client'

import { useState, useEffect, useCallback } from 'react'
import { usePathname, useSearchParams } from 'next/navigation'

interface UseVideoModalOptions {
  locale: string
}

export function useVideoModal({ locale }: UseVideoModalOptions) {
  const [isOpen, setIsOpen] = useState(false)
  const [videoId, setVideoId] = useState<string | null>(null)
  const pathname = usePathname()
  const searchParams = useSearchParams()

  // 监听 URL 变化
  useEffect(() => {
    const pathMatch = pathname.match(/\/v\/([^\/]+)$/)
    if (pathMatch) {
      const id = pathMatch[1]
      const fromParam = searchParams.get('from')
      
      // 只有从列表页来的才显示模态框
      if (fromParam === 'my-generations') {
        setVideoId(id)
        setIsOpen(true)
      }
    } else {
      setIsOpen(false)
      setVideoId(null)
    }
  }, [pathname, searchParams])

  const openModal = useCallback((id: string) => {
    setVideoId(id)
    setIsOpen(true)
    
    // 更新 URL 但不触发导航
    const newUrl = `/${locale}/v/${id}?from=my-generations`
    window.history.pushState({ modalOpen: true, videoId: id }, '', newUrl)
  }, [locale])

  const closeModal = useCallback(() => {
    setIsOpen(false)
    setVideoId(null)
    
    // 返回到列表页
    window.history.back()
  }, [])

  // 处理浏览器后退按钮
  useEffect(() => {
    const handlePopState = (event: PopStateEvent) => {
      if (!event.state?.modalOpen) {
        setIsOpen(false)
        setVideoId(null)
      }
    }

    window.addEventListener('popstate', handlePopState)
    return () => window.removeEventListener('popstate', handlePopState)
  }, [])

  return {
    isOpen,
    videoId,
    openModal,
    closeModal
  }
}