// apps/web/modules/shared/lib/jobs-api.ts
import { apiClient } from "@shared/lib/api-client";

// Job API 类型定义
export interface CreateJobRequest {
  featureCode: string;
  type: "video" | "image";
  numOutputs: number;
  modelParam: {
    modelCode: string;
    prompt?: string;
    image?: string;
    imageTail?: string;
    negativePrompt?: string;
    strength?: number;
    duration: number;
    modeCode: string;
    resolution?: string;
    aspectRatio: string;
    style: string;
    motionRange: string;
    seed?: number;
    cameraType?: string;
    cameraConfig?: string;
    cameraFixed?: boolean;
    video?: string;
    processType: "text" | "image" | "video";
  };
  options?: {
    enableMagicPrompt?: boolean;
    enableTranslatePrompt?: boolean;
    enableTranslateNegativePrompt?: boolean;
    protectionMode?: boolean;
    published?: boolean;
  };
}

export interface CreateJobResponse {
  success: boolean;
  data?: {
    user: {
      id: string;
      name: string;
      username: string;
      image: string;
    };
    job: {
      id: string;
      userId: string;
      featureCode: string;
      type: "video" | "image";
      numOutputs: number;
      status: "waiting" | "processing" | "succeeded" | "failed";
      credit: number;
      apiProviderCost: number;
      timeCostSeconds: number;
      modelCode: string;
      prompt: string;
      image: string;
      imageTail: string;
      negativePrompt: string;
      promptStrength: number;
      duration: number;
      modeCode: string;
      resolution: string;
      aspectRatio: string;
      style: string;
      motionRange: string;
      seed: number;
      processType: string;
      createdAt: string;
      updatedAt: string;
    };
    generations: Array<{
      id: string;
      mediaId: string;
      cover: string;
      thumbnail: string;
      videoUrl: string;
      mediaUrl: string;
      status: "waiting" | "processing" | "succeeded" | "failed";
      mediaType: "video" | "image";
      duration: number;
      createdAt: string;
      updatedAt: string;
    }>;
    progress: {
      completed: number;
      total: number;
    };
  };
  message?: string;
  timestamp?: string;
  error?: string;
}

export interface GetJobResponse {
  success: boolean;
  data?: {
    user: {
      id: string;
      name?: string;
      username?: string;
      image?: string;
    };
    job: {
      id: string;
      userId: string;
      featureCode: string;
      type: "video" | "image";
      numOutputs: number;
      status: "waiting" | "processing" | "succeeded" | "failed";
      credit: number;
      apiProviderCost?: number;
      timeCostSeconds?: number;
      modelCode?: string;
      prompt?: string;
      image?: string;
      imageTail?: string;
      negativePrompt?: string;
      promptStrength?: number;
      duration?: number;
      modeCode?: string;
      resolution?: string;
      aspectRatio?: string;
      style?: string;
      motionRange?: string;
      seed?: number;
      processType?: string;
      createdAt: string;
      updatedAt: string;
    };
    generations: Array<{
      id: string;
      mediaId: string;
      cover?: string;
      thumbnail?: string;
      videoUrl?: string;
      mediaUrl?: string;
      status: "waiting" | "processing" | "succeeded" | "failed";
      mediaType: "video" | "image";
      duration?: number;
      createdAt: string;
      updatedAt: string;
    }>;
    progress: {
      completed: number;
      total: number;
    };
  };
  error?: string;
}

/**
 * 创建视频生成任务
 */
export async function createVideoJob(request: CreateJobRequest): Promise<CreateJobResponse> {
  console.log("🚀 createVideoJob called with request:", JSON.stringify(request, null, 2));
  
  // 真实API调用
  try {
    console.log("📡 Sending POST request to /api/jobs...");
    const response = await apiClient.jobs.$post({
      json: request,
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error("❌ API request failed:", response.status, errorText);
      return {
        success: false,
        error: `API Error: ${response.status} - ${errorText}`,
      };
    }
    
    const data = await response.json();
    console.log("📥 Received response:", data);

    // 直接返回响应数据，响应格式已经符合CreateJobResponse
    console.log("✅ Job created successfully:", data);
    return data as CreateJobResponse;
  } catch (error) {
    console.error("💥 Error creating video job:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * 获取任务详情
 */
export async function getJobDetails(jobId: string): Promise<GetJobResponse> {
  console.log("🔍 Getting job details for jobId:", jobId);
  
  // 真实API调用
  try {
    const response = await apiClient.jobs.details[":id"].$get({
      param: {
        id: jobId,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("❌ API request failed:", response.status, errorText);
      return {
        success: false,
        error: `API Error: ${response.status} - ${errorText}`,
      };
    }

    const data = await response.json();
    console.log("✅ Job details retrieved:", data);
    return data as GetJobResponse;
  } catch (error) {
    console.error("Error getting job details:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * 上传图片到云存储
 * @deprecated 图片在用户选择时已经上传到videocdn，直接使用accessURL即可
 */
async function uploadImage(file: File | Blob): Promise<string> {
  const formData = new FormData();
  formData.append('file', file);
  
  try {
    const response = await fetch('/api/uploads', {
      method: 'POST',
      body: formData,
    });
    
    if (!response.ok) {
      throw new Error('Failed to upload image');
    }
    
    const result = await response.json();
    return result.url || result.data?.url;
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
}

/**
 * 将表单数据转换为Job API请求格式
 */
export async function convertFormDataToJobRequest(formData: any): Promise<CreateJobRequest> {
  // ✅ 不需要映射，直接使用表单值，因为表单组件应该已经提供正确的API格式值

  // 🔧 处理图片URL
  console.log("🖼️ Looking for image in formData:");
  console.log("  - formData:", JSON.stringify(formData, (key, value) => {
    if (value instanceof Blob) {
      return `Blob(${value.size} bytes, type: ${value.type})`;
    }
    return value;
  }, 2));
  
  // ✅ 修正：formData.image 现在应该是 string (uploadedURL) 或 Blob
  let imageUrl = "";
  let imageTailUrl = "";
  
  // 处理主图片
  if (typeof formData.image === 'string') {
    // 如果是字符串，说明是上传成功的URL
    imageUrl = formData.image;
  } else if (formData.image instanceof Blob) {
    // 如果是Blob，说明图片上传组件有问题，应该传递URL
    console.error("❌ formData.image is still a Blob, but should be uploadedURL!");
    throw new Error("Image upload failed. The image should be uploaded and return an accessURL.");
  } else if (formData.imageUrl) {
    imageUrl = formData.imageUrl;
  } else if (formData.imageAccessURL) {
    imageUrl = formData.imageAccessURL;
  }
  
  // 处理尾部图片
  if (typeof formData.imageTail === 'string') {
    imageTailUrl = formData.imageTail;
  } else if (formData.imageTailUrl) {
    imageTailUrl = formData.imageTailUrl;
  } else if (formData.imageTailAccessURL) {
    imageTailUrl = formData.imageTailAccessURL;
  }
  
  console.log("📸 Final imageUrl:", imageUrl);
  console.log("📸 Final imageTailUrl:", imageTailUrl);
  
  // 验证必需的图片URL
  if (!imageUrl) {
    console.error("❌ No image URL found in formData!");
    console.error("❗ formData.image should be the uploadedURL from the image upload component.");
    throw new Error("Image URL is required. Please upload an image first.");
  }

  // ✅ 获取模型配置以决定支持哪些字段
  const selectedModel = formData.model;
  
  // 构建 modelParam，只包含模型支持的字段
  const modelParam: any = {
    modelCode: selectedModel?.code || selectedModel?.value || "",
    prompt: formData.prompt || "",
    image: imageUrl, // 图生视频必需
    duration: formData.videoLength || selectedModel?.videoLengthOptions?.[0] || 5, // 使用模型默认值
    aspectRatio: formData.aspectRatio || selectedModel?.aspectRatioOptions?.[0] || "16:9", // 使用模型默认值
    processType: "image" as const,
  };

  // 根据模型配置添加可选字段
  if (imageTailUrl) {
    modelParam.imageTail = imageTailUrl;
  }
  
  if (formData.negativePrompt && selectedModel?.supportsNegativePrompt) {
    modelParam.negativePrompt = formData.negativePrompt;
  }
  
  if (formData.promptStrength && selectedModel?.supportsPromptStrength) {
    modelParam.promptStrength = formData.promptStrength / 100;
  }
  
  if (formData.resolution && selectedModel?.resolutionOptions?.length > 0) {
    modelParam.resolution = formData.resolution;
  } else if (selectedModel?.resolutionOptions?.length === 1) {
    // 如果模型只有一个分辨率选项，使用该默认值
    modelParam.resolution = selectedModel.resolutionOptions[0];
  }
  
  if (formData.seed && selectedModel?.supportsSeed) {
    modelParam.seed = formData.seed;
  }
  
  // 只有模型支持时才添加这些字段
  if (formData.style && selectedModel?.styleOptions?.length > 0) {
    modelParam.style = formData.style;
  }
  
  if (formData.motionRange && selectedModel?.motionRangeOptions?.length > 0) {
    modelParam.motionRange = formData.motionRange;
  }
  
  if (formData.mode && selectedModel?.modeOptions?.length > 0) {
    modelParam.modeCode = formData.mode;
  }

  return {
    featureCode: "image-to-video", // ✅ 修复：根据功能设置正确的 featureCode
    type: "video",
    numOutputs: formData.outputNumber || 1,
    modelParam,
    options: {
      enableMagicPrompt: false,
      enableTranslatePrompt: false,
      enableTranslateNegativePrompt: false,
      protectionMode: formData.copyProtection || false,
      published: formData.publicVisibility || false,
    },
  };
}