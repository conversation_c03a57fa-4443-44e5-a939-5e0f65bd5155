import { create } from 'zustand';

// 防止重复请求的标志
let isCreditsRequestInProgress = false;

interface CreditsState {
  // 状态
  credits: number | null;
  isLoading: boolean;

  // 操作
  setCredits: (credits: number) => void;
  decrementCredits: (amount?: number) => Promise<void>;
  fetchCredits: () => Promise<void>;
}

export const useCreditsStore = create<CreditsState>((set, get) => ({
  // 初始状态
  credits: null,
  isLoading: false,

  // 设置积分
  setCredits: (credits) => set({ credits }),

  // 减少积分（用于消费）- 使用乐观更新 + 后台验证
  decrementCredits: async (amount = 1) => {
    // 保存当前积分状态，以便在失败时恢复
    const previousCredits = get().credits;

    // 乐观更新 UI
    if (previousCredits !== null) {
      set({ credits: Math.max(0, previousCredits - amount) });
    }

    // 后台验证 - 获取服务器上的实际积分
    try {
      const response = await fetch("/api/credits");
      if (response.ok) {
        const data = await response.json();
        // 更新为服务器上的实际值
        set({ credits: data.credits });
      } else {
        // 如果请求失败，恢复到之前的状态
        set({ credits: previousCredits });
        console.error("Failed to verify credits after decrement");
      }
    } catch (error) {
      // 如果请求出错，恢复到之前的状态
      set({ credits: previousCredits });
      console.error("Error verifying credits:", error);
    }
  },

  // 从 API 获取积分
  fetchCredits: async () => {
    // 如果已经有请求在进行中，则跳过
    if (isCreditsRequestInProgress || get().isLoading) {
      return;
    }

    isCreditsRequestInProgress = true;
    set({ isLoading: true });
    
    try {
      // 使用新的 API 端点
      const response = await fetch('/api/credits');
      
      if (response.ok) {
        const data = await response.json();
        set({ credits: data.credits, isLoading: false });
      } else {
        console.error('Credits API endpoint failed:', response.status);
        set({ isLoading: false });
      }
    } catch (error) {
      console.error('Error fetching credits:', error);
      set({ isLoading: false });
    } finally {
      isCreditsRequestInProgress = false;
    }
  },
}));
