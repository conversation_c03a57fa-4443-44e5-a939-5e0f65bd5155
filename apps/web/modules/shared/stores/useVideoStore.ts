'use client'

import { create } from 'zustand'
import type { VideoData } from '@shared/components/video-detail/types'

interface VideoStore {
  videos: VideoData[]
  selectedVideoId: string | null
  
  // 从列表中获取完整数据（模态框模式）
  getVideoById: (id: string) => VideoData | null
  // 设置视频列表（包含完整详细信息）
  setVideos: (videos: VideoData[]) => void
  // 设置选中的视频ID
  setSelectedVideoId: (id: string | null) => void
}

export const useVideoStore = create<VideoStore>((set, get) => ({
  videos: [],
  selectedVideoId: null,
  
  getVideoById: (id: string) => {
    const videos = get().videos
    return videos.find(video => video.id === id) || null
  },
  
  setVideos: (videos: VideoData[]) => {
    set({ videos })
  },
  
  setSelectedVideoId: (id: string | null) => {
    set({ selectedVideoId: id })
  }
}))

