import { config } from "@repo/config";

/**
 * 构建本地化路径，处理默认语言不包含语言前缀的情况
 * @param locale 当前语言
 * @param path 路径（不包含语言前缀）
 * @returns 完整的本地化路径
 */
export function buildLocalePath(locale: string, path: string): string {
  // 确保路径以 / 开头
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  
  // 如果是默认语言（英语），不添加语言前缀
  if (locale === config.i18n.defaultLocale) {
    return normalizedPath;
  }
  
  // 非默认语言需要添加语言前缀
  return `/${locale}${normalizedPath}`;
}

/**
 * 构建视频详情页面的本地化路径
 * @param locale 当前语言
 * @param videoId 视频ID
 * @param fromParam 来源参数（可选）
 * @returns 完整的视频详情页面路径
 */
export function buildVideoPath(locale: string, videoId: string, fromParam?: string): string {
  const basePath = buildLocalePath(locale, `/v/${videoId}`);
  
  if (fromParam) {
    return `${basePath}?from=${fromParam}`;
  }
  
  return basePath;
}