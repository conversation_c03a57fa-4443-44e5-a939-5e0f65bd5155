"use client";

import { toast } from "sonner";

type ToastProps = React.ComponentProps<typeof toast>;

const useToast = () => {
  return {
    toast,
    dismiss: toast.dismiss,
    error: toast.error,
    success: toast.success,
    warning: toast.warning,
    info: toast.info,
    promise: toast.promise,
    custom: toast.custom,
    loading: toast.loading,
  };
};

export { useToast, toast };
export type { ToastProps };
