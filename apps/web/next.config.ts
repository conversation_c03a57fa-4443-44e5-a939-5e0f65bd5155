import { withContentCollections } from "@content-collections/next";
// @ts-expect-error - PrismaPlugin is not typed
import { PrismaPlugin } from "@prisma/nextjs-monorepo-workaround-plugin";
import type { NextConfig } from "next";
import nextIntlPlugin from "next-intl/plugin";

const withNextIntl = nextIntlPlugin("./modules/i18n/request.ts");

const nextConfig: NextConfig = {
	transpilePackages: ["@repo/api", "@repo/auth", "@repo/database"],
	experimental: {
		optimizePackageImports: ["@repo/api", "@repo/auth", "@repo/database"],
	},
	images: {
		remotePatterns: [
			{
				// google profile images
				protocol: "https",
				hostname: "lh3.googleusercontent.com",
			},
			{
				// github profile images
				protocol: "https",
				hostname: "avatars.githubusercontent.com",
			},
			{
				// FluxFly CDN images
				protocol: "https",
				hostname: "cdn.fluxfly.ai",
			},
			{
				// FluxFly Video CDN
				protocol: "https",
				hostname: "videocdn.fluxfly.ai",
			},
		],
		// 增加这些配置来提高兼容性
		dangerouslyAllowSVG: true,
		contentDispositionType: 'attachment',
		// 在测试环境中禁用图片优化以避免 500 错误
		unoptimized: process.env.NEXT_PUBLIC_SITE_URL?.includes('test.connectionshinttoday.tips'),
	},
	async redirects() {
		return [
			{
				source: "/app/settings",
				destination: "/app/settings/general",
				permanent: true,
			},
			{
				source: "/app/:organizationSlug/settings",
				destination: "/app/:organizationSlug/settings/general",
				permanent: true,
			},
			{
				source: "/app/admin",
				destination: "/app/admin/users",
				permanent: true,
			},
		];
	},
	eslint: {
		ignoreDuringBuilds: true,
	},
	webpack: (config, { webpack, isServer }) => {
		config.plugins.push(
			new webpack.IgnorePlugin({
				resourceRegExp: /^pg-native$|^cloudflare:sockets$/,
			}),
		);

		if (isServer) {
			config.plugins.push(new PrismaPlugin());
		}

		// Fix Windows permission issues
		config.watchOptions = {
			...config.watchOptions,
			ignored: [
				'**/node_modules/**',
				'**/.git/**',
				'**/Application Data/**',
				'**/AppData/**',
			],
		};

		return config;
	},
};

export default withContentCollections(withNextIntl(nextConfig));
