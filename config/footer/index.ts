import type { FooterConfig } from "./types";

export const footerConfig: FooterConfig = {
	sections: [
		{
			id: "quickLinks",
			titleKey: "footer.sections.quickLinks.title",
			links: [
				{
					href: "/",
					labelKey: "footer.sections.quickLinks.links.home",
				},
				{
					href: "/",
					labelKey:
						"footer.sections.quickLinks.links.github",
				},
				{
					href: "/",
					labelKey:
						"footer.sections.quickLinks.links.support",
				},
			],
		},
		{
			id: "company",
			titleKey: "footer.sections.company.title",
			links: [
				{
					href: "/contact",
					labelKey: "footer.sections.company.links.contact",
				},
				{
					href: "/legal/privacy-policy",
					labelKey: "footer.sections.company.links.privacy",
				},
				{
					href: "/legal/terms",
					labelKey: "footer.sections.company.links.terms",
				},
			],
		},
	],
	socialLinks: [
		{ href: "https://twitter.com/supapi", icon: "Twitter" },
		{ href: "https://facebook.com/supapi", icon: "Facebook" },
		{ href: "https://tiktok.com/supapi", icon: "Play" },
		{ href: "https://discord.gg/supapi", icon: "MessageSquare" },
	],
	legalLinks: [
		{ href: "/legal/privacy-policy", labelKey: "footer.legal.privacy" },
		{ href: "/legal/terms", labelKey: "footer.legal.terms" },
	],
	copyrightKey: "footer.copyright",
};

export * from "./types";
