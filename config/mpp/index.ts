import type { ModelProviderConfig } from "./types";

export const modelApiMapping: ModelProviderConfig = {
	models: {
		"kling-v2.0": {
			replicate: "kwaivgi/kling-v2.0",
			fal: "fal-ai/kling-video/v2.0"
		},
		"kling-v1.6-standard": {
			replicate: "kwaivgi/kling-v1.6-standard",
			fal: "fal-ai/kling-video/v1.6-standard"
		},
		"kling-v1.6-pro": {
			replicate: "kwaivgi/kling-v1.6-pro",
			fal: "fal-ai/kling-video/v1.6-pro"
		},
		"kling-v1.5": {
			replicate: "kwaivgi/kling-v1.5",
			fal: "fal-ai/kling-video/v1.5"
		},
		"kling-v1.0": {
			replicate: "kwaivgi/kling-v1.0",
			fal: "fal-ai/kling-video/v1.0"
		},
		"video-01": {
			replicate: "replicate/hailuo-video-01",
			fal: "fal-ai/hailuo-video-01"
		},
		"video-01-live": {
			replicate: "replicate/hailuo-video-01-live",
			fal: "fal-ai/hailuo-video-01-live"
		},
		"runway-gen4-turbo": {
			replicate: "replicate/runway-gen4-turbo",
			fal: "fal-ai/runway-gen4-turbo"
		},
		"runway-gen3-alphaturbo": {
			replicate: "replicate/runway-gen3-alpha-turbo",
			fal: "fal-ai/runway-gen3-alpha-turbo"
		},
		"pixverse-v4.5": {
			replicate: "replicate/pixverse-v4.5",
			fal: "fal-ai/pixverse/v4.5"
		},
		"pixverse-v4": {
			replicate: "replicate/pixverse-v4",
			fal: "fal-ai/pixverse/v4"
		},
		"pixverse-v3.5": {
			replicate: "replicate/pixverse-v3.5",
			fal: "fal-ai/pixverse/v3.5"
		},
		"veo-2": {
			replicate: "replicate/google-veo-2",
			fal: "fal-ai/google-veo-2"
		},
		"pika-v2.2": {
			replicate: "replicate/pika-v2.2",
			fal: "fal-ai/pika-v2.2"
		},
		"pika-v2.1": {
			replicate: "replicate/pika-v2.1",
			fal: "fal-ai/pika-v2.1"
		},
		"vidu-q1": {
			replicate: "replicate/vidu-q1",
			fal: "fal-ai/vidu-q1"
		},
		"vidu-2.0": {
			replicate: "replicate/vidu-v2.0",
			fal: "fal-ai/vidu-v2.0"
		},
		"ray-2": {
			replicate: "replicate/luma-ray-2",
			fal: "fal-ai/luma-ray-2"
		},
		"ray-1.6": {
			replicate: "replicate/luma-ray-1.6",
			fal: "fal-ai/luma-ray-1.6"
		},
		"wanx-2.1": {
			replicate: "replicate/alibaba-wanx-2.1",
			fal: "fal-ai/alibaba-wanx-2.1"
		},
		hunyuan: {
			replicate: "replicate/tencent-hunyuan-video",
			fal: "fal-ai/tencent-hunyuan-video"
		},
		"seedance-1.0-lite": {
			replicate: "replicate/seedance-1.0-lite",
			fal: "fal-ai/seedance-1.0-lite"
		},
		"seedance-1.0-pro": {
			replicate: "replicate/seedance-1.0-pro",
			fal: "fal-ai/seedance-1.0-pro"
		}
	}
};

export * from "./types";
export type { ModelProviderConfig };