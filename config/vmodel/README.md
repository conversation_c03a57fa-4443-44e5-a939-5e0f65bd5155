# 视频模型配置

## 设计理念

本配置采用**统一配置 + 功能过滤**的设计模式，所有视频生成功能（图生视频、文生视频等）共享同一个模型配置，通过 `supportedFeatures` 字段来标识每个模型支持的功能类型。

## 优势

1. **维护性好**：只需维护一个配置文件，避免重复配置
2. **数据一致性**：确保模型信息在整个应用中保持一致
3. **扩展性强**：添加新功能或新模型时，只需在一个地方修改
4. **代码复用**：组件可以在不同功能间复用

## 配置结构

```typescript
export interface VideoModel {
  // 基本信息
  id: string;
  name: string;
  description: string;
  
  // 功能支持
  supportedFeatures: SupportedFeature[]; // 关键字段
  supportsPrompt: boolean;
  supportsEndFrame: boolean;
  supportsNegativePrompt: boolean;
  supportsPromptStrength: boolean;
  supportsSeed: boolean;
  
  // 其他配置...
}

export type SupportedFeature = "image-to-video" | "text-to-video";
```

## 使用方法

### 1. 在图生视频中使用

```tsx
<ModelOptionsProvider feature="image-to-video">
  <VideoGenerationForm />
</ModelOptionsProvider>
```

### 2. 在文生视频中使用

```tsx
<ModelOptionsProvider feature="text-to-video">
  <VideoGenerationForm />
</ModelOptionsProvider>
```

### 3. 获取过滤后的模型列表

```typescript
import { getModelsForFeature } from "./lib/modelConfig";

// 获取支持图生视频的模型
const imageToVideoModels = getModelsForFeature("image-to-video");

// 获取支持文生视频的模型
const textToVideoModels = getModelsForFeature("text-to-video");
```

## 配置示例

```typescript
export const videoModelConfig: VideoModelConfig = {
  models: [
    {
      id: "kling1_6",
      name: "Kling 1.6",
      supportedFeatures: ["image-to-video", "text-to-video"], // 支持两种功能
      // ... 其他配置
    },
    {
      id: "kling2_0", 
      name: "Kling 2.0",
      supportedFeatures: ["text-to-video"], // 只支持文生视频
      // ... 其他配置
    }
  ]
};
```

## 添加新功能

如果需要添加新的视频生成功能（如视频编辑、视频增强等），只需：

1. 在 `SupportedFeature` 类型中添加新的功能类型
2. 在相应模型的 `supportedFeatures` 数组中添加新功能
3. 在新功能的组件中使用 `ModelOptionsProvider` 并指定功能类型

这样可以确保配置的一致性和可维护性。 