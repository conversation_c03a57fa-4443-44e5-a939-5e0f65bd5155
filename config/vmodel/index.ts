import type { VideoModelConfig } from "./types";

// 基础CDN URL - 从环境变量获取或使用默认值
const CDN_BASE_URL = process.env.NEXT_PUBLIC_VIDEO_CDN_BASE_URL || "https://videocdn.fluxfly.ai";

// 图标路径映射
const ICON_PATHS = {
  kling: `${CDN_BASE_URL}/icons/kling-logo.svg`,
  hailuo: `${CDN_BASE_URL}/icons/hailuo-logo.svg`,
  pixverse: `${CDN_BASE_URL}/icons/pixverse-logo.svg`,
  
  luma: `${CDN_BASE_URL}/icons/Luma-logo.svg`,
  google: `${CDN_BASE_URL}/icons/google-logo.svg`,
  vidu: `${CDN_BASE_URL}/icons/vidu-logo.svg`,
  seaweed: `${CDN_BASE_URL}/icons/seaweed-logo.svg`,
  runway: `${CDN_BASE_URL}/icons/runway-logo.svg`,
  pika: `${CDN_BASE_URL}/icons/pika-logo.svg`,
  wanx: `${CDN_BASE_URL}/icons/wanx-logo.svg`,
  hunyuan: `${CDN_BASE_URL}/icons/hunyuan-logo.svg`,
  seedance: `${CDN_BASE_URL}/icons/seaweed-logo.svg`, // seedance 使用 seaweed 图标
};

export const videoModelConfig: VideoModelConfig = {
  "models": [
    {
      "code": "kling-v2.0",
      "value": "kling-v2.0",
      "name": "Kling 2.0",
      "description": "Better motion dynamics and aesthetics",
      "descriptionKey": "videoModels.kling-v2-0.description",
      "modelType": "video",
      "apiProviderCode": "replicate",
      "isActive": true,
      "icon": ICON_PATHS.kling,
      "generationTimeSeconds": 480,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": true,
      "supportsPromptStrength": true,
      "supportsSeed": false,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "resolutionOptions": ["720P"],    
      "videoLengthOptions": [5, 10],
      "aspectRatioOptions": ["16:9", "9:16", "1:1"],      
      "pricingConfig": {
        "baseCredits": 100,
        "multipliers": {
          "videoLength": {
            "5": 1.0,
            "10": 2.0
          }
        }
      }
    },
    {
      "code": "kling-v1.6-standard",
      "value": "kling-v1.6-standard",
      "name": "Kling 1.6 Standard",
      "description": "More realistic motions and higher video quality",
      "descriptionKey": "videoModels.kling-v1-6-standard.description",
      "modelType": "video",
      "apiProviderCode": "replicate",
      "isActive": true,
      "icon": ICON_PATHS.kling,
      "generationTimeSeconds": 300,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": true,
      "supportsPromptStrength": true,
      "supportsSeed": false,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "resolutionOptions": ["720P"],      
      "videoLengthOptions": [5, 10],
      "pricingConfig": {
        "baseCredits": 20,
        "multipliers": {
          "videoLength": {
            "5": 1.0,
            "10": 2.0
          }
        }
      }
    },
    {
      "code": "kling-v1.6-pro",
      "value": "kling-v1.6-pro",
      "name": "Kling 1.6 Pro",
      "description": "More realistic motions and Higher video quality",
      "descriptionKey": "videoModels.kling-v1-6-pro.description",
      "modelType": "video",
      "apiProviderCode": "replicate",
      "isActive": true,
      "icon": ICON_PATHS.kling,
      "generationTimeSeconds": 240,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": true,
      "supportsNegativePrompt": true,
      "supportsPromptStrength": true,
      "supportsSeed": false,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "resolutionOptions": ["1080P"],          
      "videoLengthOptions": [5, 10],
      "pricingConfig": {
        "baseCredits": 40,
        "multipliers": {
          "videoLength": {
            "5": 1.0,
            "10": 2.0
          }
        }
      }
    },    
    {
      "code": "kling-v1.5",
      "value": "kling-v1.5",
      "name": "Kling 1.5",
      "description": "Suitable for complex scenes",
      "descriptionKey": "videoModels.kling-v1-5.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": true,
      "icon": ICON_PATHS.kling,
      "generationTimeSeconds": 360,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": true,
      "supportsNegativePrompt": true,
      "supportsPromptStrength": true,
      "supportsSeed": false,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "modes": [
        {
          "code": "std",
          "name": "Standard Mode",
          "nameKey": "videoModels.kling-v1-5.modes.std.name",
          "description": "Fast AI video generation speed",
          "descriptionKey": "videoModels.kling-v1-5.modes.std.description"
        },
        {
          "code": "pro",
          "name": "Professional Mode",
          "nameKey": "videoModels.kling-v1-5.modes.pro.name",
          "description": "Higher video quality but with longer generation time",
          "descriptionKey": "videoModels.kling-v1-5.modes.pro.description"
        }
      ],
      "videoLengthOptions": [5, 10],
      "pricingConfig": {
        "baseCredits": 20,
        "multipliers": {
          "mode": {
            "std": 1.0,
            "pro": 2.0
          },
          "videoLength": {
            "5": 1.0,
            "10": 2.0
          }
        }
      }
    },
    {
      "code": "kling-v1.0",
      "value": "kling-v1.0",
      "name": "Kling 1",
      "description": "Suitable for short videos",
      "descriptionKey": "videoModels.kling-v1-0.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "icon": ICON_PATHS.kling,
      "generationTimeSeconds": 360,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": true,
      "supportsNegativePrompt": true,
      "supportsPromptStrength": true,
      "supportsSeed": false,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "modes": [
        {
          "code": "std",
          "name": "Standard Mode",
          "nameKey": "videoModels.kling-v1-0.modes.std.name",
          "description": "Fast AI video generation speed",
          "descriptionKey": "videoModels.kling-v1-0.modes.std.description"
        },
        {
          "code": "pro",
          "name": "Professional Mode",
          "nameKey": "videoModels.kling-v1-0.modes.pro.name",
          "description": "Higher video quality but with longer generation time",
          "descriptionKey": "videoModels.kling-v1-0.modes.pro.description"
        }
      ],
      "videoLengthOptions": [5, 10],
      "pricingConfig": {
        "baseCredits": 10,
        "multipliers": {
          "mode": {
            "std": 1.0,
            "pro": 4.0
          },
          "videoLength": {
            "5": 1.0,
            "10": 2.0
          }
        }
      }
    },
    {
      "code": "video-01",
      "value": "video-01",
      "name": "Hailuo",
      "description": "Highest video quality",
      "descriptionKey": "videoModels.video-01.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "icon": ICON_PATHS.hailuo,
      "generationTimeSeconds": 180,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": false,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "pricingConfig": {
        "baseCredits": 35,
        "multipliers": {}
      }
    },
    {
      "code": "video-01-live",
      "value": "video-01-live",
      "name": "Hailuo Live2D",
      "description": "Good for 2D animation",
      "descriptionKey": "videoModels.video-01-live.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "icon": ICON_PATHS.hailuo,
      "generationTimeSeconds": 180,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": false,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video"],
      "pricingConfig": {
        "baseCredits": 35,
        "multipliers": {}
      }
    },
    {
      "code": "runway-gen4-turbo",
      "value": "runway-gen4-turbo",
      "name": "Runway Gen-4 Turbo",
      "description": "Efficient, consistent video creation",
      "descriptionKey": "videoModels.runway-gen4-turbo.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "icon": ICON_PATHS.runway,
      "generationTimeSeconds": 180,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video"],
      "videoLengthOptions": [5, 10],
      "aspectRatioOptions": ["16:9", "9:16", "1:1", "4:3", "3:4", "21:9"],
      "pricingConfig": {
        "baseCredits": 40,
        "multipliers": {
          "videoLength": {
            "5": 1.0,
            "10": 2.0
          }
        }
      }
    },
    {
      "code": "runway-gen3-alphaturbo",
      "value": "runway-gen3-alphaturbo",
      "name": "Runway Gen-3 Turbo",
      "description": "Multimodal, professional model",
      "descriptionKey": "videoModels.runway-gen3-alphaturbo.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "icon": ICON_PATHS.runway,
      "generationTimeSeconds": 60,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": true,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video"],
      "videoLengthOptions": [5, 10],
      "aspectRatioOptions": ["5:3", "3:5"],
      "pricingConfig": {
        "baseCredits": 40,
        "multipliers": {
          "videoLength": {
            "5": 1.0,
            "10": 2.0
          }
        }
      }
    },
    {
      "code": "pixverse-v4.5",
      "value": "pixverse-v4.5",
      "name": "Pixverse V4.5",
      "description": "Enhanced realism and camera motions",
      "descriptionKey": "videoModels.pixverse-v4-5.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": true,
      "icon": ICON_PATHS.pixverse,
      "generationTimeSeconds": 120,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": true,
      "supportsNegativePrompt": true,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "modes": [
        {
          "code": "normal",
          "name": "Normal",
          "nameKey": "videoModels.pixverse-v4-5.modes.normal.name",
          "description": "Fast AI video generation speed",
          "descriptionKey": "videoModels.pixverse-v4-5.modes.normal.description"
        },
        {
          "code": "smooth",
          "name": "Smooth",
          "nameKey": "videoModels.pixverse-v4-5.modes.smooth.name",
          "description": "Higher motion range but with longer generation time",
          "descriptionKey": "videoModels.pixverse-v4-5.modes.smooth.description"
        }
      ],
      "videoLengthOptions": [5, 8],
      "resolutionOptions": ["360P", "540P", "720P", "1080P"],
      "styleOptions": [
        {
          "code": "auto",
          "name": "Auto",
          "nameKey": "videoModels.pixverse-v4-5.styles.auto.name"
        },
        {
          "code": "anime",
          "name": "Anime",
          "nameKey": "videoModels.pixverse-v4-5.styles.anime.name"
        },
        {
          "code": "3d_animation",
          "name": "3D Animation",
          "nameKey": "videoModels.pixverse-v4-5.styles.3d_animation.name"
        },
        {
          "code": "comic",
          "name": "Comic",
          "nameKey": "videoModels.pixverse-v4-5.styles.comic.name"
        },
        {
          "code": "clay",
          "name": "Clay",
          "nameKey": "videoModels.pixverse-v4-5.styles.clay.name"
        },
        {
          "code": "cyberpunk",
          "name": "Cyberpunk",
          "nameKey": "videoModels.pixverse-v4-5.styles.cyberpunk.name"
        }
      ],
      "pricingConfig": {
        "baseCredits": 10,
        "multipliers": {
          "mode": {
            "normal": 1.0,
            "smooth": 2.0
          },
          "videoLength": {
            "5": 1.0,
            "8": 2.0
          },
          "resolution": {
            "360P": 1.0,
            "540P": 1.0,
            "720P": 1.5,
            "1080P": 3.0
          }
        }
      }
    },
    {
      "code": "pixverse-v4",
      "value": "pixverse-v4",
      "name": "Pixverse V4",
      "description": "Improved motion and coherence",
      "descriptionKey": "videoModels.pixverse-v4.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "icon": ICON_PATHS.pixverse,
      "generationTimeSeconds": 60,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": true,
      "supportsNegativePrompt": true,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "modes": [
        {
          "code": "normal",
          "name": "Normal",
          "nameKey": "videoModels.pixverse-v4.modes.normal.name",
          "description": "Fast AI video generation speed",
          "descriptionKey": "videoModels.pixverse-v4.modes.normal.description"
        },
        {
          "code": "smooth",
          "name": "Smooth",
          "nameKey": "videoModels.pixverse-v4.modes.smooth.name",
          "description": "Higher motion range but with longer generation time",
          "descriptionKey": "videoModels.pixverse-v4.modes.smooth.description"
        }
      ],
      "videoLengthOptions": [5, 8],
      "resolutionOptions": ["360P", "540P", "720P", "1080P"],
      "styleOptions": [
        {
          "code": "auto",
          "name": "Auto",
          "nameKey": "videoModels.pixverse-v4.styles.auto.name"
        },
        {
          "code": "anime",
          "name": "Anime",
          "nameKey": "videoModels.pixverse-v4.styles.anime.name"
        },
        {
          "code": "3d_animation",
          "name": "3D Animation",
          "nameKey": "videoModels.pixverse-v4.styles.3d_animation.name"
        },
        {
          "code": "comic",
          "name": "Comic",
          "nameKey": "videoModels.pixverse-v4.styles.comic.name"
        },
        {
          "code": "clay",
          "name": "Clay",
          "nameKey": "videoModels.pixverse-v4.styles.clay.name"
        },
        {
          "code": "cyberpunk",
          "name": "Cyberpunk",
          "nameKey": "videoModels.pixverse-v4.styles.cyberpunk.name"
        }
      ],
      "pricingConfig": {
        "baseCredits": 10,
        "multipliers": {
          "mode": {
            "normal": 1.0,
            "smooth": 2.0
          },
          "videoLength": {
            "5": 1.0,
            "8": 2.0
          },
          "resolution": {
            "360P": 1.0,
            "540P": 1.0,
            "720P": 1.5,
            "1080P": 3.0
          }
        }
      }
    },
    {
      "code": "pixverse-v3.5",
      "value": "pixverse-v3.5",
      "name": "Pixverse V3.5",
      "description": "Improved motion and coherence",
      "descriptionKey": "videoModels.pixverse-v3-5.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "icon": ICON_PATHS.pixverse,
      "generationTimeSeconds": 120,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": true,
      "supportsNegativePrompt": true,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "modes": [
        {
          "code": "normal",
          "name": "Normal",
          "nameKey": "videoModels.pixverse-v3-5.modes.normal.name",
          "description": "Fast AI video generation speed",
          "descriptionKey": "videoModels.pixverse-v3-5.modes.normal.description"
        },
        {
          "code": "smooth",
          "name": "Smooth",
          "nameKey": "videoModels.pixverse-v3-5.modes.smooth.name",
          "description": "Higher motion range but with longer generation time",
          "descriptionKey": "videoModels.pixverse-v3-5.modes.smooth.description"
        }
      ],
      "videoLengthOptions": [5, 8],
      "resolutionOptions": ["360P", "540P", "720P", "1080P"],
      "styleOptions": [
        {
          "code": "auto",
          "name": "Auto",
          "nameKey": "videoModels.pixverse-v3-5.styles.auto.name"
        },
        {
          "code": "anime",
          "name": "Anime",
          "nameKey": "videoModels.pixverse-v3-5.styles.anime.name"
        },
        {
          "code": "3d_animation",
          "name": "3D Animation",
          "nameKey": "videoModels.pixverse-v3-5.styles.3d_animation.name"
        },
        {
          "code": "comic",
          "name": "Comic",
          "nameKey": "videoModels.pixverse-v3-5.styles.comic.name"
        },
        {
          "code": "clay",
          "name": "Clay",
          "nameKey": "videoModels.pixverse-v3-5.styles.clay.name"
        },
        {
          "code": "cyberpunk",
          "name": "Cyberpunk",
          "nameKey": "videoModels.pixverse-v3-5.styles.cyberpunk.name"
        }
      ],
      "pricingConfig": {
        "baseCredits": 10,
        "multipliers": {
          "mode": {
            "normal": 1.0,
            "smooth": 2.0
          },
          "videoLength": {
            "5": 1.0,
            "8": 2.0
          },
          "resolution": {
            "360P": 1.0,
            "540P": 1.0,
            "720P": 1.5,
            "1080P": 3.0
          }
        }
      }
    },
    {
      "code": "veo-2",
      "value": "veo-2",
      "name": "Google Veo 2",
      "description": "HD outputs with visually rich content",
      "descriptionKey": "videoModels.veo-2.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": true,
      "icon": ICON_PATHS.google,
      "generationTimeSeconds": 300,
      "fps": 24,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "videoLengthOptions": [5, 8],
      "resolutionOptions": ["720P"],      
      "aspectRatioOptions": ["16:9", "9:16"],
      "pricingConfig": {
        "baseCredits": 180,
        "multipliers": {
          "videoLength": {
            "5": 1.0,
            "8": 2.0
          },
          "resolution": {
            "720P": 1.0
          }
        }
      }
    },
    {
      "code": "pika-v2.2",
      "value": "pika-v2.2",
      "name": "Pika 2.2",
      "description": "Better transition and transformation",
      "descriptionKey": "videoModels.pika-v2-2.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": true,
      "icon": ICON_PATHS.pika,
      "generationTimeSeconds": 100,
      "fps": 24,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": true,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "videoLengthOptions": [5, 10],
      "resolutionOptions": ["720P", "1080P"],
      "pricingConfig": {
        "baseCredits": 30,
        "multipliers": {
          "videoLength": {
            "5": 1.0,
            "10": 3.0
          },
          "resolution": {
            "720P": 1.0,
            "1080P": 2.5
          }
        }
      }
    },
    {
      "code": "pika-v2.1",
      "value": "pika-v2.1",
      "name": "Pika 2.1",
      "description": "Crystal-clear and immersive outputs",
      "descriptionKey": "videoModels.pika-v2-1.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "icon": ICON_PATHS.pika,
      "generationTimeSeconds": 100,
      "fps": 24,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": true,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "resolutionOptions": ["1080P"],      
      "pricingConfig": {
        "baseCredits": 60,
        "multipliers": {
          "videoLength": {
            "5": 1.0,
          },
          "resolution": {
            "1080P": 1.0
          }
        }
      }
    },
    {
      "code": "vidu-q1",
      "value": "vidu-q1",
      "name": "Vidu Q1",
      "description": "Enhanced quality and speed",
      "descriptionKey": "videoModels.vidu-q1.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "icon": ICON_PATHS.vidu,
      "generationTimeSeconds": 240,
      "fps": 24,
      "supportsPrompt": true,
      "supportsEndFrame": true,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "resolutionOptions": ["1080P"],
      "motionRangeOptions": [
        {
          "code": "auto",
          "name": "Auto",
          "nameKey": "videoModels.vidu-q1.motionRanges.auto.name"
        },
        {
          "code": "small",
          "name": "Small",
          "nameKey": "videoModels.vidu-q1.motionRanges.small.name"
        },
        {
          "code": "medium",
          "name": "Medium",
          "nameKey": "videoModels.vidu-q1.motionRanges.medium.name"
        },
        {
          "code": "large",
          "name": "Large",
          "nameKey": "videoModels.vidu-q1.motionRanges.large.name"
        }
      ],
      "pricingConfig": {
        "baseCredits": 25,
        "multipliers": {
          "videoLength": {
            "5": 1.0,
          },
          "resolution": {
            "1080P": 1.0
          }
        }
      }
    },
    {
      "code": "vidu-2.0",
      "value": "vidu-2.0",
      "name": "Vidu 2.0",
      "description": "Enhanced quality and speed",
      "descriptionKey": "videoModels.vidu-2-0.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "icon": ICON_PATHS.vidu,
      "generationTimeSeconds": 60,
      "fps": 32,
      "supportsPrompt": true,
      "supportsEndFrame": true,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video"],
      "resolutionOptions": ["720P", "1080P"],      
      "videoLengthOptions": [4, 8],
      "pricingConfig": {
        "baseCredits": 10,
        "multipliers": {
          "videoLength": {
            "4": 1.0,
            "8": 2.5
          },
          "resolution": {
            "720P": 1.0,
            "1080P": 2.5
          }
        }
      }
    },
    {
      "code": "ray-2",
      "value": "ray-2",
      "name": "Luma Ray 2",
      "description": "Large scale model for realistic visuals",
      "descriptionKey": "videoModels.ray-2.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "icon": ICON_PATHS.luma,
      "generationTimeSeconds": 180,
      "fps": 24,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": false,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "resolutionOptions": ["540P", "720P", "1080P", "4K"],            
      "videoLengthOptions": [5, 9],
      "aspectRatioOptions": ["16:9", "9:16", "1:1", "4:3", "3:4", "21:9"],
      "pricingConfig": {
        "baseCredits": 60,
        "multipliers": {
          "videoLength": {
            "5": 1.0,
            "9": 1.7
          },
          "resolution": {
            "540P": 1.0,
            "720P": 1.7,
            "1080P": 2.0,
            "4K": 3.0
          }
        }
      }
    },
    {
      "code": "ray-1.6",
      "value": "ray-1.6",
      "name": "Luma Ray 1.6",
      "description": "Realistic and detailed videos",
      "descriptionKey": "videoModels.ray-1-6.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "icon": ICON_PATHS.luma,
      "generationTimeSeconds": 180,
      "fps": 24,
      "supportsPrompt": true,
      "supportsEndFrame": true,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": false,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "pricingConfig": {
        "baseCredits": 60,
        "multipliers": {}
      }
    },
    {
      "code": "wanx-2.1",
      "value": "wanx-2.1",
      "name": "Wanx 2.1",
      "description": "Alibaba's model with realistic outputs",
      "descriptionKey": "videoModels.wanx-2-1.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "icon": ICON_PATHS.wanx,
      "generationTimeSeconds": 240,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "modes": [
        {
          "code": "fst",
          "name": "Fast",
          "nameKey": "videoModels.wanx-2-1.modes.fst.name",
          "description": "Fast AI video generation speed",
          "descriptionKey": "videoModels.wanx-2-1.modes.fst.description"
        },
        {
          "code": "pro",
          "name": "Professional",
          "nameKey": "videoModels.wanx-2-1.modes.pro.name",
          "description": "Enhanced visual quality but with longer generation time",
          "descriptionKey": "videoModels.wanx-2-1.modes.pro.description"
        }
      ],
      "pricingConfig": {
        "baseCredits": 20,
        "multipliers": {
          "mode": {
            "fst": 1.0,
            "pro": 2.5
          },
          "videoLength": {
            "5": 1.0
          }
        }
      }
    },
    {
      "code": "hunyuan",
      "value": "hunyuan",
      "name": "Hunyuan",
      "description": "Tencent's 13B-parameter video model",
      "descriptionKey": "videoModels.hunyuan.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "icon": ICON_PATHS.hunyuan,
      "generationTimeSeconds": 720,
      "fps": 24,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": true,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "modes": [
        {
          "code": "fst",
          "name": "Fast",
          "nameKey": "videoModels.hunyuan.modes.fst.name",
          "description": "Fast AI video generation speed",
          "descriptionKey": "videoModels.hunyuan.modes.fst.description"
        },
        {
          "code": "pro",
          "name": "Professional",
          "nameKey": "videoModels.hunyuan.modes.pro.name",
          "description": "Higher video generation quality",
          "descriptionKey": "videoModels.hunyuan.modes.pro.description"
        }
      ],
      "pricingConfig": {
        "baseCredits": 20,
        "multipliers": {
          "mode": {
            "fst": 1.0,
            "pro": 2.0
          },
          "videoLength": {
            "5": 1.0
          }
        }
      }
    },
    {
      "code": "seedance-1.0-lite",
      "value": "seedance-1.0-lite",
      "name": "Seedance 1.0 Lite",
      "description": "Accurate motion and camera control",
      "descriptionKey": "videoModels.seedance.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "icon": ICON_PATHS.seedance,
      "generationTimeSeconds": 60,
      "fps": 24,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": true,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "videoLengthOptions": [5, 10],
      "resolutionOptions": ["480P", "720P", "1080P"],
      "pricingConfig": {
        "baseCredits": 5,
        "multipliers": {
          "videoLength": {
            "5": 1.0,
            "10": 2.0
          },
          "resolution": {
            "480P": 1.0,
            "720P": 2.0,
            "1080P": 5.0
          }
        }
      }
    },
    {
      "code": "seedance-1.0-pro",
      "value": "seedance-1.0-pro",
      "name": "Seedance 1.0 Pro",
      "description": "Fluid, cohesive multi-shot video outputs",
      "descriptionKey": "videoModels.seedance.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "icon": ICON_PATHS.seedance,
      "generationTimeSeconds": 60,
      "fps": 24,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": true,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "videoLengthOptions": [5, 10],
      "resolutionOptions": ["480P", "1080P"],
      "pricingConfig": {
        "baseCredits": 15,
        "multipliers": {
          "videoLength": {
            "5": 1.0,
            "10": 2.0
          },
          "resolution": {
            "480P": 1.0,
            "1080P": 4.0
          }
        }
      }
    }    
  ]
};

export * from "./types";
export type { VideoModelConfig };
          