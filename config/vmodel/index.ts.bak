import type { VideoModelConfig } from "./types";

export const videoModelConfig: VideoModelConfig = {
  "models": [
    {
      "code": "kling-v2-0",
      "value": "kling-v2.0",
      "name": "Kling 2.0",
      "description": "Better motion dynamics and aesthetics",
      "descriptionKey": "videoModels.kling-v2-0.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": true,
      "generationTimeSeconds": 480,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": true,
      "supportsPromptStrength": true,
      "supportsSeed": false,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "videoLengthOptions": [5, 10]
    },
    {
      "code": "kling-v1-6",
      "value": "kling-v1.6",
      "name": "Kling 1.6",
      "description": "More realistic motions",
      "descriptionKey": "videoModels.kling-v1-6.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": true,
      "generationTimeSeconds": 240,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": true,
      "supportsNegativePrompt": true,
      "supportsPromptStrength": true,
      "supportsSeed": false,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "modes": [
        {
          "code": "std",
          "name": "Standard Mode",
          "nameKey": "videoModels.kling-v1-6.modes.std.name",
          "description": "Fast AI video generation speed",
          "descriptionKey": "videoModels.kling-v1-6.modes.std.description"
        },
        {
          "code": "pro",
          "name": "Professional Mode",
          "nameKey": "videoModels.kling-v1-6.modes.pro.name",
          "description": "Higher video quality but with longer generation time",
          "descriptionKey": "videoModels.kling-v1-6.modes.pro.description"
        }
      ],
      "videoLengthOptions": [5, 10]
    },
    {
      "code": "kling-v1-5",
      "value": "kling-v1.5",
      "name": "Kling 1.5",
      "description": "Suitable for complex scenes",
      "descriptionKey": "videoModels.kling-v1-5.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": true,
      "generationTimeSeconds": 360,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": true,
      "supportsNegativePrompt": true,
      "supportsPromptStrength": true,
      "supportsSeed": false,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "modes": [
        {
          "code": "std",
          "name": "Standard Mode",
          "nameKey": "videoModels.kling-v1-5.modes.std.name",
          "description": "Fast AI video generation speed",
          "descriptionKey": "videoModels.kling-v1-5.modes.std.description"
        },
        {
          "code": "pro",
          "name": "Professional Mode",
          "nameKey": "videoModels.kling-v1-5.modes.pro.name",
          "description": "Higher video quality but with longer generation time",
          "descriptionKey": "videoModels.kling-v1-5.modes.pro.description"
        }
      ],
      "videoLengthOptions": [5, 10]
    },
    {
      "code": "kling-v1-0",
      "value": "kling-v1.0",
      "name": "Kling 1",
      "description": "Suitable for short videos",
      "descriptionKey": "videoModels.kling-v1-0.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "generationTimeSeconds": 360,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": true,
      "supportsNegativePrompt": true,
      "supportsPromptStrength": true,
      "supportsSeed": false,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "modes": [
        {
          "code": "std",
          "name": "Standard Mode",
          "nameKey": "videoModels.kling-v1-0.modes.std.name",
          "description": "Fast AI video generation speed",
          "descriptionKey": "videoModels.kling-v1-0.modes.std.description"
        },
        {
          "code": "pro",
          "name": "Professional Mode",
          "nameKey": "videoModels.kling-v1-0.modes.pro.name",
          "description": "Higher video quality but with longer generation time",
          "descriptionKey": "videoModels.kling-v1-0.modes.pro.description"
        }
      ],
      "videoLengthOptions": [5, 10]
    },
    {
      "code": "video-01",
      "value": "video-01",
      "name": "Hailuo",
      "description": "Highest video quality",
      "descriptionKey": "videoModels.video-01.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "generationTimeSeconds": 180,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": false,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"]
    },
    {
      "code": "video-01-live",
      "value": "video-01-live",
      "name": "Hailuo Live2D",
      "description": "Good for 2D animation",
      "descriptionKey": "videoModels.video-01-live.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "generationTimeSeconds": 180,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": false,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video"]
    },
    {
      "code": "runway-gen4-turbo",
      "value": "runway-gen4-turbo",
      "name": "Runway Gen-4 Turbo",
      "description": "Efficient, consistent video creation",
      "descriptionKey": "videoModels.runway-gen4-turbo.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "generationTimeSeconds": 180,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video"],
      "videoLengthOptions": [5, 10],
      "aspectRatioOptions": ["16:9", "9:16", "1:1", "4:3", "3:4", "21:9"]
    },
    {
      "code": "runway-gen3-alphaturbo",
      "value": "runway-gen3-alphaturbo",
      "name": "Runway Gen-3 Turbo",
      "description": "Multimodal, professional model",
      "descriptionKey": "videoModels.runway-gen3-alphaturbo.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "generationTimeSeconds": 60,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": true,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video"],
      "videoLengthOptions": [5, 10],
      "aspectRatioOptions": ["5:3", "3:5"]
    },
    {
      "code": "pixverse-v4-5",
      "value": "pixverse-v4.5",
      "name": "Pixverse V4.5",
      "description": "Enhanced realism and camera motions",
      "descriptionKey": "videoModels.pixverse-v4-5.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "generationTimeSeconds": 120,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": true,
      "supportsNegativePrompt": true,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "modes": [
        {
          "code": "normal",
          "name": "Normal",
          "nameKey": "videoModels.pixverse-v4-5.modes.normal.name",
          "description": "Fast AI video generation speed",
          "descriptionKey": "videoModels.pixverse-v4-5.modes.normal.description"
        },
        {
          "code": "smooth",
          "name": "Smooth",
          "nameKey": "videoModels.pixverse-v4-5.modes.smooth.name",
          "description": "Higher motion range but with longer generation time",
          "descriptionKey": "videoModels.pixverse-v4-5.modes.smooth.description"
        }
      ],
      "videoLengthOptions": [5, 8],
      "resolutionOptions": ["360P", "540P", "720P", "1080P"],
      "styleOptions": [
        {
          "code": "auto",
          "name": "Auto",
          "nameKey": "videoModels.pixverse-v4-5.styles.auto.name"
        },
        {
          "code": "anime",
          "name": "Anime",
          "nameKey": "videoModels.pixverse-v4-5.styles.anime.name"
        },
        {
          "code": "3d_animation",
          "name": "3D Animation",
          "nameKey": "videoModels.pixverse-v4-5.styles.3d_animation.name"
        },
        {
          "code": "comic",
          "name": "Comic",
          "nameKey": "videoModels.pixverse-v4-5.styles.comic.name"
        },
        {
          "code": "clay",
          "name": "Clay",
          "nameKey": "videoModels.pixverse-v4-5.styles.clay.name"
        },
        {
          "code": "cyberpunk",
          "name": "Cyberpunk",
          "nameKey": "videoModels.pixverse-v4-5.styles.cyberpunk.name"
        }
      ]
    },
    {
      "code": "pixverse-v4",
      "value": "pixverse-v4",
      "name": "Pixverse V4",
      "description": "Improved motion and coherence",
      "descriptionKey": "videoModels.pixverse-v4.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "generationTimeSeconds": 60,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": true,
      "supportsNegativePrompt": true,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "modes": [
        {
          "code": "normal",
          "name": "Normal",
          "nameKey": "videoModels.pixverse-v4.modes.normal.name",
          "description": "Fast AI video generation speed",
          "descriptionKey": "videoModels.pixverse-v4.modes.normal.description"
        },
        {
          "code": "smooth",
          "name": "Smooth",
          "nameKey": "videoModels.pixverse-v4.modes.smooth.name",
          "description": "Higher motion range but with longer generation time",
          "descriptionKey": "videoModels.pixverse-v4.modes.smooth.description"
        }
      ],
      "videoLengthOptions": [5, 8],
      "resolutionOptions": ["360P", "540P", "720P", "1080P"],
      "styleOptions": [
        {
          "code": "auto",
          "name": "Auto",
          "nameKey": "videoModels.pixverse-v4.styles.auto.name"
        },
        {
          "code": "anime",
          "name": "Anime",
          "nameKey": "videoModels.pixverse-v4.styles.anime.name"
        },
        {
          "code": "3d_animation",
          "name": "3D Animation",
          "nameKey": "videoModels.pixverse-v4.styles.3d_animation.name"
        },
        {
          "code": "comic",
          "name": "Comic",
          "nameKey": "videoModels.pixverse-v4.styles.comic.name"
        },
        {
          "code": "clay",
          "name": "Clay",
          "nameKey": "videoModels.pixverse-v4.styles.clay.name"
        },
        {
          "code": "cyberpunk",
          "name": "Cyberpunk",
          "nameKey": "videoModels.pixverse-v4.styles.cyberpunk.name"
        }
      ]
    },
    {
      "code": "pixverse-v3-5",
      "value": "pixverse-v3.5",
      "name": "Pixverse V3.5",
      "description": "Improved motion and coherence",
      "descriptionKey": "videoModels.pixverse-v3-5.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "generationTimeSeconds": 120,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": true,
      "supportsNegativePrompt": true,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "modes": [
        {
          "code": "normal",
          "name": "Normal",
          "nameKey": "videoModels.pixverse-v3-5.modes.normal.name",
          "description": "Fast AI video generation speed",
          "descriptionKey": "videoModels.pixverse-v3-5.modes.normal.description"
        },
        {
          "code": "smooth",
          "name": "Smooth",
          "nameKey": "videoModels.pixverse-v3-5.modes.smooth.name",
          "description": "Higher motion range but with longer generation time",
          "descriptionKey": "videoModels.pixverse-v3-5.modes.smooth.description"
        }
      ],
      "videoLengthOptions": [5, 8],
      "resolutionOptions": ["360P", "540P", "720P", "1080P"],
      "styleOptions": [
        {
          "code": "auto",
          "name": "Auto",
          "nameKey": "videoModels.pixverse-v3-5.styles.auto.name"
        },
        {
          "code": "anime",
          "name": "Anime",
          "nameKey": "videoModels.pixverse-v3-5.styles.anime.name"
        },
        {
          "code": "3d_animation",
          "name": "3D Animation",
          "nameKey": "videoModels.pixverse-v3-5.styles.3d_animation.name"
        },
        {
          "code": "comic",
          "name": "Comic",
          "nameKey": "videoModels.pixverse-v3-5.styles.comic.name"
        },
        {
          "code": "clay",
          "name": "Clay",
          "nameKey": "videoModels.pixverse-v3-5.styles.clay.name"
        },
        {
          "code": "cyberpunk",
          "name": "Cyberpunk",
          "nameKey": "videoModels.pixverse-v3-5.styles.cyberpunk.name"
        }
      ]
    },
    {
      "code": "veo-2",
      "value": "veo-2",
      "name": "Google Veo 2",
      "description": "HD outputs with visually rich content",
      "descriptionKey": "videoModels.veo-2.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "generationTimeSeconds": 300,
      "fps": 24,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "videoLengthOptions": [5, 8],
      "aspectRatioOptions": ["16:9", "9:16"]
    },
    {
      "code": "pika-v2-2",
      "value": "pika-v2.2",
      "name": "Pika 2.2",
      "description": "Better transition and transformation",
      "descriptionKey": "videoModels.pika-v2-2.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "generationTimeSeconds": 100,
      "fps": 24,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": true,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "videoLengthOptions": [5, 10],
      "resolutionOptions": ["720P", "1080P"]
    },
    {
      "code": "pika-v2-1",
      "value": "pika-v2.1",
      "name": "Pika 2.1",
      "description": "Crystal-clear and immersive outputs",
      "descriptionKey": "videoModels.pika-v2-1.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "generationTimeSeconds": 100,
      "fps": 24,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": true,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"]
    },
    {
      "code": "vidu-q1",
      "value": "vidu-q1",
      "name": "Vidu Q1",
      "description": "Enhanced quality and speed",
      "descriptionKey": "videoModels.vidu-q1.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "generationTimeSeconds": 240,
      "fps": 24,
      "supportsPrompt": true,
      "supportsEndFrame": true,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "motionRangeOptions": [
        {
          "code": "auto",
          "name": "Auto",
          "nameKey": "videoModels.vidu-q1.motionRanges.auto.name"
        },
        {
          "code": "small",
          "name": "Small",
          "nameKey": "videoModels.vidu-q1.motionRanges.small.name"
        },
        {
          "code": "medium",
          "name": "Medium",
          "nameKey": "videoModels.vidu-q1.motionRanges.medium.name"
        },
        {
          "code": "large",
          "name": "Large",
          "nameKey": "videoModels.vidu-q1.motionRanges.large.name"
        }
      ]
    },
    {
      "code": "vidu-2-0",
      "value": "vidu-2.0",
      "name": "Vidu 2.0",
      "description": "Enhanced quality and speed",
      "descriptionKey": "videoModels.vidu-2-0.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "generationTimeSeconds": 60,
      "fps": 32,
      "supportsPrompt": true,
      "supportsEndFrame": true,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video"],
      "videoLengthOptions": [4, 8]
    },
    {
      "code": "ray-2",
      "value": "ray-2",
      "name": "Luma Ray 2",
      "description": "Large scale model for realistic visuals",
      "descriptionKey": "videoModels.ray-2.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "generationTimeSeconds": 180,
      "fps": 24,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": false,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "videoLengthOptions": [5, 9],
      "aspectRatioOptions": ["16:9", "9:16", "1:1", "4:3", "3:4", "21:9"]
    },
    {
      "code": "ray-1-6",
      "value": "ray-1.6",
      "name": "Luma Ray 1.6",
      "description": "Realistic and detailed videos",
      "descriptionKey": "videoModels.ray-1-6.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "generationTimeSeconds": 180,
      "fps": 24,
      "supportsPrompt": true,
      "supportsEndFrame": true,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": false,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"]
    },
    {
      "code": "wanx-2-1",
      "value": "wanx-2.1",
      "name": "Wanx 2.1",
      "description": "Alibaba's model with realistic outputs",
      "descriptionKey": "videoModels.wanx-2-1.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "generationTimeSeconds": 240,
      "fps": 30,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "modes": [
        {
          "code": "fst",
          "name": "Fast",
          "nameKey": "videoModels.wanx-2-1.modes.fst.name",
          "description": "Fast AI video generation speed",
          "descriptionKey": "videoModels.wanx-2-1.modes.fst.description"
        },
        {
          "code": "pro",
          "name": "Professional",
          "nameKey": "videoModels.wanx-2-1.modes.pro.name",
          "description": "Enhanced visual quality but with longer generation time",
          "descriptionKey": "videoModels.wanx-2-1.modes.pro.description"
        }
      ]
    },
    {
      "code": "hunyuan",
      "value": "hunyuan",
      "name": "Hunyuan",
      "description": "Tencent's 13B-parameter video model",
      "descriptionKey": "videoModels.hunyuan.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "generationTimeSeconds": 720,
      "fps": 24,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": true,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "modes": [
        {
          "code": "fst",
          "name": "Fast",
          "nameKey": "videoModels.hunyuan.modes.fst.name",
          "description": "Fast AI video generation speed",
          "descriptionKey": "videoModels.hunyuan.modes.fst.description"
        },
        {
          "code": "pro",
          "name": "Professional",
          "nameKey": "videoModels.hunyuan.modes.pro.name",
          "description": "Higher video generation quality",
          "descriptionKey": "videoModels.hunyuan.modes.pro.description"
        }
      ]
    },
    {
      "code": "seedance",
      "value": "seedance",
      "name": "Seedance",
      "description": "Accurate motion and camera control",
      "descriptionKey": "videoModels.seedance.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "generationTimeSeconds": 60,
      "fps": 24,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": true,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "videoLengthOptions": [5, 10],
      "resolutionOptions": ["480P", "720P"]
    },
    {
      "code": "seaweed",
      "value": "seaweed",
      "name": "Seaweed",
      "description": "ByteDance's model for dynamic videos",
      "descriptionKey": "videoModels.seaweed.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "generationTimeSeconds": 120,
      "fps": 24,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": false,
      "supportsFixCamera": false,
      "supportedFeatures": ["image-to-video", "text-to-video"],
      "videoLengthOptions": [5, 10],
      "aspectRatioOptions": ["16:9", "9:16", "1:1", "4:3", "3:4", "21:9"]
    },
    {
      "code": "vidu-1-5",
      "value": "vidu-1.5",
      "name": "Vidu 1.5",
      "description": "Fast video generation",
      "descriptionKey": "videoModels.vidu-1-5.description",
      "modelType": "video",
      "apiProviderCode": "fal",
      "isActive": false,
      "generationTimeSeconds": 120,
      "fps": 16,
      "supportsPrompt": true,
      "supportsEndFrame": false,
      "supportsNegativePrompt": false,
      "supportsPromptStrength": false,
      "supportsSeed": true,
      "supportsFixCamera": false,
      "supportedFeatures": ["text-to-video"],
      "videoLengthOptions": [4, 8],
      "aspectRatioOptions": ["16:9", "9:16", "1:1"],
      "resolutionOptions": ["360P", "720P", "1080P"]
    }
  ]
};

export * from "./types";
export type { VideoModelConfig };

