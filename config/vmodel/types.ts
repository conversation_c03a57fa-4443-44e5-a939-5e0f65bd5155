/**
 * 模型的工作模式
 */
export interface ModelMode {
	/** 模式代码 */
	code: string;
	/** 模式显示名称 */
	name: string;
	/** 模式名称的国际化键 */
	nameKey?: string;
	/** 模式描述 */
	description: string;
	/** 模式描述的国际化键 */
	descriptionKey?: string;
	/** 是否为默认模式 */
	isDefault?: boolean;
	/** 是否支持结束帧 */
	supportsEndFrame?: boolean;
}

/**
 * 运动范围配置
 */
export interface MotionRangeConfig {
	/** 运动范围代码 */
	code: string;
	/** 运动范围显示名称 */
	name: string;
	/** 运动范围名称的国际化键 */
	nameKey?: string;
}

/**
 * 风格配置
 */
export interface StyleConfig {
	/** 风格代码 */
	code: string;
	/** 风格显示名称 */
	name: string;
	/** 风格名称的国际化键 */
	nameKey?: string;
}

/**
 * 价格配置
 */
export interface PricingConfig {
	/** 基础积分 */
	baseCredits: number;
	/** 乘数配置 */
	multipliers: {
		/** 视频长度乘数 */
		videoLength?: Record<string, number>;
		/** 模式乘数 */
		mode?: Record<string, number>;
		/** 分辨率乘数 */
		resolution?: Record<string, number>;
		/** 运动范围乘数 */
		motionRange?: Record<string, number>;
		/** 风格乘数 */
		style?: Record<string, number>;
	};
}

/**
 * 视频宽高比类型
 */
export type AspectRatio =
	| "16:9"
	| "9:16"
	| "1:1"
	| "4:3"
	| "3:4"
	| "21:9"
	| "5:3"
	| "3:5";

/**
 * 视频分辨率类型
 */
export type Resolution = "360P" | "480P" | "540P" | "720P" | "1080P" | "4K";

/**
 * 运动范围类型（保留用于向后兼容）
 */
export type MotionRange = "Auto" | "Small" | "Medium" | "Large";

/**
 * 视频风格类型（保留用于向后兼容）
 */
export type VideoStyle =
	| "realistic"
	| "anime"
	| "3d_animation"
	| "clay"
	| "cyberpunk"
	| "comic"
	| "watercolor";

/**
 * 支持的功能类型
 */
export type SupportedFeature = "image-to-video" | "text-to-video";

/**
 * 视频模型定义
 */
export interface VideoModel {
	// 必需字段
	/** 模型代码，用于关联其他表 */
	code: string;
	/** 模型值 */
	value: string;
	/** 模型显示名称 */
	name: string;
	/** 模型描述 */
	description: string;
	/** 模型描述的国际化键 */
	descriptionKey?: string;
	/** 模型类型 */
	modelType?: string;
	/** API提供商代码 */
	apiProviderCode?: string;
	/** 是否激活显示 */
	isActive: boolean;
	/** 视频生成所需时间（秒） */
	generationTimeSeconds: number;
	/** 视频帧率 */
	fps: number;
	/** 是否支持提示词 */
	supportsPrompt: boolean;
	/** 是否支持结束帧 */
	supportsEndFrame: boolean;
	/** 是否支持负面提示词 */
	supportsNegativePrompt: boolean;
	/** 是否支持提示词强度 */
	supportsPromptStrength: boolean;
	/** 是否支持种子 */
	supportsSeed: boolean;
	/** 是否支持固定相机 */
	supportsFixCamera: boolean;
	/** 支持的功能类型 */
	supportedFeatures: SupportedFeature[];

	// 可选字段
	/** 模型图标URL */
	icon?: string;
	/** 支持的模式列表 */
	modes?: ModelMode[];
	/** 支持的视频长度选项（秒） */
	videoLengthOptions?: number[];
	/** 支持的宽高比选项 */
	aspectRatioOptions?: AspectRatio[];
	/** 支持的分辨率选项 */
	resolutionOptions?: Resolution[];
	/** 支持的运动范围选项 */
	motionRangeOptions?: MotionRangeConfig[];
	/** 支持的风格选项 */
	styleOptions?: StyleConfig[];
	/** 批次选项（视频模型通常不支持批量生成） */
	batchOptions?: number[];
	/** 价格配置 */
	pricingConfig?: PricingConfig;
}

/**
 * 默认设置配置
 */
export interface DefaultSettings {
	// 必需字段
	/** 选择的模型ID */
	modelId: string;
	/** 视频帧率 */
	fps: number;
	/** 视频长度（秒） */
	videoLength: number;

	// 可选字段
	/** 选择的模式ID */
	modeId?: string;
	/** 视频宽高比 */
	aspectRatio?: AspectRatio;
	/** 视频分辨率 */
	resolution?: Resolution;
	/** 运动范围 */
	motionRange?: string;
	/** 视频风格 */
	style?: string;
	/** 是否使用提示词 */
	usePrompt?: boolean;
	/** 是否使用种子 */
	useSeed?: boolean;
	/** 种子值 */
	seed?: number;
	/** 是否使用负面提示词 */
	useNegativePrompt?: boolean;
	/** 是否使用提示词强度 */
	usePromptStrength?: boolean;
	/** 提示词强度 */
	promptStrength?: number;
}

/**
 * 完整的视频模型配置
 */
export interface VideoModelConfig {
	/** 视频模型列表 */
	models: VideoModel[];
	/** 默认设置 - 可选，因为现在我们已经使用isDefault标志 */
	defaultSettings?: DefaultSettings;
}
