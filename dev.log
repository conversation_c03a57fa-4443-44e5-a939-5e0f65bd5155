
> supapi@ dev /home/<USER>/app/video-generator/video-generator-web
> dotenv -c -- turbo dev --filter='@repo/web' --concurrency 15

turbo 2.5.4

• Packages in scope: @repo/web
• Running dev in 1 packages
• Remote caching disabled
@repo/database:generate: cache bypass, force executing 4ce8dd9acce96b0d
@repo/database:generate: 
@repo/database:generate: > @repo/database@0.0.0 generate /home/<USER>/app/video-generator/video-generator-web/packages/database
@repo/database:generate: > prisma generate --no-hints --schema=./prisma/schema.prisma
@repo/database:generate: 
@repo/database:generate: Prisma schema loaded from prisma/schema.prisma
@repo/database:generate: ┌─────────────────────────────────────────────────────────┐
@repo/database:generate: │  Update available 6.11.1 -> 6.12.0                      │
@repo/database:generate: │  Run the following to update                            │
@repo/database:generate: │    npm i --save-dev prisma@latest                       │
@repo/database:generate: │    npm i @prisma/client@latest                          │
@repo/database:generate: └─────────────────────────────────────────────────────────┘
@repo/database:generate: 
@repo/database:generate: ✔ Generated Prisma Client (v6.11.1) to ./prisma/generated/client in 102ms
@repo/database:generate: 
@repo/database:generate: ✔ Generated Zod Prisma Types to ./prisma/zod in 314ms
@repo/database:generate: 
@repo/database:generate: ✔ Generated Prisma Json Types Generator (3.5.1) to ./prisma in 267ms
@repo/database:generate: 
@repo/web:dev: cache bypass, force executing eb40acaea4029216
@repo/web:dev: 
@repo/web:dev: > @repo/web@0.0.0 dev /home/<USER>/app/video-generator/video-generator-web/apps/web
@repo/web:dev: > next dev --turbo
@repo/web:dev: 
@repo/web:dev: Starting content-collections content-collections.ts
@repo/web:dev: build started ...
@repo/web:dev: ... finished build of 4 collections and 17 documents in 34ms
@repo/web:dev: start watching ...
@repo/web:dev:    ▲ Next.js 15.3.5 (Turbopack)
@repo/web:dev:    - Local:        http://localhost:3000
@repo/web:dev:    - Network:      http://************:3000
@repo/web:dev: 
@repo/web:dev:  ✓ Starting...
@repo/web:dev:  ✓ Compiled middleware in 438ms
@repo/web:dev:  ✓ Ready in 1310ms
@repo/web:dev: ... config changed
@repo/web:dev: build started ...
@repo/web:dev: ... finished build of 4 collections and 17 documents in 30ms
@repo/web:dev:  ELIFECYCLE  Command failed.
  x Internal errors encountered: external process killed a task

 ELIFECYCLE  Command failed with exit code 1.
