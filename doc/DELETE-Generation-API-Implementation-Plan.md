# DELETE /api/generations/:generationId API 实现方案

## 概述

本文档详细描述了实现 `DELETE /api/generations/:generationId` API 的完整方案和实施步骤。该API将实现软删除功能，允许用户删除自己的视频生成内容，管理员可以删除任何内容。

## 技术架构

### 1. 架构层次
- **API层**: `/packages/api/src/routes/generations/router.ts` - Hono路由定义
- **服务层**: `/packages/api/src/routes/generations/lib/generation-service.ts` - 业务逻辑
- **数据层**: `/packages/jobs/src/lib/generations-manager.ts` - 数据库操作封装
- **类型层**: `/packages/api/src/routes/generations/types.ts` & `schemas.ts` - 类型定义

### 2. 删除策略
- **软删除**: 使用 `deleted: true` 标记，不物理删除数据
- **权限控制**: 用户只能删除自己的内容，管理员可删除任何内容  
- **状态检查**: 防止重复删除已删除的内容
- **关联处理**: 考虑与Job等关联数据的处理

## API 响应格式设计

采用更直观的响应格式，避免过度包装的数据结构：

### DELETE /api/generations/:generationId 响应格式

**成功响应 (200)**
```json
{
  "deleted": "gen_001"
}
```

**失败响应**
```json
// 权限错误 (403)
{
  "failed": {
    "gen_001": "Access denied"
  }
}

// 不存在 (404)
{
  "failed": {
    "gen_001": "Generation not found"
  }
}

// 已删除 (409)
{
  "failed": {
    "gen_001": "Generation already deleted"
  }
}

// 不能删除 (400)
{
  "failed": {
    "gen_001": "In use by job #xyz"
  }
}
```

### POST /api/generations/batch-delete 响应格式

**请求体**
```json
{
  "generationIds": ["gen_001", "gen_002", "gen_003"]
}
```

**全部成功响应 (200)**
```json
{
  "deleted": ["gen_001", "gen_002", "gen_003"]
}
```

**全部失败响应 (400/403/404/409)**
```json
{
  "error": "Some generations could not be deleted"
}

// 或者更具体的错误信息
{
  "error": "Access denied for some generations"
}

{
  "error": "Generations not found: gen_003, gen_004"
}

{
  "error": "Generation gen_001 already deleted"
}
```

### 响应格式优势

1. **更直观**: 直接看到成功/失败的ID列表
2. **原子性**: 批量删除使用事务，保证全部成功或全部失败
3. **简化处理**: 客户端无需处理复杂的部分成功情况
4. **错误明确**: 提供具体的错误信息，便于用户理解
5. **减少嵌套**: 避免过度包装的数据结构

## 详细实现方案

### 第一步：扩展 GenerationsManager 类

**文件**: `packages/jobs/src/lib/generations-manager.ts`

#### 1.1 添加软删除方法

```typescript
/**
 * 软删除Generation（标记为已删除）
 * 返回更直观的响应格式
 */
static async softDeleteGeneration(
  generationId: string,
  userId: string,
  isAdmin: boolean = false
): Promise<{
  deleted?: string;
  failed?: Record<string, string>;
}> {
  try {
    // 1. 检查Generation是否存在
    const generation = await db.generation.findUnique({
      where: { id: generationId },
      select: {
        id: true,
        userId: true,
        deleted: true,
        canDelete: true,
      }
    });

    if (!generation) {
      return {
        failed: {
          [generationId]: 'Generation not found'
        }
      };
    }

    // 2. 检查是否已被删除
    if (generation.deleted) {
      return {
        failed: {
          [generationId]: 'Generation already deleted'
        }
      };
    }

    // 3. 权限检查
    if (!isAdmin && generation.userId !== userId) {
      return {
        failed: {
          [generationId]: 'Access denied'
        }
      };
    }

    // 4. 检查是否允许删除
    if (!generation.canDelete) {
      return {
        failed: {
          [generationId]: 'Generation cannot be deleted'
        }
      };
    }

    // 5. 执行软删除
    const deletedGeneration = await db.generation.update({
      where: { id: generationId },
      data: {
        deleted: true,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        userId: true,
        deleted: true,
      }
    });

    logger.info('[GenerationsManager] Generation soft deleted:', {
      generationId,
      userId,
      isAdmin,
      deletedAt: new Date()
    });

    return {
      deleted: deletedGeneration.id
    };

  } catch (error) {
    logger.error('[GenerationsManager] Error soft deleting generation:', {
      generationId,
      userId,
      isAdmin,
      error: error instanceof Error ? error.message : String(error)
    });
    
    return {
      failed: {
        [generationId]: 'Internal server error'
      }
    };
  }
}
```

#### 1.2 添加批量删除方法

```typescript
/**
 * 批量软删除Generation - 事务版本
 * 全部成功或全部失败，保证原子性
 */
static async batchSoftDeleteGenerations(
  generationIds: string[],
  userId: string,
  isAdmin: boolean = false
): Promise<{
  deleted?: string[];
  error?: string;
}> {
  try {
    if (generationIds.length === 0) {
      return { error: 'No generation IDs provided' };
    }

    // 使用事务确保原子性
    const result = await db.$transaction(async (tx) => {
      // 1. 批量查询并验证
      const generations = await tx.generation.findMany({
        where: { id: { in: generationIds } },
        select: {
          id: true,
          userId: true,
          deleted: true,
          canDelete: true,
        }
      });

      // 2. 验证所有条件
      const foundIds = generations.map(g => g.id);
      const notFoundIds = generationIds.filter(id => !foundIds.includes(id));
      
      if (notFoundIds.length > 0) {
        throw new Error(`Generations not found: ${notFoundIds.join(', ')}`);
      }

      for (const gen of generations) {
        if (gen.deleted) {
          throw new Error(`Generation ${gen.id} already deleted`);
        }
        if (!isAdmin && gen.userId !== userId) {
          throw new Error(`Access denied for generation ${gen.id}`);
        }
        if (!gen.canDelete) {
          throw new Error(`Generation ${gen.id} cannot be deleted`);
        }
      }

      // 3. 全部验证通过，执行批量删除
      await tx.generation.updateMany({
        where: { id: { in: generationIds } },
        data: {
          deleted: true,
          updatedAt: new Date(),
        }
      });

      return generationIds;
    });

    logger.info('[GenerationsManager] Batch soft delete completed:', {
      generationIds,
      userId,
      isAdmin,
      deletedCount: result.length
    });

    return { deleted: result };

  } catch (error) {
    logger.error('[GenerationsManager] Error in batch soft delete:', {
      generationIds,
      userId,
      isAdmin,
      error: error instanceof Error ? error.message : String(error)
    });
    
    return { 
      error: error instanceof Error ? error.message : 'Internal server error' 
    };
  }
}
```

#### 1.3 添加恢复删除方法

```typescript
/**
 * 恢复已删除的Generation（管理员功能）
 */
static async restoreGeneration(
  generationId: string,
  adminUserId: string
): Promise<{
  success: true;
  restoredGeneration: {
    id: string;
    userId: string;
    deleted: boolean;
  };
} | {
  success: false;
  error: string;
}> {
  try {
    // 1. 检查Generation是否存在且已删除
    const generation = await db.generation.findUnique({
      where: { id: generationId },
      select: {
        id: true,
        userId: true,
        deleted: true,
      }
    });

    if (!generation) {
      return {
        success: false,
        error: 'Generation not found'
      };
    }

    if (!generation.deleted) {
      return {
        success: false,
        error: 'Generation is not deleted'
      };
    }

    // 2. 执行恢复
    const restoredGeneration = await db.generation.update({
      where: { id: generationId },
      data: {
        deleted: false,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        userId: true,
        deleted: true,
      }
    });

    logger.info('[GenerationsManager] Generation restored:', {
      generationId,
      adminUserId,
      originalUserId: generation.userId,
      restoredAt: new Date()
    });

    return {
      success: true,
      restoredGeneration
    };

  } catch (error) {
    logger.error('[GenerationsManager] Error restoring generation:', {
      generationId,
      adminUserId,
      error: error instanceof Error ? error.message : String(error)
    });
    
    return {
      success: false,
      error: 'Internal server error'
    };
  }
}
```

### 第二步：扩展 GenerationService 类

**文件**: `packages/api/src/routes/generations/lib/generation-service.ts`

#### 2.1 添加删除服务方法

```typescript
/**
 * 软删除generation
 */
static async deleteGeneration(
  user: Session["user"], 
  generationId: string
): Promise<{
  success: true;
  data: {
    id: string;
    deleted: boolean;
    deletedAt: string;
  };
} | {
  success: false;
  error: string;
}> {
  try {
    const isAdmin = user.role?.includes('admin') || false;
    
    const result = await GenerationsManager.softDeleteGeneration(
      generationId,
      user.id,
      isAdmin
    );

    if (!result.success) {
      return {
        success: false,
        error: result.error
      };
    }

    console.log(`[GenerationService] User ${user.id}${isAdmin ? ' (admin)' : ''} deleted generation ${generationId}`);

    return {
      success: true,
      data: {
        id: result.deletedGeneration.id,
        deleted: result.deletedGeneration.deleted,
        deletedAt: new Date().toISOString(),
      }
    };

  } catch (error) {
    console.error('[GenerationService] Error deleting generation:', error);
    return {
      success: false,
      error: 'Internal server error'
    };
  }
}

/**
 * 批量删除generations - 事务版本
 */
static async batchDeleteGenerations(
  user: Session["user"], 
  generationIds: string[]
): Promise<{
  deleted?: string[];
  error?: string;
}> {
  try {
    const isAdmin = user.role?.includes('admin') || false;
    
    const result = await GenerationsManager.batchSoftDeleteGenerations(
      generationIds,
      user.id,
      isAdmin
    );

    if (result.deleted) {
      console.log(`[GenerationService] User ${user.id}${isAdmin ? ' (admin)' : ''} batch deleted ${result.deleted.length} generations`);
    } else {
      console.warn(`[GenerationService] Batch deletion failed for user ${user.id}:`, result.error);
    }

    return result;

  } catch (error) {
    console.error('[GenerationService] Error batch deleting generations:', error);
    return {
      error: 'Internal server error'
    };
  }
}
```

### 第三步：扩展类型定义

**文件**: `packages/api/src/routes/generations/types.ts`

#### 3.1 添加删除相关类型

```typescript
// 删除响应类型（更直观的格式）
export interface DeleteGenerationResponse {
  deleted?: string;
  failed?: Record<string, string>;
}

// 批量删除响应类型
export interface BatchDeleteGenerationResponse {
  deleted?: string[];
  error?: string;
}
```

### 第四步：扩展Schema定义

**文件**: `packages/api/src/routes/generations/schemas.ts`

#### 4.1 添加删除相关Schema

```typescript
import { z } from "zod";

// 删除响应Schema（更直观的格式）
export const DeleteGenerationResponseSchema = z.object({
  deleted: z.string().optional().describe("Successfully deleted generation ID"),
  failed: z.record(z.string(), z.string()).optional().describe("Failed deletions with reasons")
}).refine(data => data.deleted || data.failed, {
  message: "Response must contain either deleted or failed"
});

// 批量删除请求Body Schema
export const BatchDeleteGenerationsRequestSchema = z.object({
  generationIds: z.array(z.string().min(1)).min(1).max(50).describe("Array of generation IDs to delete (max 50)"),
});

// 批量删除响应Schema
export const BatchDeleteGenerationResponseSchema = z.union([
  z.object({
    deleted: z.array(z.string()).describe("Successfully deleted generation IDs")
  }),
  z.object({
    error: z.string().describe("Error message")
  })
]);
```

### 第五步：扩展API路由

**文件**: `packages/api/src/routes/generations/router.ts`

#### 5.1 添加DELETE路由

```typescript
// 在现有路由后添加以下代码

// DELETE /api/generations/:generationId - 软删除单个generation
.delete(
  "/:generationId",
  authMiddleware,
  describeRoute({
    tags: ["Generations"],
    summary: "Delete a generation",
    description: "Soft delete a specific generation. Users can only delete their own generations unless they are admin. The generation will be marked as deleted but not physically removed from the database.",
    parameters: [
      {
        name: "generationId",
        in: "path",
        description: "Generation ID to delete",
        schema: { type: "string" },
        required: true,
      },
    ],
    responses: {
      200: {
        description: "Generation deleted successfully",
        content: {
          'application/json': {
            schema: DeleteGenerationSuccessResponseSchema,
          },
        },
      },
      400: {
        description: "Bad request - Invalid parameters or generation cannot be deleted",
        content: {
          'application/json': {
            schema: DeleteGenerationErrorResponseSchema,
          },
        },
      },
      401: {
        description: "Unauthorized - Authentication required",
        content: {
          'application/json': {
            schema: DeleteGenerationErrorResponseSchema,
          },
        },
      },
      403: {
        description: "Forbidden - Access denied (not the owner or admin)",
        content: {
          'application/json': {
            schema: DeleteGenerationErrorResponseSchema,
          },
        },
      },
      404: {
        description: "Generation not found",
        content: {
          'application/json': {
            schema: DeleteGenerationErrorResponseSchema,
          },
        },
      },
      409: {
        description: "Conflict - Generation already deleted",
        content: {
          'application/json': {
            schema: DeleteGenerationErrorResponseSchema,
          },
        },
      },
      500: {
        description: "Internal server error",
        content: {
          'application/json': {
            schema: DeleteGenerationErrorResponseSchema,
          },
        },
      },
    },
  }),
  validator("param", GenerationIdParamSchema),
  async (c) => {
    const user = c.get("user");
    const { generationId } = c.req.valid("param");

    console.log(`[GenerationsAPI] User ${user.id} requesting deletion of generation ${generationId}`);

    const result = await GenerationService.deleteGeneration(user, generationId);
    
    if (result.success) {
      console.log(`[GenerationsAPI] Generation ${generationId} deleted successfully by user ${user.id}`);
      return c.json(result, 200);
    } else {
      console.warn(`[GenerationsAPI] Failed to delete generation ${generationId} by user ${user.id}:`, result.error);
      
      // 根据错误类型返回相应的状态码
      if (result.error === 'Generation not found') {
        return c.json(result, 404);
      } else if (result.error === 'Access denied') {
        return c.json(result, 403);
      } else if (result.error === 'Generation already deleted') {
        return c.json(result, 409);
      } else if (result.error === 'Generation cannot be deleted') {
        return c.json(result, 400);
      } else {
        return c.json(result, 500);
      }
    }
  }
)

// POST /api/generations/batch-delete - 批量软删除generations（可选扩展功能）
.post(
  "/batch-delete",
  authMiddleware,
  describeRoute({
    tags: ["Generations"],
    summary: "Batch delete generations",
    description: "Soft delete multiple generations at once. Users can only delete their own generations unless they are admin. Maximum 50 generations per request.",
    requestBody: {
      required: true,
      content: {
        'application/json': {
          schema: BatchDeleteGenerationsRequestSchema,
        },
      },
    },
    responses: {
      200: {
        description: "Generations deleted successfully",
        content: {
          'application/json': {
            schema: BatchDeleteGenerationSuccessResponseSchema,
          },
        },
      },
      400: {
        description: "Bad request - Invalid parameters",
        content: {
          'application/json': {
            schema: BatchDeleteGenerationErrorResponseSchema,
          },
        },
      },
      401: {
        description: "Unauthorized - Authentication required",
        content: {
          'application/json': {
            schema: BatchDeleteGenerationErrorResponseSchema,
          },
        },
      },
      403: {
        description: "Forbidden - Access denied",
        content: {
          'application/json': {
            schema: BatchDeleteGenerationErrorResponseSchema,
          },
        },
      },
      500: {
        description: "Internal server error",
        content: {
          'application/json': {
            schema: BatchDeleteGenerationErrorResponseSchema,
          },
        },
      },
    },
  }),
  validator("json", BatchDeleteGenerationsRequestSchema),
  async (c) => {
    const user = c.get("user");
    const { generationIds } = c.req.valid("json");

    console.log(`[GenerationsAPI] User ${user.id} requesting batch deletion of ${generationIds.length} generations`);

    const result = await GenerationService.batchDeleteGenerations(user, generationIds);
    
    if (result.deleted) {
      console.log(`[GenerationsAPI] Batch deletion completed by user ${user.id}: ${result.deleted.length}/${generationIds.length} deleted`);
      return c.json(result, 200);
    } else {
      console.warn(`[GenerationsAPI] Failed batch deletion by user ${user.id}:`, result.error);
      
      // 根据错误类型返回相应的状态码
      const errorMsg = result.error || '';
      if (errorMsg.includes('not found')) {
        return c.json(result, 404);
      } else if (errorMsg.includes('Access denied')) {
        return c.json(result, 403);
      } else if (errorMsg.includes('already deleted')) {
        return c.json(result, 409);
      } else if (errorMsg.includes('No generation IDs')) {
        return c.json(result, 400);
      } else {
        return c.json(result, 500);
      }
    }
  }
);
```

#### 5.2 更新imports

```typescript
// 在文件顶部的import部分添加：
import { 
  GetGenerationsRequestSchema, 
  GenerationIdParamSchema,
  LikeSuccessResponseSchema,
  LikeErrorResponseSchema,
  DeleteGenerationSuccessResponseSchema,  // 新增
  DeleteGenerationErrorResponseSchema,    // 新增
  BatchDeleteGenerationsRequestSchema,    // 新增
  BatchDeleteGenerationSuccessResponseSchema, // 新增
  BatchDeleteGenerationErrorResponseSchema    // 新增
} from "./schemas";
```

## 实施步骤总结

### 阶段一：核心功能实现
1. ✅ **扩展GenerationsManager** - 添加软删除相关数据库操作方法
2. ✅ **扩展GenerationService** - 添加删除业务逻辑服务方法  
3. ✅ **扩展类型定义** - 添加删除相关TypeScript类型
4. ✅ **扩展Schema定义** - 添加删除相关Zod验证Schema
5. ✅ **扩展API路由** - 添加DELETE端点和路由处理逻辑

### 阶段二：测试和验证
1. **单元测试** - 为GenerationsManager的新方法编写测试
2. **集成测试** - 测试完整的API删除流程
3. **权限测试** - 验证用户权限控制是否正确
4. **边界测试** - 测试各种错误情况和边界条件

### 阶段三：代码质量检查
1. **Lint检查** - 运行 `pnpm lint` 确保代码规范
2. **类型检查** - 运行 `pnpm type-check` 确保类型正确
3. **格式化** - 运行 `pnpm format` 统一代码格式

### 阶段四：文档更新
1. **API文档** - 更新OpenAPI文档（自动生成）
2. **CHANGELOG** - 记录新增功能
3. **README** - 如有必要，更新相关说明

## 安全考虑

### 1. 权限控制
- ✅ 普通用户只能删除自己的Generation
- ✅ 管理员可以删除任何用户的Generation
- ✅ 检查`canDelete`字段控制删除权限

### 2. 数据安全
- ✅ 使用软删除，避免数据丢失
- ✅ 记录操作日志，便于审计
- ✅ 事务性操作，确保数据一致性

### 3. API安全
- ✅ 认证中间件保护所有端点
- ✅ 参数验证防止注入攻击
- ✅ 错误处理避免信息泄露

## 性能考虑

### 1. 数据库优化
- ✅ 利用现有的数据库索引（id, userId等）
- ✅ 单次操作和批量操作分别优化
- ✅ 避免不必要的数据查询

### 2. API性能
- ✅ 适当的HTTP状态码减少客户端重试
- ✅ 日志记录便于性能监控
- ✅ 批量操作支持减少请求次数

## 监控和日志

### 1. 操作日志
- ✅ 记录所有删除操作（包括用户ID、时间戳）
- ✅ 记录权限检查结果
- ✅ 记录错误和异常情况

### 2. 性能监控
- ✅ 记录删除操作耗时
- ✅ 监控批量删除的效率
- ✅ 跟踪API调用频率

## 扩展功能（未来可选）

### 1. 恢复功能
- 管理员恢复已删除的Generation
- 批量恢复功能

### 2. 回收站功能
- 查看已删除的Generation列表
- 定时清理长期删除的数据

### 3. 删除审计
- 删除操作审计日志
- 删除统计报表

## 风险评估

### 高风险
- ❌ 无：采用软删除，数据可恢复

### 中风险  
- ⚠️ 权限控制错误可能导致误删
- 🛡️ 缓解：完整的权限检查和测试

### 低风险
- ⚠️ 批量删除可能影响性能
- 🛡️ 缓解：限制批量大小（最多50个）

## 结论

该删除API实现方案采用了软删除策略，确保数据安全和可恢复性。通过完整的权限控制、错误处理和日志记录，保证了API的安全性和可维护性。实现分为五个明确的步骤，便于分阶段开发和测试。

所有代码都遵循现有项目的架构模式和编码规范，确保与现有系统的一致性和兼容性。

## 响应格式更新说明

**重要更新**: 文档已更新为使用更直观的响应格式，主要变化包括：

### 1. GenerationsManager 层更新
- `softDeleteGeneration` 返回 `{deleted?: string, failed?: Record<string, string>}`
- `batchSoftDeleteGenerations` 返回 `{deleted?: string[], error?: string}` **（使用事务）**
- 移除了 `success` 字段的包装层
- **批量删除采用事务机制，保证原子性操作**

### 2. GenerationService 层更新
需要相应更新以匹配新的响应格式：
```typescript
// 单个删除服务需要适配新格式
const result = await GenerationsManager.softDeleteGeneration(generationId, user.id, isAdmin);
if (result.deleted) {
  return result; // 直接返回 {deleted: "gen_001"}
} else {
  return result; // 直接返回 {failed: {"gen_001": "reason"}}
}

// 批量删除服务 - 事务版本
const result = await GenerationsManager.batchSoftDeleteGenerations(generationIds, user.id, isAdmin);
// 直接返回 {deleted: ["gen_001", "gen_002"]} 或 {error: "具体错误信息"}
return result;
```

### 3. API路由层更新
需要更新状态码判断逻辑：
```typescript
// 单个删除
if (result.deleted) {
  return c.json(result, 200);
} else if (result.failed) {
  const errorMessage = Object.values(result.failed)[0];
  if (errorMessage === 'Generation not found') return c.json(result, 404);
  if (errorMessage === 'Access denied') return c.json(result, 403);
  // ... 其他错误判断
}

// 批量删除 - 事务版本
if (result.deleted) {
  return c.json(result, 200); // 全部成功
} else {
  const errorMsg = result.error || '';
  if (errorMsg.includes('not found')) return c.json(result, 404);
  if (errorMsg.includes('Access denied')) return c.json(result, 403);
  if (errorMsg.includes('already deleted')) return c.json(result, 409);
  return c.json(result, 400);
}
```

### 4. Schema定义更新
- 单个删除：使用 `z.object().refine()` 确保响应包含 `deleted` 或 `failed` 中的至少一个
- 批量删除：使用 `z.union()` 支持 `{deleted: string[]}` 或 `{error: string}` 两种格式
- 移除了复杂的嵌套 `success/data/error` 结构

### 5. TypeScript类型更新
- 简化了类型定义，移除了多余的包装类型
- 批量删除使用 `{deleted?: string[], error?: string}` 类型
- 使用联合类型更直观地表达成功/失败状态

### 6. 事务机制优势
- **原子性**：批量删除要么全部成功，要么全部失败
- **一致性**：避免部分删除导致的数据不一致
- **简化处理**：客户端无需处理复杂的部分成功情况
- **错误明确**：提供具体的错误信息，便于用户理解

这种设计更符合大多数实际业务场景的需求，特别是批量操作的原子性要求。