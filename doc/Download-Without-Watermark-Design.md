# Download Without Watermark 功能详细设计方案

## 1. 项目概述

### 1.1 功能描述
实现视频无水印下载功能，允许付费用户下载没有水印的高质量视频内容。该功能将与现有的计费系统集成，确保只有符合条件的用户才能访问无水印内容。

### 1.2 当前状态分析
- ✅ 前端UI已实现（ActionMenu组件中的"Download without watermark"选项）
- ✅ 数据库结构已支持（Generation表中的`videoUrlNoWatermark`字段）
- ❌ 后端API端点缺失
- ❌ 权限验证逻辑缺失
- ❌ 实际下载逻辑缺失

## 2. 技术架构设计

### 2.1 系统组件架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端UI组件    │───▶│   API Gateway   │───▶│   业务服务层    │
│  ActionMenu     │    │  下载API端点    │    │ DownloadService │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   权限验证层    │    │   文件存储系统  │
                       │ AuthMiddleware  │    │   CDN/Storage   │
                       └─────────────────┘    └─────────────────┘
```

### 2.2 数据流设计
```
用户点击下载 → 前端发送请求 → API权限验证 → 检查用户订阅状态 → 
生成下载链接 → 记录下载统计 → 返回下载URL → 用户下载文件
```

### 2.3 权限控制策略
1. **用户订阅状态检查**: 只有Pro、Lifetime、Enterprise用户可以下载无水印内容
2. **内容所有权验证**: 用户只能下载自己生成的内容或公开发布的内容
3. **下载次数限制**: 根据订阅计划限制下载次数（可选）
4. **时效性控制**: 生成的下载链接具有过期时间

## 3. 数据库设计

### 3.1 现有表结构分析
```sql
-- Generation表（已存在）
model Generation {
  id                    String         @id
  userId                String
  videoUrl              String?        -- 带水印视频URL
  videoUrlNoWatermark   String?        -- 无水印视频URL ✅
  downloadNum           Int            @default(0) -- 下载统计
  canDelete             Boolean        @default(true)
  canPublish            Boolean        @default(true)
  -- ... 其他字段
}

-- User表（已存在）
model User {
  id                 String       @id
  subscriptions      Subscription[] -- 订阅关系 ✅
  creditUsage        CreditUsage?   -- 积分使用 ✅
  -- ... 其他字段
}
```

### 3.2 新增表结构设计
```sql
-- 下载记录表（新增）
model DownloadRecord {
  id                String     @id @default(cuid())
  userId            String
  generationId      String
  downloadType      String     -- "watermark" | "no_watermark"
  downloadedAt      DateTime   @default(now())
  ipAddress         String?
  userAgent         String?
  
  user              User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  generation        Generation @relation(fields: [generationId], references: [id], onDelete: Cascade)
  
  @@index([userId])
  @@index([generationId])
  @@index([downloadedAt])
  @@index([downloadType])
  @@map("download_record")
}
```

### 3.3 更新现有表关联
```sql
-- 更新User表
model User {
  // ... 现有字段
  downloadRecords    DownloadRecord[]  -- 新增下载记录关联
  // ... 其他关联
}

-- 更新Generation表
model Generation {
  // ... 现有字段
  downloadRecords    DownloadRecord[]  -- 新增下载记录关联
  // ... 其他关联
}
```

## 4. API接口设计

### 4.1 下载API端点
```typescript
// GET /api/generations/videos/:generationId/downloads/watermark
// GET /api/generations/videos/:generationId/downloads/no-watermark
interface DownloadRequest {
  generationId: string;
  downloadType: 'watermark' | 'no-watermark'; // 从路径中提取
}

// 带水印下载响应
interface WatermarkDownloadResponse {
  videoUrl: string;
}

// 无水印下载响应  
interface NoWatermarkDownloadResponse {
  videoUrlNoWatermark: string;
}

// 错误响应
interface DownloadErrorResponse {
  error: string;
  errorCode?: string;
}
```

### 4.2 API实现结构
```typescript
// packages/api/src/routes/generations/
├── index.ts          -- 路由导出
├── router.ts         -- 路由定义
├── schemas.ts        -- 请求/响应验证
├── types.ts          -- 类型定义
└── lib/
    ├── download-service.ts    -- 下载业务逻辑
    ├── permission-service.ts  -- 权限验证逻辑
    └── storage-service.ts     -- 存储和签名URL服务
```

## 5. 权限验证逻辑

### 5.1 权限检查流程
```typescript
class PermissionService {
  static async canDownloadWithoutWatermark(
    user: User, 
    generationId: string
  ): Promise<{canDownload: boolean, reason?: string, errorCode?: string}> {
    // 1. 检查用户订阅状态
    const subscription = await this.getUserActiveSubscription(user.id);
    if (!subscription || subscription.plan === 'free') {
      return { 
        canDownload: false, 
        reason: '需要升级到Pro版本才能下载无水印内容',
        errorCode: 'SUBSCRIPTION_REQUIRED'
      };
    }
    
    // 2. 检查内容所有权
    const generation = await db.generation.findUnique({
      where: { id: generationId }
    });
    
    if (!generation) {
      return { 
        canDownload: false, 
        reason: '内容不存在或已被删除',
        errorCode: 'CONTENT_NOT_FOUND'
      };
    }
    
    // 用户只能下载自己的内容或公开内容
    if (generation.userId !== user.id && 
        generation.publishStatus !== 'published') {
      return { 
        canDownload: false, 
        reason: '您没有权限访问此内容',
        errorCode: 'ACCESS_DENIED'
      };
    }
    
    // 3. 检查无水印视频是否存在
    if (!generation.videoUrlNoWatermark) {
      return { 
        canDownload: false, 
        reason: '该内容暂无无水印版本',
        errorCode: 'NO_WATERMARK_VERSION'
      };
    }
    
    return { canDownload: true };
  }
}
```

### 5.2 订阅计划权限映射
```typescript
const DOWNLOAD_PERMISSIONS = {
  free: { 
    canDownloadWithWatermark: true, 
    canDownloadWithoutWatermark: false 
  },
  pro: { 
    canDownloadWithWatermark: true, 
    canDownloadWithoutWatermark: true 
  },
  lifetime: { 
    canDownloadWithWatermark: true, 
    canDownloadWithoutWatermark: true 
  },
  enterprise: { 
    canDownloadWithWatermark: true, 
    canDownloadWithoutWatermark: true 
  }
};
```

## 6. 安全性设计

### 6.1 下载链接安全
- **临时URL生成**: 使用签名URL，设置较短的过期时间（如30分钟）
- **防盗链机制**: 验证请求来源和User-Agent
- **下载限速**: 防止恶意大量下载

### 6.2 权限绕过防护
- **JWT Token验证**: 确保用户身份真实性
- **实时权限检查**: 每次下载都验证最新的订阅状态
- **API限流**: 防止暴力请求

### 6.3 数据保护
- **敏感信息加密**: 下载URL包含加密参数
- **审计日志**: 记录所有下载行为
- **IP限制**: 可选的地理位置限制

## 7. 文件存储策略

### 7.1 存储架构
```
CDN (CloudFlare)
    ↓
Object Storage (AWS S3 / 阿里云OSS)
    ├── /watermark/      -- 带水印视频
    └── /no-watermark/   -- 无水印视频
```

### 7.2 文件命名规则
```
/no-watermark/{userId}/{generationId}/video.mp4
```

### 7.3 CDN配置
- **缓存策略**: 无水印内容设置较短的缓存时间
- **访问控制**: 通过签名URL控制访问权限
- **带宽优化**: 根据用户地理位置选择最优CDN节点

## 8. 详细实施步骤

### 第一阶段：数据库结构扩展

#### Step 1.1: 更新数据库Schema

**位置**: `packages/database/prisma/schema.prisma`

**操作**: 在现有schema末尾添加新的model定义

```sql
// 下载记录表
model DownloadRecord {
  id                String     @id @default(cuid())
  userId            String
  generationId      String
  downloadType      String     // "watermark" | "no_watermark"
  downloadedAt      DateTime   @default(now())
  ipAddress         String?
  userAgent         String?
  
  user              User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  generation        Generation @relation(fields: [generationId], references: [id], onDelete: Cascade)
  
  @@index([userId])
  @@index([generationId])
  @@index([downloadedAt])
  @@index([downloadType])
  @@map("download_record")
}
```

#### Step 1.2: 更新现有模型关联

```sql
model User {
  // ... 现有字段
  downloadRecords    DownloadRecord[]  // 新增
  // ... 其他关联
}

model Generation {
  // ... 现有字段  
  downloadRecords    DownloadRecord[]  // 新增
  // ... 其他关联
}
```

**命令执行**:
```bash
# 从项目根目录执行
cd packages/database
pnpm prisma db push
pnpm prisma generate
```

### 第二阶段：后端API实现

#### Step 2.1: 创建下载API类型定义

**在现有文件中添加**: `packages/api/src/routes/generations/types.ts`

在现有types.ts文件末尾添加下载相关类型：

```typescript
// 在现有类型定义后添加

// 下载相关类型
export const DownloadTypeSchema = z.enum(['watermark', 'no_watermark']);
export type DownloadType = z.infer<typeof DownloadTypeSchema>;

export const DownloadRequestSchema = z.object({
  generationId: z.string().cuid(),
  type: DownloadTypeSchema,
});
export type DownloadRequest = z.infer<typeof DownloadRequestSchema>;

export const DownloadResponseDataSchema = z.object({
  downloadUrl: z.string().url(),
  expiresAt: z.string().datetime(),
  filename: z.string(),
  fileSize: z.number().optional(),
});

export const DownloadResponseSchema = z.object({
  success: z.literal(true),
  data: DownloadResponseDataSchema,
});

export const DownloadErrorResponseSchema = z.object({
  success: z.literal(false),
  error: z.string(),
  errorCode: z.string().optional(),
});

export interface PermissionCheckResult {
  canDownload: boolean;
  reason?: string;
  errorCode?: string;
}

export interface UserSubscriptionInfo {
  planType: 'free' | 'pro' | 'lifetime' | 'enterprise';
  isActive: boolean;
  expiresAt?: Date;
}
```

#### Step 2.2: 创建权限验证服务

**文件**: `packages/api/src/routes/generations/lib/permission-service.ts`

```typescript
import { db } from "@repo/database";
import type { User } from "@repo/database";
import type { PermissionCheckResult, UserSubscriptionInfo } from "../types";

export class PermissionService {
  static async canDownloadWithoutWatermark(
    user: User,
    generationId: string
  ): Promise<PermissionCheckResult> {
    try {
      // 1. 获取Generation信息
      const generation = await db.generation.findUnique({
        where: { id: generationId },
        select: {
          id: true,
          userId: true,
          videoUrlNoWatermark: true,
          publishStatus: true,
          canRead: true,
          status: true,
        },
      });

      if (!generation) {
        return {
          canDownload: false,
          reason: "内容不存在或已被删除",
          errorCode: "CONTENT_NOT_FOUND",
        };
      }

      // 2. 检查内容状态
      if (generation.status !== "succeeded") {
        return {
          canDownload: false,
          reason: "内容尚未生成完成",
          errorCode: "CONTENT_NOT_READY",
        };
      }

      // 3. 检查无水印版本是否存在
      if (!generation.videoUrlNoWatermark) {
        return {
          canDownload: false,
          reason: "该内容暂无无水印版本",
          errorCode: "NO_WATERMARK_VERSION",
        };
      }

      // 4. 检查访问权限
      const hasAccess = await this.checkContentAccess(user, generation);
      if (!hasAccess.canAccess) {
        return {
          canDownload: false,
          reason: hasAccess.reason,
          errorCode: "ACCESS_DENIED",
        };
      }

      // 5. 检查用户订阅状态
      const subscription = await this.getUserSubscription(user.id);
      if (!this.canDownloadWithoutWatermarkByPlan(subscription)) {
        return {
          canDownload: false,
          reason: "需要升级到Pro版本才能下载无水印内容",
          errorCode: "SUBSCRIPTION_REQUIRED",
        };
      }

      return { canDownload: true };
    } catch (error) {
      console.error("[PermissionService] Error checking download permission:", error);
      return {
        canDownload: false,
        reason: "权限检查失败",
        errorCode: "PERMISSION_CHECK_ERROR",
      };
    }
  }

  private static async checkContentAccess(user: User, generation: any) {
    // 用户可以访问自己的内容
    if (generation.userId === user.id) {
      return { canAccess: true };
    }

    // 管理员可以访问所有内容
    if (user.role?.includes("admin")) {
      return { canAccess: true };
    }

    // 检查是否为公开发布的内容
    if (generation.publishStatus === "published" && generation.canRead) {
      return { canAccess: true };
    }

    return {
      canAccess: false,
      reason: "您没有权限访问此内容",
    };
  }

  private static async getUserSubscription(userId: string): Promise<UserSubscriptionInfo> {
    try {
      const subscription = await db.subscription.findFirst({
        where: { userId, status: "active" },
        include: { plan: true },
        orderBy: { createdAt: "desc" },
      });

      if (!subscription) {
        return { planType: "free", isActive: false };
      }

      const planType = this.determinePlanType(subscription.plan);
      return {
        planType,
        isActive: subscription.status === "active",
        expiresAt: subscription.currentPeriodEnd || undefined,
      };
    } catch (error) {
      console.error("[PermissionService] Error getting user subscription:", error);
      return { planType: "free", isActive: false };
    }
  }

  private static determinePlanType(plan: any): 'free' | 'pro' | 'lifetime' | 'enterprise' {
    const { priceId } = plan;

    if (priceId === process.env.NEXT_PUBLIC_PRICE_ID_PRO_MONTHLY || 
        priceId === process.env.NEXT_PUBLIC_PRICE_ID_PRO_YEARLY) {
      return 'pro';
    }
    
    if (priceId === process.env.NEXT_PUBLIC_PRICE_ID_LIFETIME) {
      return 'lifetime';
    }

    return 'free';
  }

  private static canDownloadWithoutWatermarkByPlan(subscription: UserSubscriptionInfo): boolean {
    const allowedPlans = ['pro', 'lifetime', 'enterprise'];
    return subscription.isActive && allowedPlans.includes(subscription.planType);
  }
}
```

#### Step 2.3: 创建存储服务

**文件**: `packages/api/src/routes/generations/lib/storage-service.ts`

```typescript
import { createHmac } from "crypto";

interface SignedUrlOptions {
  expiresIn: number; // 过期时间（秒）
  filename?: string; // 建议的文件名
}

interface FileInfo {
  size?: number;
  contentType?: string;
  lastModified?: Date;
}

/**
 * 生成签名下载URL
 */
export async function generateSignedUrl(
  fileUrl: string,
  options: SignedUrlOptions
): Promise<string> {
  const { expiresIn, filename } = options;
  const expires = Math.floor(Date.now() / 1000) + expiresIn;
  
  // 构建签名参数
  const params = new URLSearchParams();
  params.set('expires', expires.toString());
  
  if (filename) {
    params.set('filename', filename);
  }

  // 生成签名
  const signature = generateSignature(fileUrl, expires, filename);
  params.set('signature', signature);

  // 构建最终URL
  const separator = fileUrl.includes('?') ? '&' : '?';
  return `${fileUrl}${separator}${params.toString()}`;
}

/**
 * 生成URL签名
 */
function generateSignature(fileUrl: string, expires: number, filename?: string): string {
  const secret = process.env.DOWNLOAD_URL_SECRET || 'default-secret';
  const data = `${fileUrl}:${expires}:${filename || ''}`;
  
  return createHmac('sha256', secret)
    .update(data)
    .digest('hex')
    .substring(0, 16); // 取前16位
}

/**
 * 获取文件信息
 */
export async function getFileInfo(fileUrl: string): Promise<FileInfo | null> {
  try {
    // 发送HEAD请求获取文件信息
    const response = await fetch(fileUrl, { method: 'HEAD' });
    
    if (!response.ok) {
      return null;
    }

    return {
      size: response.headers.get('content-length') 
        ? parseInt(response.headers.get('content-length')!) 
        : undefined,
      contentType: response.headers.get('content-type') || undefined,
      lastModified: response.headers.get('last-modified') 
        ? new Date(response.headers.get('last-modified')!) 
        : undefined,
    };
  } catch (error) {
    console.error('[StorageService] Error getting file info:', error);
    return null;
  }
}

/**
 * 验证签名URL的有效性
 */
export function validateSignedUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    const expires = urlObj.searchParams.get('expires');
    const signature = urlObj.searchParams.get('signature');
    const filename = urlObj.searchParams.get('filename');

    if (!expires || !signature) {
      return false;
    }

    // 检查是否过期
    const expiresTime = parseInt(expires);
    if (Date.now() / 1000 > expiresTime) {
      return false;
    }

    // 验证签名
    const baseUrl = url.split('?')[0];
    const expectedSignature = generateSignature(baseUrl, expiresTime, filename);
    
    return signature === expectedSignature;
  } catch (error) {
    return false;
  }
}
```

#### Step 2.4: 创建下载服务

**文件**: `packages/api/src/routes/generations/lib/download-service.ts`

```typescript
import { db } from "@repo/database";
import type { User } from "@repo/database";
import type {
  DownloadRequest,
  DownloadResponse,
  DownloadErrorResponse,
  DownloadType,
} from "../types";
import { PermissionService } from "./permission-service";
import { generateSignedUrl, getFileInfo } from "./storage-service";

export class DownloadService {
  /**
   * 处理下载请求
   */
  static async handleDownload(
    user: User,
    params: DownloadRequest,
    clientInfo: { ipAddress?: string; userAgent?: string }
  ): Promise<DownloadResponse | DownloadErrorResponse> {
    try {
      const { generationId, type } = params;

      // 1. 权限检查
      if (type === "no_watermark") {
        const permissionCheck = await PermissionService.canDownloadWithoutWatermark(
          user,
          generationId
        );

        if (!permissionCheck.canDownload) {
          return {
            success: false,
            error: permissionCheck.reason || "下载权限不足",
            errorCode: permissionCheck.errorCode,
          };
        }
      }

      // 2. 获取文件URL
      const fileUrl = await this.getFileUrl(generationId, type);
      if (!fileUrl) {
        return {
          success: false,
          error: "文件不存在或已过期",
          errorCode: "FILE_NOT_FOUND",
        };
      }

      // 3. 生成签名下载URL
      const downloadUrl = await generateSignedUrl(fileUrl, {
        expiresIn: 30 * 60, // 30分钟过期
        filename: this.generateFilename(generationId, type),
      });

      // 4. 获取文件信息
      const fileInfo = await getFileInfo(fileUrl);

      // 5. 记录下载行为
      await this.recordDownload(user.id, generationId, type, clientInfo);

      // 6. 更新下载统计
      await this.updateDownloadStats(generationId);

      const expiresAt = new Date(Date.now() + 30 * 60 * 1000); // 30分钟后

      // 根据下载类型返回不同格式
      if (type === 'watermark') {
        return {
          videoUrl: downloadUrl,
        };
      } else {
        return {
          videoUrlNoWatermark: downloadUrl,
        };
      }
    } catch (error) {
      console.error("[DownloadService] Error handling download:", error);
      return {
        error: "下载服务异常，请稍后重试",
        errorCode: "DOWNLOAD_SERVICE_ERROR",
      };
    }
  }

  /**
   * 获取文件URL
   */
  private static async getFileUrl(
    generationId: string,
    type: DownloadType
  ): Promise<string | null> {
    const generation = await db.generation.findUnique({
      where: { id: generationId },
      select: {
        videoUrl: true,
        videoUrlNoWatermark: true,
      },
    });

    if (!generation) {
      return null;
    }

    return type === "watermark" 
      ? generation.videoUrl 
      : generation.videoUrlNoWatermark;
  }

  /**
   * 生成文件名
   */
  private static generateFilename(generationId: string, type: DownloadType): string {
    const timestamp = new Date().toISOString().split('T')[0];
    const suffix = type === "watermark" ? "watermark" : "no-watermark";
    return `video-${generationId.slice(-8)}-${suffix}-${timestamp}.mp4`;
  }

  /**
   * 记录下载行为
   */
  private static async recordDownload(
    userId: string,
    generationId: string,
    type: DownloadType,
    clientInfo: { ipAddress?: string; userAgent?: string }
  ): Promise<void> {
    try {
      await db.downloadRecord.create({
        data: {
          userId,
          generationId,
          downloadType: type,
          ipAddress: clientInfo.ipAddress,
          userAgent: clientInfo.userAgent,
        },
      });
    } catch (error) {
      // 记录下载失败不应该阻塞下载流程
      console.error("[DownloadService] Error recording download:", error);
    }
  }

  /**
   * 更新下载统计
   */
  private static async updateDownloadStats(generationId: string): Promise<void> {
    try {
      await db.generation.update({
        where: { id: generationId },
        data: {
          downloadNum: {
            increment: 1,
          },
        },
      });
    } catch (error) {
      // 统计更新失败不应该阻塞下载流程
      console.error("[DownloadService] Error updating download stats:", error);
    }
  }
}
```

#### Step 2.5: 集成下载路由到现有的generations路由器

**在现有文件中添加**: `packages/api/src/routes/generations/router.ts`

在现有的generationsRouter中添加下载路由：

```typescript
// 在现有的import中添加
import { DownloadService } from "./lib/download-service";
import { DownloadRequestSchema, DownloadResponseSchema, DownloadErrorResponseSchema } from "./schemas";

// 在现有的generationsRouter链式调用中添加：
  
  // GET /api/generations/videos/:generationId/downloads/:type - 生成下载链接
  .get(
    "/videos/:generationId/downloads/:type",
    authMiddleware,
    describeRoute({
      tags: ["Generations"],
      summary: "Generate download link for generation",
      description: "Generate a signed download link for a specific generation. Supports both watermark and no-watermark downloads based on user subscription.",
      parameters: [
        {
          name: "generationId",
          in: "path",
          description: "Generation ID",
          schema: { type: "string" },
          required: true,
        },
        {
          name: "type",
          in: "path",
          description: "Download type",
          schema: { 
            type: "string",
            enum: ["watermark", "no-watermark"]
          },
          required: true,
        },
      ],
      responses: {
        200: { description: "Download link generated successfully" },
        400: { description: "Bad request - Invalid parameters" },
        401: { description: "Unauthorized - Authentication required" },
        403: { description: "Forbidden - Insufficient permissions" },
        404: { description: "Not found - Generation not found" },
        500: { description: "Internal server error" },
      },
    }),
    validator("param", z.object({
      generationId: z.string().cuid(),
      type: z.enum(["watermark", "no-watermark"]),
    })),
    async (c) => {
      const user = c.get("user");
      const { generationId, type } = c.req.valid("param");

      const clientInfo = {
        ipAddress: c.req.header("cf-connecting-ip") || 
                  c.req.header("x-forwarded-for") || 
                  c.req.header("x-real-ip"),
        userAgent: c.req.header("user-agent"),
      };

      console.log(`[GenerationsAPI] User ${user.id} requesting ${type} download for generation ${generationId}`);

      const result = await DownloadService.handleDownload(
        user,
        { generationId, type },
        clientInfo
      );

      if (result.success) {
        console.log(`[GenerationsAPI] Download link generated for user ${user.id}, generation ${generationId}`);
        return c.json(result, 200);
      } else {
        console.warn(`[GenerationsAPI] Download failed for user ${user.id}:`, result.error);
        
        switch (result.errorCode) {
          case "CONTENT_NOT_FOUND":
            return c.json(result, 404);
          case "ACCESS_DENIED":
          case "SUBSCRIPTION_REQUIRED":
            return c.json(result, 403);
          case "CONTENT_NOT_READY":
          case "NO_WATERMARK_VERSION":
            return c.json(result, 400);
          default:
            return c.json(result, 500);
        }
      }
    }
  );

// 现有的GenerationsRouter类型会自动包含新的下载路由
```

#### Step 2.6: 更新schema定义

**在现有文件中添加**: `packages/api/src/routes/generations/schemas.ts`

在现有的schemas.ts文件末尾添加下载相关的验证schema：

```typescript
// 下载相关的schema定义（在文件末尾添加）
export const DownloadTypeSchema = z.enum(['watermark', 'no-watermark']);
export const DownloadParamsSchema = z.object({
  generationId: z.string().cuid(),
  type: DownloadTypeSchema,
});
// 带水印下载响应schema
export const WatermarkDownloadResponseSchema = z.object({
  videoUrl: z.string().url(),
});

// 无水印下载响应schema
export const NoWatermarkDownloadResponseSchema = z.object({
  videoUrlNoWatermark: z.string().url(),
});

// 错误响应schema
export const DownloadErrorResponseSchema = z.object({
  error: z.string(),
  errorCode: z.string().optional(),
});
```

#### Step 2.7: 路由已集成

**无需额外注册** - 下载功能已集成到现有的generationsRouter中，会随现有路由自动注册。

```typescript
// Download functionality is now integrated into generationsRouter

// downloadsRouter is no longer needed - download functionality is integrated into generationsRouter

```

### 第三阶段：前端集成

#### Step 3.1: 创建下载Hook

**文件**: `apps/web/modules/marketing/shared/hooks/useDownload.ts`

```typescript
"use client";

import { useState } from "react";
import { useSession } from "@repo/auth/client";

interface DownloadOptions {
  generationId: string;
  type: 'watermark' | 'no_watermark';
}

interface DownloadResult {
  success: boolean;
  data?: {
    downloadUrl: string;
    expiresAt: string;
    filename: string;
    fileSize?: number;
  };
  error?: string;
  errorCode?: string;
}

export function useDownload() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { data: session } = useSession();

  const download = async (options: DownloadOptions): Promise<DownloadResult> => {
    if (!session?.user) {
      return {
        success: false,
        error: "请先登录",
        errorCode: "NOT_AUTHENTICATED",
      };
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(
        `/api/generations/videos/${options.generationId}/downloads/${options.type}`,
        {
          method: "GET",
          headers: {
            "Authorization": `Bearer ${session.accessToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      const result = await response.json();

      // 检查是否有错误
      if (result.error) {
        setError(result.error);
        return {
          success: false,
          error: result.error,
          errorCode: result.errorCode,
        };
      }

      // 根据响应格式获取视频URL
      const videoUrl = result.videoUrl || result.videoUrlNoWatermark;
      
      if (videoUrl) {
        // 触发浏览器下载
        const link = document.createElement('a');
        link.href = videoUrl;
        link.download = `video_${options.generationId}_${options.type}.mp4`;
        link.target = '_blank';
        link.rel = 'noopener noreferrer';
        
        // 添加到DOM并触发点击
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        return {
          success: true,
          data: { downloadUrl: videoUrl },
        };
      } else {
        setError("下载链接获取失败");
        return {
          success: false,
          error: "下载链接获取失败",
      }
    } catch (err) {
      const errorMessage = "网络错误，请稍后重试";
      setError(errorMessage);
      console.error("Download error:", err);
      
      return {
        success: false,
        error: errorMessage,
        errorCode: "NETWORK_ERROR",
      };
    } finally {
      setLoading(false);
    }
  };

  return {
    download,
    loading,
    error,
    clearError: () => setError(null),
  };
}
```

#### Step 3.2: 创建用户订阅状态Hook

**文件**: `apps/web/modules/marketing/shared/hooks/useUserSubscription.ts`

```typescript
"use client";

import { useState, useEffect } from "react";
import { useSession } from "@repo/auth/client";

interface UserSubscription {
  planType: 'free' | 'pro' | 'lifetime' | 'enterprise';
  isActive: boolean;
  expiresAt?: string;
  canDownloadWithoutWatermark: boolean;
}

export function useUserSubscription() {
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [loading, setLoading] = useState(true);
  const { data: session } = useSession();

  useEffect(() => {
    if (!session?.user) {
      setSubscription({
        planType: 'free',
        isActive: false,
        canDownloadWithoutWatermark: false,
      });
      setLoading(false);
      return;
    }

    fetchUserSubscription();
  }, [session]);

  const fetchUserSubscription = async () => {
    try {
      // TODO: 实现API调用获取订阅信息
      // const response = await fetch('/api/user/subscription');
      // const data = await response.json();
      
      // 临时模拟数据
      const mockSubscription: UserSubscription = {
        planType: 'free', // 可以根据session中的信息判断
        isActive: false,
        canDownloadWithoutWatermark: false,
      };

      setSubscription(mockSubscription);
    } catch (error) {
      console.error('Error fetching subscription:', error);
      // 错误时默认为免费用户
      setSubscription({
        planType: 'free',
        isActive: false,
        canDownloadWithoutWatermark: false,
      });
    } finally {
      setLoading(false);
    }
  };

  return {
    subscription,
    loading,
    refresh: fetchUserSubscription,
  };
}
```

#### Step 3.3: 更新ActionMenu组件

**文件**: `apps/web/modules/marketing/shared/components/GenerationItemCard/components/ActionMenu.tsx`

在现有组件中添加下载逻辑：

```typescript
// 在文件顶部添加导入
import { useDownload } from "../../../hooks/useDownload";
import { useUserSubscription } from "../../../hooks/useUserSubscription";

// 在组件内部添加hooks
export function ActionMenu({
  generation,
  actions,
  visible,
  className,
}: ActionMenuProps) {
  // ... 现有代码
  
  const { download, loading: downloadLoading } = useDownload();
  const { subscription } = useUserSubscription();
  const [showUpgradePrompt, setShowUpgradePrompt] = useState(false);

  // 处理带水印下载
  const handleDownloadWithWatermark = async () => {
    try {
      const result = await download({
        generationId: generation.id,
        type: 'watermark',
      });

      if (result.success) {
        console.log('下载开始');
      } else {
        console.error('下载失败:', result.error);
      }
    } catch (error) {
      console.error('下载异常:', error);
    }
  };

  // 处理无水印下载
  const handleDownloadWithoutWatermark = async () => {
    // 检查用户权限
    if (!subscription?.canDownloadWithoutWatermark) {
      setShowUpgradePrompt(true);
      return;
    }

    try {
      const result = await download({
        generationId: generation.id,
        type: 'no_watermark',
      });

      if (result.success) {
        console.log('无水印下载开始');
      } else {
        if (result.errorCode === 'SUBSCRIPTION_REQUIRED') {
          setShowUpgradePrompt(true);
        } else {
          console.error('下载失败:', result.error);
        }
      }
    } catch (error) {
      console.error('下载异常:', error);
    }
  };

  // 更新下载按钮的onClick处理器
  // ... 在现有的下载子菜单中更新按钮点击事件
}
```

#### Step 3.4: 创建升级提示组件

**文件**: `apps/web/modules/marketing/shared/components/UpgradePrompt.tsx`

```typescript
"use client";

import { Button } from "@ui/components/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@ui/components/dialog";
import { Crown, Download } from "lucide-react";

interface UpgradePromptProps {
  open: boolean;
  onClose: () => void;
  onUpgrade: () => void;
  feature?: string;
}

export function UpgradePrompt({ 
  open, 
  onClose, 
  onUpgrade, 
  feature = "无水印下载" 
}: UpgradePromptProps) {
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-2">
            <Crown className="w-5 h-5 text-yellow-500" />
            <DialogTitle>升级到Pro版本</DialogTitle>
          </div>
          <DialogDescription className="text-left">
            解锁{feature}功能，享受完整的视频生成体验
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 rounded-lg p-4 mb-4">
            <div className="flex items-start gap-3">
              <Download className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-sm mb-1">无水印下载</h4>
                <p className="text-xs text-muted-foreground">
                  下载高质量、无水印的视频内容，用于商业用途
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-2 text-xs text-muted-foreground">
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
              <span>无限制视频生成</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
              <span>优先处理队列</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
              <span>高级AI模型访问</span>
            </div>
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={onClose} className="flex-1">
            稍后再说
          </Button>
          <Button onClick={onUpgrade} className="flex-1">
            立即升级
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
```

### 第四阶段：测试与部署

#### Step 4.1: 环境变量配置

**文件**: `.env.example`

```bash
# 下载URL签名密钥
DOWNLOAD_URL_SECRET=your-secret-key-here

# CDN配置
NEXT_PUBLIC_CDN_BASE_URL=https://cdn.yourdomain.com
NEXT_PUBLIC_VIDEO_CDN_BASE_URL=https://videocdn.yourdomain.com

# 订阅计划价格ID
NEXT_PUBLIC_PRICE_ID_PRO_MONTHLY=price_xxx
NEXT_PUBLIC_PRICE_ID_PRO_YEARLY=price_xxx
NEXT_PUBLIC_PRICE_ID_LIFETIME=price_xxx
```

#### Step 4.2: 部署检查清单

- [ ] 数据库迁移已应用
- [ ] 环境变量已配置
- [ ] 下载API端点测试通过
- [ ] 权限验证测试通过
- [ ] 前端下载交互实现
- [ ] CDN和存储服务配置正确
- [ ] 签名URL安全机制测试通过
- [ ] 错误处理和用户反馈完善

#### Step 4.3: 测试脚本

**文件**: `test-download.sh`

```bash
#!/bin/bash

echo "Testing Download API..."

# 1. 测试带水印下载
echo "Testing watermark download..."
curl -X GET "http://localhost:3000/api/generations/videos/test-id/downloads/watermark" \
  -H "Authorization: Bearer YOUR_TEST_TOKEN" \
  -H "Content-Type: application/json"

echo -e "\n"

# 2. 测试无水印下载
echo "Testing no-watermark download..."
curl -X GET "http://localhost:3000/api/generations/videos/test-id/downloads/no-watermark" \
  -H "Authorization: Bearer YOUR_TEST_TOKEN" \
  -H "Content-Type: application/json"

echo -e "\n"

# 3. 测试权限验证
echo "Testing unauthorized access..."
curl -X GET "http://localhost:3000/api/generations/videos/test-id/downloads/no-watermark" \
  -H "Content-Type: application/json"

echo "Test completed!"
```

## 9. 性能优化方案

### 9.1 缓存策略
- **API响应缓存**: 下载URL缓存5分钟
- **权限信息缓存**: 用户订阅状态缓存10分钟
- **CDN缓存**: 静态文件缓存24小时

### 9.2 异步处理
- **下载统计异步更新**: 避免阻塞下载流程
- **日志记录异步**: 降低API响应时间

### 9.3 负载均衡
- **多CDN节点**: 全球分布的下载节点
- **智能路由**: 根据用户位置选择最优下载路径

## 10. 监控和分析

### 10.1 关键指标监控
- **下载成功率**: 成功下载 / 总下载请求
- **下载速度**: 平均下载时间
- **用户转化率**: 免费用户 → 付费用户转化
- **错误率**: API错误响应比例

### 10.2 用户行为分析
- **下载偏好**: 有水印 vs 无水印下载比例
- **时间分布**: 下载高峰时段分析
- **地理分布**: 用户下载地理位置统计

### 10.3 业务价值分析
- **收入影响**: 无水印下载对订阅收入的影响
- **用户留存**: 该功能对用户留存的提升效果
- **功能使用率**: 付费用户中的功能使用比例

## 11. 风险评估和应对

### 11.1 技术风险
- **CDN故障**: 多CDN备份方案
- **存储服务中断**: 多云存储备份
- **高并发压力**: 限流和负载均衡

### 11.2 业务风险
- **版权问题**: 加强内容审核机制
- **滥用风险**: 下载次数限制和行为监控
- **收入影响**: A/B测试验证商业模式

### 11.3 安全风险
- **链接泄露**: 短期有效签名URL
- **账号共享**: IP和设备指纹检测
- **数据泄露**: 加密存储和传输

## 12. 项目时间规划

### 第一阶段 (Week 1-2): 基础设施搭建
- [ ] 创建下载API路由和基础结构
- [ ] 实现权限验证服务
- [ ] 添加下载记录表和相关migration
- [ ] 基础单元测试

### 第二阶段 (Week 3-4): 核心功能实现
- [ ] 实现下载业务逻辑
- [ ] 集成文件存储和CDN
- [ ] 前端下载交互实现
- [ ] 错误处理和用户反馈

### 第三阶段 (Week 5-6): 安全和优化
- [ ] 安全性加固（签名URL、限流等）
- [ ] 性能优化（缓存、异步处理）
- [ ] 监控和日志系统
- [ ] 集成测试和用户测试

### 第四阶段 (Week 7): 上线准备
- [ ] 生产环境部署
- [ ] 监控告警配置
- [ ] 用户文档和帮助说明
- [ ] 灰度发布和全量上线

## 13. 后续优化方向

### 13.1 功能增强
- **批量下载**: 支持多个视频的批量下载
- **下载管理**: 用户下载历史和管理界面
- **离线下载**: 大文件的后台下载通知机制

### 13.2 商业化优化
- **下载积分系统**: 按次数消费的灵活计费模式
- **会员专属**: 不同会员等级的下载特权
- **企业功能**: 企业用户的批量授权和管理

### 13.3 技术升级
- **边缘计算**: 利用边缘节点加速下载
- **AI优化**: 智能压缩和格式转换
- **区块链验证**: 内容版权和下载记录上链

---

*本文档会根据开发过程中的实际情况持续更新和完善。*