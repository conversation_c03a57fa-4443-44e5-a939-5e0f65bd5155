# Download Without Watermark 功能实施步骤详解

## 前置准备

### 环境要求确认
- [x] Node.js 20+
- [x] PostgreSQL 数据库
- [x] pnpm 包管理器
- [x] 已配置的CDN和对象存储服务

### 项目结构确认
```
video-generator-web/
├── apps/web/                 # Next.js前端应用
├── packages/
│   ├── api/                 # Hono API后端
│   ├── database/            # Prisma数据库
│   └── ...
└── config/                  # 应用配置
```

## 第一阶段：数据库结构扩展

### Step 1.1: 创建下载记录表迁移文件

**位置**: `packages/database/prisma/schema.prisma`

**操作**: 在现有schema末尾添加新的model定义

```sql
// 在schema.prisma文件末尾添加
model DownloadRecord {
  id                String     @id @default(cuid())
  userId            String
  generationId      String
  downloadType      String     // "watermark" | "no_watermark"
  downloadedAt      DateTime   @default(now())
  ipAddress         String?
  userAgent         String?
  
  user              User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  generation        Generation @relation(fields: [generationId], references: [id], onDelete: Cascade)
  
  @@index([userId])
  @@index([generationId])
  @@index([downloadedAt])
  @@index([downloadType])
  @@map("download_record")
}
```

**命令执行**:
```bash
# 从项目根目录执行
cd packages/database
pnpm prisma db push
pnpm prisma generate
```

### Step 1.2: 更新User模型关联

**位置**: `packages/database/prisma/schema.prisma`

**操作**: 在User model中添加关联关系

```sql
model User {
  // ... 现有字段
  downloadRecords    DownloadRecord[]  // 新增这一行
  // ... 其他关联
}
```

### Step 1.3: 更新Generation模型关联

**位置**: `packages/database/prisma/schema.prisma`

**操作**: 在Generation model中添加关联关系

```sql
model Generation {
  // ... 现有字段
  downloadRecords    DownloadRecord[]  // 新增这一行
  // ... 其他关联
}
```

**执行数据库更新**:
```bash
# 从项目根目录执行
cd packages/database
pnpm prisma db push
pnpm prisma generate
```

## 第二阶段：后端API实现

### Step 2.1: 创建下载API基础结构

**位置**: `packages/api/src/routes/downloads/`

**创建文件结构**:
```bash
mkdir -p packages/api/src/routes/downloads/lib
```

### Step 2.2: 创建类型定义文件

**文件**: `packages/api/src/routes/downloads/types.ts`

```typescript
import { z } from "zod";

// 下载请求类型枚举
export const DownloadTypeSchema = z.enum(['watermark', 'no_watermark']);
export type DownloadType = z.infer<typeof DownloadTypeSchema>;

// 下载请求参数
export const DownloadRequestSchema = z.object({
  generationId: z.string().cuid(),
  type: DownloadTypeSchema,
});
export type DownloadRequest = z.infer<typeof DownloadRequestSchema>;

// 下载响应数据
export const DownloadResponseDataSchema = z.object({
  downloadUrl: z.string().url(),
  expiresAt: z.string().datetime(),
  filename: z.string(),
  fileSize: z.number().optional(),
});
export type DownloadResponseData = z.infer<typeof DownloadResponseDataSchema>;

// 成功响应
export const DownloadResponseSchema = z.object({
  success: z.literal(true),
  data: DownloadResponseDataSchema,
});
export type DownloadResponse = z.infer<typeof DownloadResponseSchema>;

// 错误响应
export const DownloadErrorResponseSchema = z.object({
  success: z.literal(false),
  error: z.string(),
  errorCode: z.string().optional(),
});
export type DownloadErrorResponse = z.infer<typeof DownloadErrorResponseSchema>;

// 权限检查结果
export interface PermissionCheckResult {
  canDownload: boolean;
  reason?: string;
  errorCode?: string;
}

// 用户订阅信息
export interface UserSubscriptionInfo {
  planType: 'free' | 'pro' | 'lifetime' | 'enterprise';
  isActive: boolean;
  expiresAt?: Date;
}
```

### Step 2.3: 创建权限验证服务

**文件**: `packages/api/src/routes/downloads/lib/permission-service.ts`

```typescript
import { db } from "@repo/database";
import type { User } from "@repo/database";
import type { PermissionCheckResult, UserSubscriptionInfo } from "../types";

export class PermissionService {
  /**
   * 检查用户是否可以下载无水印内容
   */
  static async canDownloadWithoutWatermark(
    user: User,
    generationId: string
  ): Promise<PermissionCheckResult> {
    try {
      // 1. 获取Generation信息
      const generation = await db.generation.findUnique({
        where: { id: generationId },
        select: {
          id: true,
          userId: true,
          videoUrlNoWatermark: true,
          publishStatus: true,
          canRead: true,
          status: true,
        },
      });

      if (!generation) {
        return {
          canDownload: false,
          reason: "内容不存在或已被删除",
          errorCode: "CONTENT_NOT_FOUND",
        };
      }

      // 2. 检查内容状态
      if (generation.status !== "succeeded") {
        return {
          canDownload: false,
          reason: "内容尚未生成完成",
          errorCode: "CONTENT_NOT_READY",
        };
      }

      // 3. 检查无水印版本是否存在
      if (!generation.videoUrlNoWatermark) {
        return {
          canDownload: false,
          reason: "该内容暂无无水印版本",
          errorCode: "NO_WATERMARK_VERSION",
        };
      }

      // 4. 检查访问权限
      const hasAccess = await this.checkContentAccess(user, generation);
      if (!hasAccess.canAccess) {
        return {
          canDownload: false,
          reason: hasAccess.reason,
          errorCode: "ACCESS_DENIED",
        };
      }

      // 5. 检查用户订阅状态
      const subscription = await this.getUserSubscription(user.id);
      if (!this.canDownloadWithoutWatermarkByPlan(subscription)) {
        return {
          canDownload: false,
          reason: "需要升级到Pro版本才能下载无水印内容",
          errorCode: "SUBSCRIPTION_REQUIRED",
        };
      }

      return { canDownload: true };
    } catch (error) {
      console.error("[PermissionService] Error checking download permission:", error);
      return {
        canDownload: false,
        reason: "权限检查失败",
        errorCode: "PERMISSION_CHECK_ERROR",
      };
    }
  }

  /**
   * 检查用户对内容的访问权限
   */
  private static async checkContentAccess(
    user: User,
    generation: any
  ): Promise<{ canAccess: boolean; reason?: string }> {
    // 用户可以访问自己的内容
    if (generation.userId === user.id) {
      return { canAccess: true };
    }

    // 管理员可以访问所有内容
    if (user.role?.includes("admin")) {
      return { canAccess: true };
    }

    // 检查是否为公开发布的内容
    if (generation.publishStatus === "published" && generation.canRead) {
      return { canAccess: true };
    }

    return {
      canAccess: false,
      reason: "您没有权限访问此内容",
    };
  }

  /**
   * 获取用户订阅信息
   */
  private static async getUserSubscription(userId: string): Promise<UserSubscriptionInfo> {
    try {
      const subscription = await db.subscription.findFirst({
        where: {
          userId,
          status: "active",
        },
        include: {
          plan: true,
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      if (!subscription) {
        return { planType: "free", isActive: false };
      }

      // 根据plan的priceId或其他字段判断计划类型
      const planType = this.determinePlanType(subscription.plan);

      return {
        planType,
        isActive: subscription.status === "active",
        expiresAt: subscription.currentPeriodEnd || undefined,
      };
    } catch (error) {
      console.error("[PermissionService] Error getting user subscription:", error);
      return { planType: "free", isActive: false };
    }
  }

  /**
   * 根据计划配置判断计划类型
   */
  private static determinePlanType(plan: any): 'free' | 'pro' | 'lifetime' | 'enterprise' {
    // 这里需要根据实际的plan配置来判断
    // 可以根据priceId、productId或其他标识字段来区分
    const { priceId, productId, amount } = plan;

    // 根据config中的配置判断
    if (priceId === process.env.NEXT_PUBLIC_PRICE_ID_PRO_MONTHLY || 
        priceId === process.env.NEXT_PUBLIC_PRICE_ID_PRO_YEARLY) {
      return 'pro';
    }
    
    if (priceId === process.env.NEXT_PUBLIC_PRICE_ID_LIFETIME) {
      return 'lifetime';
    }

    // 可以添加更多判断逻辑...

    return 'free';
  }

  /**
   * 根据订阅计划判断是否可以下载无水印内容
   */
  private static canDownloadWithoutWatermarkByPlan(subscription: UserSubscriptionInfo): boolean {
    const allowedPlans = ['pro', 'lifetime', 'enterprise'];
    return subscription.isActive && allowedPlans.includes(subscription.planType);
  }
}
```

### Step 2.4: 创建下载服务

**文件**: `packages/api/src/routes/downloads/lib/download-service.ts`

```typescript
import { db } from "@repo/database";
import type { User } from "@repo/database";
import type {
  DownloadRequest,
  DownloadResponse,
  DownloadErrorResponse,
  DownloadType,
} from "../types";
import { PermissionService } from "./permission-service";
import { generateSignedUrl, getFileInfo } from "./storage-service";

export class DownloadService {
  /**
   * 处理下载请求
   */
  static async handleDownload(
    user: User,
    params: DownloadRequest,
    clientInfo: { ipAddress?: string; userAgent?: string }
  ): Promise<DownloadResponse | DownloadErrorResponse> {
    try {
      const { generationId, type } = params;

      // 1. 权限检查
      if (type === "no_watermark") {
        const permissionCheck = await PermissionService.canDownloadWithoutWatermark(
          user,
          generationId
        );

        if (!permissionCheck.canDownload) {
          return {
            success: false,
            error: permissionCheck.reason || "下载权限不足",
            errorCode: permissionCheck.errorCode,
          };
        }
      }

      // 2. 获取文件URL
      const fileUrl = await this.getFileUrl(generationId, type);
      if (!fileUrl) {
        return {
          success: false,
          error: "文件不存在或已过期",
          errorCode: "FILE_NOT_FOUND",
        };
      }

      // 3. 生成签名下载URL
      const downloadUrl = await generateSignedUrl(fileUrl, {
        expiresIn: 30 * 60, // 30分钟过期
        filename: this.generateFilename(generationId, type),
      });

      // 4. 获取文件信息
      const fileInfo = await getFileInfo(fileUrl);

      // 5. 记录下载行为
      await this.recordDownload(user.id, generationId, type, clientInfo);

      // 6. 更新下载统计
      await this.updateDownloadStats(generationId);

      const expiresAt = new Date(Date.now() + 30 * 60 * 1000); // 30分钟后

      return {
        success: true,
        data: {
          downloadUrl,
          expiresAt: expiresAt.toISOString(),
          filename: this.generateFilename(generationId, type),
          fileSize: fileInfo?.size,
        },
      };
    } catch (error) {
      console.error("[DownloadService] Error handling download:", error);
      return {
        success: false,
        error: "下载服务异常，请稍后重试",
        errorCode: "DOWNLOAD_SERVICE_ERROR",
      };
    }
  }

  /**
   * 获取文件URL
   */
  private static async getFileUrl(
    generationId: string,
    type: DownloadType
  ): Promise<string | null> {
    const generation = await db.generation.findUnique({
      where: { id: generationId },
      select: {
        videoUrl: true,
        videoUrlNoWatermark: true,
      },
    });

    if (!generation) {
      return null;
    }

    return type === "watermark" 
      ? generation.videoUrl 
      : generation.videoUrlNoWatermark;
  }

  /**
   * 生成文件名
   */
  private static generateFilename(generationId: string, type: DownloadType): string {
    const timestamp = new Date().toISOString().split('T')[0];
    const suffix = type === "watermark" ? "watermark" : "no-watermark";
    return `video-${generationId.slice(-8)}-${suffix}-${timestamp}.mp4`;
  }

  /**
   * 记录下载行为
   */
  private static async recordDownload(
    userId: string,
    generationId: string,
    type: DownloadType,
    clientInfo: { ipAddress?: string; userAgent?: string }
  ): Promise<void> {
    try {
      await db.downloadRecord.create({
        data: {
          userId,
          generationId,
          downloadType: type,
          ipAddress: clientInfo.ipAddress,
          userAgent: clientInfo.userAgent,
        },
      });
    } catch (error) {
      // 记录下载失败不应该阻塞下载流程
      console.error("[DownloadService] Error recording download:", error);
    }
  }

  /**
   * 更新下载统计
   */
  private static async updateDownloadStats(generationId: string): Promise<void> {
    try {
      await db.generation.update({
        where: { id: generationId },
        data: {
          downloadNum: {
            increment: 1,
          },
        },
      });
    } catch (error) {
      // 统计更新失败不应该阻塞下载流程
      console.error("[DownloadService] Error updating download stats:", error);
    }
  }
}
```

### Step 2.5: 创建存储服务

**文件**: `packages/api/src/routes/downloads/lib/storage-service.ts`

```typescript
import { config } from "@repo/config";
import { createHmac } from "crypto";

interface SignedUrlOptions {
  expiresIn: number; // 过期时间（秒）
  filename?: string; // 建议的文件名
}

interface FileInfo {
  size?: number;
  contentType?: string;
  lastModified?: Date;
}

/**
 * 生成签名下载URL
 */
export async function generateSignedUrl(
  fileUrl: string,
  options: SignedUrlOptions
): Promise<string> {
  const { expiresIn, filename } = options;
  const expires = Math.floor(Date.now() / 1000) + expiresIn;
  
  // 构建签名参数
  const params = new URLSearchParams();
  params.set('expires', expires.toString());
  
  if (filename) {
    params.set('filename', filename);
  }

  // 生成签名
  const signature = generateSignature(fileUrl, expires, filename);
  params.set('signature', signature);

  // 构建最终URL
  const separator = fileUrl.includes('?') ? '&' : '?';
  return `${fileUrl}${separator}${params.toString()}`;
}

/**
 * 生成URL签名
 */
function generateSignature(fileUrl: string, expires: number, filename?: string): string {
  const secret = process.env.DOWNLOAD_URL_SECRET || 'default-secret';
  const data = `${fileUrl}:${expires}:${filename || ''}`;
  
  return createHmac('sha256', secret)
    .update(data)
    .digest('hex')
    .substring(0, 16); // 取前16位
}

/**
 * 获取文件信息
 */
export async function getFileInfo(fileUrl: string): Promise<FileInfo | null> {
  try {
    // 发送HEAD请求获取文件信息
    const response = await fetch(fileUrl, { method: 'HEAD' });
    
    if (!response.ok) {
      return null;
    }

    return {
      size: response.headers.get('content-length') 
        ? parseInt(response.headers.get('content-length')!) 
        : undefined,
      contentType: response.headers.get('content-type') || undefined,
      lastModified: response.headers.get('last-modified') 
        ? new Date(response.headers.get('last-modified')!) 
        : undefined,
    };
  } catch (error) {
    console.error('[StorageService] Error getting file info:', error);
    return null;
  }
}

/**
 * 验证签名URL的有效性
 */
export function validateSignedUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    const expires = urlObj.searchParams.get('expires');
    const signature = urlObj.searchParams.get('signature');
    const filename = urlObj.searchParams.get('filename');

    if (!expires || !signature) {
      return false;
    }

    // 检查是否过期
    const expiresTime = parseInt(expires);
    if (Date.now() / 1000 > expiresTime) {
      return false;
    }

    // 验证签名
    const baseUrl = url.split('?')[0];
    const expectedSignature = generateSignature(baseUrl, expiresTime, filename);
    
    return signature === expectedSignature;
  } catch (error) {
    return false;
  }
}
```

### Step 2.6: 创建API路由

**文件**: `packages/api/src/routes/downloads/router.ts`

```typescript
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";
import { DownloadService } from "./lib/download-service";
import { DownloadRequestSchema } from "./types";

export const downloadsRouter = new Hono()
  .basePath("/downloads")
  
  // GET /api/downloads/generations/:id - 生成下载链接
  .get(
    "/generations/:id",
    authMiddleware,
    describeRoute({
      tags: ["Downloads"],
      summary: "Generate download link for generation",
      description: "Generate a signed download link for a specific generation. Supports both watermark and no-watermark downloads based on user subscription.",
      parameters: [
        {
          name: "id",
          in: "path",
          description: "Generation ID",
          schema: { type: "string" },
          required: true,
        },
        {
          name: "type",
          in: "query",
          description: "Download type",
          schema: { 
            type: "string",
            enum: ["watermark", "no_watermark"],
            default: "watermark"
          },
          required: false,
        },
      ],
      responses: {
        200: {
          description: "Download link generated successfully",
          content: {
            'application/json': {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean", enum: [true] },
                  data: {
                    type: "object",
                    properties: {
                      downloadUrl: { type: "string", format: "uri" },
                      expiresAt: { type: "string", format: "date-time" },
                      filename: { type: "string" },
                      fileSize: { type: "number" },
                    },
                    required: ["downloadUrl", "expiresAt", "filename"],
                  },
                },
                required: ["success", "data"],
              },
            },
          },
        },
        400: {
          description: "Bad request - Invalid parameters",
        },
        401: {
          description: "Unauthorized - Authentication required",
        },
        403: {
          description: "Forbidden - Insufficient permissions",
        },
        404: {
          description: "Not found - Generation not found",
        },
        500: {
          description: "Internal server error",
        },
      },
    }),
    validator("query", z.object({
      type: z.enum(["watermark", "no_watermark"]).default("watermark"),
    })),
    async (c) => {
      const user = c.get("user");
      const generationId = c.req.param("id");
      const { type } = c.req.valid("query");

      // 获取客户端信息
      const clientInfo = {
        ipAddress: c.req.header("cf-connecting-ip") || 
                  c.req.header("x-forwarded-for") || 
                  c.req.header("x-real-ip"),
        userAgent: c.req.header("user-agent"),
      };

      console.log(`[DownloadsAPI] User ${user.id} requesting ${type} download for generation ${generationId}`);

      const result = await DownloadService.handleDownload(
        user,
        { generationId, type },
        clientInfo
      );

      if (result.success) {
        console.log(`[DownloadsAPI] Download link generated for user ${user.id}, generation ${generationId}`);
        return c.json(result, 200);
      } else {
        console.warn(`[DownloadsAPI] Download failed for user ${user.id}:`, result.error);
        
        // 根据错误码返回相应的状态码
        switch (result.errorCode) {
          case "CONTENT_NOT_FOUND":
            return c.json(result, 404);
          case "ACCESS_DENIED":
            return c.json(result, 403);
          case "SUBSCRIPTION_REQUIRED":
            return c.json(result, 403);
          case "CONTENT_NOT_READY":
          case "NO_WATERMARK_VERSION":
            return c.json(result, 400);
          default:
            return c.json(result, 500);
        }
      }
    }
  );

export type DownloadsRouter = typeof downloadsRouter;
```

### Step 2.7: 注册路由

**文件**: `packages/api/src/routes/downloads/index.ts`

```typescript
export { downloadsRouter } from "./router";
export type { DownloadsRouter } from "./router";
export * from "./types";
```

**文件**: `packages/api/src/app.ts`

在现有路由注册部分添加：

```typescript
import { downloadsRouter } from "./routes/downloads";

// 在路由注册部分添加
app.route("/api", downloadsRouter);
```

## 第三阶段：前端集成

### Step 3.1: 创建下载Hook

**文件**: `apps/web/modules/marketing/shared/hooks/useDownload.ts`

```typescript
"use client";

import { useState } from "react";
import { useSession } from "@repo/auth/client";

interface DownloadOptions {
  generationId: string;
  type: 'watermark' | 'no_watermark';
}

interface DownloadResult {
  success: boolean;
  data?: {
    downloadUrl: string;
    expiresAt: string;
    filename: string;
    fileSize?: number;
  };
  error?: string;
  errorCode?: string;
}

export function useDownload() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { data: session } = useSession();

  const download = async (options: DownloadOptions): Promise<DownloadResult> => {
    if (!session?.user) {
      return {
        success: false,
        error: "请先登录",
        errorCode: "NOT_AUTHENTICATED",
      };
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(
        `/api/downloads/generations/${options.generationId}?type=${options.type}`,
        {
          method: "GET",
          headers: {
            "Authorization": `Bearer ${session.accessToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      const result: DownloadResult = await response.json();

      if (result.success && result.data) {
        // 触发浏览器下载
        const link = document.createElement('a');
        link.href = result.data.downloadUrl;
        link.download = result.data.filename;
        link.target = '_blank';
        link.rel = 'noopener noreferrer';
        
        // 添加到DOM并触发点击
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        return result;
      } else {
        setError(result.error || "下载失败");
        return result;
      }
    } catch (err) {
      const errorMessage = "网络错误，请稍后重试";
      setError(errorMessage);
      console.error("Download error:", err);
      
      return {
        success: false,
        error: errorMessage,
        errorCode: "NETWORK_ERROR",
      };
    } finally {
      setLoading(false);
    }
  };

  return {
    download,
    loading,
    error,
    clearError: () => setError(null),
  };
}
```

### Step 3.2: 创建用户订阅状态Hook

**文件**: `apps/web/modules/marketing/shared/hooks/useUserSubscription.ts`

```typescript
"use client";

import { useState, useEffect } from "react";
import { useSession } from "@repo/auth/client";

interface UserSubscription {
  planType: 'free' | 'pro' | 'lifetime' | 'enterprise';
  isActive: boolean;
  expiresAt?: string;
  canDownloadWithoutWatermark: boolean;
}

export function useUserSubscription() {
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [loading, setLoading] = useState(true);
  const { data: session } = useSession();

  useEffect(() => {
    if (!session?.user) {
      setSubscription({
        planType: 'free',
        isActive: false,
        canDownloadWithoutWatermark: false,
      });
      setLoading(false);
      return;
    }

    // 这里可以调用API获取用户订阅信息
    // 暂时基于本地逻辑判断
    fetchUserSubscription();
  }, [session]);

  const fetchUserSubscription = async () => {
    try {
      // TODO: 实现API调用获取订阅信息
      // const response = await fetch('/api/user/subscription');
      // const data = await response.json();
      
      // 临时模拟数据
      const mockSubscription: UserSubscription = {
        planType: 'free', // 可以根据session中的信息判断
        isActive: false,
        canDownloadWithoutWatermark: false,
      };

      setSubscription(mockSubscription);
    } catch (error) {
      console.error('Error fetching subscription:', error);
      // 错误时默认为免费用户
      setSubscription({
        planType: 'free',
        isActive: false,
        canDownloadWithoutWatermark: false,
      });
    } finally {
      setLoading(false);
    }
  };

  return {
    subscription,
    loading,
    refresh: fetchUserSubscription,
  };
}
```

### Step 3.3: 更新ActionMenu组件

**文件**: `apps/web/modules/marketing/shared/components/GenerationItemCard/components/ActionMenu.tsx`

更新现有的下载按钮点击处理：

```typescript
// 在文件顶部添加导入
import { useDownload } from "../../../hooks/useDownload";
import { useUserSubscription } from "../../../hooks/useUserSubscription";
import { useState } from "react";

// 在组件内部添加hooks
export function ActionMenu({
  generation,
  actions,
  visible,
  className,
}: ActionMenuProps) {
  // ... 现有代码
  
  const { download, loading: downloadLoading } = useDownload();
  const { subscription } = useUserSubscription();
  const [showUpgradePrompt, setShowUpgradePrompt] = useState(false);

  // 处理带水印下载
  const handleDownloadWithWatermark = async () => {
    try {
      const result = await download({
        generationId: generation.id,
        type: 'watermark',
      });

      if (result.success) {
        // 可以显示成功提示
        console.log('下载开始');
      } else {
        // 显示错误信息
        console.error('下载失败:', result.error);
      }
    } catch (error) {
      console.error('下载异常:', error);
    }
  };

  // 处理无水印下载
  const handleDownloadWithoutWatermark = async () => {
    // 检查用户权限
    if (!subscription?.canDownloadWithoutWatermark) {
      setShowUpgradePrompt(true);
      return;
    }

    try {
      const result = await download({
        generationId: generation.id,
        type: 'no_watermark',
      });

      if (result.success) {
        console.log('无水印下载开始');
      } else {
        if (result.errorCode === 'SUBSCRIPTION_REQUIRED') {
          setShowUpgradePrompt(true);
        } else {
          console.error('下载失败:', result.error);
        }
      }
    } catch (error) {
      console.error('下载异常:', error);
    }
  };

  // 在现有的下载子菜单部分更新onClick处理
  // 修改第228行和第239行的onClick处理器：

  // 带水印下载按钮
  <button
    type="button"
    onClick={handleDownloadWithWatermark}
    disabled={downloadLoading}
    className="flex items-center gap-2 w-full px-2.5 py-1.5 text-xs rounded cursor-pointer transition-colors text-left whitespace-nowrap text-white hover:bg-gray-700/80 border-none outline-none focus:outline-none focus:ring-0 disabled:opacity-50"
  >
    <div className="relative w-3.5 h-3.5 flex items-center justify-center">
      <Download className="w-3 h-3" />
      <Tag className="w-1.5 h-1.5 absolute -top-0.5 -right-0.5 text-yellow-400" />
    </div>
    <span>{downloadLoading ? '下载中...' : 'Download with watermark'}</span>
  </button>

  // 无水印下载按钮
  <button
    type="button"
    onClick={handleDownloadWithoutWatermark}
    disabled={downloadLoading}
    className="flex items-center gap-2 w-full px-2.5 py-1.5 text-xs rounded cursor-pointer transition-colors text-left whitespace-nowrap text-white hover:bg-gray-700/80 border-none outline-none focus:outline-none focus:ring-0 disabled:opacity-50"
  >
    <div className="relative w-3.5 h-3.5 flex items-center justify-center">
      <Download className="w-3.5 h-3.5 text-green-400" />
    </div>
    <span>{downloadLoading ? '下载中...' : 'Download without watermark'}</span>
  </button>

  // ... 现有代码的其余部分
}
```

### Step 3.4: 创建升级提示组件

**文件**: `apps/web/modules/marketing/shared/components/UpgradePrompt.tsx`

```typescript
"use client";

import { Button } from "@ui/components/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@ui/components/dialog";
import { Crown, Download, X } from "lucide-react";

interface UpgradePromptProps {
  open: boolean;
  onClose: () => void;
  onUpgrade: () => void;
  feature?: string;
}

export function UpgradePrompt({ 
  open, 
  onClose, 
  onUpgrade, 
  feature = "无水印下载" 
}: UpgradePromptProps) {
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-2">
            <Crown className="w-5 h-5 text-yellow-500" />
            <DialogTitle>升级到Pro版本</DialogTitle>
          </div>
          <DialogDescription className="text-left">
            解锁{feature}功能，享受完整的视频生成体验
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 rounded-lg p-4 mb-4">
            <div className="flex items-start gap-3">
              <Download className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-sm mb-1">无水印下载</h4>
                <p className="text-xs text-muted-foreground">
                  下载高质量、无水印的视频内容，用于商业用途
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-2 text-xs text-muted-foreground">
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
              <span>无限制视频生成</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
              <span>优先处理队列</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
              <span>高级AI模型访问</span>
            </div>
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={onClose} className="flex-1">
            稍后再说
          </Button>
          <Button onClick={onUpgrade} className="flex-1">
            立即升级
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
```

## 第四阶段：测试与部署

### Step 4.1: 单元测试

**文件**: `packages/api/src/routes/downloads/__tests__/permission-service.test.ts`

```typescript
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { PermissionService } from '../lib/permission-service';
import { db } from '@repo/database';

// Mock数据库
vi.mock('@repo/database');

describe('PermissionService', () => {
  beforeEach(() => {
    // 重置mocks
    vi.clearAllMocks();
  });

  describe('canDownloadWithoutWatermark', () => {
    it('should allow pro users to download without watermark', async () => {
      // Mock数据
      const mockUser = { id: 'user-1', role: null };
      const mockGeneration = {
        id: 'gen-1',
        userId: 'user-1',
        videoUrlNoWatermark: 'https://example.com/video.mp4',
        publishStatus: 'published',
        canRead: true,
        status: 'succeeded',
      };
      const mockSubscription = {
        status: 'active',
        plan: { priceId: process.env.NEXT_PUBLIC_PRICE_ID_PRO_MONTHLY },
      };

      // Mock数据库查询
      (db.generation.findUnique as any).mockResolvedValue(mockGeneration);
      (db.subscription.findFirst as any).mockResolvedValue(mockSubscription);

      const result = await PermissionService.canDownloadWithoutWatermark(
        mockUser as any,
        'gen-1'
      );

      expect(result.canDownload).toBe(true);
    });

    it('should deny free users to download without watermark', async () => {
      const mockUser = { id: 'user-1', role: null };
      const mockGeneration = {
        id: 'gen-1',
        userId: 'user-1',
        videoUrlNoWatermark: 'https://example.com/video.mp4',
        publishStatus: 'published',
        canRead: true,
        status: 'succeeded',
      };

      (db.generation.findUnique as any).mockResolvedValue(mockGeneration);
      (db.subscription.findFirst as any).mockResolvedValue(null);

      const result = await PermissionService.canDownloadWithoutWatermark(
        mockUser as any,
        'gen-1'
      );

      expect(result.canDownload).toBe(false);
      expect(result.errorCode).toBe('SUBSCRIPTION_REQUIRED');
    });

    // 更多测试用例...
  });
});
```

### Step 4.2: 集成测试

**文件**: `packages/api/src/routes/downloads/__tests__/router.test.ts`

```typescript
import { describe, it, expect, beforeEach } from 'vitest';
import request from 'supertest';
import { app } from '../../../app';

describe('Downloads API', () => {
  let authToken: string;

  beforeEach(async () => {
    // 获取测试用户的认证token
    authToken = await getTestUserToken();
  });

  describe('GET /api/downloads/generations/:id', () => {
    it('should generate download link for watermark version', async () => {
      const response = await request(app)
        .get('/api/downloads/generations/test-gen-id?type=watermark')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('downloadUrl');
      expect(response.body.data).toHaveProperty('expiresAt');
      expect(response.body.data).toHaveProperty('filename');
    });

    it('should require authentication', async () => {
      await request(app)
        .get('/api/downloads/generations/test-gen-id')
        .expect(401);
    });

    // 更多集成测试...
  });
});

async function getTestUserToken(): Promise<string> {
  // 实现获取测试token的逻辑
  return 'test-token';
}
```

### Step 4.3: 环境变量配置

**文件**: `.env.example`

添加必要的环境变量：

```bash
# 下载URL签名密钥
DOWNLOAD_URL_SECRET=your-secret-key-here

# CDN配置
NEXT_PUBLIC_CDN_BASE_URL=https://cdn.yourdomain.com
NEXT_PUBLIC_VIDEO_CDN_BASE_URL=https://videocdn.yourdomain.com

# 订阅计划价格ID
NEXT_PUBLIC_PRICE_ID_PRO_MONTHLY=price_xxx
NEXT_PUBLIC_PRICE_ID_PRO_YEARLY=price_xxx
NEXT_PUBLIC_PRICE_ID_LIFETIME=price_xxx
```

### Step 4.4: 数据库迁移

**命令序列**:

```bash
# 1. 生成并应用数据库迁移
cd packages/database
pnpm prisma db push

# 2. 生成Prisma客户端
pnpm prisma generate

# 3. 验证数据库结构
pnpm prisma studio # 可选，用于查看数据库结构
```

### Step 4.5: 本地测试

**测试脚本**: `test-download.sh`

```bash
#!/bin/bash

# 测试下载API的脚本
echo "Testing Download API..."

# 1. 测试带水印下载
echo "Testing watermark download..."
curl -X GET "http://localhost:3000/api/downloads/generations/test-id?type=watermark" \
  -H "Authorization: Bearer YOUR_TEST_TOKEN" \
  -H "Content-Type: application/json"

echo -e "\n"

# 2. 测试无水印下载
echo "Testing no-watermark download..."
curl -X GET "http://localhost:3000/api/downloads/generations/test-id?type=no_watermark" \
  -H "Authorization: Bearer YOUR_TEST_TOKEN" \
  -H "Content-Type: application/json"

echo -e "\n"

# 3. 测试权限验证
echo "Testing unauthorized access..."
curl -X GET "http://localhost:3000/api/downloads/generations/test-id?type=no_watermark" \
  -H "Content-Type: application/json"

echo "Test completed!"
```

### Step 4.6: 部署前检查清单

- [ ] 数据库迁移已应用
- [ ] 环境变量已配置
- [ ] API端点测试通过  
- [ ] 权限验证测试通过
- [ ] 前端UI集成测试通过
- [ ] CDN和存储服务配置正确
- [ ] 监控和日志配置就绪
- [ ] 错误处理和用户反馈完善

## 第五阶段：监控与优化

### Step 5.1: 添加监控指标

**文件**: `packages/api/src/routes/downloads/lib/metrics.ts`

```typescript
// 下载相关指标收集
export class DownloadMetrics {
  static recordDownloadRequest(type: 'watermark' | 'no_watermark', success: boolean) {
    // 实现指标记录逻辑
    console.log(`Download request: type=${type}, success=${success}`);
  }

  static recordDownloadDuration(duration: number) {
    // 记录下载处理时间
    console.log(`Download duration: ${duration}ms`);
  }

  static recordSubscriptionCheck(planType: string, allowed: boolean) {
    // 记录订阅检查结果
    console.log(`Subscription check: plan=${planType}, allowed=${allowed}`);
  }
}
```

### Step 5.2: 性能优化

**缓存实现**: `packages/api/src/routes/downloads/lib/cache.ts`

```typescript
import { LRUCache } from 'lru-cache';

// 用户订阅信息缓存
const subscriptionCache = new LRUCache<string, any>({
  max: 1000,
  ttl: 10 * 60 * 1000, // 10分钟
});

export function getCachedSubscription(userId: string) {
  return subscriptionCache.get(userId);
}

export function setCachedSubscription(userId: string, subscription: any) {
  subscriptionCache.set(userId, subscription);
}

// 下载URL缓存
const downloadUrlCache = new LRUCache<string, string>({
  max: 500,
  ttl: 5 * 60 * 1000, // 5分钟
});

export function getCachedDownloadUrl(key: string) {
  return downloadUrlCache.get(key);
}

export function setCachedDownloadUrl(key: string, url: string) {
  downloadUrlCache.set(key, url);
}
```

## 总结

通过以上详细的实施步骤，您可以完整地实现"Download without watermark"功能。整个实施过程分为五个阶段：

1. **数据库结构扩展**: 添加下载记录表和相关索引
2. **后端API实现**: 实现权限验证、下载服务和API路由
3. **前端集成**: 更新UI组件、添加下载逻辑和用户反馈
4. **测试与部署**: 单元测试、集成测试和部署配置
5. **监控与优化**: 添加监控指标和性能优化

每个步骤都包含了具体的代码实现和配置说明，确保功能的完整性和可靠性。建议按照阶段顺序逐步实施，并在每个阶段完成后进行充分的测试验证。