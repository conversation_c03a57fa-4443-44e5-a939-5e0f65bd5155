# GenerationItemCard UI组件设计方案

## 1. 概述

本文档详细描述了 `GenerationItemCard` UI组件的设计方案和实施步骤。该组件为可重用组件，主要用于 `/my-generations` 页面展示用户的视频生成历史记录，同时也可以在其他需要展示视频生成内容的页面中使用，提供丰富的交互功能。

## 2. 设计需求分析

### 2.1 界面状态

基于提供的设计图片，组件需要支持三种主要状态：

1. **默认状态** (图1)：
   - 视频缩略图作为背景
   - 左上角发布状态标签（Unpublished/Published）
   - 右上角收藏按钮
   - 底部用户信息（头像 + 用户名）

2. **悬停状态** (图2)：
   - 用户信息区域显示渐变背景
   - 显示紫色渐变的"Create Similar Video"按钮
   - 右下角显示三个点(...)操作菜单图标
   - 保持收藏按钮可见

3. **菜单展开状态** (图3)：
   - 显示下拉操作菜单，包含：
     - Publish（发布）
     - Add to favorite（添加到收藏）
     - Download（下载）
     - Share（分享）
     - Copy Link（复制链接）
     - Delete（删除）

### 2.2 数据模型

基于 `Generation` 数据模型，组件需要处理以下关键字段：

```typescript
interface GenerationData {
  id: string;
  userId: string;
  cover?: string;           // 视频封面
  thumbnail?: string;       // 缩略图
  videoUrl?: string;        // 视频URL
  mediaUrl?: string;        // 媒体URL
  publishStatus?: 'reviewing' | 'published' | 'rejected';
  favorite: boolean;        // 收藏状态
  status: 'waiting' | 'processing' | 'succeeded' | 'failed';
  canPublish: boolean;      // 是否可发布
  canDelete: boolean;       // 是否可删除
  canCreateSimilar: boolean; // 是否可创建相似视频
  createdAt: Date;
  user: {
    name: string;
    image?: string;
  };
}
```

## 3. 技术架构

### 3.1 技术栈
- **框架**: Next.js 15 with React 19
- **样式**: Tailwind CSS
- **UI组件**: shadcn/ui (Radix UI)
- **图标**: Lucide React
- **类型**: TypeScript

### 3.2 组件结构

组件位于 `apps/web/modules/marketing/shared/components/` 目录下，便于跨模块重用：

```
GenerationItemCard/
├── GenerationItemCard.tsx          # 主组件
├── components/
│   ├── VideoPreview.tsx           # 视频预览组件
│   ├── UserInfo.tsx               # 用户信息组件
│   ├── ActionButtons.tsx          # 操作按钮组件
│   └── StatusBadge.tsx            # 状态标签组件
├── hooks/
│   ├── useCardActions.ts          # 卡片操作逻辑
│   └── useVideoPlay.ts            # 视频播放控制
└── types.ts                       # 类型定义
```

## 4. 详细设计

### 4.1 主组件结构

```typescript
interface GenerationItemCardProps {
  generation: GenerationData;
  onPublish?: (id: string) => void;
  onFavorite?: (id: string) => void;
  onDownload?: (id: string) => void;
  onShare?: (id: string) => void;
  onCopyLink?: (id: string) => void;
  onDelete?: (id: string) => void;
  onCreateSimilar?: (id: string) => void;
  className?: string;
}
```

### 4.2 布局设计

采用相对定位的容器，包含以下层级：

1. **视频背景层**: 固定宽高比的视频/图片容器
2. **渐变遮罩层**: 底部渐变遮罩，提供文字可读性
3. **状态标签层**: 左上角发布状态
4. **收藏按钮层**: 右上角固定位置
5. **用户信息层**: 底部用户头像和姓名
6. **悬停操作层**: 仅在悬停时显示的操作按钮

### 4.3 样式规范

```css
/* 容器尺寸 */
.card-container {
  width: 279px;
  height: 206.667px;
  border-radius: 0.75rem; /* rounded-lg */
}

/* 视频区域 */
.video-area {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 0.75rem;
  overflow: hidden;
}

/* 渐变遮罩 */
.gradient-overlay {
  background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.8) 100%);
}

/* 创建相似视频按钮渐变 */
.create-similar-btn {
  background: linear-gradient(85.93deg, #c721ff 0%, #ff3466 100%);
}
```

### 4.4 交互逻辑

#### 4.4.1 悬停效果

```typescript
const [isHovered, setIsHovered] = useState(false);

const handleMouseEnter = () => {
  setIsHovered(true);
  // 触发视频播放（如果是视频）
  if (videoRef.current) {
    videoRef.current.play().catch(console.error);
  }
};

const handleMouseLeave = () => {
  setIsHovered(false);
  // 暂停视频播放
  if (videoRef.current) {
    videoRef.current.pause();
  }
};
```

#### 4.4.2 下拉菜单

使用 shadcn/ui 的 `DropdownMenu` 组件：

```typescript
const menuItems = [
  {
    icon: <Upload className="w-4 h-4" />,
    label: "Publish",
    action: onPublish,
    show: generation.canPublish && generation.publishStatus !== 'published'
  },
  {
    icon: <Star className="w-4 h-4" />,
    label: generation.favorite ? "Remove from favorite" : "Add to favorite",
    action: onFavorite,
    show: true
  },
  {
    icon: <Download className="w-4 h-4" />,
    label: "Download",
    action: onDownload,
    show: generation.status === 'succeeded'
  },
  {
    icon: <Share2 className="w-4 h-4" />,
    label: "Share",
    action: onShare,
    show: true
  },
  {
    icon: <Link className="w-4 h-4" />,
    label: "Copy Link",
    action: onCopyLink,
    show: true
  },
  {
    icon: <Trash2 className="w-4 h-4" />,
    label: "Delete",
    action: onDelete,
    show: generation.canDelete,
    variant: "destructive"
  }
];
```

## 5. 实施步骤

### 5.1 第一阶段：基础组件开发

1. **创建组件目录结构**
   ```bash
   mkdir -p apps/web/modules/marketing/shared/components/GenerationItemCard
   ```

2. **实现主组件框架**
   - 创建 `GenerationItemCard.tsx`
   - 定义 Props 接口
   - 实现基础布局结构

3. **实现视频预览功能**
   - 处理视频/图片显示逻辑
   - 实现悬停播放功能
   - 添加加载状态处理

### 5.2 第二阶段：交互功能开发

1. **实现状态管理**
   - 悬停状态控制
   - 收藏状态切换
   - 菜单展开状态

2. **实现操作按钮**
   - "Create Similar Video" 按钮
   - 收藏按钮
   - 三点菜单触发器

3. **实现下拉菜单**
   - 使用 shadcn/ui DropdownMenu
   - 条件渲染菜单项
   - 添加操作确认对话框

### 5.3 第三阶段：样式优化

1. **精细化样式调整**
   - 匹配设计图的视觉效果
   - 优化悬停动画
   - 确保响应式设计

2. **无障碍性优化**
   - 添加适当的 ARIA 属性
   - 键盘导航支持
   - 屏幕阅读器友好

### 5.4 第四阶段：集成测试

1. **单元测试**
   - 组件渲染测试
   - 交互行为测试
   - Props 验证测试

2. **集成测试**
   - 与父组件的集成
   - API 调用测试
   - 错误处理测试

## 6. 文件结构

### 6.1 组件文件

```
apps/web/modules/marketing/shared/components/
└── GenerationItemCard/
    ├── index.ts                          # 导出文件
    ├── GenerationItemCard.tsx            # 主组件
    ├── components/
    │   ├── VideoPreview.tsx              # 视频预览
    │   ├── UserInfo.tsx                  # 用户信息
    │   ├── StatusBadge.tsx               # 状态标签
    │   └── ActionMenu.tsx                # 操作菜单
    ├── hooks/
    │   ├── useCardActions.ts             # 卡片操作
    │   └── useVideoPlay.ts               # 视频播放
    └── types.ts                          # 类型定义
```

### 6.2 核心代码结构

```typescript
// GenerationItemCard.tsx
export function GenerationItemCard({ generation, ...actions }: Props) {
  const [isHovered, setIsHovered] = useState(false);
  const { handleFavorite, handleDownload, ... } = useCardActions(generation, actions);
  const { videoRef, isPlaying } = useVideoPlay();

  return (
    <div 
      className="relative w-[279px] h-[206.667px] bg-f-bg-layout group cursor-pointer rounded-lg md:rounded-xl"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* 视频预览区域 */}
      <VideoPreview 
        ref={videoRef}
        src={generation.videoUrl || generation.mediaUrl}
        poster={generation.cover || generation.thumbnail}
        className="size-full rounded-lg md:rounded-xl"
      />
      
      {/* 渐变遮罩 */}
      <div className="absolute bottom-0 left-0 w-full bg-gradient-to-b from-black/0 to-black/80 p-2">
        {/* 用户信息区域 */}
        <UserInfo 
          user={generation.user}
          isHovered={isHovered}
          onCreateSimilar={() => actions.onCreateSimilar?.(generation.id)}
        />
        
        {/* 操作按钮区域 */}
        <div className="flex items-center justify-between gap-1 mt-0 group-hover:mt-3">
          <div className="flex-1" />
          <div className="flex items-center gap-x-2">
            <ActionMenu 
              generation={generation}
              actions={actions}
              visible={isHovered}
            />
            <FavoriteButton 
              isFavorited={generation.favorite}
              onToggle={() => handleFavorite(generation.id)}
            />
          </div>
        </div>
      </div>
      
      {/* 状态标签 */}
      <StatusBadge 
        status={generation.publishStatus}
        className="absolute left-3 top-3"
      />
      
      {/* 收藏按钮（右上角） */}
      <button 
        className="absolute right-2 top-2 hidden group-hover:flex items-center gap-x-1 rounded p-1 transition-all text-f-text hover:bg-f-other-2"
        onClick={() => handleFavorite(generation.id)}
      >
        <Heart className="size-4" fill={generation.favorite ? "currentColor" : "none"} />
      </button>
    </div>
  );
}
```

## 7. 性能优化

1. **懒加载**: 使用 `React.lazy()` 进行组件懒加载
2. **图片优化**: 使用 Next.js `Image` 组件优化图片加载
3. **视频预加载**: 仅在悬停时加载视频
4. **虚拟化**: 对于大量卡片，考虑使用虚拟滚动

## 8. 可访问性考虑

1. **键盘导航**: 支持 Tab 键导航
2. **屏幕阅读器**: 提供适当的 `aria-label` 和 `alt` 文本
3. **焦点管理**: 确保焦点状态清晰可见
4. **颜色对比**: 确保文本和背景有足够的对比度

## 9. 测试策略

### 9.1 单元测试
- 组件渲染测试
- Props 传递测试
- 事件处理测试

### 9.2 集成测试
- 与父组件交互测试
- API 调用测试

### 9.3 E2E 测试
- 用户操作流程测试
- 不同设备尺寸测试

## 10. 使用示例

### 10.1 在 `/my-generations` 页面中使用

```typescript
// apps/web/app/(saas)/my-generations/page.tsx
import { GenerationItemCard } from '@marketing/shared/components/GenerationItemCard';
import { getGenerations } from '@repo/database';

export default async function MyGenerationsPage() {
  const generations = await getGenerations();

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {generations.map((generation) => (
        <GenerationItemCard
          key={generation.id}
          generation={generation}
          onPublish={handlePublish}
          onFavorite={handleFavorite}
          onDownload={handleDownload}
          onShare={handleShare}
          onCopyLink={handleCopyLink}
          onDelete={handleDelete}
          onCreateSimilar={handleCreateSimilar}
        />
      ))}
    </div>
  );
}
```

### 10.2 在其他页面中重用

```typescript
// apps/web/modules/marketing/image-to-video/components/VideoGallery.tsx
import { GenerationItemCard } from '@marketing/shared/components/GenerationItemCard';

export function VideoGallery({ videos }: { videos: Generation[] }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {videos.map((video) => (
        <GenerationItemCard
          key={video.id}
          generation={video}
          // 只启用部分功能
          onFavorite={handleFavorite}
          onShare={handleShare}
          onCreateSimilar={handleCreateSimilar}
        />
      ))}
    </div>
  );
}
```

## 11. 后续优化方向

1. **动画增强**: 添加更流畅的过渡动画
2. **主题支持**: 支持明暗主题切换  
3. **国际化**: 支持多语言
4. **自定义配置**: 允许自定义显示字段和操作
5. **拖拽排序**: 支持卡片拖拽重新排序
6. **虚拟化**: 大量数据时的性能优化

---

本设计方案基于现有的项目架构和设计系统，将组件放置在 `marketing/shared/components` 目录下以便跨模块重用，确保与整体风格保持一致，同时提供丰富的用户交互体验。