# GET /api/generations API 设计方案

## 1. 概述

### 1.1 API 目标
设计一个通用的内容生成历史查询API，支持多种过滤条件和分页功能，返回生成内容、关联任务和用户信息。

### 1.2 适用场景
- 用户查看个人生成历史
- 管理员浏览全平台生成内容
- 内容展示页面的数据获取
- 移动端和Web端的内容列表

## 2. API 规范

### 2.1 基本信息
- **方法**: GET
- **路径**: `/api/generations`
- **认证**: 需要用户认证（authMiddleware）
- **限流**: 应用标准查询限流策略

### 2.2 请求参数

#### 查询参数 (Query Parameters)

| 参数名 | 类型 | 必填 | 默认值 | 说明 | 示例 |
|--------|------|------|--------|------|------|
| `userId` | string | 否 | - | 用户ID过滤，影响jobs和generations查询 | `clp7j2k3x0001js8n2v4m5b6c` |
| `generationType` | string | 否 | - | 生成类型，对应Job.type字段 | `video` \| `image` |
| `pageSize` | number | 否 | 20 | 每页数量，范围: 1-100 | `10` |
| `page` | number | 否 | 1 | 页码，从1开始 | `1` |
| `publishStatus` | string | 否 | - | 发布状态过滤 | `published` \| `reviewing` \| `rejected` |
| `mediaType` | string | 否 | - | 媒体类型过滤，对应Generation.mediaType | `video` \| `image` |
| `favorite` | boolean | 否 | - | 收藏状态过滤 | `true` \| `false` |

#### 参数验证规则

```typescript
const GetGenerationsRequestSchema = z.object({
  userId: z.string().cuid().optional(),
  generationType: z.enum(['video', 'image']).optional(),
  pageSize: z.coerce.number().min(1).max(100).default(20),
  page: z.coerce.number().min(1).default(1),
  publishStatus: z.enum(['reviewing', 'published', 'rejected']).optional(),
  mediaType: z.enum(['video', 'image']).optional(),
  favorite: z.coerce.boolean().optional(),
});
```

### 2.3 响应格式

#### 成功响应 (200 OK)

```json
{
  "success": true,
  "data": {
    "generations": [
      {
        "id": "gen_clp7j2k3x0001js8n2v4m5b6c",
        "userId": "user_clp7j2k3x0001js8n2v4m5b6c",
        "jobId": "job_clp7j2k3x0001js8n2v4m5b6c",
        "mediaId": "media_123456",
        "externalTaskId": "replicate_task_123",
        "cover": "https://cdn.example.com/covers/123.jpg",
        "thumbnail": "https://cdn.example.com/thumbs/123.jpg",
        "videoUrl": "https://cdn.example.com/videos/123.mp4",
        "starNum": 15,
        "shareNum": 3,
        "playNum": 127,
        "downloadNum": 8,
        "videoRatio": "16:9",
        "duration": 5,
        "klVideoId": "kl_video_123",
        "publishStatus": "published",
        "publishDate": "2024-01-15T10:30:00.000Z",
        "protectionMode": false,
        "isLike": true,
        "favorite": true,
        "score": 85,
        "sort": 0,
        "mediaUrl": "https://cdn.example.com/media/123.mp4",
        "videoUrlNoWatermark": "https://cdn.example.com/no-watermark/123.mp4",
        "mediaType": "video",
        "status": "succeeded",
        "deleted": false,
        "canRead": true,
        "canCopyPrompt": true,
        "canPublish": true,
        "canProtectCopy": true,
        "canDelete": true,
        "canOwn": true,
        "canCreateSimilar": true,
        "createdAt": "2024-01-15T10:00:00.000Z",
        "updatedAt": "2024-01-15T10:35:00.000Z"
      }
    ],
    "jobs": [
      {
        "id": "job_clp7j2k3x0001js8n2v4m5b6c",
        "userId": "user_clp7j2k3x0001js8n2v4m5b6c",
        "featureCode": "image-to-video",
        "type": "video",
        "credit": 10,
        "apiProviderCost": 0.0152,
        "status": "succeeded",
        "numOutputs": 1,
        "timeCostSeconds": 45,
        "modelCode": "runway-gen3",
        "apiProviderCode": "replicate",
        "prompt": "A beautiful sunset over mountains",
        "image": "https://cdn.example.com/input/123.jpg",
        "imageTail": null,
        "negativePrompt": "blurry, low quality",
        "promptStrength": 0.8,
        "duration": 5,
        "modeCode": "standard",
        "resolution": "1280x720",
        "aspectRatio": "16:9",
        "style": "cinematic",
        "motionRange": "medium",
        "seed": 42,
        "cameraType": "static",
        "cameraConfig": null,
        "cameraFixed": true,
        "video": null,
        "generationTemplate": null,
        "templateId": null,
        "templateImage": null,
        "processType": "image",
        "published": true,
        "protectionMode": false,
        "enableMagicPrompt": true,
        "enableTranslatePrompt": false,
        "enableTranslateNegativePrompt": false,
        "externalTaskId": "replicate_task_123",
        "errorMessage": null,
        "createdAt": "2024-01-15T10:00:00.000Z",
        "updatedAt": "2024-01-15T10:35:00.000Z"
      }
    ],
    "users": [
      {
        "id": "user_clp7j2k3x0001js8n2v4m5b6c",
        "name": "John Doe",
        "image": "https://avatar.example.com/john.jpg"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 156,
      "totalPages": 8,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

#### 错误响应

**400 Bad Request - 参数验证失败**
```json
{
  "success": false,
  "error": "Invalid parameters",
  "details": {
    "field": "pageSize",
    "message": "Page size must be between 1 and 100"
  }
}
```

**401 Unauthorized - 未认证**
```json
{
  "success": false,
  "error": "Authentication required"
}
```

**403 Forbidden - 权限不足**
```json
{
  "success": false,
  "error": "Access denied"
}
```

**429 Too Many Requests - 限流**
```json
{
  "success": false,
  "error": "Rate limit exceeded",
  "retryAfter": 60
}
```

**500 Internal Server Error - 服务器错误**
```json
{
  "success": false,
  "error": "Internal server error"
}
```

## 3. 数据库查询设计

### 3.1 查询策略

#### 主查询逻辑
1. **基础过滤**: 根据传入参数构建where条件
2. **用户权限**: 确保用户只能访问有权限的数据
3. **关联查询**: 使用JOIN优化性能，减少N+1查询
4. **分页处理**: 使用offset/limit实现分页
5. **排序规则**: 默认按创建时间倒序

#### 查询构建逻辑

```typescript
// 构建基础where条件
const buildWhereCondition = (params: GetGenerationsRequest) => {
  const conditions: any = {
    deleted: false, // 排除已删除的记录
  };

  // 用户ID过滤
  if (params.userId) {
    conditions.userId = params.userId;
  }

  // 媒体类型过滤
  if (params.mediaType) {
    conditions.mediaType = params.mediaType;
  }

  // 发布状态过滤
  if (params.publishStatus) {
    conditions.publishStatus = params.publishStatus;
  }

  // 收藏状态过滤
  if (params.favorite !== undefined) {
    conditions.favorite = params.favorite;
  }

  // 生成类型过滤（需要关联Job表）
  if (params.generationType) {
    conditions.job = {
      type: params.generationType
    };
  }

  return conditions;
};
```

### 3.2 Prisma 查询实现

```typescript
// 主查询 - 获取generations
const generations = await db.generation.findMany({
  where: buildWhereCondition(params),
  include: {
    job: true,
    user: {
      select: {
        id: true,
        name: true,
        image: true,
      }
    }
  },
  orderBy: {
    createdAt: 'desc'
  },
  skip: (params.page - 1) * params.pageSize,
  take: params.pageSize,
});

// 计数查询 - 用于分页
const total = await db.generation.count({
  where: buildWhereCondition(params),
});
```

### 3.3 数据处理和去重

```typescript
// 提取唯一的jobs和users
const uniqueJobs = Array.from(
  new Map(generations.map(g => [g.job.id, g.job])).values()
);

const uniqueUsers = Array.from(
  new Map(generations.map(g => [g.user.id, g.user])).values()
);

// 清理generations数据（移除重复的关联数据）
const cleanGenerations = generations.map(g => {
  const { job, user, ...generation } = g;
  return generation;
});
```

## 4. 权限和安全设计

### 4.1 访问控制

#### 用户权限矩阵

| 用户类型 | 访问权限 | 限制条件 |
|----------|----------|----------|
| 普通用户 | 只能查看自己的生成内容 | `userId` 必须等于当前用户ID |
| 管理员 | 可以查看所有用户的内容 | 无限制 |
| 组织成员 | 可以查看组织内的内容 | `userId` 在同一组织内 |

#### 权限检查逻辑

```typescript
const checkPermissions = (currentUser: User, params: GetGenerationsRequest) => {
  // 如果指定了userId且不是当前用户
  if (params.userId && params.userId !== currentUser.id) {
    // 检查是否为管理员
    if (!currentUser.role?.includes('admin')) {
      // 检查是否在同一组织（如果有组织功能）
      // TODO: 实现组织权限检查
      throw new Error('Access denied');
    }
  }
  
  // 如果没有指定userId，普通用户默认只能查看自己的
  if (!params.userId && !currentUser.role?.includes('admin')) {
    params.userId = currentUser.id;
  }
};
```

### 4.2 数据安全

#### 敏感信息过滤
- 过滤掉其他用户的私有信息
- 根据`canRead`权限控制数据访问
- 隐藏未发布内容的详细信息

#### 速率限制
- 普通用户: 每分钟30次请求
- 管理员: 每分钟100次请求
- IP限制: 每分钟200次请求

## 5. 性能优化

### 5.1 数据库优化

#### 索引策略
```sql
-- 必需的索引
CREATE INDEX idx_generation_user_id ON generation(userId);
CREATE INDEX idx_generation_media_type ON generation(mediaType);
CREATE INDEX idx_generation_publish_status ON generation(publishStatus);
CREATE INDEX idx_generation_favorite ON generation(favorite);
CREATE INDEX idx_generation_created_at ON generation(createdAt);
CREATE INDEX idx_generation_deleted ON generation(deleted);

-- 复合索引
CREATE INDEX idx_generation_user_type_created ON generation(userId, mediaType, createdAt DESC);
CREATE INDEX idx_generation_publish_favorite ON generation(publishStatus, favorite);

-- Job表相关索引
CREATE INDEX idx_job_type ON job(type);
CREATE INDEX idx_job_user_type ON job(userId, type);
```

#### 查询优化
- 使用`select`指定需要的字段，减少数据传输
- 利用`include`进行关联查询，避免N+1问题
- 实现查询结果缓存（Redis）

### 5.2 缓存策略

#### 缓存层级
1. **应用层缓存**: 查询结果缓存（5分钟）
2. **数据库查询缓存**: Prisma查询缓存
3. **CDN缓存**: 静态资源缓存

#### 缓存键设计
```typescript
const getCacheKey = (params: GetGenerationsRequest, userId: string) => {
  const keyParts = [
    'generations',
    userId,
    params.generationType || 'all',
    params.mediaType || 'all',
    params.publishStatus || 'all',
    params.favorite?.toString() || 'all',
    params.page.toString(),
    params.pageSize.toString()
  ];
  return keyParts.join(':');
};
```

### 5.3 响应优化

#### 数据传输优化
- 使用gzip压缩响应
- 实现ETags支持条件请求
- 支持字段选择器（可选扩展）

#### 分页优化
- 默认页面大小限制为20
- 最大页面大小限制为100
- 提供总数和分页信息

## 6. 实施步骤

### 6.1 开发阶段

#### 第一阶段：基础实现
1. **创建路由文件** (1工作日)
   - 在 `packages/api/src/routes/` 下创建 `generations` 目录
   - 实现基础路由结构

2. **实现数据访问层** (2工作日)
   - 创建 GenerationService 类
   - 实现基础查询逻辑
   - 添加参数验证

3. **集成认证和权限** (1工作日)
   - 集成 authMiddleware
   - 实现权限检查逻辑

#### 第二阶段：优化和测试
1. **性能优化** (2工作日)
   - 添加数据库索引
   - 实现缓存机制
   - 优化查询性能

2. **测试** (2工作日)
   - 单元测试
   - 集成测试
   - 性能测试

#### 第三阶段：部署和监控
1. **部署准备** (1工作日)
   - 生产环境配置
   - 数据库迁移
   - API文档更新

2. **监控和告警** (1工作日)
   - 添加日志记录
   - 设置性能监控
   - 配置错误告警

### 6.2 文件结构

```
packages/api/src/routes/generations/
├── index.ts              # 路由导出
├── router.ts             # 路由定义
├── types.ts              # TypeScript类型定义
├── schemas.ts            # Zod验证schemas
├── lib/
│   ├── generation-service.ts  # 业务逻辑服务
│   ├── query-builder.ts       # 查询构建器
│   └── permission-checker.ts  # 权限检查
└── __tests__/
    ├── router.test.ts
    ├── service.test.ts
    └── permissions.test.ts
```

## 7. 代码示例

### 7.1 路由实现

```typescript
// packages/api/src/routes/generations/router.ts
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { authMiddleware } from "../../middleware/auth";
import { GenerationService } from "./lib/generation-service";
import { GetGenerationsRequestSchema } from "./schemas";

export const generationsRouter = new Hono()
  .basePath("/generations")
  
  .get(
    "/",
    authMiddleware,
    describeRoute({
      tags: ["Generations"],
      summary: "Get generations list",
      description: "Get paginated list of generations with filtering options",
      responses: {
        200: {
          description: "Generations retrieved successfully",
        },
        400: {
          description: "Bad request - Invalid parameters",
        },
        401: {
          description: "Unauthorized",
        },
        403: {
          description: "Forbidden - Access denied",
        },
      },
    }),
    validator("query", GetGenerationsRequestSchema),
    async (c) => {
      const user = c.get("user");
      const params = c.req.valid("query");

      const result = await GenerationService.getGenerations(user, params);
      
      return c.json(result, 200);
    }
  );
```

### 7.2 服务层实现

```typescript
// packages/api/src/routes/generations/lib/generation-service.ts
import { db } from "@repo/database";
import type { User } from "@repo/database";
import type { GetGenerationsRequest, GetGenerationsResponse } from "../types";

export class GenerationService {
  static async getGenerations(
    user: User, 
    params: GetGenerationsRequest
  ): Promise<GetGenerationsResponse> {
    
    // 权限检查
    this.checkPermissions(user, params);
    
    // 构建查询条件
    const whereCondition = this.buildWhereCondition(params, user);
    
    // 执行查询
    const [generations, total] = await Promise.all([
      db.generation.findMany({
        where: whereCondition,
        include: {
          job: true,
          user: {
            select: { id: true, name: true, image: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: (params.page - 1) * params.pageSize,
        take: params.pageSize,
      }),
      db.generation.count({ where: whereCondition })
    ]);

    // 处理数据
    const uniqueJobs = this.extractUniqueJobs(generations);
    const uniqueUsers = this.extractUniqueUsers(generations);
    const cleanGenerations = this.cleanGenerationsData(generations);

    return {
      success: true,
      data: {
        generations: cleanGenerations,
        jobs: uniqueJobs,
        users: uniqueUsers,
        pagination: this.buildPaginationInfo(params, total)
      }
    };
  }

  private static checkPermissions(user: User, params: GetGenerationsRequest) {
    // 权限检查逻辑
    if (params.userId && params.userId !== user.id) {
      if (!user.role?.includes('admin')) {
        throw new Error('Access denied');
      }
    }
    
    // 普通用户默认只能查看自己的
    if (!params.userId && !user.role?.includes('admin')) {
      params.userId = user.id;
    }
  }

  private static buildWhereCondition(params: GetGenerationsRequest, user: User) {
    const conditions: any = {
      deleted: false,
    };

    if (params.userId) conditions.userId = params.userId;
    if (params.mediaType) conditions.mediaType = params.mediaType;
    if (params.publishStatus) conditions.publishStatus = params.publishStatus;
    if (params.favorite !== undefined) conditions.favorite = params.favorite;
    
    if (params.generationType) {
      conditions.job = { type: params.generationType };
    }

    return conditions;
  }

  // ... 其他辅助方法
}
```

### 7.3 Schema定义

```typescript
// packages/api/src/routes/generations/schemas.ts
import { z } from "zod";

export const GetGenerationsRequestSchema = z.object({
  userId: z.string().cuid().optional(),
  generationType: z.enum(['video', 'image']).optional(),
  pageSize: z.coerce.number().min(1).max(100).default(20),
  page: z.coerce.number().min(1).default(1),
  publishStatus: z.enum(['reviewing', 'published', 'rejected']).optional(),
  mediaType: z.enum(['video', 'image']).optional(),
  favorite: z.coerce.boolean().optional(),
});

export const GetGenerationsResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    generations: z.array(z.any()), // 可以使用具体的Generation schema
    jobs: z.array(z.any()),        // 可以使用具体的Job schema
    users: z.array(z.object({
      id: z.string(),
      name: z.string(),
      image: z.string().nullable(),
    })),
    pagination: z.object({
      page: z.number(),
      pageSize: z.number(),
      total: z.number(),
      totalPages: z.number(),
      hasNext: z.boolean(),
      hasPrev: z.boolean(),
    }),
  }),
});
```

## 8. 测试策略

### 8.1 单元测试

```typescript
// packages/api/src/routes/generations/__tests__/service.test.ts
import { describe, test, expect, beforeEach } from 'vitest';
import { GenerationService } from '../lib/generation-service';

describe('GenerationService', () => {
  test('should return user own generations', async () => {
    const user = { id: 'user1', role: 'user' };
    const params = { page: 1, pageSize: 10 };
    
    const result = await GenerationService.getGenerations(user, params);
    
    expect(result.success).toBe(true);
    expect(result.data.generations).toBeDefined();
  });

  test('should filter by mediaType', async () => {
    const user = { id: 'user1', role: 'user' };
    const params = { page: 1, pageSize: 10, mediaType: 'video' };
    
    const result = await GenerationService.getGenerations(user, params);
    
    expect(result.data.generations.every(g => g.mediaType === 'video')).toBe(true);
  });

  test('should deny access to other user data for non-admin', async () => {
    const user = { id: 'user1', role: 'user' };
    const params = { userId: 'user2', page: 1, pageSize: 10 };
    
    await expect(GenerationService.getGenerations(user, params))
      .rejects.toThrow('Access denied');
  });
});
```

### 8.2 集成测试

```typescript
// packages/api/src/routes/generations/__tests__/router.test.ts
import { describe, test, expect } from 'vitest';
import { testClient } from 'hono/testing';
import { generationsRouter } from '../router';

describe('GET /generations', () => {
  test('should return 401 without authentication', async () => {
    const res = await testClient(generationsRouter).generations.$get();
    expect(res.status).toBe(401);
  });

  test('should return generations with valid auth', async () => {
    // Mock authentication
    const res = await testClient(generationsRouter).generations.$get({}, {
      headers: { Authorization: 'Bearer valid-token' }
    });
    
    expect(res.status).toBe(200);
  });
});
```

## 9. 监控和运维

### 9.1 日志记录

```typescript
// 添加结构化日志
import { logger } from "@repo/logs";

logger.info('Generations API called', {
  userId: user.id,
  params: params,
  resultCount: generations.length,
  queryTimeMs: Date.now() - startTime,
});
```

### 9.2 性能监控

- **响应时间**: 监控API响应时间，设置告警阈值
- **查询性能**: 监控数据库查询时间
- **缓存命中率**: 监控缓存效果
- **错误率**: 监控API错误率

### 9.3 告警配置

- 响应时间 > 2秒时告警
- 错误率 > 5%时告警
- 数据库查询时间 > 1秒时告警

## 10. 后续扩展

### 10.1 可选功能

1. **全文搜索**: 添加prompt内容搜索
2. **高级过滤**: 支持日期范围、标签等过滤
3. **批量操作**: 支持批量收藏、删除等操作
4. **导出功能**: 支持数据导出为CSV/JSON

### 10.2 API版本管理

- 使用版本前缀：`/api/v1/generations`
- 保持向后兼容性
- 提供迁移指南

---

**总结**: 这个API设计方案提供了完整的实现指导，包括详细的技术规范、安全考虑、性能优化和运维监控。按照此方案实施，可以构建一个健壮、高性能的生成内容查询API。