# My Videos & Images Page 设计方案

## 1. 概述

本文档详细描述了 My Videos 和 My Images 页面的设计方案和实施步骤。该页面允许用户查看、管理和操作他们生成的视频和图片内容，支持筛选、收藏、批量操作等功能。

## 2. 需求分析

### 2.1 功能需求

基于提供的设计图和要求，页面需要支持：

1. **Tab 导航**：
   - Videos（视频）- `/my-videos` （默认页面）
   - Images（图片）- `/my-images`

2. **筛选功能**：
   - Favorites 复选框筛选收藏内容
   - 根据 tab 筛选视频/图片类型
   - Publish Status 筛选

3. **批量操作**：
   - Select 模式切换
   - 选择项目后在左侧显示操作工具栏
   - 支持批量删除、下载、收藏等操作

4. **卡片展示**：
   - 使用现有的 `GenerationItemCard` 组件
   - 响应式网格布局
   - 支持单项操作（发布、收藏、下载、分享等）

### 2.2 技术需求

- 复用现有的 `/api/generations` API
- 使用现有的 `GenerationItemCard` 组件
- 支持 SSR 和客户端状态管理
- 响应式设计

## 3. 页面架构设计

### 3.1 路由结构

```
apps/web/app/(marketing)/[locale]/
├── my-videos/
│   └── page.tsx               # Videos tab (默认)
└── my-images/
    └── page.tsx               # Images tab
```

**URL 路径示例**：
- 英文：`/en/my-videos`, `/en/my-images`
- 中文：`/zh/my-videos`, `/zh/my-images`  
- 德文：`/de/my-videos`, `/de/my-images`

### 3.2 组件层次结构

```
MediaLayout                    # 共享布局
├── TabNavigation              # Tab 导航组件 (Videos/Images)
├── FilterBar                  # 筛选栏组件
│   ├── FavoritesFilter       # 收藏筛选
│   ├── PublishStatusFilter   # 发布状态筛选  
│   └── SelectModeToggle      # 选择模式切换
├── SelectionToolbar          # 批量操作工具栏 (左侧显示)
├── InfiniteMediaGrid         # 无限滚动内容网格
│   ├── GenerationItemCard[]  # 现有卡片组件
│   ├── LoadingSpinner        # 加载状态
│   └── IntersectionObserver  # 滚动检测
└── EndOfList                 # 列表结束提示
```

## 4. API 集成方案

### 4.1 现有 API 分析

现有的 `/api/generations` 接口已经支持所需的所有筛选参数，完美支持无限滚动：

```typescript
interface GetGenerationsParams {
  generationType?: 'video' | 'image';    // 对应 job.type
  mediaType?: 'video' | 'image';         // 对应 generation.mediaType  
  favorite?: boolean;                    // 收藏筛选
  publishStatus?: 'reviewing' | 'published' | 'rejected';
  pageSize?: number;                     // 每次加载数量 (建议 20-50)
  page?: number;                         // 页码 (用于无限滚动分页)
}
```

### 4.2 无限滚动策略

```typescript
// 无限滚动加载策略
interface InfiniteScrollState {
  items: Generation[];                   // 已加载的所有数据
  page: number;                          // 当前页码
  hasNextPage: boolean;                  // 是否有更多数据
  isLoading: boolean;                    // 是否正在加载
  error: string | null;                  // 错误状态
}

// API 响应结构
interface GenerationsResponse {
  success: boolean;
  data: {
    generations: Generation[];
    pagination: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
      hasNext: boolean;                  // 关键：判断是否还有更多数据
      hasPrev: boolean;
    };
  };
}
```

### 4.3 Tab 对应的 API 调用

```typescript
// Videos tab - /[locale]/my-videos  
const videoParams = {
  mediaType: 'video',
  generationType: 'video',
  pageSize: 20,                          // 每次加载 20 个
  page: 1                                // 从第一页开始
};

// Images tab - /[locale]/my-images
const imageParams = {
  mediaType: 'image', 
  generationType: 'image',
  pageSize: 20,
  page: 1
};

// 加载更多数据时
const loadMoreParams = {
  ...baseParams,
  page: currentPage + 1                  // 递增页码
};
```

## 5. 详细设计

### 5.1 页面布局设计

```typescript
// 共享布局组件 - apps/web/modules/marketing/shared/components/MediaLayout.tsx
import { useTranslations } from 'next-intl';

interface MediaLayoutProps {
  children: React.ReactNode;
  currentTab: 'videos' | 'images';
  locale: string;
}

export default function MediaLayout({ children, currentTab, locale }: MediaLayoutProps) {
  const t = useTranslations('MyMedia');
  
  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-4">
          {currentTab === 'videos' ? t('myVideos') : t('myImages')}
        </h1>
        <TabNavigation currentTab={currentTab} locale={locale} />
      </div>
      {children}
    </div>
  );
}
```

### 5.2 Tab 导航组件

```typescript
// modules/marketing/shared/components/TabNavigation.tsx
'use client';

import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { cn } from '@/lib/utils';

interface TabNavigationProps {
  currentTab: 'videos' | 'images';
  locale: string;
}

export function TabNavigation({ currentTab, locale }: TabNavigationProps) {
  const t = useTranslations('MyMedia');
  
  const tabs = [
    { 
      key: 'videos',
      label: t('videos'), 
      href: `/${locale}/my-videos` 
    },
    { 
      key: 'images',
      label: t('images'), 
      href: `/${locale}/my-images` 
    },
  ];

  return (
    <div className="border-b border-border">
      <nav className="flex space-x-8">
        {tabs.map((tab) => {
          const isActive = currentTab === tab.key;
          
          return (
            <Link
              key={tab.key}
              href={tab.href}
              className={cn(
                'py-2 px-1 border-b-2 font-medium text-sm transition-colors',
                isActive
                  ? 'border-primary text-primary'
                  : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
              )}
            >
              {tab.label}
            </Link>
          );
        })}
      </nav>
    </div>
  );
}
```

### 5.3 筛选栏组件

```typescript
// components/FilterBar.tsx
'use client';

import { useState } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface FilterBarProps {
  onFiltersChange: (filters: FilterState) => void;
  onSelectModeToggle: (enabled: boolean) => void;
  selectedCount: number;
}

interface FilterState {
  favorites: boolean;
  publishStatus?: 'reviewing' | 'published' | 'rejected';
}

export function FilterBar({ onFiltersChange, onSelectModeToggle, selectedCount }: FilterBarProps) {
  const [filters, setFilters] = useState<FilterState>({ favorites: false });
  const [selectMode, setSelectMode] = useState(false);

  const handleFavoritesChange = (checked: boolean) => {
    const newFilters = { ...filters, favorites: checked };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleSelectModeToggle = () => {
    const newMode = !selectMode;
    setSelectMode(newMode);
    onSelectModeToggle(newMode);
  };

  return (
    <div className="flex items-center justify-between py-4 border-b border-border">
      <div className="flex items-center space-x-6">
        {/* Favorites Filter */}
        <div className="flex items-center space-x-2">
          <Checkbox
            id="favorites"
            checked={filters.favorites}
            onCheckedChange={handleFavoritesChange}
          />
          <label htmlFor="favorites" className="text-sm font-medium">
            Favorites
          </label>
        </div>

        {/* Publish Status Filter */}
        <Select
          value={filters.publishStatus || 'all'}
          onValueChange={(value) => {
            const newFilters = {
              ...filters,
              publishStatus: value === 'all' ? undefined : value as FilterState['publishStatus']
            };
            setFilters(newFilters);
            onFiltersChange(newFilters);
          }}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Publish Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="reviewing">Reviewing</SelectItem>
            <SelectItem value="published">Published</SelectItem>
            <SelectItem value="rejected">Rejected</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex items-center space-x-4">
        <Button
          variant={selectMode ? "default" : "outline"}
          size="sm"
          onClick={handleSelectModeToggle}
        >
          Select
        </Button>

        {selectedCount > 0 && (
          <span className="text-sm text-muted-foreground">
            {selectedCount} item{selectedCount > 1 ? 's' : ''} selected
          </span>
        )}
      </div>
    </div>
  );
}
```

### 5.4 批量操作工具栏（左侧显示）

```typescript
// components/SelectionToolbar.tsx
'use client';

import { Button } from '@/components/ui/button';
import { Download, Heart, Trash2, X } from 'lucide-react';

interface SelectionToolbarProps {
  selectedCount: number;
  onBulkDownload: () => void;
  onBulkFavorite: () => void;
  onBulkDelete: () => void;
  onCancel: () => void;
}

export function SelectionToolbar({
  selectedCount,
  onBulkDownload,
  onBulkFavorite,
  onBulkDelete,
  onCancel
}: SelectionToolbarProps) {
  if (selectedCount === 0) return null;

  return (
    <div className="fixed left-4 top-1/2 transform -translate-y-1/2 bg-background border border-border rounded-lg shadow-lg px-3 py-4 z-50">
      <div className="flex flex-col items-center space-y-4">
        <span className="text-xs font-medium text-center">
          {selectedCount} item{selectedCount > 1 ? 's' : ''}
        </span>

        <div className="flex flex-col items-center space-y-2">
          <Button size="sm" variant="outline" onClick={onBulkDownload} className="w-full">
            <Download className="w-4 h-4" />
          </Button>

          <Button size="sm" variant="outline" onClick={onBulkFavorite} className="w-full">
            <Heart className="w-4 h-4" />
          </Button>

          <Button size="sm" variant="destructive" onClick={onBulkDelete} className="w-full">
            <Trash2 className="w-4 h-4" />
          </Button>

          <Button size="sm" variant="ghost" onClick={onCancel} className="w-full">
            <X className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
```

### 5.5 主页面组件

```typescript
// apps/web/app/(marketing)/[locale]/my-videos/page.tsx
import { MediaLayout } from '@marketing/shared/components/MediaLayout';
import { InfiniteMediaContent } from '@marketing/shared/components/InfiniteMediaContent';

interface PageProps {
  params: { locale: string };
}

export default function MyVideosPage({ params: { locale } }: PageProps) {
  return (
    <MediaLayout currentTab="videos" locale={locale}>
      <InfiniteMediaContent type="video" locale={locale} />
    </MediaLayout>
  );
}

// apps/web/app/(marketing)/[locale]/my-images/page.tsx
import { MediaLayout } from '@marketing/shared/components/MediaLayout';
import { InfiniteMediaContent } from '@marketing/shared/components/InfiniteMediaContent';

interface PageProps {
  params: { locale: string };
}

export default function MyImagesPage({ params: { locale } }: PageProps) {
  return (
    <MediaLayout currentTab="images" locale={locale}>
      <InfiniteMediaContent type="image" locale={locale} />
    </MediaLayout>
  );
}
```

### 5.6 无限滚动内容组件

```typescript
// modules/marketing/shared/components/InfiniteMediaContent.tsx
'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { useTranslations } from 'next-intl';
import { GenerationItemCard } from '@marketing/shared/components/GenerationItemCard';
import { FilterBar } from './FilterBar';
import { SelectionToolbar } from './SelectionToolbar';
import { LoadingSpinner } from './LoadingSpinner';
import { useInfiniteGenerations } from '../hooks/useInfiniteGenerations';

interface Props {
  type: 'video' | 'image';
  locale: string;
}

export function InfiniteMediaContent({ type, locale }: Props) {
  const t = useTranslations('MyMedia');
  const [filters, setFilters] = useState({ favorites: false });
  const [selectMode, setSelectMode] = useState(false);
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());
  
  // 无限滚动相关状态
  const loadMoreRef = useRef<HTMLDivElement>(null);
  
  const { 
    data: generations, 
    isLoading, 
    isLoadingMore,
    error, 
    hasNextPage,
    loadMore,
    refetch 
  } = useInfiniteGenerations({ type, filters });

  // Intersection Observer 用于检测滚动到底部
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting && hasNextPage && !isLoadingMore) {
          loadMore();
        }
      },
      { threshold: 0.1, rootMargin: '100px' } // 提前 100px 开始加载
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current);
      }
    };
  }, [hasNextPage, isLoadingMore, loadMore]);

  // 筛选条件变化时重置列表
  useEffect(() => {
    setSelectedIds(new Set());
    refetch();
  }, [filters, refetch]);

  const handleCardSelect = (id: string, selected: boolean) => {
    const newSelected = new Set(selectedIds);
    if (selected) {
      newSelected.add(id);
    } else {
      newSelected.delete(id);
    }
    setSelectedIds(newSelected);
  };

  const handleBulkDownload = async () => {
    console.log('Bulk download:', Array.from(selectedIds));
  };

  const handleBulkFavorite = async () => {
    console.log('Bulk favorite:', Array.from(selectedIds));
    await refetch();
  };

  const handleBulkDelete = async () => {
    if (confirm(`Are you sure you want to delete ${selectedIds.size} items?`)) {
      console.log('Bulk delete:', Array.from(selectedIds));
      setSelectedIds(new Set());
      await refetch();
    }
  };

  const handleCancelSelection = () => {
    setSelectedIds(new Set());
    setSelectMode(false);
  };

  if (isLoading && generations.length === 0) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner />
      </div>
    );
  }

  if (error && generations.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500 mb-4">Error loading content</p>
        <button 
          onClick={() => refetch()} 
          className="px-4 py-2 bg-primary text-primary-foreground rounded"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <FilterBar
        onFiltersChange={setFilters}
        onSelectModeToggle={setSelectMode}
        selectedCount={selectedIds.size}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {generations.map((generation) => (
          <GenerationItemCard
            key={generation.id}
            generation={generation}
            selectMode={selectMode}
            selected={selectedIds.has(generation.id)}
            onSelect={(selected) => handleCardSelect(generation.id, selected)}
            onPublish={() => {/* 实现发布逻辑 */}}
            onFavorite={() => {/* 实现收藏逻辑 */}}
            onDownload={() => {/* 实现下载逻辑 */}}
            onShare={() => {/* 实现分享逻辑 */}}
            onCopyLink={() => {/* 实现复制链接逻辑 */}}
            onDelete={() => {/* 实现删除逻辑 */}}
            onCreateSimilar={() => {/* 实现创建相似逻辑 */}}
          />
        ))}
      </div>

      {/* 加载更多触发区域 */}
      <div ref={loadMoreRef} className="py-4">
        {isLoadingMore && (
          <div className="flex justify-center">
            <LoadingSpinner />
          </div>
        )}
        
        {!hasNextPage && generations.length > 0 && (
          <div className="text-center py-8">
            <p className="text-muted-foreground">No more content to load</p>
          </div>
        )}
      </div>

      {generations.length === 0 && !isLoading && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No content found</p>
        </div>
      )}

      <SelectionToolbar
        selectedCount={selectedIds.size}
        onBulkDownload={handleBulkDownload}
        onBulkFavorite={handleBulkFavorite}
        onBulkDelete={handleBulkDelete}
        onCancel={handleCancelSelection}
      />
    </div>
  );
}
```

### 5.7 无限滚动数据获取 Hook

```typescript
// hooks/useInfiniteGenerations.ts
'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@repo/auth/hooks';

interface UseInfiniteGenerationsParams {
  type: 'video' | 'image';
  filters: {
    favorites: boolean;
    publishStatus?: string;
  };
}

interface InfiniteScrollState {
  data: Generation[];
  page: number;
  hasNextPage: boolean;
  isLoading: boolean;
  isLoadingMore: boolean;
  error: string | null;
}

export function useInfiniteGenerations({ type, filters }: UseInfiniteGenerationsParams) {
  const [state, setState] = useState<InfiniteScrollState>({
    data: [],
    page: 0,
    hasNextPage: true,
    isLoading: true,
    isLoadingMore: false,
    error: null,
  });
  
  const { user } = useAuth();

  // 构建基础查询参数
  const buildParams = useCallback((page: number) => {
    const params = new URLSearchParams();
    
    // 根据 tab 类型设置筛选参数
    if (type === 'video') {
      params.append('mediaType', 'video');
      params.append('generationType', 'video');
    } else if (type === 'image') {
      params.append('mediaType', 'image');
      params.append('generationType', 'image');
    }

    // 添加其他筛选条件
    if (filters.favorites) {
      params.append('favorite', 'true');
    }
    
    if (filters.publishStatus) {
      params.append('publishStatus', filters.publishStatus);
    }

    // 分页参数
    params.append('page', page.toString());
    params.append('pageSize', '20'); // 每次加载 20 个

    return params;
  }, [type, filters]);

  // 获取数据的核心函数
  const fetchGenerations = useCallback(async (page: number, isLoadMore = false) => {
    if (!user) return;

    setState(prev => ({
      ...prev,
      isLoading: !isLoadMore,
      isLoadingMore: isLoadMore,
      error: null,
    }));

    try {
      const params = buildParams(page);
      const response = await fetch(`/api/generations?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch generations');
      }

      const result = await response.json();
      const newData = result.data.generations;
      const pagination = result.data.pagination;

      setState(prev => ({
        ...prev,
        data: isLoadMore ? [...prev.data, ...newData] : newData,
        page: pagination.page,
        hasNextPage: pagination.hasNext,
        isLoading: false,
        isLoadingMore: false,
        error: null,
      }));
    } catch (err) {
      setState(prev => ({
        ...prev,
        error: err.message,
        isLoading: false,
        isLoadingMore: false,
      }));
    }
  }, [user, buildParams]);

  // 加载更多数据
  const loadMore = useCallback(() => {
    if (!state.hasNextPage || state.isLoadingMore) return;
    fetchGenerations(state.page + 1, true);
  }, [state.hasNextPage, state.isLoadingMore, state.page, fetchGenerations]);

  // 重新获取数据（重置列表）
  const refetch = useCallback(() => {
    setState(prev => ({
      ...prev,
      data: [],
      page: 0,
      hasNextPage: true,
    }));
    fetchGenerations(1, false);
  }, [fetchGenerations]);

  // 初始加载和筛选条件变化时重新加载
  useEffect(() => {
    refetch();
  }, [type, filters, user, refetch]);

  return {
    data: state.data,
    isLoading: state.isLoading,
    isLoadingMore: state.isLoadingMore,
    error: state.error,
    hasNextPage: state.hasNextPage,
    loadMore,
    refetch,
  };
}
```

### 5.8 加载状态组件

```typescript
// components/LoadingSpinner.tsx
export function LoadingSpinner({ size = 'md' }: { size?: 'sm' | 'md' | 'lg' }) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
  };

  return (
    <div className="flex justify-center items-center">
      <div
        className={`${sizeClasses[size]} animate-spin rounded-full border-2 border-gray-300 border-t-primary`}
      />
    </div>
  );
}
```

## 6. GenerationItemCard 增强

由于需要支持选择模式，需要对现有的 `GenerationItemCard` 组件进行小幅修改：

```typescript
// 扩展 GenerationItemCard 的 Props
interface GenerationItemCardProps {
  // ... 现有的 props
  selectMode?: boolean;
  selected?: boolean;
  onSelect?: (selected: boolean) => void;
}

// 在组件中添加选择功能
export function GenerationItemCard({ 
  selectMode, 
  selected, 
  onSelect,
  ...otherProps 
}: GenerationItemCardProps) {
  return (
    <div className="relative">
      {/* 选择模式下的复选框 */}
      {selectMode && (
        <div className="absolute top-2 left-2 z-10">
          <Checkbox
            checked={selected}
            onCheckedChange={onSelect}
            className="bg-background/80 backdrop-blur-sm"
          />
        </div>
      )}
      
      {/* 现有的卡片内容 */}
      {/* ... */}
    </div>
  );
}
```

## 7. 无限滚动实施步骤

### 7.1 第一阶段：基础架构搭建

1. **创建路由结构**
   ```bash
   mkdir -p apps/web/app/\(marketing\)/\[locale\]/my-videos
   mkdir -p apps/web/app/\(marketing\)/\[locale\]/my-images
   ```

2. **创建组件目录**
   ```bash
   mkdir -p apps/web/modules/marketing/shared/components
   mkdir -p apps/web/modules/marketing/shared/hooks
   ```

3. **添加国际化翻译**
   ```bash
   # 在 packages/i18n/translations/ 下添加翻译文件
   # en.json, zh.json, de.json 等
   ```

4. **实现基础页面结构**
   - 创建 `MediaLayout` 共享组件（支持国际化）
   - 创建 `[locale]/my-videos/page.tsx` 和 `[locale]/my-images/page.tsx`
   - 实现 `TabNavigation` 组件（支持多语言链接）
   - 实现 `LoadingSpinner` 组件

### 7.2 第二阶段：无限滚动核心功能

1. **实现无限滚动 Hook**
   - 创建 `useInfiniteGenerations` hook
   - 实现分页状态管理
   - 处理加载更多逻辑
   - 实现筛选条件变化时的重置逻辑

2. **实现 Intersection Observer**
   - 检测滚动到底部
   - 自动触发加载更多
   - 处理加载状态和错误状态

3. **实现筛选功能**
   - 创建 `FilterBar` 组件
   - Favorites 复选框筛选
   - Publish Status 下拉筛选
   - 筛选条件变化时重置列表

### 7.3 第三阶段：批量操作功能

1. **扩展 GenerationItemCard**
   - 添加选择模式支持
   - 实现选择状态管理
   - 确保在无限滚动中正确处理选择状态

2. **实现 SelectionToolbar（左侧显示）**
   - 垂直布局的工具栏
   - 批量下载功能
   - 批量收藏功能
   - 批量删除功能
   - 选择状态实时更新

### 7.4 第四阶段：无限滚动优化和测试

1. **无限滚动性能优化**
   - 图片/视频懒加载优化
   - 防抖处理避免频繁请求
   - 内存管理：考虑大量数据时的内存使用
   - 预加载策略：提前触发下一页加载

2. **用户体验优化**
   - 加载状态指示器
   - 错误重试机制
   - 空状态处理
   - 加载完成提示

3. **响应式设计**
   - 移动端无限滚动体验
   - 触摸滑动优化
   - 不同屏幕尺寸的网格适配

4. **测试**
   - 无限滚动功能测试
   - 大数据量性能测试
   - 网络异常情况测试
   - 筛选条件变化测试

## 8. 无限滚动技术考虑

### 8.1 状态管理

使用 React 内置的 `useState` 和 `useCallback` 管理无限滚动状态，确保状态更新的性能和一致性。

### 8.2 性能优化

1. **Intersection Observer**：使用原生 API 检测滚动，性能优于监听 scroll 事件
2. **防抖加载**：避免用户快速滚动时的重复请求
3. **内存管理**：大量数据时考虑虚拟化或清理不可见项目
4. **预加载策略**：在用户接近底部前提前加载下一页

### 8.3 用户体验

1. **加载状态**：清晰的加载指示器和进度反馈
2. **错误恢复**：网络错误时的重试机制
3. **流畅滚动**：避免滚动卡顿和跳跃
4. **数据一致性**：筛选条件变化时正确重置列表

### 8.4 无障碍性

1. **键盘导航**：支持键盘滚动和操作
2. **屏幕阅读器**：加载状态的语音提示
3. **焦点管理**：新加载内容的焦点处理

## 9. 文件结构总览

```
apps/web/app/(marketing)/[locale]/
├── my-videos/
│   └── page.tsx                   # Videos 页面
├── my-images/
│   └── page.tsx                   # Images 页面

apps/web/modules/marketing/shared/components/
├── MediaLayout.tsx                # 共享布局组件（支持国际化）
├── TabNavigation.tsx              # Tab 导航（多语言链接）
├── FilterBar.tsx                  # 筛选栏
├── SelectionToolbar.tsx           # 选择工具栏（左侧显示）
├── InfiniteMediaContent.tsx       # 无限滚动主内容组件
└── LoadingSpinner.tsx             # 加载状态组件

apps/web/modules/marketing/shared/hooks/
└── useInfiniteGenerations.ts      # 无限滚动数据获取 hook

packages/i18n/translations/
├── en.json                        # 英文翻译
├── zh.json                        # 中文翻译
└── de.json                        # 德文翻译
```

## 10. 依赖关系

### 10.1 现有组件依赖

- `@marketing/shared/components/GenerationItemCard` - 卡片组件
- `@/components/ui/*` - shadcn/ui 组件库
- `@repo/auth/hooks` - 用户认证
- `next-intl` - 国际化支持

### 10.2 API 依赖

- `/api/generations` - 现有的生成内容 API

### 10.3 国际化依赖

- `@repo/i18n` - 翻译文件和配置
- `next-intl` - Next.js 国际化库

### 10.4 新增依赖

无需新增外部依赖，全部使用现有的技术栈。

## 11. 使用示例

### 11.1 Marketing 路由组页面使用示例

```typescript
// apps/web/app/(marketing)/[locale]/my-videos/page.tsx
import { MediaLayout } from '@marketing/shared/components/MediaLayout';
import { InfiniteMediaContent } from '@marketing/shared/components/InfiniteMediaContent';

interface PageProps {
  params: { locale: string };
}

export default function MyVideosPage({ params: { locale } }: PageProps) {
  return (
    <MediaLayout currentTab="videos" locale={locale}>
      <InfiniteMediaContent type="video" locale={locale} />
    </MediaLayout>
  );
}

// apps/web/app/(marketing)/[locale]/my-images/page.tsx  
import { MediaLayout } from '@marketing/shared/components/MediaLayout';
import { InfiniteMediaContent } from '@marketing/shared/components/InfiniteMediaContent';

export default function MyImagesPage({ params: { locale } }: PageProps) {
  return (
    <MediaLayout currentTab="images" locale={locale}>
      <InfiniteMediaContent type="image" locale={locale} />
    </MediaLayout>
  );
}
```

### 11.2 国际化翻译文件示例

```json
// packages/i18n/translations/en.json
{
  "MyMedia": {
    "myVideos": "My Videos",
    "myImages": "My Images", 
    "videos": "Videos",
    "images": "Images",
    "favorites": "Favorites",
    "publishStatus": "Publish Status",
    "select": "Select",
    "noContent": "No content found",
    "loading": "Loading...",
    "loadingMore": "Loading more...",
    "noMoreContent": "No more content to load",
    "errorLoading": "Error loading content",
    "retry": "Retry"
  }
}

// packages/i18n/translations/zh.json
{
  "MyMedia": {
    "myVideos": "我的视频",
    "myImages": "我的图片",
    "videos": "视频",
    "images": "图片", 
    "favorites": "收藏夹",
    "publishStatus": "发布状态",
    "select": "选择",
    "noContent": "未找到内容",
    "loading": "加载中...",
    "loadingMore": "加载更多...",
    "noMoreContent": "没有更多内容了",
    "errorLoading": "加载内容出错",
    "retry": "重试"
  }
}
```

### 11.3 无限滚动 Hook 重用示例

```typescript
// 在其他组件中重用无限滚动逻辑
import { useInfiniteGenerations } from '@marketing/shared/hooks/useInfiniteGenerations';

export function VideoGallery({ locale }: { locale: string }) {
  const { 
    data: videos, 
    isLoading, 
    isLoadingMore, 
    hasNextPage, 
    loadMore 
  } = useInfiniteGenerations({ 
    type: 'video', 
    filters: { favorites: false } 
  });

  return (
    <div className="container mx-auto">
      <h2 className="text-xl font-bold mb-4">Featured Videos</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {videos.map(video => (
          <VideoCard key={video.id} video={video} />
        ))}
      </div>
      {isLoadingMore && <LoadingSpinner />}
    </div>
  );
}
```

## 12. 无限滚动后续扩展方向

1. **虚拟化滚动**：处理超大数据集（1000+ 项目）
2. **搜索功能**：实时搜索与无限滚动结合
3. **智能预加载**：基于用户行为的预测加载
4. **离线缓存**：缓存已加载内容支持离线浏览
5. **滚动位置记忆**：页面返回时恢复滚动位置
6. **分组显示**：按日期/类别分组的无限滚动
7. **滚动性能监控**：监控滚动性能和用户体验指标

---

## 总结

本设计方案基于更新的需求，将页面放置在 **(marketing)** 路由组下，采用**无限滚动**模式来替代分页，并支持完整的**国际化**功能。方案特点：

1. **Marketing 路由组集成**：页面位于 `(marketing)/[locale]/` 下，支持多语言路径
2. **国际化支持**：完整的多语言支持（英文、中文、德文等）
3. **无限滚动体验**：用户可以连续向下滚动查看更多内容，无需点击分页
4. **智能加载策略**：使用 Intersection Observer 在用户接近底部时自动加载更多数据
5. **复用现有 API**：完美利用现有的 `/api/generations` 分页接口实现无限滚动
6. **左侧工具栏**：批量操作工具栏在左侧垂直显示，符合设计要求
7. **性能优化**：防抖加载、错误重试、内存管理等优化措施

**URL 路径示例**：
- `/en/my-videos` - 英文我的视频页面
- `/zh/my-videos` - 中文我的视频页面
- `/en/my-images` - 英文我的图片页面
- `/zh/my-images` - 中文我的图片页面

**核心技术优势**：
- **国际化集成**：利用现有的 next-intl 框架
- **Intersection Observer**：高性能的滚动检测
- **状态统一管理**：一个 Hook 管理所有无限滚动状态
- **多语言用户界面**：所有文本都支持国际化

通过这个设计方案，可以实现现代化的、支持多语言的、用户体验优秀的 My Videos 和 My Images 页面。