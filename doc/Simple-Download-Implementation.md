# 简化版下载功能实现

## 🎯 功能范围

**只实现核心功能**：
- ✅ 更新Generation表的downloadNum字段
- ✅ 返回直接的下载URL
- ❌ 暂不实现：订阅权限检查
- ❌ 暂不实现：数据库schema新增
- ❌ 暂不实现：签名URL和过期时间

## 📋 实现方案

### API端点
```bash
# 带水印下载 - 返回videoUrl字段
GET /api/generations/videos/:generationId/downloads/watermark

# 无水印下载 - 返回mediaUrl字段  
GET /api/generations/videos/:generationId/downloads/no-watermark
```

### 响应格式
```json
// 带水印下载响应
{
    "videoUrl": "https://videocdn.pollo.ai/.../wm/video.mp4"
}

// 无水印下载响应
{
    "videoUrlNoWatermark": "https://videocdn.pollo.ai/.../ori/video.mp4"
}

// 错误响应
{
    "error": "Generation not found"
}
```

## 🔧 具体实现

### Step 1: 在现有generations路由中添加下载端点

**文件**: `packages/api/src/routes/generations/router.ts`

在现有的generationsRouter中添加：

```typescript
// 在现有import中添加
import { z } from "zod";

// 在现有路由链中添加两个新端点
  
  // 带水印下载
  .get(
    "/videos/:generationId/downloads/watermark",
    authMiddleware,
    async (c) => {
      const user = c.get("user");
      const generationId = c.req.param("generationId");

      try {
        // 查找generation
        const generation = await db.generation.findUnique({
          where: { id: generationId },
          select: {
            id: true,
            userId: true,
            videoUrl: true,
            downloadNum: true,
          }
        });

        if (!generation) {
          return c.json({ error: "Generation not found" }, 404);
        }

        // 简单权限检查：用户只能下载自己的内容
        if (generation.userId !== user.id) {
          return c.json({ error: "Access denied" }, 403);
        }

        if (!generation.videoUrl) {
          return c.json({ error: "Video not available" }, 404);
        }

        // 更新下载次数
        await db.generation.update({
          where: { id: generationId },
          data: {
            downloadNum: {
              increment: 1
            }
          }
        });

        // 返回下载URL
        return c.json({
          videoUrl: generation.videoUrl
        });

      } catch (error) {
        console.error("[Download] Error:", error);
        return c.json({ error: "Internal server error" }, 500);
      }
    }
  )

  // 无水印下载  
  .get(
    "/videos/:generationId/downloads/no-watermark",
    authMiddleware,
    async (c) => {
      const user = c.get("user");
      const generationId = c.req.param("generationId");

      try {
        // 查找generation
        const generation = await db.generation.findUnique({
          where: { id: generationId },
          select: {
            id: true,
            userId: true,
            mediaUrl: true,
            downloadNum: true,
          }
        });

        if (!generation) {
          return c.json({ error: "Generation not found" }, 404);
        }

        // 简单权限检查：用户只能下载自己的内容
        if (generation.userId !== user.id) {
          return c.json({ error: "Access denied" }, 403);
        }

        if (!generation.mediaUrl) {
          return c.json({ error: "No-watermark video not available" }, 404);
        }

        // 更新下载次数
        await db.generation.update({
          where: { id: generationId },
          data: {
            downloadNum: {
              increment: 1
            }
          }
        });

        // 返回无水印下载URL
        return c.json({
          videoUrlNoWatermark: generation.mediaUrl
        });

      } catch (error) {
        console.error("[Download] Error:", error);
        return c.json({ error: "Internal server error" }, 500);
      }
    }
  )
```

### Step 2: 前端调用示例

**更新现有文件**: `apps/web/modules/marketing/shared/components/GenerationItemCard/components/ActionMenu.tsx`

在现有的handleDownloadClick函数中：

```typescript
const handleDownloadClick = async (type: 'watermark' | 'no-watermark') => {
  try {
    const endpoint = type === 'watermark' 
      ? `/api/generations/videos/${generation.id}/downloads/watermark`
      : `/api/generations/videos/${generation.id}/downloads/no-watermark`;
    
    const response = await fetch(endpoint, {
      headers: {
        'Authorization': `Bearer ${session?.accessToken}`,
      },
    });

    const result = await response.json();

    if (response.ok) {
      // 直接使用返回的URL进行下载
      const downloadUrl = result.videoUrl || result.videoUrlNoWatermark;
      
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `video_${generation.id}_${type}.mp4`;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      console.error('Download failed:', result.error);
      // 显示错误提示
    }
  } catch (error) {
    console.error('Network error:', error);
  }
};
```

## 🧪 测试

### API测试命令

```bash
# 测试带水印下载
curl -X GET "http://localhost:3000/api/generations/videos/YOUR_GENERATION_ID/downloads/watermark" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 测试无水印下载
curl -X GET "http://localhost:3000/api/generations/videos/YOUR_GENERATION_ID/downloads/no-watermark" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 预期响应

**成功响应**:
```json
{
  "videoUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm84hl47d01qnkvxudh1tv25y/wm/video.mp4"
}
```

**错误响应**:
```json
{
  "error": "Generation not found"
}
```

## ✅ 验证要点

1. **downloadNum更新**: 每次下载后，数据库中的downloadNum字段应该+1
2. **URL返回**: 带水印返回videoUrl，无水印返回mediaUrl
3. **权限检查**: 用户只能下载自己的内容
4. **错误处理**: 不存在的ID或无权限访问时返回相应错误

## 🚀 部署步骤

1. **后端**: 在现有generations路由中添加两个新端点
2. **前端**: 更新ActionMenu组件的下载逻辑
3. **测试**: 使用curl或前端界面测试下载功能
4. **验证**: 检查数据库downloadNum字段是否正确更新

---

这个简化版本避免了复杂的权限系统、签名URL和数据库结构变更，专注于核心的下载计数和URL返回功能。