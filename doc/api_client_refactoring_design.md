# API Client Refactoring Design

## Executive Summary

This document proposes a comprehensive refactoring of the API call implementation in `packages/api/src/routes/jobs/lib/job-service.ts` to improve modularity, testability, and maintainability through the introduction of specialized components: `ModelApiClient`, `DownstreamRequestHandler`, `DownstreamResponseHandler`, and supporting infrastructure.

## Current Architecture Analysis

### Current Implementation Overview

The existing system (`job-service.ts:277-417`) has the following structure:

```typescript
JobService
├── processJobOutputsAsync() 
├── createSingleOutput()
├── Direct VideoProviderManager usage
└── Mixed responsibilities (job orchestration + API communication)
```

### Current Provider System

```typescript
VideoProviderManager
├── Provider selection by model
├── Basic health checking
├── Simple failover logic
└── Concrete providers (FalProvider, ReplicateProvider)
```

### Identified Problems

1. **Mixed Responsibilities**: `JobService` handles both job orchestration AND API communication details
2. **Tight Coupling**: Direct dependency on `videoProviderManager` 
3. **Limited Abstraction**: No separation between request preparation, API calling, and response processing
4. **Hardcoded Logic**: API request mapping scattered across provider implementations
5. **Poor Testability**: Difficult to mock and test different components in isolation
6. **No Middleware Support**: No infrastructure for cross-cutting concerns (logging, metrics, caching)
7. **Limited Error Handling**: Basic retry logic without sophisticated error recovery strategies

## Proposed Architecture

### Architecture Overview

```
┌─────────────────┐    ┌──────────────────────┐    ┌────────────────────┐
│   JobService    │───▶│   ModelApiClient     │───▶│  ProviderRegistry  │
│                 │    │                      │    │                    │
└─────────────────┘    └──────────────────────┘    └────────────────────┘
                                │                              │
                                ▼                              ▼
                       ┌─────────────────┐            ┌─────────────────┐
                       │  Middleware     │            │   Provider      │
                       │  Pipeline       │            │   Pool          │
                       └─────────────────┘            └─────────────────┘
                                │
                ┌───────────────┼───────────────┐
                ▼               ▼               ▼
    ┌─────────────────┐ ┌─────────────┐ ┌─────────────────┐
    │ Request Handler │ │ API Client  │ │Response Handler │
    └─────────────────┘ └─────────────┘ └─────────────────┘
```

### Core Components

#### 1. ModelApiClient (Orchestrator)

**Purpose**: High-level API client that orchestrates video generation requests across multiple providers.

**Responsibilities**:
- Coordinate request/response handling pipeline
- Manage middleware execution chain  
- Handle high-level error scenarios and recovery
- Provide unified interface for different providers
- Load balancing and provider selection

**Interface**:
```typescript
interface ModelApiClient {
  generateVideo(request: VideoGenerationBusinessRequest): Promise<VideoGenerationResult>
  checkStatus(taskId: string, provider?: string): Promise<VideoGenerationStatus>
  cancelTask(taskId: string): Promise<void>
  
  // Configuration
  addMiddleware(middleware: ApiMiddleware): void
  setProviderSelector(selector: ProviderSelector): void
}
```

#### 2. DownstreamRequestHandler (Request Transformer)

**Purpose**: Transform business requests into provider-specific API requests.

**Responsibilities**:
- Map business models to provider-specific formats
- Apply request validation rules
- Handle parameter transformation and normalization
- Support request middleware pipeline (logging, metrics, etc.)
- Manage request-specific configurations

**Interface**:
```typescript
interface DownstreamRequestHandler {
  prepareRequest(
    businessRequest: VideoGenerationBusinessRequest,
    provider: string,
    context: RequestContext
  ): Promise<ProviderSpecificRequest>
  
  validateRequest(request: ProviderSpecificRequest): Promise<ValidationResult>
  transformRequest(request: ProviderSpecificRequest, transformers: RequestTransformer[]): Promise<ProviderSpecificRequest>
}
```

#### 3. DownstreamResponseHandler (Response Transformer) 

**Purpose**: Process and transform provider responses into standardized format.

**Responsibilities**:
- Parse provider-specific response formats
- Map responses to standard business models
- Handle response validation and error extraction
- Support response middleware pipeline
- Manage response normalization

**Interface**:
```typescript
interface DownstreamResponseHandler {
  parseResponse(
    rawResponse: ProviderResponse,
    provider: string,
    context: ResponseContext
  ): Promise<StandardVideoGenerationResponse>
  
  validateResponse(response: StandardVideoGenerationResponse): Promise<ValidationResult>
  transformResponse(response: StandardVideoGenerationResponse, transformers: ResponseTransformer[]): Promise<VideoGenerationResult>
}
```

#### 4. ApiClient (Transport Layer)

**Purpose**: Handle actual HTTP communication with provider APIs.

**Responsibilities**:
- Execute HTTP requests with proper configuration
- Handle network-level retries and timeouts
- Manage connection pooling and keep-alive
- Implement circuit breaker patterns
- Support multiple transport protocols

**Interface**:
```typescript
interface ApiClient {
  post<T>(url: string, data: any, options?: RequestOptions): Promise<T>
  get<T>(url: string, options?: RequestOptions): Promise<T>
  put<T>(url: string, data: any, options?: RequestOptions): Promise<T>
  delete<T>(url: string, options?: RequestOptions): Promise<T>
  
  // Configuration
  setRetryPolicy(policy: RetryPolicy): void
  setTimeout(timeout: number): void
  setCircuitBreaker(config: CircuitBreakerConfig): void
}
```

#### 5. Middleware System

**Purpose**: Implement cross-cutting concerns through a pipeline architecture.

**Middleware Types**:

- **Logging Middleware**: Request/response logging with correlation IDs
- **Metrics Middleware**: Performance metrics, success rates, latency tracking
- **Caching Middleware**: Response caching for repeated requests
- **Authentication Middleware**: API key management and refresh
- **Rate Limiting Middleware**: Provider-specific rate limiting
- **Transformation Middleware**: Custom request/response transformations
- **Validation Middleware**: Input/output validation
- **Circuit Breaker Middleware**: Automatic failure detection and recovery

**Interface**:
```typescript
interface ApiMiddleware {
  name: string
  order: number
  
  onRequest?(request: MiddlewareRequest, context: MiddlewareContext): Promise<MiddlewareRequest>
  onResponse?(response: MiddlewareResponse, context: MiddlewareContext): Promise<MiddlewareResponse>
  onError?(error: Error, context: MiddlewareContext): Promise<void>
}
```

#### 6. Enhanced Provider Registry

**Purpose**: Advanced provider management with intelligent selection and health monitoring.

**Responsibilities**:
- Dynamic provider discovery and registration
- Sophisticated health checking with metrics
- Load balancing strategies (round-robin, weighted, least-load)
- Intelligent failover with fallback chains
- Provider capability matching
- Performance monitoring and optimization

**Interface**:
```typescript
interface ProviderRegistry {
  registerProvider(provider: VideoProvider, config: ProviderConfig): void
  getProvider(criteria: ProviderSelectionCriteria): Promise<VideoProvider>
  getProvidersByCapability(capability: ProviderCapability): Promise<VideoProvider[]>
  
  // Health management
  healthCheck(provider?: string): Promise<HealthStatus>
  setHealthCheckInterval(interval: number): void
  
  // Load balancing
  setLoadBalancingStrategy(strategy: LoadBalancingStrategy): void
  getProviderLoad(provider: string): Promise<ProviderLoad>
}
```

## Detailed Implementation Plan

### Phase 1: Core Infrastructure (Week 1-2)

#### 1.1 Create Base Interfaces and Types

**File**: `packages/api/src/lib/api-client/types.ts`

```typescript
// Business domain types
export interface VideoGenerationBusinessRequest {
  prompt: string
  image?: string
  imageTail?: string
  negativePrompt?: string
  duration: number
  aspectRatio: string
  style: string
  motionRange: string
  seed?: number
  resolution?: string
  modelCode: string
  webhookUrl?: string
  options?: GenerationOptions
}

export interface VideoGenerationResult {
  taskId: string
  status: TaskStatus
  estimatedTime?: number
  resultUrl?: string
  errorMessage?: string
  cost?: number
  provider: string
  metadata?: Record<string, any>
}

// Provider communication types  
export interface ProviderSpecificRequest {
  provider: string
  endpoint: string
  method: string
  headers: Record<string, string>
  body: any
  timeout?: number
}

export interface ProviderResponse {
  statusCode: number
  headers: Record<string, string>
  body: any
  provider: string
}

// Context types
export interface RequestContext {
  correlationId: string
  userId: string
  jobId: string
  provider: string
  attempt: number
  startTime: number
}

export interface ResponseContext extends RequestContext {
  duration: number
  statusCode: number
}

// Middleware types
export interface MiddlewareRequest {
  businessRequest: VideoGenerationBusinessRequest
  providerRequest: ProviderSpecificRequest
  context: RequestContext
}

export interface MiddlewareResponse {
  providerResponse: ProviderResponse
  businessResponse: VideoGenerationResult
  context: ResponseContext
}

export type TaskStatus = 'waiting' | 'processing' | 'succeed' | 'failed' | 'cancelled'
```

#### 1.2 Implement ApiClient (Transport Layer)

**File**: `packages/api/src/lib/api-client/transport/api-client.ts`

```typescript
import { HttpClient } from './http-client'
import { CircuitBreaker } from './circuit-breaker'
import { RetryPolicy } from './retry-policy'

export class ApiClient implements IApiClient {
  private httpClient: HttpClient
  private circuitBreaker: CircuitBreaker
  private retryPolicy: RetryPolicy

  constructor(config: ApiClientConfig) {
    this.httpClient = new HttpClient(config.http)
    this.circuitBreaker = new CircuitBreaker(config.circuitBreaker)
    this.retryPolicy = new RetryPolicy(config.retry)
  }

  async post<T>(url: string, data: any, options?: RequestOptions): Promise<T> {
    return this.executeWithResilience(async () => {
      return this.httpClient.post<T>(url, data, {
        ...options,
        timeout: options?.timeout || this.config.timeout,
      })
    })
  }

  private async executeWithResilience<T>(operation: () => Promise<T>): Promise<T> {
    return this.circuitBreaker.execute(async () => {
      return this.retryPolicy.execute(operation)
    })
  }
}
```

#### 1.3 Build Middleware Pipeline

**File**: `packages/api/src/lib/api-client/middleware/pipeline.ts`

```typescript
export class MiddlewarePipeline {
  private middlewares: ApiMiddleware[] = []

  addMiddleware(middleware: ApiMiddleware): void {
    this.middlewares.push(middleware)
    this.middlewares.sort((a, b) => a.order - b.order)
  }

  async executeRequest(
    request: MiddlewareRequest,
    finalHandler: (req: MiddlewareRequest) => Promise<MiddlewareResponse>
  ): Promise<MiddlewareResponse> {
    
    // Build request chain
    let currentRequest = request
    for (const middleware of this.middlewares) {
      if (middleware.onRequest) {
        currentRequest = await middleware.onRequest(currentRequest, request.context)
      }
    }

    // Execute API call
    let response: MiddlewareResponse
    try {
      response = await finalHandler(currentRequest)
    } catch (error) {
      // Execute error handlers in reverse order
      for (const middleware of this.middlewares.reverse()) {
        if (middleware.onError) {
          await middleware.onError(error, request.context)
        }
      }
      throw error
    }

    // Build response chain (reverse order)
    let currentResponse = response
    for (const middleware of this.middlewares.reverse()) {
      if (middleware.onResponse) {
        currentResponse = await middleware.onResponse(currentResponse, response.context)
      }
    }

    return currentResponse
  }
}
```

### Phase 2: Request and Response Handlers (Week 2-3)

#### 2.1 Implement DownstreamRequestHandler

**File**: `packages/api/src/lib/api-client/handlers/request-handler.ts`

```typescript
export class DownstreamRequestHandler implements IDownstreamRequestHandler {
  private transformers: Map<string, RequestTransformer> = new Map()
  private validators: Map<string, RequestValidator> = new Map()

  async prepareRequest(
    businessRequest: VideoGenerationBusinessRequest,
    provider: string,
    context: RequestContext
  ): Promise<ProviderSpecificRequest> {
    
    // Get provider-specific transformer
    const transformer = this.getTransformer(provider)
    
    // Transform business request to provider format
    const providerRequest = await transformer.transform(businessRequest, context)
    
    // Validate the transformed request
    const validator = this.getValidator(provider)
    const validationResult = await validator.validate(providerRequest)
    
    if (!validationResult.isValid) {
      throw new ValidationError(validationResult.errors)
    }

    return providerRequest
  }

  registerTransformer(provider: string, transformer: RequestTransformer): void {
    this.transformers.set(provider, transformer)
  }

  private getTransformer(provider: string): RequestTransformer {
    const transformer = this.transformers.get(provider)
    if (!transformer) {
      throw new Error(`No request transformer found for provider: ${provider}`)
    }
    return transformer
  }
}
```

#### 2.2 Implement Provider-Specific Request Transformers

**File**: `packages/api/src/lib/api-client/transformers/fal-request-transformer.ts`

```typescript
export class FalRequestTransformer implements RequestTransformer {
  async transform(
    request: VideoGenerationBusinessRequest,
    context: RequestContext
  ): Promise<ProviderSpecificRequest> {
    
    const endpoint = this.getModelEndpoint(request.modelCode)
    
    const body = {
      prompt: request.prompt,
      image_url: request.image,
      duration: request.duration,
      aspect_ratio: this.mapAspectRatio(request.aspectRatio),
      style: request.style,
      motion_range: request.motionRange,
      seed: request.seed,
      negative_prompt: request.negativePrompt,
      ...(request.imageTail && { image_tail_url: request.imageTail }),
    }

    return {
      provider: 'fal',
      endpoint: `https://fal.run/${endpoint}`,
      method: 'POST',
      headers: {
        'Authorization': `Key ${process.env.FAL_API_KEY}`,
        'Content-Type': 'application/json',
        'X-Correlation-ID': context.correlationId,
      },
      body,
      timeout: 30000,
    }
  }

  private getModelEndpoint(modelCode: string): string {
    const endpoints = {
      'fal-luma-dream-machine': 'fal-ai/luma-dream-machine',
      'fal-runway-gen3': 'fal-ai/runway-gen3',
      // ... other mappings
    }
    
    const endpoint = endpoints[modelCode]
    if (!endpoint) {
      throw new Error(`Unsupported model: ${modelCode}`)
    }
    
    return endpoint
  }

  private mapAspectRatio(ratio: string): string {
    const mapping = {
      'ASPECT_16_9': '16:9',
      'ASPECT_9_16': '9:16',
      // ... other mappings
    }
    return mapping[ratio] || '16:9'
  }
}
```

#### 2.3 Implement DownstreamResponseHandler

**File**: `packages/api/src/lib/api-client/handlers/response-handler.ts`

```typescript
export class DownstreamResponseHandler implements IDownstreamResponseHandler {
  private parsers: Map<string, ResponseParser> = new Map()
  private transformers: ResponseTransformer[] = []

  async parseResponse(
    rawResponse: ProviderResponse,
    provider: string,
    context: ResponseContext
  ): Promise<StandardVideoGenerationResponse> {
    
    const parser = this.getParser(provider)
    const parsed = await parser.parse(rawResponse, context)
    
    // Apply transformations
    let result = parsed
    for (const transformer of this.transformers) {
      result = await transformer.transform(result, context)
    }
    
    return result
  }

  registerParser(provider: string, parser: ResponseParser): void {
    this.parsers.set(provider, parser)
  }

  addTransformer(transformer: ResponseTransformer): void {
    this.transformers.push(transformer)
  }
}
```

### Phase 3: Enhanced Provider Registry (Week 3-4)

#### 3.1 Implement Provider Registry

**File**: `packages/api/src/lib/api-client/registry/provider-registry.ts`

```typescript
export class ProviderRegistry implements IProviderRegistry {
  private providers: Map<string, RegisteredProvider> = new Map()
  private loadBalancer: LoadBalancer
  private healthChecker: HealthChecker
  private providerSelector: ProviderSelector

  constructor(config: ProviderRegistryConfig) {
    this.loadBalancer = new LoadBalancer(config.loadBalancing)
    this.healthChecker = new HealthChecker(config.healthCheck)
    this.providerSelector = new ProviderSelector(config.selection)
  }

  async getProvider(criteria: ProviderSelectionCriteria): Promise<VideoProvider> {
    // Get eligible providers based on criteria
    const eligibleProviders = this.getEligibleProviders(criteria)
    
    if (eligibleProviders.length === 0) {
      throw new Error('No providers available for the given criteria')
    }

    // Apply health filtering
    const healthyProviders = await this.healthChecker.filterHealthy(eligibleProviders)
    
    if (healthyProviders.length === 0) {
      throw new Error('No healthy providers available')
    }

    // Select provider using load balancing strategy
    const selectedProvider = await this.loadBalancer.selectProvider(healthyProviders)
    
    return selectedProvider.instance
  }

  async registerProvider(provider: VideoProvider, config: ProviderConfig): Promise<void> {
    const registeredProvider: RegisteredProvider = {
      instance: provider,
      config,
      status: 'active',
      registeredAt: new Date(),
      metrics: new ProviderMetrics(),
    }

    this.providers.set(provider.name, registeredProvider)
    
    // Start health monitoring
    await this.healthChecker.monitor(provider.name)
    
    console.log(`Provider registered: ${provider.name}`)
  }

  private getEligibleProviders(criteria: ProviderSelectionCriteria): RegisteredProvider[] {
    return Array.from(this.providers.values()).filter(provider => {
      // Check model support
      if (criteria.modelCode && !provider.instance.supportedModels.includes(criteria.modelCode)) {
        return false
      }

      // Check feature support
      if (criteria.features && !this.hasRequiredFeatures(provider, criteria.features)) {
        return false
      }

      // Check cost constraints
      if (criteria.maxCost && provider.config.averageCost > criteria.maxCost) {
        return false
      }

      return provider.status === 'active'
    })
  }
}
```

#### 3.2 Implement Load Balancing Strategies

**File**: `packages/api/src/lib/api-client/registry/load-balancer.ts`

```typescript
export class LoadBalancer {
  private strategy: LoadBalancingStrategy

  constructor(config: LoadBalancingConfig) {
    this.strategy = this.createStrategy(config.strategy)
  }

  async selectProvider(providers: RegisteredProvider[]): Promise<RegisteredProvider> {
    return this.strategy.select(providers)
  }

  private createStrategy(strategyType: string): LoadBalancingStrategy {
    switch (strategyType) {
      case 'round-robin':
        return new RoundRobinStrategy()
      case 'weighted':
        return new WeightedStrategy()
      case 'least-load':
        return new LeastLoadStrategy()
      case 'response-time':
        return new ResponseTimeStrategy()
      default:
        return new RoundRobinStrategy()
    }
  }
}

export class LeastLoadStrategy implements LoadBalancingStrategy {
  async select(providers: RegisteredProvider[]): Promise<RegisteredProvider> {
    // Find provider with lowest current load
    const providerLoads = await Promise.all(
      providers.map(async provider => ({
        provider,
        load: await this.getCurrentLoad(provider)
      }))
    )

    const selectedProvider = providerLoads.reduce((min, current) => 
      current.load < min.load ? current : min
    )

    return selectedProvider.provider
  }

  private async getCurrentLoad(provider: RegisteredProvider): Promise<number> {
    // Implementation to get current load (active requests, queue size, etc.)
    return provider.metrics.activeRequests + (provider.metrics.queueSize * 0.5)
  }
}
```

### Phase 4: ModelApiClient Integration (Week 4-5)

#### 4.1 Implement ModelApiClient

**File**: `packages/api/src/lib/api-client/model-api-client.ts`

```typescript
export class ModelApiClient implements IModelApiClient {
  private requestHandler: DownstreamRequestHandler
  private responseHandler: DownstreamResponseHandler
  private apiClient: ApiClient
  private providerRegistry: ProviderRegistry
  private middlewarePipeline: MiddlewarePipeline
  private correlationIdGenerator: CorrelationIdGenerator

  constructor(config: ModelApiClientConfig) {
    this.requestHandler = new DownstreamRequestHandler()
    this.responseHandler = new DownstreamResponseHandler()
    this.apiClient = new ApiClient(config.transport)
    this.providerRegistry = new ProviderRegistry(config.registry)
    this.middlewarePipeline = new MiddlewarePipeline()
    this.correlationIdGenerator = new CorrelationIdGenerator()

    this.initializeMiddleware(config.middleware)
    this.registerProviders(config.providers)
  }

  async generateVideo(request: VideoGenerationBusinessRequest): Promise<VideoGenerationResult> {
    const correlationId = this.correlationIdGenerator.generate()
    const context: RequestContext = {
      correlationId,
      userId: request.userId || 'anonymous',
      jobId: request.jobId || 'unknown',
      provider: '',
      attempt: 1,
      startTime: Date.now(),
    }

    // Select provider
    const provider = await this.providerRegistry.getProvider({
      modelCode: request.modelCode,
      features: this.extractRequiredFeatures(request),
    })
    
    context.provider = provider.name

    // Build middleware request
    const middlewareRequest: MiddlewareRequest = {
      businessRequest: request,
      providerRequest: null, // Will be populated by request handler
      context,
    }

    // Execute through middleware pipeline
    const response = await this.middlewarePipeline.executeRequest(
      middlewareRequest,
      async (req) => this.executeApiCall(req, provider)
    )

    return response.businessResponse
  }

  private async executeApiCall(
    request: MiddlewareRequest,
    provider: VideoProvider
  ): Promise<MiddlewareResponse> {
    
    // Prepare provider-specific request
    const providerRequest = await this.requestHandler.prepareRequest(
      request.businessRequest,
      provider.name,
      request.context
    )

    request.providerRequest = providerRequest

    // Execute API call
    const rawResponse = await this.apiClient.post(
      providerRequest.endpoint,
      providerRequest.body,
      {
        headers: providerRequest.headers,
        timeout: providerRequest.timeout,
      }
    )

    const providerResponse: ProviderResponse = {
      statusCode: rawResponse.status,
      headers: rawResponse.headers,
      body: rawResponse.data,
      provider: provider.name,
    }

    // Parse and transform response
    const responseContext: ResponseContext = {
      ...request.context,
      duration: Date.now() - request.context.startTime,
      statusCode: rawResponse.status,
    }

    const businessResponse = await this.responseHandler.parseResponse(
      providerResponse,
      provider.name,
      responseContext
    )

    return {
      providerResponse,
      businessResponse,
      context: responseContext,
    }
  }

  addMiddleware(middleware: ApiMiddleware): void {
    this.middlewarePipeline.addMiddleware(middleware)
  }
}
```

### Phase 5: Pre-built Middleware (Week 5-6)

#### 5.1 Logging Middleware

**File**: `packages/api/src/lib/api-client/middleware/logging-middleware.ts`

```typescript
export class LoggingMiddleware implements ApiMiddleware {
  name = 'logging'
  order = 1

  constructor(private logger: Logger) {}

  async onRequest(request: MiddlewareRequest, context: MiddlewareContext): Promise<MiddlewareRequest> {
    this.logger.info('API Request', {
      correlationId: context.correlationId,
      provider: context.provider,
      modelCode: request.businessRequest.modelCode,
      userId: context.userId,
      jobId: context.jobId,
    })

    return request
  }

  async onResponse(response: MiddlewareResponse, context: MiddlewareContext): Promise<MiddlewareResponse> {
    this.logger.info('API Response', {
      correlationId: context.correlationId,
      provider: context.provider,
      statusCode: context.statusCode,
      duration: context.duration,
      status: response.businessResponse.status,
    })

    return response
  }

  async onError(error: Error, context: MiddlewareContext): Promise<void> {
    this.logger.error('API Error', {
      correlationId: context.correlationId,
      provider: context.provider,
      error: error.message,
      stack: error.stack,
    })
  }
}
```

#### 5.2 Metrics Middleware

**File**: `packages/api/src/lib/api-client/middleware/metrics-middleware.ts`

```typescript
export class MetricsMiddleware implements ApiMiddleware {
  name = 'metrics'
  order = 2

  constructor(private metricsCollector: MetricsCollector) {}

  async onRequest(request: MiddlewareRequest, context: MiddlewareContext): Promise<MiddlewareRequest> {
    this.metricsCollector.incrementCounter('api_requests_total', {
      provider: context.provider,
      model: request.businessRequest.modelCode,
    })

    return request
  }

  async onResponse(response: MiddlewareResponse, context: MiddlewareContext): Promise<MiddlewareResponse> {
    this.metricsCollector.recordHistogram('api_request_duration_seconds', context.duration / 1000, {
      provider: context.provider,
      status: response.businessResponse.status,
    })

    this.metricsCollector.incrementCounter('api_responses_total', {
      provider: context.provider,
      status: response.businessResponse.status,
      status_code: context.statusCode.toString(),
    })

    return response
  }

  async onError(error: Error, context: MiddlewareContext): Promise<void> {
    this.metricsCollector.incrementCounter('api_errors_total', {
      provider: context.provider,
      error_type: error.constructor.name,
    })
  }
}
```

### Phase 6: Integration with JobService (Week 6)

#### 6.1 Refactor JobService

**File**: `packages/api/src/routes/jobs/lib/job-service.ts` (Updated)

```typescript
export class JobService {
  private modelApiClient: ModelApiClient

  constructor() {
    this.modelApiClient = new ModelApiClient({
      transport: {
        timeout: 30000,
        retries: 3,
      },
      registry: {
        loadBalancing: { strategy: 'least-load' },
        healthCheck: { interval: 60000 },
      },
      middleware: [
        { type: 'logging', config: {} },
        { type: 'metrics', config: {} },
        { type: 'caching', config: { ttl: 300 } },
      ],
    })
  }

  // ... existing createJob method unchanged ...

  private static async createSingleOutput(
    job: any,
    jobParams: any,
    request: CreateJobRequest,
    outputIndex: number
  ) {
    try {
      // ✅ New: Use ModelApiClient instead of direct provider calls
      const apiRequest: VideoGenerationBusinessRequest = {
        prompt: jobParams.prompt || '',
        image: jobParams.image,
        imageTail: jobParams.imageTail,
        negativePrompt: jobParams.negativePrompt,
        duration: jobParams.duration,
        aspectRatio: jobParams.aspectRatio,
        style: jobParams.style,
        motionRange: jobParams.motionRange,
        seed: jobParams.seed ? jobParams.seed + outputIndex : undefined,
        resolution: jobParams.resolution,
        modelCode: jobParams.modelCode,
        webhookUrl: `${process.env.WEBHOOK_BASE_URL}/api/webhooks/video`,
        
        // ✅ Enhanced: Additional context
        userId: job.userId,
        jobId: job.id,
        options: {
          correlationId: `${job.id}-${outputIndex}`,
          timeout: 300000, // 5 minutes
          priority: request.options?.priority || 'normal',
        }
      }

      console.log(`📡 Calling ModelApiClient for output ${outputIndex}`)

      // ✅ Clean: Single API call through ModelApiClient
      const response = await this.modelApiClient.generateVideo(apiRequest)

      console.log(`✅ ModelApiClient response for output ${outputIndex}:`, response)

      // ✅ Same: Create Generation record (unchanged)
      const generation = await db.generation.create({
        data: {
          id: createId(),
          userId: job.userId,
          jobId: job.id,
          mediaId: createId(),
          externalTaskId: response.taskId,
          mediaType: request.type,
          status: "processing",
          createdAt: new Date(),
          updatedAt: new Date(),
        }
      })

      console.log(`✅ Created generation ${generation.id} with externalTaskId: ${response.taskId}`)

      return generation

    } catch (error) {
      console.error(`❌ Error creating output ${outputIndex}:`, error)
      throw error
    }
  }
}
```

## Benefits of the Refactored Architecture

### 1. **Separation of Concerns**
- **JobService**: Focus purely on job orchestration and database operations
- **ModelApiClient**: Handle API communication coordination
- **Request/Response Handlers**: Manage data transformations
- **Transport Layer**: Handle network communication

### 2. **Enhanced Testability**
- **Isolated Components**: Each component can be tested independently
- **Mock-friendly**: Easy to mock dependencies for unit testing
- **Integration Testing**: Clear boundaries for integration test scenarios

### 3. **Improved Maintainability**
- **Single Responsibility**: Each class has a focused responsibility
- **Provider-agnostic**: Business logic independent of provider specifics
- **Configuration-driven**: Behavior controlled through configuration

### 4. **Better Error Handling**
- **Layered Error Handling**: Errors handled at appropriate abstraction levels
- **Resilience Patterns**: Circuit breakers, retries, timeouts
- **Error Context**: Rich error information with correlation tracking

### 5. **Performance Optimization**
- **Connection Pooling**: Efficient HTTP connection management
- **Caching**: Response caching for repeated requests
- **Load Balancing**: Optimal provider selection based on load

### 6. **Observability**
- **Comprehensive Logging**: Structured logging with correlation IDs
- **Metrics Collection**: Performance and business metrics
- **Distributed Tracing**: Request flow tracking across components

### 7. **Extensibility**
- **Middleware System**: Easy addition of cross-cutting concerns
- **Provider Plugins**: Simple registration of new providers
- **Transform Pipeline**: Flexible request/response transformations

## Migration Strategy

### Phase 1: Parallel Implementation (Weeks 1-3)
- Implement new architecture alongside existing system
- No changes to current JobService
- Build and test new components independently

### Phase 2: Gradual Migration (Weeks 4-5)
- Create feature flag for new vs old implementation
- Migrate low-risk operations first
- Monitor performance and error rates

### Phase 3: Full Cutover (Week 6)
- Switch all operations to new architecture
- Remove old code after stability confirmation
- Update documentation and deployment procedures

### Phase 4: Optimization (Week 7+)
- Fine-tune middleware configurations
- Optimize provider selection algorithms
- Add advanced features (batching, prioritization)

## Configuration Example

### ModelApiClient Configuration

```typescript
// config/api-client.ts
export const modelApiClientConfig: ModelApiClientConfig = {
  transport: {
    timeout: 30000,
    retries: 3,
    circuitBreaker: {
      errorThreshold: 50,
      resetTimeout: 60000,
    },
  },
  
  registry: {
    loadBalancing: {
      strategy: 'least-load',
      weights: {
        'fal': 0.6,
        'replicate': 0.4,
      },
    },
    
    healthCheck: {
      interval: 60000,
      timeout: 5000,
      retries: 2,
    },
    
    selection: {
      preferredProviders: ['fal', 'replicate'],
      fallbackChain: true,
      costOptimization: true,
    },
  },
  
  middleware: [
    {
      type: 'logging',
      order: 1,
      config: {
        level: 'info',
        includeBody: false,
      },
    },
    {
      type: 'metrics',
      order: 2,
      config: {
        namespace: 'video_generation',
        labels: ['provider', 'model', 'status'],
      },
    },
    {
      type: 'caching',
      order: 3,
      config: {
        ttl: 300,
        keyGenerator: 'content-hash',
        storage: 'redis',
      },
    },
    {
      type: 'validation',
      order: 4,
      config: {
        strict: true,
        schemas: './schemas/',
      },
    },
  ],
  
  providers: [
    {
      name: 'fal',
      transformer: 'FalRequestTransformer',
      parser: 'FalResponseParser',
      config: {
        apiKey: process.env.FAL_API_KEY,
        weight: 0.6,
        priority: 1,
      },
    },
    {
      name: 'replicate',
      transformer: 'ReplicateRequestTransformer',
      parser: 'ReplicateResponseParser',
      config: {
        apiKey: process.env.REPLICATE_API_TOKEN,
        weight: 0.4,
        priority: 2,
      },
    },
  ],
}
```

## Conclusion

This refactoring proposal transforms the current tightly-coupled API implementation into a modular, extensible, and maintainable architecture. The new design provides clear separation of concerns, enhanced testability, comprehensive observability, and robust error handling while maintaining backward compatibility during the migration process.

The proposed `ModelApiClient`, `DownstreamRequestHandler`, and `DownstreamResponseHandler` components, along with the supporting middleware system and enhanced provider registry, create a solid foundation for scaling the video generation system to support multiple providers efficiently and reliably.

The phased implementation approach ensures minimal risk while delivering immediate benefits in terms of code quality, maintainability, and system resilience.