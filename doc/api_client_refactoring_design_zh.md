# API客户端重构设计方案

## 执行摘要

本文档提出了对 `packages/api/src/routes/jobs/lib/job-service.ts` 中API调用实现的全面重构方案，通过引入专门的组件来提高模块化、可测试性和可维护性：`ModelApiClient`、`DownstreamRequestHandler`、`DownstreamResponseHandler` 及其支持基础设施。

## 现有架构分析

### 当前实现概述

现有系统 (`job-service.ts:277-417`) 具有以下结构：

```typescript
JobService
├── processJobOutputsAsync() 
├── createSingleOutput()
├── 直接使用 VideoProviderManager
└── 混合职责（任务编排 + API通信）
```

### 当前提供商系统

```typescript
VideoProviderManager
├── 按模型选择提供商
├── 基础健康检查
├── 简单故障转移逻辑
└── 具体提供商 (FalProvider, ReplicateProvider)
```

### 识别的问题

1. **职责混合**: `JobService` 同时处理任务编排和API通信细节
2. **紧耦合**: 直接依赖 `videoProviderManager`
3. **抽象有限**: 业务逻辑和提供商特定细节之间缺乏分离
4. **硬编码逻辑**: API请求映射分散在各个提供商实现中
5. **可测试性差**: 难以模拟和独立测试不同组件
6. **无中间件支持**: 缺乏横切关注点的基础设施（日志、指标、缓存）
7. **错误处理有限**: 基础重试逻辑，没有复杂的错误恢复策略

## 提案架构

### 架构概览

```
┌─────────────────┐    ┌──────────────────────┐    ┌────────────────────┐
│   JobService    │───▶│   ModelApiClient     │───▶│  ProviderRegistry  │
│   任务服务       │    │   模型API客户端       │    │   提供商注册表      │
└─────────────────┘    └──────────────────────┘    └────────────────────┘
                                │                              │
                                ▼                              ▼
                       ┌─────────────────┐            ┌─────────────────┐
                       │  Middleware     │            │   Provider      │
                       │  Pipeline       │            │   Pool          │
                       │  中间件管道      │            │   提供商池       │
                       └─────────────────┘            └─────────────────┘
                                │
                ┌───────────────┼───────────────┐
                ▼               ▼               ▼
    ┌─────────────────┐ ┌─────────────┐ ┌─────────────────┐
    │ Request Handler │ │ API Client  │ │Response Handler │
    │ 请求处理器       │ │ API客户端   │ │ 响应处理器       │
    └─────────────────┘ └─────────────┘ └─────────────────┘
```

### 核心组件

#### 1. ModelApiClient (编排器)

**目的**: 高级API客户端，协调跨多个提供商的视频生成请求。

**职责**:
- 协调请求/响应处理管道
- 管理中间件执行链
- 处理高级错误场景和恢复
- 为不同提供商提供统一接口
- 负载均衡和提供商选择

**接口**:
```typescript
interface ModelApiClient {
  generateVideo(request: VideoGenerationBusinessRequest): Promise<VideoGenerationResult>
  checkStatus(taskId: string, provider?: string): Promise<VideoGenerationStatus>
  cancelTask(taskId: string): Promise<void>
  
  // 配置
  addMiddleware(middleware: ApiMiddleware): void
  setProviderSelector(selector: ProviderSelector): void
}
```

#### 2. DownstreamRequestHandler (请求转换器)

**目的**: 将业务请求转换为提供商特定的API请求。

**职责**:
- 将业务模型映射到提供商特定格式
- 应用请求验证规则
- 处理参数转换和标准化
- 支持请求中间件管道（日志、指标等）
- 管理请求特定配置

**接口**:
```typescript
interface DownstreamRequestHandler {
  prepareRequest(
    businessRequest: VideoGenerationBusinessRequest,
    provider: string,
    context: RequestContext
  ): Promise<ProviderSpecificRequest>
  
  validateRequest(request: ProviderSpecificRequest): Promise<ValidationResult>
  transformRequest(request: ProviderSpecificRequest, transformers: RequestTransformer[]): Promise<ProviderSpecificRequest>
}
```

#### 3. DownstreamResponseHandler (响应转换器)

**目的**: 处理和转换提供商响应为标准格式。

**职责**:
- 解析提供商特定的响应格式
- 将响应映射到标准业务模型
- 处理响应验证和错误提取
- 支持响应中间件管道
- 管理响应标准化

**接口**:
```typescript
interface DownstreamResponseHandler {
  parseResponse(
    rawResponse: ProviderResponse,
    provider: string,
    context: ResponseContext
  ): Promise<StandardVideoGenerationResponse>
  
  validateResponse(response: StandardVideoGenerationResponse): Promise<ValidationResult>
  transformResponse(response: StandardVideoGenerationResponse, transformers: ResponseTransformer[]): Promise<VideoGenerationResult>
}
```

#### 4. ApiClient (传输层)

**目的**: 处理与提供商API的实际HTTP通信。

**职责**:
- 使用适当配置执行HTTP请求
- 处理网络级重试和超时
- 管理连接池和保持连接
- 实现断路器模式
- 支持多种传输协议

**接口**:
```typescript
interface ApiClient {
  post<T>(url: string, data: any, options?: RequestOptions): Promise<T>
  get<T>(url: string, options?: RequestOptions): Promise<T>
  put<T>(url: string, data: any, options?: RequestOptions): Promise<T>
  delete<T>(url: string, options?: RequestOptions): Promise<T>
  
  // 配置
  setRetryPolicy(policy: RetryPolicy): void
  setTimeout(timeout: number): void
  setCircuitBreaker(config: CircuitBreakerConfig): void
}
```

#### 5. 中间件系统

**目的**: 通过管道架构实现横切关注点。

**中间件类型**:

- **日志中间件**: 带相关ID的请求/响应日志
- **指标中间件**: 性能指标、成功率、延迟跟踪
- **缓存中间件**: 重复请求的响应缓存
- **认证中间件**: API密钥管理和刷新
- **限流中间件**: 提供商特定的限流
- **转换中间件**: 自定义请求/响应转换
- **验证中间件**: 输入/输出验证
- **断路器中间件**: 自动故障检测和恢复

**接口**:
```typescript
interface ApiMiddleware {
  name: string
  order: number
  
  onRequest?(request: MiddlewareRequest, context: MiddlewareContext): Promise<MiddlewareRequest>
  onResponse?(response: MiddlewareResponse, context: MiddlewareContext): Promise<MiddlewareResponse>
  onError?(error: Error, context: MiddlewareContext): Promise<void>
}
```

#### 6. 增强的提供商注册表

**目的**: 具有智能选择和健康监控的高级提供商管理。

**职责**:
- 动态提供商发现和注册
- 带指标的复杂健康检查
- 负载均衡策略（轮询、加权、最少负载）
- 带故障转移链的智能故障转移
- 提供商能力匹配
- 性能监控和优化

**接口**:
```typescript
interface ProviderRegistry {
  registerProvider(provider: VideoProvider, config: ProviderConfig): void
  getProvider(criteria: ProviderSelectionCriteria): Promise<VideoProvider>
  getProvidersByCapability(capability: ProviderCapability): Promise<VideoProvider[]>
  
  // 健康管理
  healthCheck(provider?: string): Promise<HealthStatus>
  setHealthCheckInterval(interval: number): void
  
  // 负载均衡
  setLoadBalancingStrategy(strategy: LoadBalancingStrategy): void
  getProviderLoad(provider: string): Promise<ProviderLoad>
}
```

## 详细实施计划

### 第一阶段：核心基础设施 (第1-2周)

#### 1.1 创建基础接口和类型

**文件**: `packages/api/src/lib/api-client/types.ts`

```typescript
// 业务域类型
export interface VideoGenerationBusinessRequest {
  prompt: string
  image?: string
  imageTail?: string
  negativePrompt?: string
  duration: number
  aspectRatio: string
  style: string
  motionRange: string
  seed?: number
  resolution?: string
  modelCode: string
  webhookUrl?: string
  options?: GenerationOptions
}

export interface VideoGenerationResult {
  taskId: string
  status: TaskStatus
  estimatedTime?: number
  resultUrl?: string
  errorMessage?: string
  cost?: number
  provider: string
  metadata?: Record<string, any>
}

// 提供商通信类型
export interface ProviderSpecificRequest {
  provider: string
  endpoint: string
  method: string
  headers: Record<string, string>
  body: any
  timeout?: number
}

export interface ProviderResponse {
  statusCode: number
  headers: Record<string, string>
  body: any
  provider: string
}

// 上下文类型
export interface RequestContext {
  correlationId: string
  userId: string
  jobId: string
  provider: string
  attempt: number
  startTime: number
}

export interface ResponseContext extends RequestContext {
  duration: number
  statusCode: number
}

// 中间件类型
export interface MiddlewareRequest {
  businessRequest: VideoGenerationBusinessRequest
  providerRequest: ProviderSpecificRequest
  context: RequestContext
}

export interface MiddlewareResponse {
  providerResponse: ProviderResponse
  businessResponse: VideoGenerationResult
  context: ResponseContext
}

export type TaskStatus = 'waiting' | 'processing' | 'succeed' | 'failed' | 'cancelled'
```

#### 1.2 实现ApiClient（传输层）

**文件**: `packages/api/src/lib/api-client/transport/api-client.ts`

```typescript
import { HttpClient } from './http-client'
import { CircuitBreaker } from './circuit-breaker'
import { RetryPolicy } from './retry-policy'

export class ApiClient implements IApiClient {
  private httpClient: HttpClient
  private circuitBreaker: CircuitBreaker
  private retryPolicy: RetryPolicy

  constructor(config: ApiClientConfig) {
    this.httpClient = new HttpClient(config.http)
    this.circuitBreaker = new CircuitBreaker(config.circuitBreaker)
    this.retryPolicy = new RetryPolicy(config.retry)
  }

  async post<T>(url: string, data: any, options?: RequestOptions): Promise<T> {
    return this.executeWithResilience(async () => {
      return this.httpClient.post<T>(url, data, {
        ...options,
        timeout: options?.timeout || this.config.timeout,
      })
    })
  }

  private async executeWithResilience<T>(operation: () => Promise<T>): Promise<T> {
    return this.circuitBreaker.execute(async () => {
      return this.retryPolicy.execute(operation)
    })
  }
}
```

#### 1.3 构建中间件管道

**文件**: `packages/api/src/lib/api-client/middleware/pipeline.ts`

```typescript
export class MiddlewarePipeline {
  private middlewares: ApiMiddleware[] = []

  addMiddleware(middleware: ApiMiddleware): void {
    this.middlewares.push(middleware)
    this.middlewares.sort((a, b) => a.order - b.order)
  }

  async executeRequest(
    request: MiddlewareRequest,
    finalHandler: (req: MiddlewareRequest) => Promise<MiddlewareResponse>
  ): Promise<MiddlewareResponse> {
    
    // 构建请求链
    let currentRequest = request
    for (const middleware of this.middlewares) {
      if (middleware.onRequest) {
        currentRequest = await middleware.onRequest(currentRequest, request.context)
      }
    }

    // 执行API调用
    let response: MiddlewareResponse
    try {
      response = await finalHandler(currentRequest)
    } catch (error) {
      // 以相反顺序执行错误处理器
      for (const middleware of this.middlewares.reverse()) {
        if (middleware.onError) {
          await middleware.onError(error, request.context)
        }
      }
      throw error
    }

    // 构建响应链（相反顺序）
    let currentResponse = response
    for (const middleware of this.middlewares.reverse()) {
      if (middleware.onResponse) {
        currentResponse = await middleware.onResponse(currentResponse, response.context)
      }
    }

    return currentResponse
  }
}
```

### 第二阶段：请求和响应处理器 (第2-3周)

#### 2.1 实现DownstreamRequestHandler

**文件**: `packages/api/src/lib/api-client/handlers/request-handler.ts`

```typescript
export class DownstreamRequestHandler implements IDownstreamRequestHandler {
  private transformers: Map<string, RequestTransformer> = new Map()
  private validators: Map<string, RequestValidator> = new Map()

  async prepareRequest(
    businessRequest: VideoGenerationBusinessRequest,
    provider: string,
    context: RequestContext
  ): Promise<ProviderSpecificRequest> {
    
    // 获取提供商特定的转换器
    const transformer = this.getTransformer(provider)
    
    // 将业务请求转换为提供商格式
    const providerRequest = await transformer.transform(businessRequest, context)
    
    // 验证转换后的请求
    const validator = this.getValidator(provider)
    const validationResult = await validator.validate(providerRequest)
    
    if (!validationResult.isValid) {
      throw new ValidationError(validationResult.errors)
    }

    return providerRequest
  }

  registerTransformer(provider: string, transformer: RequestTransformer): void {
    this.transformers.set(provider, transformer)
  }

  private getTransformer(provider: string): RequestTransformer {
    const transformer = this.transformers.get(provider)
    if (!transformer) {
      throw new Error(`找不到提供商的请求转换器: ${provider}`)
    }
    return transformer
  }
}
```

#### 2.2 实现提供商特定的请求转换器

**文件**: `packages/api/src/lib/api-client/transformers/fal-request-transformer.ts`

```typescript
export class FalRequestTransformer implements RequestTransformer {
  async transform(
    request: VideoGenerationBusinessRequest,
    context: RequestContext
  ): Promise<ProviderSpecificRequest> {
    
    const endpoint = this.getModelEndpoint(request.modelCode)
    
    const body = {
      prompt: request.prompt,
      image_url: request.image,
      duration: request.duration,
      aspect_ratio: this.mapAspectRatio(request.aspectRatio),
      style: request.style,
      motion_range: request.motionRange,
      seed: request.seed,
      negative_prompt: request.negativePrompt,
      ...(request.imageTail && { image_tail_url: request.imageTail }),
    }

    return {
      provider: 'fal',
      endpoint: `https://fal.run/${endpoint}`,
      method: 'POST',
      headers: {
        'Authorization': `Key ${process.env.FAL_API_KEY}`,
        'Content-Type': 'application/json',
        'X-Correlation-ID': context.correlationId,
      },
      body,
      timeout: 30000,
    }
  }

  private getModelEndpoint(modelCode: string): string {
    const endpoints = {
      'fal-luma-dream-machine': 'fal-ai/luma-dream-machine',
      'fal-runway-gen3': 'fal-ai/runway-gen3',
      // ... 其他映射
    }
    
    const endpoint = endpoints[modelCode]
    if (!endpoint) {
      throw new Error(`不支持的模型: ${modelCode}`)
    }
    
    return endpoint
  }

  private mapAspectRatio(ratio: string): string {
    const mapping = {
      'ASPECT_16_9': '16:9',
      'ASPECT_9_16': '9:16',
      // ... 其他映射
    }
    return mapping[ratio] || '16:9'
  }
}
```

#### 2.3 实现DownstreamResponseHandler

**文件**: `packages/api/src/lib/api-client/handlers/response-handler.ts`

```typescript
export class DownstreamResponseHandler implements IDownstreamResponseHandler {
  private parsers: Map<string, ResponseParser> = new Map()
  private transformers: ResponseTransformer[] = []

  async parseResponse(
    rawResponse: ProviderResponse,
    provider: string,
    context: ResponseContext
  ): Promise<StandardVideoGenerationResponse> {
    
    const parser = this.getParser(provider)
    const parsed = await parser.parse(rawResponse, context)
    
    // 应用转换
    let result = parsed
    for (const transformer of this.transformers) {
      result = await transformer.transform(result, context)
    }
    
    return result
  }

  registerParser(provider: string, parser: ResponseParser): void {
    this.parsers.set(provider, parser)
  }

  addTransformer(transformer: ResponseTransformer): void {
    this.transformers.push(transformer)
  }
}
```

### 第三阶段：增强的提供商注册表 (第3-4周)

#### 3.1 实现提供商注册表

**文件**: `packages/api/src/lib/api-client/registry/provider-registry.ts`

```typescript
export class ProviderRegistry implements IProviderRegistry {
  private providers: Map<string, RegisteredProvider> = new Map()
  private loadBalancer: LoadBalancer
  private healthChecker: HealthChecker
  private providerSelector: ProviderSelector

  constructor(config: ProviderRegistryConfig) {
    this.loadBalancer = new LoadBalancer(config.loadBalancing)
    this.healthChecker = new HealthChecker(config.healthCheck)
    this.providerSelector = new ProviderSelector(config.selection)
  }

  async getProvider(criteria: ProviderSelectionCriteria): Promise<VideoProvider> {
    // 根据条件获取符合条件的提供商
    const eligibleProviders = this.getEligibleProviders(criteria)
    
    if (eligibleProviders.length === 0) {
      throw new Error('给定条件下没有可用的提供商')
    }

    // 应用健康过滤
    const healthyProviders = await this.healthChecker.filterHealthy(eligibleProviders)
    
    if (healthyProviders.length === 0) {
      throw new Error('没有健康的提供商可用')
    }

    // 使用负载均衡策略选择提供商
    const selectedProvider = await this.loadBalancer.selectProvider(healthyProviders)
    
    return selectedProvider.instance
  }

  async registerProvider(provider: VideoProvider, config: ProviderConfig): Promise<void> {
    const registeredProvider: RegisteredProvider = {
      instance: provider,
      config,
      status: 'active',
      registeredAt: new Date(),
      metrics: new ProviderMetrics(),
    }

    this.providers.set(provider.name, registeredProvider)
    
    // 开始健康监控
    await this.healthChecker.monitor(provider.name)
    
    console.log(`提供商已注册: ${provider.name}`)
  }

  private getEligibleProviders(criteria: ProviderSelectionCriteria): RegisteredProvider[] {
    return Array.from(this.providers.values()).filter(provider => {
      // 检查模型支持
      if (criteria.modelCode && !provider.instance.supportedModels.includes(criteria.modelCode)) {
        return false
      }

      // 检查功能支持
      if (criteria.features && !this.hasRequiredFeatures(provider, criteria.features)) {
        return false
      }

      // 检查成本约束
      if (criteria.maxCost && provider.config.averageCost > criteria.maxCost) {
        return false
      }

      return provider.status === 'active'
    })
  }
}
```

#### 3.2 实现负载均衡策略

**文件**: `packages/api/src/lib/api-client/registry/load-balancer.ts`

```typescript
export class LoadBalancer {
  private strategy: LoadBalancingStrategy

  constructor(config: LoadBalancingConfig) {
    this.strategy = this.createStrategy(config.strategy)
  }

  async selectProvider(providers: RegisteredProvider[]): Promise<RegisteredProvider> {
    return this.strategy.select(providers)
  }

  private createStrategy(strategyType: string): LoadBalancingStrategy {
    switch (strategyType) {
      case 'round-robin':
        return new RoundRobinStrategy()
      case 'weighted':
        return new WeightedStrategy()
      case 'least-load':
        return new LeastLoadStrategy()
      case 'response-time':
        return new ResponseTimeStrategy()
      default:
        return new RoundRobinStrategy()
    }
  }
}

export class LeastLoadStrategy implements LoadBalancingStrategy {
  async select(providers: RegisteredProvider[]): Promise<RegisteredProvider> {
    // 找到当前负载最低的提供商
    const providerLoads = await Promise.all(
      providers.map(async provider => ({
        provider,
        load: await this.getCurrentLoad(provider)
      }))
    )

    const selectedProvider = providerLoads.reduce((min, current) => 
      current.load < min.load ? current : min
    )

    return selectedProvider.provider
  }

  private async getCurrentLoad(provider: RegisteredProvider): Promise<number> {
    // 获取当前负载的实现（活跃请求、队列大小等）
    return provider.metrics.activeRequests + (provider.metrics.queueSize * 0.5)
  }
}
```

### 第四阶段：ModelApiClient集成 (第4-5周)

#### 4.1 实现ModelApiClient

**文件**: `packages/api/src/lib/api-client/model-api-client.ts`

```typescript
export class ModelApiClient implements IModelApiClient {
  private requestHandler: DownstreamRequestHandler
  private responseHandler: DownstreamResponseHandler
  private apiClient: ApiClient
  private providerRegistry: ProviderRegistry
  private middlewarePipeline: MiddlewarePipeline
  private correlationIdGenerator: CorrelationIdGenerator

  constructor(config: ModelApiClientConfig) {
    this.requestHandler = new DownstreamRequestHandler()
    this.responseHandler = new DownstreamResponseHandler()
    this.apiClient = new ApiClient(config.transport)
    this.providerRegistry = new ProviderRegistry(config.registry)
    this.middlewarePipeline = new MiddlewarePipeline()
    this.correlationIdGenerator = new CorrelationIdGenerator()

    this.initializeMiddleware(config.middleware)
    this.registerProviders(config.providers)
  }

  async generateVideo(request: VideoGenerationBusinessRequest): Promise<VideoGenerationResult> {
    const correlationId = this.correlationIdGenerator.generate()
    const context: RequestContext = {
      correlationId,
      userId: request.userId || 'anonymous',
      jobId: request.jobId || 'unknown',
      provider: '',
      attempt: 1,
      startTime: Date.now(),
    }

    // 选择提供商
    const provider = await this.providerRegistry.getProvider({
      modelCode: request.modelCode,
      features: this.extractRequiredFeatures(request),
    })
    
    context.provider = provider.name

    // 构建中间件请求
    const middlewareRequest: MiddlewareRequest = {
      businessRequest: request,
      providerRequest: null, // 将由请求处理器填充
      context,
    }

    // 通过中间件管道执行
    const response = await this.middlewarePipeline.executeRequest(
      middlewareRequest,
      async (req) => this.executeApiCall(req, provider)
    )

    return response.businessResponse
  }

  private async executeApiCall(
    request: MiddlewareRequest,
    provider: VideoProvider
  ): Promise<MiddlewareResponse> {
    
    // 准备提供商特定的请求
    const providerRequest = await this.requestHandler.prepareRequest(
      request.businessRequest,
      provider.name,
      request.context
    )

    request.providerRequest = providerRequest

    // 执行API调用
    const rawResponse = await this.apiClient.post(
      providerRequest.endpoint,
      providerRequest.body,
      {
        headers: providerRequest.headers,
        timeout: providerRequest.timeout,
      }
    )

    const providerResponse: ProviderResponse = {
      statusCode: rawResponse.status,
      headers: rawResponse.headers,
      body: rawResponse.data,
      provider: provider.name,
    }

    // 解析和转换响应
    const responseContext: ResponseContext = {
      ...request.context,
      duration: Date.now() - request.context.startTime,
      statusCode: rawResponse.status,
    }

    const businessResponse = await this.responseHandler.parseResponse(
      providerResponse,
      provider.name,
      responseContext
    )

    return {
      providerResponse,
      businessResponse,
      context: responseContext,
    }
  }

  addMiddleware(middleware: ApiMiddleware): void {
    this.middlewarePipeline.addMiddleware(middleware)
  }
}
```

### 第五阶段：预构建中间件 (第5-6周)

#### 5.1 日志中间件

**文件**: `packages/api/src/lib/api-client/middleware/logging-middleware.ts`

```typescript
export class LoggingMiddleware implements ApiMiddleware {
  name = 'logging'
  order = 1

  constructor(private logger: Logger) {}

  async onRequest(request: MiddlewareRequest, context: MiddlewareContext): Promise<MiddlewareRequest> {
    this.logger.info('API请求', {
      correlationId: context.correlationId,
      provider: context.provider,
      modelCode: request.businessRequest.modelCode,
      userId: context.userId,
      jobId: context.jobId,
    })

    return request
  }

  async onResponse(response: MiddlewareResponse, context: MiddlewareContext): Promise<MiddlewareResponse> {
    this.logger.info('API响应', {
      correlationId: context.correlationId,
      provider: context.provider,
      statusCode: context.statusCode,
      duration: context.duration,
      status: response.businessResponse.status,
    })

    return response
  }

  async onError(error: Error, context: MiddlewareContext): Promise<void> {
    this.logger.error('API错误', {
      correlationId: context.correlationId,
      provider: context.provider,
      error: error.message,
      stack: error.stack,
    })
  }
}
```

#### 5.2 指标中间件

**文件**: `packages/api/src/lib/api-client/middleware/metrics-middleware.ts`

```typescript
export class MetricsMiddleware implements ApiMiddleware {
  name = 'metrics'
  order = 2

  constructor(private metricsCollector: MetricsCollector) {}

  async onRequest(request: MiddlewareRequest, context: MiddlewareContext): Promise<MiddlewareRequest> {
    this.metricsCollector.incrementCounter('api_requests_total', {
      provider: context.provider,
      model: request.businessRequest.modelCode,
    })

    return request
  }

  async onResponse(response: MiddlewareResponse, context: MiddlewareContext): Promise<MiddlewareResponse> {
    this.metricsCollector.recordHistogram('api_request_duration_seconds', context.duration / 1000, {
      provider: context.provider,
      status: response.businessResponse.status,
    })

    this.metricsCollector.incrementCounter('api_responses_total', {
      provider: context.provider,
      status: response.businessResponse.status,
      status_code: context.statusCode.toString(),
    })

    return response
  }

  async onError(error: Error, context: MiddlewareContext): Promise<void> {
    this.metricsCollector.incrementCounter('api_errors_total', {
      provider: context.provider,
      error_type: error.constructor.name,
    })
  }
}
```

### 第六阶段：与JobService集成 (第6周)

#### 6.1 重构JobService

**文件**: `packages/api/src/routes/jobs/lib/job-service.ts` (更新版)

```typescript
export class JobService {
  private modelApiClient: ModelApiClient

  constructor() {
    this.modelApiClient = new ModelApiClient({
      transport: {
        timeout: 30000,
        retries: 3,
      },
      registry: {
        loadBalancing: { strategy: 'least-load' },
        healthCheck: { interval: 60000 },
      },
      middleware: [
        { type: 'logging', config: {} },
        { type: 'metrics', config: {} },
        { type: 'caching', config: { ttl: 300 } },
      ],
    })
  }

  // ... 现有的createJob方法保持不变 ...

  private static async createSingleOutput(
    job: any,
    jobParams: any,
    request: CreateJobRequest,
    outputIndex: number
  ) {
    try {
      // ✅ 新功能：使用ModelApiClient替代直接调用提供商
      const apiRequest: VideoGenerationBusinessRequest = {
        prompt: jobParams.prompt || '',
        image: jobParams.image,
        imageTail: jobParams.imageTail,
        negativePrompt: jobParams.negativePrompt,
        duration: jobParams.duration,
        aspectRatio: jobParams.aspectRatio,
        style: jobParams.style,
        motionRange: jobParams.motionRange,
        seed: jobParams.seed ? jobParams.seed + outputIndex : undefined,
        resolution: jobParams.resolution,
        modelCode: jobParams.modelCode,
        webhookUrl: `${process.env.WEBHOOK_BASE_URL}/api/webhooks/video`,
        
        // ✅ 增强：额外的上下文
        userId: job.userId,
        jobId: job.id,
        options: {
          correlationId: `${job.id}-${outputIndex}`,
          timeout: 300000, // 5分钟
          priority: request.options?.priority || 'normal',
        }
      }

      console.log(`📡 调用ModelApiClient生成输出 ${outputIndex}`)

      // ✅ 简洁：通过ModelApiClient进行单个API调用
      const response = await this.modelApiClient.generateVideo(apiRequest)

      console.log(`✅ ModelApiClient响应输出 ${outputIndex}:`, response)

      // ✅ 相同：创建Generation记录（未更改）
      const generation = await db.generation.create({
        data: {
          id: createId(),
          userId: job.userId,
          jobId: job.id,
          mediaId: createId(),
          externalTaskId: response.taskId,
          mediaType: request.type,
          status: "processing",
          createdAt: new Date(),
          updatedAt: new Date(),
        }
      })

      console.log(`✅ 创建生成记录 ${generation.id}，外部任务ID: ${response.taskId}`)

      return generation

    } catch (error) {
      console.error(`❌ 创建输出 ${outputIndex} 时出错:`, error)
      throw error
    }
  }
}
```

## 重构架构的优势

### 1. **关注点分离**
- **JobService**: 专注于任务编排和数据库操作
- **ModelApiClient**: 处理API通信协调
- **请求/响应处理器**: 管理数据转换
- **传输层**: 处理网络通信

### 2. **增强的可测试性**
- **独立组件**: 每个组件可以独立测试
- **易于模拟**: 容易模拟依赖项进行单元测试
- **集成测试**: 清晰的边界用于集成测试场景

### 3. **改进的可维护性**
- **单一职责**: 每个类都有专注的职责
- **提供商无关**: 业务逻辑独立于提供商特定细节
- **配置驱动**: 通过配置控制行为

### 4. **更好的错误处理**
- **分层错误处理**: 在适当的抽象级别处理错误
- **韧性模式**: 断路器、重试、超时
- **错误上下文**: 带相关跟踪的丰富错误信息

### 5. **性能优化**
- **连接池**: 高效的HTTP连接管理
- **缓存**: 重复请求的响应缓存
- **负载均衡**: 基于负载的最优提供商选择

### 6. **可观察性**
- **全面日志**: 带相关ID的结构化日志
- **指标收集**: 性能和业务指标
- **分布式跟踪**: 跨组件的请求流跟踪

### 7. **可扩展性**
- **中间件系统**: 容易添加横切关注点
- **提供商插件**: 简单注册新提供商
- **转换管道**: 灵活的请求/响应转换

## 迁移策略

### 第一阶段：并行实现 (第1-3周)
- 在现有系统旁边实现新架构
- 不对当前JobService进行更改
- 独立构建和测试新组件

### 第二阶段：渐进迁移 (第4-5周)
- 为新旧实现创建功能标志
- 首先迁移低风险操作
- 监控性能和错误率

### 第三阶段：完全切换 (第6周)
- 将所有操作切换到新架构
- 在稳定性确认后删除旧代码
- 更新文档和部署程序

### 第四阶段：优化 (第7周+)
- 微调中间件配置
- 优化提供商选择算法
- 添加高级功能（批处理、优先级）

## 配置示例

### ModelApiClient配置

```typescript
// config/api-client.ts
export const modelApiClientConfig: ModelApiClientConfig = {
  transport: {
    timeout: 30000,
    retries: 3,
    circuitBreaker: {
      errorThreshold: 50,
      resetTimeout: 60000,
    },
  },
  
  registry: {
    loadBalancing: {
      strategy: 'least-load',
      weights: {
        'fal': 0.6,
        'replicate': 0.4,
      },
    },
    
    healthCheck: {
      interval: 60000,
      timeout: 5000,
      retries: 2,
    },
    
    selection: {
      preferredProviders: ['fal', 'replicate'],
      fallbackChain: true,
      costOptimization: true,
    },
  },
  
  middleware: [
    {
      type: 'logging',
      order: 1,
      config: {
        level: 'info',
        includeBody: false,
      },
    },
    {
      type: 'metrics',
      order: 2,
      config: {
        namespace: 'video_generation',
        labels: ['provider', 'model', 'status'],
      },
    },
    {
      type: 'caching',
      order: 3,
      config: {
        ttl: 300,
        keyGenerator: 'content-hash',
        storage: 'redis',
      },
    },
    {
      type: 'validation',
      order: 4,
      config: {
        strict: true,
        schemas: './schemas/',
      },
    },
  ],
  
  providers: [
    {
      name: 'fal',
      transformer: 'FalRequestTransformer',
      parser: 'FalResponseParser',
      config: {
        apiKey: process.env.FAL_API_KEY,
        weight: 0.6,
        priority: 1,
      },
    },
    {
      name: 'replicate',
      transformer: 'ReplicateRequestTransformer',
      parser: 'ReplicateResponseParser',
      config: {
        apiKey: process.env.REPLICATE_API_TOKEN,
        weight: 0.4,
        priority: 2,
      },
    },
  ],
}
```

## 结论

这个重构提案将当前紧耦合的API实现转换为模块化、可扩展和可维护的架构。新设计提供了清晰的关注点分离、增强的可测试性、全面的可观察性和强大的错误处理，同时在迁移过程中保持向后兼容性。

提案的 `ModelApiClient`、`DownstreamRequestHandler` 和 `DownstreamResponseHandler` 组件，以及支持的中间件系统和增强的提供商注册表，为扩展视频生成系统以高效可靠地支持多个提供商创建了坚实的基础。

分阶段实施方法确保最小风险，同时在代码质量、可维护性和系统韧性方面提供即时收益。