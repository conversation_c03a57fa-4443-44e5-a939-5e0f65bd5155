# 批量API设计文档

## 概述

批量API为应用程序中的视频生成管理提供高效的批处理操作。它遵循RESTful设计原则，使用基于事务的操作来确保数据一致性和原子性。

## 基础配置

- **基础路径**: `/bulk`
- **身份验证**: 所有端点都需要认证
- **内容类型**: `application/json`
- **最大批量大小**: 每次请求最多50个项目

## 核心设计原则

### 1. 原子操作
- 所有批量操作都基于事务
- 要么全部成功，要么全部不处理
- 在整个应用程序中维护数据一致性

### 2. 基于权限的访问
- 用户只能操作自己的生成内容
- 管理员用户拥有提升的权限
- 细粒度的访问控制错误报告

### 3. 全面的错误处理
- 详细的错误响应和特定的HTTP状态码
- 针对不同失败场景的清晰错误消息
- 一致的错误响应格式

### 4. 性能优化
- 批量大小限制以防止资源耗尽
- 使用单一事务的高效数据库查询
- 全面的日志记录用于监控和调试

## API接口

### 1. 批量删除生成内容

**接口**: `POST /api/bulk/generations/delete`

**描述**: 使用原子事务一次性软删除多个生成内容。

#### 请求格式

```json
{
  "generationIds": ["string"] // 1-50个生成内容ID
}
```

#### 响应格式

**成功 (200)**:
```json
{
  "deleted": ["string"] // 成功删除的生成内容ID数组
}
```

**错误 (4xx/5xx)**:
```json
{
  "error": "string" // 描述性错误消息
}
```

#### HTTP状态码

| 状态码 | 描述 | 场景 |
|--------|------|------|
| `200` | 成功 | 所有生成内容删除成功 |
| `400` | 请求错误 | 无效参数、空数组或某些生成内容无法删除 |
| `401` | 未授权 | 需要身份验证 |
| `403` | 禁止访问 | 对某些生成内容的访问被拒绝 |
| `404` | 未找到 | 某些生成内容未找到 |
| `409` | 冲突 | 某些生成内容已被删除 |
| `500` | 内部服务器错误 | 数据库或系统错误 |

#### 请求示例

```bash
curl -X POST /api/bulk/generations/delete \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "generationIds": [
      "gen_12345",
      "gen_67890",
      "gen_abcdef"
    ]
  }'
```

#### 响应示例

**成功**:
```json
{
  "deleted": [
    "gen_12345",
    "gen_67890",
    "gen_abcdef"
  ]
}
```

**错误 - 访问被拒绝**:
```json
{
  "error": "对某些生成内容的访问被拒绝"
}
```

### 2. 批量收藏生成内容

**接口**: `POST /api/bulk/generations/favorite`

**描述**: 一次性为多个生成内容设置收藏状态。

#### 请求格式

```json
{
  "generationIds": ["string"], // 1-50个生成内容ID
  "favorite": boolean          // 目标收藏状态
}
```

#### 响应格式

**成功 (200)**:
```json
{
  "updated": ["string"] // 成功更新的生成内容ID数组
}
```

**错误 (4xx/5xx)**:
```json
{
  "error": "string" // 描述性错误消息
}
```

#### HTTP状态码

| 状态码 | 描述 | 场景 |
|--------|------|------|
| `200` | 成功 | 所有生成内容更新成功 |
| `400` | 请求错误 | 无效参数或空数组 |
| `401` | 未授权 | 需要身份验证 |
| `403` | 禁止访问 | 对某些生成内容的访问被拒绝 |
| `404` | 未找到 | 某些生成内容未找到 |
| `500` | 内部服务器错误 | 数据库或系统错误 |

#### 请求示例

```bash
curl -X POST /api/bulk/generations/favorite \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "generationIds": [
      "gen_12345",
      "gen_67890"
    ],
    "favorite": true
  }'
```

#### 响应示例

**成功**:
```json
{
  "updated": [
    "gen_12345",
    "gen_67890"
  ]
}
```

**错误 - 未找到**:
```json
{
  "error": "某些生成内容未找到"
}
```

## 安全考虑

### 身份验证和授权

- **身份验证**: 所有端点都需要有效的会话令牌
- **用户隔离**: 用户只能访问自己的生成内容
- **管理员覆盖**: 管理员用户拥有提升的权限
- **速率限制**: 通过批量大小限制隐式实现

### 数据验证

- **输入验证**: Zod模式强制执行严格的类型检查
- **数组长度**: 每次请求1-50个项目以防止滥用
- **ID格式**: 基于字符串的生成内容标识符
- **布尔验证**: 收藏状态的严格布尔类型

## 错误处理策略

### 错误分类

1. **客户端错误 (4xx)**
   - `400`: 无效的请求参数
   - `401`: 缺少或无效的身份验证
   - `403`: 权限不足
   - `404`: 资源未找到
   - `409`: 与当前状态冲突

2. **服务器错误 (5xx)**
   - `500`: 内部服务器错误或数据库问题

### 错误响应格式

所有错误都遵循一致的格式:
```json
{
  "error": "人类可读的错误消息"
}
```

### 错误映射逻辑

API将服务级错误映射到适当的HTTP状态码:

- 包含 "not found" → `404`
- 包含 "Access denied" → `403`
- 包含 "already deleted" → `409`
- 包含 "No generation IDs" → `400`
- 包含 "cannot be deleted" → `400`
- 默认 → `500`

## 性能特征

### 吞吐量限制

- **最大批量大小**: 每次请求最多50个项目
- **并发请求**: 受身份验证速率限制约束
- **事务超时**: 应用数据库级事务超时

### 数据库操作

- **原子事务**: 所有操作都使用数据库事务
- **软删除**: 生成内容被标记为已删除，而不是物理删除
- **批量更新**: 高效的批量SQL操作
- **索引使用**: 利用现有的数据库索引来提高性能

## 监控和日志记录

### 请求日志

```javascript
console.log(`[BulkAPI] 用户 ${user.id} 请求批量删除 ${generationIds.length} 个生成内容`);
```

### 成功日志

```javascript
console.log(`[BulkAPI] 用户 ${user.id} 批量删除完成: ${result.deleted.length}/${generationIds.length} 已删除`);
```

### 错误日志

```javascript
console.warn(`[BulkAPI] 用户 ${user.id} 批量删除失败:`, result.error);
```

## OpenAPI集成

批量API完全使用OpenAPI规范进行文档化:

- **标签**: "Bulk Operations"
- **请求/响应模式**: 基于Zod的验证
- **全面文档**: 详细的描述和示例
- **类型安全**: 完整的TypeScript集成

## 未来增强

### 潜在扩展

1. **额外的批量操作**
   - 批量归档/取消归档
   - 批量标签分配
   - 批量元数据更新

2. **增强的错误报告**
   - 部分成功响应
   - 每个项目的错误详情
   - 边缘情况的警告消息

3. **性能优化**
   - 大型结果集的分页
   - 超大批次的后台处理
   - 频繁访问数据的缓存

4. **高级功能**
   - 验证的试运行模式
   - 长时间运行操作的进度跟踪
   - 完成时的Webhook通知

## API使用指南

### 最佳实践

1. **批量大小优化**
   - 使用合理的批量大小（通常10-30个项目最佳）
   - 考虑网络延迟与处理效率的平衡

2. **错误处理**
   - 始终检查响应状态码
   - 为瞬态故障实现重试逻辑
   - 记录所有错误响应以供调试

3. **用户体验**
   - 为批量操作显示进度指示器
   - 为部分失败提供清晰的反馈
   - 允许用户重试失败的操作

4. **性能考虑**
   - 避免来自同一用户的并发批量请求
   - 如需要，实现客户端请求队列
   - 在适当时缓存结果

### 集成示例

#### 前端集成 (React)

```typescript
const bulkDeleteGenerations = async (generationIds: string[]) => {
  try {
    const response = await fetch('/api/bulk/generations/delete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ generationIds })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error);
    }

    const result = await response.json();
    console.log(`成功删除 ${result.deleted.length} 个生成内容`);
    return result;
  } catch (error) {
    console.error('批量删除失败:', error);
    throw error;
  }
};
```

#### 服务层集成

```typescript
// 使用现有的GenerationService
const result = await GenerationService.batchDeleteGenerations(user, generationIds);

if (result.deleted) {
  // 处理成功
  updateUI(result.deleted);
} else if (result.error) {
  // 处理错误
  showErrorMessage(result.error);
}
```

## 结论

批量API为视频生成内容的批处理操作提供了一个强大、安全且高效的解决方案。其设计优先考虑数据一致性、用户安全性和操作可靠性，同时保持高性能和全面的错误处理。

API的基于事务的方法确保数据完整性，而其全面的错误处理和日志记录提供了出色的调试和监控能力。该设计是可扩展的，可以随着应用程序的发展适应未来的批量操作。