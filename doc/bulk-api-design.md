# Bulk API Design

## Overview

The Bulk API provides efficient batch operations for managing video generations in the application. It supports three core operations: **batch deletion**, **batch favoriting**, and **batch downloading**. The API follows RESTful design principles with transaction-based operations to ensure data consistency, security, and atomicity.

## Base Configuration

- **Base Path**: `/bulk`
- **Authentication**: Required for all endpoints
- **Content Type**: `application/json`
- **Maximum Batch Size**: 50 items per request

## Core Design Principles

### 1. Atomic Operations
- All bulk operations are transaction-based
- Either all items succeed or none are processed
- Maintains data consistency across the application

### 2. Permission-Based Access
- Users can only operate on their own generations
- Admin users have elevated permissions
- Fine-grained error reporting for access control

### 3. Comprehensive Error Handling
- Detailed error responses with specific HTTP status codes
- Clear error messages for different failure scenarios
- Consistent error response format

### 4. Performance Optimization
- Batch size limits to prevent resource exhaustion
- Efficient database queries with single transactions
- Comprehensive logging for monitoring and debugging

## API Endpoints

### 1. Batch Delete Generations

**Endpoint**: `POST /api/bulk/generations/delete`

**Description**: Soft delete multiple generations at once using atomic transactions.

#### Request Schema

```json
{
  "generationIds": ["string"] // 1-50 generation IDs
}
```

#### Response Schema

**Success (200)**:
```json
{
  "deleted": ["string"] // Array of successfully deleted generation IDs
}
```

**Error (4xx/5xx)**:
```json
{
  "error": "string" // Descriptive error message
}
```

#### HTTP Status Codes

| Code | Description | Scenario |
|------|-------------|----------|
| `200` | Success | All generations deleted successfully |
| `400` | Bad Request | Invalid parameters, empty array, or some generations cannot be deleted |
| `401` | Unauthorized | Authentication required |
| `403` | Forbidden | Access denied for some generations |
| `404` | Not Found | Some generations not found |
| `409` | Conflict | Some generations already deleted |
| `500` | Internal Server Error | Database or system error |

#### Request Example

```bash
curl -X POST /api/bulk/generations/delete \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "generationIds": [
      "gen_12345",
      "gen_67890",
      "gen_abcdef"
    ]
  }'
```

#### Response Examples

**Success**:
```json
{
  "deleted": [
    "gen_12345",
    "gen_67890",
    "gen_abcdef"
  ]
}
```

**Error - Access Denied**:
```json
{
  "error": "Access denied for some generations"
}
```

### 2. Batch Favorite Generations

**Endpoint**: `POST /api/bulk/generations/favorite`

**Description**: Set favorite status for multiple generations at once.

#### Request Schema

```json
{
  "generationIds": ["string"], // 1-50 generation IDs
  "favorite": boolean          // Target favorite status
}
```

#### Response Schema

**Success (200)**:
```json
{
  "updated": ["string"] // Array of successfully updated generation IDs
}
```

**Error (4xx/5xx)**:
```json
{
  "error": "string" // Descriptive error message
}
```

#### HTTP Status Codes

| Code | Description | Scenario |
|------|-------------|----------|
| `200` | Success | All generations updated successfully |
| `400` | Bad Request | Invalid parameters or empty array |
| `401` | Unauthorized | Authentication required |
| `403` | Forbidden | Access denied for some generations |
| `404` | Not Found | Some generations not found |
| `500` | Internal Server Error | Database or system error |

#### Request Example

```bash
curl -X POST /api/bulk/generations/favorite \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "generationIds": [
      "gen_12345",
      "gen_67890"
    ],
    "favorite": true
  }'
```

#### Response Examples

**Success**:
```json
{
  "updated": [
    "gen_12345",
    "gen_67890"
  ]
}
```

**Error - Not Found**:
```json
{
  "error": "Some generations not found"
}
```

### 3. Batch Download Generations

**Endpoint**: `POST /api/bulk/generations/download`

**Description**: Generate temporary download URLs for multiple generations at once.

#### Request Schema

```json
{
  "generationIds": ["string"] // 1-50 generation IDs
}
```

#### Response Schema

**Success (200)**:
```json
{
  "downloadUrls": ["string"] // Array of signed temporary download URLs
}
```

**Error (4xx/5xx)**:
```json
{
  "error": "string" // Descriptive error message
}
```

#### HTTP Status Codes

| Code | Description | Scenario |
|------|-------------|----------|
| `200` | Success | All download URLs generated successfully |
| `400` | Bad Request | Invalid parameters or empty array |
| `401` | Unauthorized | Authentication required |
| `403` | Forbidden | Access denied for some generations |
| `404` | Not Found | Some generations or files not found |
| `422` | Unprocessable Entity | Some files are not ready for download |
| `500` | Internal Server Error | Storage service or system error |
| `503` | Service Unavailable | Storage service temporarily unavailable |

#### Request Example

```bash
curl -X POST /api/bulk/generations/download \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "generationIds": [
      "gen_12345",
      "gen_67890",
      "gen_abcdef"
    ]
  }'
```

#### Response Examples

**Success**:
```json
{
  "downloadUrls": [
    "https://storage.example.com/signed-url-1?token=abc123&expires=1704106800",
    "https://storage.example.com/signed-url-2?token=def456&expires=1704106800", 
    "https://storage.example.com/signed-url-3?token=ghi789&expires=1704106800"
  ]
}
```

**Error - Access Denied**:
```json
{
  "error": "Access denied for some generations"
}
```

**Error - Files Not Ready**:
```json
{
  "error": "Some files are still processing and not available for download"
}
```

#### Security Considerations

**Download URL Security**:
- **Signed URLs**: All download URLs are cryptographically signed with time-based expiration
- **Limited Lifetime**: URLs expire after 1 hour to prevent unauthorized sharing
- **Single Use**: URLs may be limited to single-use or IP-restricted (implementation dependent)
- **HTTPS Only**: All download URLs must use HTTPS protocol

**Access Control**:
- Users can only download their own generations
- Admin users have elevated permissions for all files
- File access is validated before URL generation

**Rate Limiting**:
- Download URL generation is rate-limited per user
- Large batches may be queued or processed asynchronously

#### Performance Characteristics

**URL Generation**:
- **Batch Processing**: Efficient parallel URL generation for multiple files
- **Simplified Response**: Direct URL array for easy client-side processing
- **Storage Integration**: Direct integration with cloud storage providers (S3, GCS, etc.)

**File Validation**:
- **Existence Check**: Verifies files exist before generating URLs
- **Status Validation**: Ensures generations are in 'succeeded' status
- **Size Limits**: May enforce per-file or total batch size limits

#### Error Handling Specifics

**Partial Success Handling**:
The API follows an all-or-nothing approach consistent with other bulk operations. If any generation fails validation or URL generation, the entire request fails with appropriate error details.

**File Status Validation**:
- `422 Unprocessable Entity`: When some generations are still processing
- `404 Not Found`: When generation records exist but files are missing
- `403 Forbidden`: When user lacks download permissions

## Security Considerations

### Authentication & Authorization

- **Authentication**: All endpoints require valid session tokens
- **User Isolation**: Users can only access their own generations
- **Admin Override**: Admin users have elevated permissions
- **Rate Limiting**: Implicit through batch size restrictions

### Data Validation

- **Input Validation**: Zod schemas enforce strict type checking
- **Array Length**: 1-50 items per request to prevent abuse
- **ID Format**: String-based generation identifiers
- **Boolean Validation**: Strict boolean type for favorite status

## Error Handling Strategy

### Error Classification

1. **Client Errors (4xx)**
   - `400`: Invalid request parameters
   - `401`: Missing or invalid authentication
   - `403`: Insufficient permissions
   - `404`: Resource not found
   - `409`: Conflict with current state

2. **Server Errors (5xx)**
   - `500`: Internal server error or database issues

### Error Response Format

All errors follow a consistent format:
```json
{
  "error": "Human-readable error message"
}
```

### Error Mapping Logic

The API maps service-level errors to appropriate HTTP status codes:

- Contains "not found" → `404`
- Contains "Access denied" → `403`
- Contains "already deleted" → `409`
- Contains "No generation IDs" → `400`
- Contains "cannot be deleted" → `400`
- Default → `500`

## Performance Characteristics

### Throughput Limits

- **Maximum Batch Size**: 50 items per request
- **Concurrent Requests**: Limited by authentication rate limits
- **Transaction Timeout**: Database-level transaction timeouts apply

### Database Operations

- **Atomic Transactions**: All operations use database transactions
- **Soft Delete**: Generations are marked as deleted, not physically removed
- **Bulk Updates**: Efficient batch SQL operations
- **Index Usage**: Leverages existing database indexes for performance

## Monitoring & Logging

### Request Logging

```javascript
console.log(`[BulkAPI] User ${user.id} requesting batch deletion of ${generationIds.length} generations`);
```

### Success Logging

```javascript
console.log(`[BulkAPI] Batch deletion completed by user ${user.id}: ${result.deleted.length}/${generationIds.length} deleted`);
```

### Error Logging

```javascript
console.warn(`[BulkAPI] Failed batch deletion by user ${user.id}:`, result.error);
```

### Download Operation Logging

```javascript
// Request logging
console.log(`[BulkAPI] User ${user.id} requesting batch download for ${generationIds.length} generations`);

// Success logging
console.log(`[BulkAPI] Batch download URLs generated by user ${user.id}: ${result.downloadUrls.length}/${generationIds.length} URLs created`);

// File validation logging
console.log(`[BulkAPI] File validation for user ${user.id}: ${validFiles.length} files ready, ${processingFiles.length} still processing`);

// URL expiration logging
console.log(`[BulkAPI] Download URLs for user ${user.id} expire at ${expirationTime}, total size: ${totalFileSize} bytes`);

// Error logging
console.warn(`[BulkAPI] Failed batch download by user ${user.id}:`, result.error);
```

## OpenAPI Integration

The bulk API is fully documented with OpenAPI specifications:

- **Tags**: "Bulk Operations"
- **Request/Response Schemas**: Zod-based validation
- **Comprehensive Documentation**: Detailed descriptions and examples
- **Type Safety**: Full TypeScript integration

## Future Enhancements

### Potential Extensions

1. **Additional Bulk Operations**
   - Bulk archive/unarchive
   - Bulk tag assignment
   - Bulk metadata updates
   - Bulk sharing/privacy settings
   - Bulk quality/resolution conversion

2. **Enhanced Download Features**
   - Bulk download as ZIP archive
   - Custom download formats/qualities
   - Batch watermark application
   - Download progress tracking
   - Resume interrupted downloads

3. **Enhanced Error Reporting**
   - Partial success responses
   - Per-item error details
   - Warning messages for edge cases
   - Detailed validation reports

4. **Performance Optimizations**
   - Pagination for large result sets
   - Background processing for very large batches
   - Caching for frequently accessed data
   - Asynchronous download URL generation

5. **Advanced Features**
   - Dry-run mode for validation
   - Progress tracking for long-running operations
   - Webhook notifications for completion
   - Scheduled bulk operations
   - Bulk operation templates/presets

## API Usage Guidelines

### Best Practices

1. **Batch Size Optimization**
   - Use reasonable batch sizes (10-30 items typically optimal)
   - Consider network latency vs. processing efficiency

2. **Error Handling**
   - Always check response status codes
   - Implement retry logic for transient failures
   - Log all error responses for debugging

3. **User Experience**
   - Show progress indicators for bulk operations
   - Provide clear feedback on partial failures
   - Allow users to retry failed operations

4. **Performance Considerations**
   - Avoid concurrent bulk requests from the same user
   - Implement client-side request queuing if needed
   - Cache results when appropriate

5. **Download Management**
   - Respect download URL expiration times (1 hour)
   - Implement download progress tracking for large files
   - Handle network interruptions with retry mechanisms
   - Consider file size limits and user bandwidth
   - Validate file integrity after download

### Integration Examples

#### Frontend Integration (React)

```typescript
const bulkDeleteGenerations = async (generationIds: string[]) => {
  try {
    const response = await fetch('/api/bulk/generations/delete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ generationIds })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error);
    }

    const result = await response.json();
    console.log(`Successfully deleted ${result.deleted.length} generations`);
    return result;
  } catch (error) {
    console.error('Bulk delete failed:', error);
    throw error;
  }
};
```

#### Batch Download Integration (React)

```typescript
const bulkDownloadGenerations = async (generationIds: string[]) => {
  try {
    const response = await fetch('/api/bulk/generations/download', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ generationIds })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error);
    }

    const result = await response.json();
    
    // Handle multiple downloads
    result.downloadUrls.forEach((url, index) => {
      const filename = `generation_${generationIds[index]}.mp4`; // Generate filename from ID
      downloadFile(url, filename);
    });
    
    console.log(`Successfully generated ${result.downloadUrls.length} download URLs`);
    return result;
  } catch (error) {
    console.error('Bulk download failed:', error);
    throw error;
  }
};

const downloadFile = (url: string, filename: string) => {
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  a.target = '_blank'; // Open in new tab if download fails
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
};
```

#### Service Layer Integration

```typescript
// Using the existing GenerationService
const result = await GenerationService.batchDeleteGenerations(user, generationIds);

if (result.deleted) {
  // Handle success
  updateUI(result.deleted);
} else if (result.error) {
  // Handle error
  showErrorMessage(result.error);
}

// Batch download example
const downloadResult = await GenerationService.batchGenerateDownloadUrls(user, generationIds);

if (downloadResult.downloadUrls) {
  // Handle successful URL generation
  downloadResult.downloadUrls.forEach((url, index) => {
    console.log(`Download URL for ${generationIds[index]}: ${url}`);
  });
} else if (downloadResult.error) {
  // Handle error
  showErrorMessage(downloadResult.error);
}
```

## Conclusion

The Bulk API provides a robust, secure, and efficient solution for batch operations on video generations. Its comprehensive design supports three core operations: **batch deletion**, **batch favoriting**, and **batch downloading**, each prioritizing data consistency, user security, and operational reliability while maintaining high performance.

### Key Strengths

1. **Transaction-Based Integrity**: All-or-nothing approach ensures data consistency
2. **Comprehensive Security**: Signed URLs, proper authentication, and permission validation
3. **Rich Download Features**: Temporary URLs with metadata, expiration, and file information
4. **Excellent Observability**: Detailed logging and error reporting for monitoring and debugging
5. **Extensible Architecture**: Clean design allows for easy addition of new bulk operations

### Production-Ready Features

- **Scalability**: Efficient batch processing with reasonable size limits (50 items)
- **Security**: Cryptographic signatures, time-based expiration, and access controls
- **Reliability**: Comprehensive error handling with specific HTTP status codes
- **Monitoring**: Rich logging for operational visibility and troubleshooting
- **Developer Experience**: Full OpenAPI documentation with TypeScript integration

The design is extensible and production-ready, capable of handling enterprise-scale bulk operations while maintaining user security and system reliability. The addition of batch download functionality with signed URLs and comprehensive metadata makes it suitable for high-volume content management scenarios.