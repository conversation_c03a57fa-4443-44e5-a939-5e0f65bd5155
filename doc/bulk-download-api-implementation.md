# 批量下载API实施完成

## ✅ 已完成的实施

### 1. Schema定义 ✅
**文件**: `packages/api/src/routes/bulk/schemas.ts`

```typescript
// 批量下载请求 Schema
export const BatchDownloadGenerationsRequestSchema = z.object({
  generationIds: z.array(z.string()).min(1, "At least one generation ID is required").max(50, "Maximum 50 generations allowed per request"),
});

// 批量下载响应 Schema
export const BatchDownloadGenerationResponseSchema = z.union([
  // 成功响应
  z.object({
    downloadUrls: z.array(z.string()).describe("Array of signed download URLs"),
  }),
  // 失败响应
  z.object({
    error: z.string().describe("Error message describing why the batch operation failed"),
  }),
]);
```

### 2. 服务层方法 ✅
**文件**: `packages/api/src/routes/generations/lib/generation-service.ts`

```typescript
/**
 * 批量生成下载链接
 */
static async batchGenerateDownloadUrls(
  user: Session["user"], 
  generationIds: string[]
): Promise<{
  downloadUrls?: string[];
  error?: string;
}> {
  try {
    const isAdmin = user.role?.includes('admin') || false;
    
    console.log(`[GenerationService] User ${user.id}${isAdmin ? ' (admin)' : ''} requesting batch download for ${generationIds.length} generations`);

    // 使用 GenerationsManager 的批量下载方法
    const result = await GenerationsManager.batchGenerateDownloadUrls(
      generationIds,
      user.id,
      isAdmin
    );

    if (result.downloadUrls) {
      console.log(`[GenerationService] User ${user.id} batch download generated ${result.downloadUrls.length} URLs successfully`);
    } else {
      console.warn(`[GenerationService] Batch download failed for user ${user.id}:`, result.error);
    }

    return result;

  } catch (error) {
    console.error('[GenerationService] Error batch generating download URLs:', error);
    return {
      error: 'Internal server error'
    };
  }
}
```

### 3. 路由实现 ✅
**文件**: `packages/api/src/routes/bulk/router.ts`

**接口**: `POST /api/bulk/generations/download`

**特性**:
- ✅ 完整的OpenAPI文档
- ✅ Zod schema验证
- ✅ 认证中间件
- ✅ 详细的HTTP状态码处理
- ✅ 完整的错误处理和日志记录

```typescript
// POST /api/bulk/generations/download - 批量生成下载链接
.post(
  "/generations/download",
  authMiddleware,
  describeRoute({
    tags: ["Bulk Operations"],
    summary: "Batch generate download URLs",
    description: "Generate signed download URLs for multiple generations at once...",
    // ... 完整的OpenAPI配置
  }),
  validator("json", BatchDownloadGenerationsRequestSchema),
  async (c) => {
    const user = c.get("user");
    const { generationIds } = c.req.valid("json");

    const result = await GenerationService.batchGenerateDownloadUrls(user, generationIds);
    
    // 完整的错误处理和状态码映射
    if (result.downloadUrls) {
      return c.json(result, 200);
    } else if (result.error) {
      // 根据错误类型返回相应的状态码
      // 404, 403, 422, 400, 503, 500
    }
  }
)
```

## ⚠️ 待实施项目

### 1. GenerationsManager 批量下载方法 🔴
**文件**: `packages/jobs/src/generations-manager.ts` (需要实施)

需要实现 `GenerationsManager.batchGenerateDownloadUrls()` 方法：

```typescript
/**
 * 批量生成下载链接
 * 参考现有的 downloadWithWatermark 和 downloadWithoutWatermark 方法
 */
static async batchGenerateDownloadUrls(
  generationIds: string[],
  userId: string,
  isAdmin: boolean = false
): Promise<{
  downloadUrls?: string[];
  error?: string;
}> {
  try {
    // 1. 参数验证
    if (!generationIds.length) {
      return { error: 'No generation IDs provided' };
    }

    if (generationIds.length > 50) {
      return { error: 'Maximum 50 generations allowed per request' };
    }

    // 2. 权限验证 - 批量检查用户是否可以下载这些文件
    const generations = await db.generation.findMany({
      where: {
        id: { in: generationIds },
        deletedAt: null, // 只处理未删除的
        status: 'succeeded' // 只处理成功生成的
      },
      select: {
        id: true,
        userId: true,
        mediaUrl: true,
        status: true
      }
    });

    // 检查是否所有文件都存在
    if (generations.length !== generationIds.length) {
      const foundIds = generations.map(g => g.id);
      const missingIds = generationIds.filter(id => !foundIds.includes(id));
      return { error: `Generations not found or not ready: ${missingIds.join(', ')}` };
    }

    // 权限检查 - 用户只能下载自己的文件（除非是管理员）
    if (!isAdmin) {
      const unauthorizedFiles = generations.filter(g => g.userId !== userId);
      if (unauthorizedFiles.length > 0) {
        return { error: 'Access denied for some generations' };
      }
    }

    // 3. 生成签名下载链接
    const downloadUrls: string[] = [];
    
    for (const generation of generations) {
      if (!generation.mediaUrl) {
        return { error: `File not available for generation ${generation.id}` };
      }

      // 生成签名URL (1小时有效期)
      // 注意：这里需要根据实际的存储服务实现
      // 例如：S3, Google Cloud Storage, 等
      try {
        const signedUrl = await this.generateSignedDownloadUrl(
          generation.mediaUrl,
          3600 // 1小时过期
        );
        downloadUrls.push(signedUrl);
      } catch (error) {
        console.error(`Failed to generate signed URL for generation ${generation.id}:`, error);
        return { error: `Failed to generate download URL for generation ${generation.id}` };
      }
    }

    console.log(`[GenerationsManager] Generated ${downloadUrls.length} download URLs for user ${userId}`);
    return { downloadUrls };

  } catch (error) {
    console.error('[GenerationsManager] Error in batchGenerateDownloadUrls:', error);
    return { error: 'Internal server error' };
  }
}

/**
 * 生成签名下载URL的辅助方法
 * 需要根据实际使用的存储服务实现
 */
private static async generateSignedDownloadUrl(
  mediaUrl: string, 
  expirationSeconds: number
): Promise<string> {
  // 实现示例 - 需要根据实际存储服务调整
  
  // AWS S3 示例:
  // const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
  // const { GetObjectCommand } = require('@aws-sdk/client-s3');
  // return await getSignedUrl(s3Client, new GetObjectCommand({
  //   Bucket: 'your-bucket',
  //   Key: extractKeyFromUrl(mediaUrl)
  // }), { expiresIn: expirationSeconds });

  // Google Cloud Storage 示例:
  // const { Storage } = require('@google-cloud/storage');
  // const storage = new Storage();
  // const file = storage.bucket('your-bucket').file(extractKeyFromUrl(mediaUrl));
  // const [url] = await file.getSignedUrl({
  //   action: 'read',
  //   expires: Date.now() + expirationSeconds * 1000
  // });
  // return url;

  // 临时实现 - 直接返回原URL（生产环境中应该替换为签名URL）
  console.warn('[GenerationsManager] Using direct URL instead of signed URL - should implement proper signing for production');
  return mediaUrl;
}
```

### 2. 存储服务集成 🔴
需要根据实际使用的存储服务实现签名URL生成：

**AWS S3**:
```bash
npm install @aws-sdk/client-s3 @aws-sdk/s3-request-presigner
```

**Google Cloud Storage**:
```bash
npm install @google-cloud/storage
```

**Azure Blob Storage**:
```bash
npm install @azure/storage-blob
```

### 3. 环境配置 🔴
需要在环境变量中配置存储服务相关信息：

```env
# AWS S3
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=your-region
AWS_S3_BUCKET=your-bucket-name

# Google Cloud Storage
GOOGLE_CLOUD_PROJECT_ID=your-project-id
GOOGLE_CLOUD_STORAGE_BUCKET=your-bucket-name
GOOGLE_APPLICATION_CREDENTIALS=path-to-service-account.json

# Azure (if applicable)
AZURE_STORAGE_CONNECTION_STRING=your-connection-string
AZURE_STORAGE_CONTAINER=your-container-name
```

## 🧪 测试步骤

### 1. 单元测试
```typescript
describe('GenerationsManager.batchGenerateDownloadUrls', () => {
  it('should generate download URLs for valid generations', async () => {
    const result = await GenerationsManager.batchGenerateDownloadUrls(
      ['gen1', 'gen2'], 
      'user123', 
      false
    );
    expect(result.downloadUrls).toHaveLength(2);
    expect(result.error).toBeUndefined();
  });

  it('should return error for non-existent generations', async () => {
    const result = await GenerationsManager.batchGenerateDownloadUrls(
      ['non-existent'], 
      'user123', 
      false
    );
    expect(result.error).toContain('not found');
  });

  it('should enforce access control', async () => {
    const result = await GenerationsManager.batchGenerateDownloadUrls(
      ['other-user-gen'], 
      'user123', 
      false
    );
    expect(result.error).toContain('Access denied');
  });
});
```

### 2. 集成测试
```bash
# 测试API端点
curl -X POST /api/bulk/generations/download \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{"generationIds": ["gen1", "gen2", "gen3"]}'
```

### 3. 前端测试
已实现的前端功能可以直接测试：
- 选择多个项目
- 点击下载按钮
- 验证API调用和文件下载

## 📊 性能考虑

### 1. 并发处理
```typescript
// 并行生成签名URL，而不是串行
const downloadUrls = await Promise.all(
  generations.map(async (generation) => {
    return await this.generateSignedDownloadUrl(generation.mediaUrl, 3600);
  })
);
```

### 2. 缓存策略
```typescript
// 可以考虑缓存签名URL (短时间)
const cacheKey = `download_url_${generation.id}_${userId}`;
const cachedUrl = await redis.get(cacheKey);
if (cachedUrl) {
  return cachedUrl;
}

const signedUrl = await this.generateSignedDownloadUrl(generation.mediaUrl, 3600);
await redis.setex(cacheKey, 300, signedUrl); // 缓存5分钟
return signedUrl;
```

### 3. 批量数据库查询优化
```typescript
// 使用单次查询获取所有需要的数据
const generations = await db.generation.findMany({
  where: {
    id: { in: generationIds },
    deletedAt: null,
    status: 'succeeded'
  },
  select: {
    id: true,
    userId: true,
    mediaUrl: true,
    status: true,
    mediaType: true // 可用于确定文件扩展名
  }
});
```

## 🚀 部署清单

### 后端部署 ⚠️
- [ ] 实现 `GenerationsManager.batchGenerateDownloadUrls` 方法
- [ ] 配置存储服务 (S3/GCS/Azure)
- [ ] 设置环境变量
- [ ] 运行数据库迁移 (如果需要)
- [ ] 部署和测试API端点

### 前端部署 ✅
- [x] Hook层实现完成
- [x] 组件集成完成
- [x] 用户界面就绪

### 测试部署 ⚠️
- [ ] 编写单元测试
- [ ] 执行集成测试
- [ ] 进行用户验收测试
- [ ] 性能测试

---

**实施状态**: 🟡 API层完成75%，存储服务待集成  
**完成时间**: 2024-01-01  
**负责人**: 后端开发团队

**下一步**: 实现 `GenerationsManager.batchGenerateDownloadUrls` 方法并配置存储服务