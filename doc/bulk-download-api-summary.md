# 批量下载接口设计总结

## 🚀 新增接口

**接口路径**: `POST /api/bulk/generations/download`

## 📋 接口规范

### 请求格式
```json
{
  "generationIds": ["gen_12345", "gen_67890", "gen_abcdef"]
}
```

### 响应格式
```json
{
  "downloadUrls": [
    "https://signed-url.example.com/file1?token=abc123&expires=1704106800",
    "https://signed-url.example.com/file2?token=def456&expires=1704106800",
    "https://signed-url.example.com/file3?token=ghi789&expires=1704106800"
  ]
}
```

## 🔒 安全特性

- **签名URL**: 加密签名的临时下载链接
- **1小时过期**: 防止未授权长期访问
- **权限验证**: 用户只能下载自己的文件
- **HTTPS强制**: 所有下载链接使用HTTPS

## ⚡ 性能特点

- **批量处理**: 最多50个文件/请求
- **并行生成**: 多个URL同时生成
- **原子操作**: 全部成功或全部失败
- **详细日志**: 完整的性能和错误日志

## 🎯 错误处理

| 状态码 | 描述 | 场景 |
|--------|------|------|
| `200` | 成功 | 所有URL生成成功 |
| `400` | 参数错误 | 无效参数或空数组 |
| `403` | 权限不足 | 无权访问某些文件 |
| `404` | 文件不存在 | 某些文件未找到 |
| `422` | 文件未就绪 | 某些文件仍在处理中 |
| `503` | 服务不可用 | 存储服务临时不可用 |

## 💡 实现要点

### 服务层集成
- 需要 `GenerationService.batchGenerateDownloadUrls()` 方法
- 集成云存储服务 (S3, GCS等)
- 文件状态验证 (确保文件已完成处理)

### 前端集成
- 简化的URL数组处理
- 处理多文件并发下载
- URL过期时间管理 (1小时)
- 直接触发浏览器下载

### 数据库查询
- 批量权限验证
- 文件状态检查 (`status = 'succeeded'`)
- 媒体URL和元数据获取

## 🔧 技术栈

- **框架**: Hono + OpenAPI
- **验证**: Zod schemas
- **认证**: authMiddleware
- **存储**: 云存储服务集成
- **日志**: 结构化日志记录

## 📊 使用场景

1. **批量导出**: 用户选择多个作品批量下载
2. **备份恢复**: 用户备份自己的创作内容
3. **内容分享**: 生成临时分享链接
4. **数据迁移**: 批量转移用户数据

## ⚠️ 注意事项

- 下载URL有效期为1小时，需要及时使用
- 大文件下载可能耗时较长，需要进度指示
- 并发下载需要控制，避免带宽过载
- 文件完整性验证建议在客户端实现

## 🚀 下一步实施

1. **后端实现**: 实现 GenerationService 方法
2. **存储集成**: 配置签名URL生成
3. **前端集成**: 更新 useBulkOperations hook
4. **测试验证**: 单元测试和集成测试
5. **性能监控**: 添加相关指标和警报

---

**文档状态**: ✅ 设计完成，已补充到 [bulk-api-design.md](./bulk-api-design.md)
**创建时间**: 2024-01-01
**负责人**: 系统架构团队