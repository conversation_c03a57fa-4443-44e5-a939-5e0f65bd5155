# calculateCredits函数抽离方案

## 一、分析结果

✅ **可以抽离**，经过深度分析发现：

1. **代码重复问题**：前端 `useCreditCalculation.ts:56-98` 和后端 `job-service.ts:39-70` 有几乎相同的积分计算逻辑
2. **纯函数特性**：`calculateCredits` 是纯函数，无副作用，依赖明确
3. **业务通用性**：积分计算逻辑在多个场景需要使用（任务创建、前端预览、费用估算等）

## 二、详细重构方案

### 2.1 新建共享工具函数

**位置**: `packages/utils/lib/credit-calculator.ts`

**原因选择utils包**：
- 积分计算是纯计算逻辑，不涉及数据库操作
- `@repo/credits` 包主要处理数据库CRUD操作
- `@repo/utils` 更适合纯工具函数

### 2.2 统一接口设计

```typescript
// packages/utils/lib/credit-calculator.ts
export interface CreditCalculationInput {
  modelConfig: {
    pricingConfig: {
      baseCredits: number;
      multipliers: {
        videoLength?: Record<string, number>;
        mode?: Record<string, number>;
        resolution?: Record<string, number>;
        style?: Record<string, number>;
      };
    };
  };
  params: {
    duration?: number;
    modeCode?: string;
    resolution?: string;
    style?: string;
  };
}

export interface CreditCalculationResult {
  totalCredits: number;
  appliedMultipliers: Array<{
    type: string;
    value: string;
    multiplier: number;
  }>;
}

export function calculateCredits(input: CreditCalculationInput): CreditCalculationResult
```

### 2.3 迁移步骤

1. **第一步：创建共享函数**
   - 在 `packages/utils/lib/credit-calculator.ts` 创建新函数
   - 整合前后端逻辑，使用更严格的类型定义
   - 添加完整的错误处理和测试

2. **第二步：更新后端**
   - 修改 `job-service.ts`，移除内部 `calculateCredits` 函数
   - 导入新的共享函数：`import { calculateCredits } from "@repo/utils/credit-calculator"`
   - 适配调用方式

3. **第三步：更新前端**
   - 重构 `useCreditCalculation.ts`，使用共享函数
   - 保持React hook的响应式特性
   - 确保UI显示逻辑不变

### 2.4 依赖关系处理

**当前依赖**：
- `HTTPException` from `hono/http-exception` (仅后端需要)

**解决方案**：
- 共享函数抛出标准Error，调用方负责转换为具体框架的异常类型
- 前端可以直接catch Error，后端将Error转换为HTTPException

### 2.5 完整实现示例

```typescript
// packages/utils/lib/credit-calculator.ts
export class CreditCalculationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'CreditCalculationError';
  }
}

export function calculateCredits(input: CreditCalculationInput): CreditCalculationResult {
  const { modelConfig, params } = input;
  
  if (!modelConfig.pricingConfig) {
    throw new CreditCalculationError("No pricing configuration found for this model");
  }

  const { baseCredits, multipliers } = modelConfig.pricingConfig;
  let totalCredits = baseCredits;
  const appliedMultipliers: Array<{type: string; value: string; multiplier: number}> = [];

  // 应用各种乘数
  if (multipliers.videoLength && params.duration) {
    const lengthMultiplier = multipliers.videoLength[params.duration.toString()];
    if (lengthMultiplier) {
      totalCredits *= lengthMultiplier;
      appliedMultipliers.push({
        type: 'videoLength',
        value: params.duration.toString(),
        multiplier: lengthMultiplier
      });
    }
  }

  // ... 其他乘数逻辑

  return {
    totalCredits: Math.ceil(totalCredits),
    appliedMultipliers
  };
}
```

## 三、优势

1. **消除重复代码**：前后端共享同一套计算逻辑
2. **类型安全**：使用TypeScript严格类型定义
3. **易于测试**：纯函数便于单元测试
4. **易于维护**：修改计算逻辑只需改一处
5. **可扩展性**：未来新增计算因子只需在一个地方添加

## 四、实施建议

**建议立即重构**，因为：

1. **消除关键重复**：前后端已有相同逻辑，存在不一致风险
2. **提高可维护性**：计算逻辑集中管理，便于future pricing策略调整  
3. **类型安全**：统一接口减少运行时错误

**推荐放置位置**：`packages/utils/lib/credit-calculator.ts`
- 作为纯计算工具函数，比放在credits包更合适
- 便于前后端共享使用

**风险评估**：低风险，现有逻辑已经稳定，抽离过程不会改变业务逻辑，只是代码组织优化。