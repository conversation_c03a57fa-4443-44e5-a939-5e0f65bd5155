# Credit Calculator Refactoring Design

## 概述

本文档描述了将积分计算逻辑从 `job-service.ts` 中的 `calculateCredits` 函数抽离到共享工具包的详细重构方案。

## 问题分析

### 当前问题

1. **代码重复**：前端 `useCreditCalculation.ts` 和后端 `job-service.ts` 存在相同的积分计算逻辑
2. **维护困难**：修改计算规则需要在多个地方同步更新
3. **类型不一致**：前后端使用不同的接口定义，容易产生不一致
4. **测试复杂**：需要分别为前后端编写相同的测试用例

### 影响范围

**后端文件**：
- `packages/api/src/routes/jobs/lib/job-service.ts` (第39-70行)

**前端文件**：
- `apps/web/modules/marketing/image-to-video/hooks/useCreditCalculation.ts` (第56-98行)

**配置文件**：
- `config/vmodel/index.ts` (定价配置)

## 重构方案

### 目标架构

```mermaid
graph TD
    A[config/vmodel/index.ts] --> B[packages/utils/lib/credit-calculator.ts]
    B --> C[job-service.ts]
    B --> D[useCreditCalculation.ts]
    B --> E[其他需要积分计算的模块]
```

### 新建共享模块

#### 文件位置
`packages/utils/lib/credit-calculator.ts`

#### 选择理由
- 积分计算是纯计算逻辑，无副作用
- `@repo/utils` 包专门用于共享工具函数
- 避免与 `@repo/credits` 包的数据库操作混淆

## 接口设计

### 核心类型定义

```typescript
/**
 * 积分计算输入参数
 */
export interface CreditCalculationInput {
  /** 模型配置 */
  modelConfig: {
    pricingConfig: {
      baseCredits: number;
      multipliers: {
        videoLength?: Record<string, number>;
        mode?: Record<string, number>;
        resolution?: Record<string, number>;
        style?: Record<string, number>;
      };
    };
  };
  /** 任务参数 */
  params: {
    duration?: number;
    modeCode?: string;
    resolution?: string;
    style?: string;
  };
}

/**
 * 积分计算结果
 */
export interface CreditCalculationResult {
  /** 计算得出的总积分 */
  totalCredits: number;
  /** 应用的乘数详情 */
  appliedMultipliers: Array<{
    type: string;
    value: string;
    multiplier: number;
  }>;
  /** 计算过程描述 */
  calculationSteps: string[];
}

/**
 * 积分计算错误
 */
export class CreditCalculationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'CreditCalculationError';
  }
}
```

### 核心函数

```typescript
/**
 * 计算积分消耗
 * @param input 计算输入参数
 * @returns 计算结果
 * @throws CreditCalculationError 当配置缺失或无效时
 */
export function calculateCredits(input: CreditCalculationInput): CreditCalculationResult;
```

## 完整实现

### packages/utils/lib/credit-calculator.ts

```typescript
/**
 * 积分计算工具模块
 * 提供统一的积分计算逻辑，供前后端共享使用
 */

/**
 * 积分计算输入参数
 */
export interface CreditCalculationInput {
  modelConfig: {
    pricingConfig: {
      baseCredits: number;
      multipliers: {
        videoLength?: Record<string, number>;
        mode?: Record<string, number>;
        resolution?: Record<string, number>;
        style?: Record<string, number>;
      };
    };
  };
  params: {
    duration?: number;
    modeCode?: string;
    resolution?: string;
    style?: string;
  };
}

/**
 * 应用的乘数信息
 */
export interface AppliedMultiplier {
  type: 'videoLength' | 'mode' | 'resolution' | 'style';
  value: string;
  multiplier: number;
}

/**
 * 积分计算结果
 */
export interface CreditCalculationResult {
  totalCredits: number;
  appliedMultipliers: AppliedMultiplier[];
  calculationSteps: string[];
}

/**
 * 积分计算错误
 */
export class CreditCalculationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'CreditCalculationError';
  }
}

/**
 * 计算积分消耗
 * @param input 计算输入参数
 * @returns 计算结果
 * @throws CreditCalculationError 当配置缺失或无效时
 */
export function calculateCredits(input: CreditCalculationInput): CreditCalculationResult {
  const { modelConfig, params } = input;
  
  // 验证必需的配置
  if (!modelConfig.pricingConfig) {
    throw new CreditCalculationError("No pricing configuration found for this model");
  }

  const { baseCredits, multipliers } = modelConfig.pricingConfig;
  let totalCredits = baseCredits;
  const appliedMultipliers: AppliedMultiplier[] = [];
  const calculationSteps: string[] = [`Base credits: ${baseCredits}`];

  // 应用视频长度乘数
  if (multipliers.videoLength && params.duration) {
    const lengthMultiplier = multipliers.videoLength[params.duration.toString()];
    if (lengthMultiplier) {
      totalCredits *= lengthMultiplier;
      appliedMultipliers.push({
        type: 'videoLength',
        value: params.duration.toString(),
        multiplier: lengthMultiplier
      });
      calculationSteps.push(`Video length (${params.duration}s): ×${lengthMultiplier}`);
    }
  }

  // 应用模式乘数
  if (multipliers.mode && params.modeCode) {
    const modeMultiplier = multipliers.mode[params.modeCode];
    if (modeMultiplier) {
      totalCredits *= modeMultiplier;
      appliedMultipliers.push({
        type: 'mode',
        value: params.modeCode,
        multiplier: modeMultiplier
      });
      calculationSteps.push(`Mode (${params.modeCode}): ×${modeMultiplier}`);
    }
  }

  // 应用分辨率乘数
  if (multipliers.resolution && params.resolution) {
    const resolutionMultiplier = multipliers.resolution[params.resolution];
    if (resolutionMultiplier) {
      totalCredits *= resolutionMultiplier;
      appliedMultipliers.push({
        type: 'resolution',
        value: params.resolution,
        multiplier: resolutionMultiplier
      });
      calculationSteps.push(`Resolution (${params.resolution}): ×${resolutionMultiplier}`);
    }
  }

  // 应用风格乘数
  if (multipliers.style && params.style) {
    const styleMultiplier = multipliers.style[params.style];
    if (styleMultiplier) {
      totalCredits *= styleMultiplier;
      appliedMultipliers.push({
        type: 'style',
        value: params.style,
        multiplier: styleMultiplier
      });
      calculationSteps.push(`Style (${params.style}): ×${styleMultiplier}`);
    }
  }

  return {
    totalCredits: Math.ceil(totalCredits),
    appliedMultipliers,
    calculationSteps
  };
}

/**
 * 计算多个输出的总积分
 * @param input 计算输入参数
 * @param numOutputs 输出数量
 * @returns 总积分
 */
export function calculateTotalCredits(
  input: CreditCalculationInput, 
  numOutputs: number
): number {
  const result = calculateCredits(input);
  return result.totalCredits * numOutputs;
}
```

## 迁移步骤

### 步骤1：创建共享工具函数

1. **创建新文件**：`packages/utils/lib/credit-calculator.ts`
2. **实现核心逻辑**：整合前后端现有逻辑
3. **添加单元测试**：确保计算逻辑正确
4. **更新包导出**：在 `packages/utils/index.ts` 中导出新模块

```typescript
// packages/utils/index.ts
export * from "./lib/base-url";
export * from "./lib/metadata";
export * from "./lib/credit-calculator";  // 新增
```

### 步骤2：更新后端代码

**修改文件**：`packages/api/src/routes/jobs/lib/job-service.ts`

**变更内容**：

```typescript
// 删除原有的内联函数 (第39-70行)
// const calculateCredits = (modelConfig: any, params: any) => { ... }

// 添加导入
import { 
  calculateCredits, 
  CreditCalculationError,
  type CreditCalculationInput 
} from "@repo/utils/credit-calculator";
import { HTTPException } from "hono/http-exception";

// 修改调用方式 (第73行附近)
try {
  const calculationInput: CreditCalculationInput = {
    modelConfig: model,
    params: {
      duration: request.modelParam.duration,
      modeCode: request.modelParam.modeCode,
      resolution: request.modelParam.resolution,
      style: request.modelParam.style,
    }
  };
  
  const result = calculateCredits(calculationInput);
  const singleCredit = result.totalCredits;
  const totalCredit = singleCredit * request.numOutputs;
} catch (error) {
  if (error instanceof CreditCalculationError) {
    throw new HTTPException(400, { message: error.message });
  }
  throw error;
}
```

### 步骤3：更新前端代码

**修改文件**：`apps/web/modules/marketing/image-to-video/hooks/useCreditCalculation.ts`

**变更内容**：

```typescript
import { useMemo } from "react";
import { 
  calculateCredits, 
  type CreditCalculationInput,
  type CreditCalculationResult as UtilResult
} from "@repo/utils/credit-calculator";
import type { ModelMode, VideoModel } from "@repo/config/vmodel/types";

// 保持现有接口不变，内部使用共享逻辑
export function useCreditCalculation({
  selectedModel,
  selectedMode,
  videoLength,
  aspectRatio,
  outputNumber = 1,
  resolution,
  style,
}: CreditCalculationParams): CreditCalculationResult {
  return useMemo(() => {
    if (!selectedModel) {
      return {
        totalCredits: 0,
        singleCredits: 0,
        pricingKey: null,
        calculationMethod: "fallback",
      };
    }

    if (!selectedModel.pricingConfig) {
      return {
        totalCredits: 5 * outputNumber,
        singleCredits: 5,
        pricingKey: null,
        calculationMethod: "fallback",
        details: `No pricing config found, using fallback: 5 × ${outputNumber}`,
      };
    }

    try {
      const calculationInput: CreditCalculationInput = {
        modelConfig: selectedModel,
        params: {
          duration: videoLength || undefined,
          modeCode: selectedMode?.code,
          resolution: resolution || undefined,
          style: style || undefined,
        }
      };

      const result = calculateCredits(calculationInput);
      const singleCredits = result.totalCredits;
      const totalCredits = singleCredits * outputNumber;

      // 构建定价键
      const pricingKeyParts = ['image-to-video', selectedModel.code];
      if (selectedMode) pricingKeyParts.push(selectedMode.code);
      if (resolution) pricingKeyParts.push(resolution);
      if (videoLength) pricingKeyParts.push(videoLength.toString());
      if (style) pricingKeyParts.push(style);
      const pricingKey = pricingKeyParts.join('_');

      return {
        totalCredits,
        singleCredits,
        pricingKey,
        calculationMethod: "exact",
        details: `${result.calculationSteps.join(' → ')} = ${singleCredits} × ${outputNumber} = ${totalCredits}`,
      };
    } catch (error) {
      console.error('Credit calculation error:', error);
      return {
        totalCredits: 5 * outputNumber,
        singleCredits: 5,
        pricingKey: null,
        calculationMethod: "fallback",
        details: `Calculation error, using fallback: 5 × ${outputNumber}`,
      };
    }
  }, [selectedModel, selectedMode, videoLength, aspectRatio, outputNumber, resolution, style]);
}
```

### 步骤4：添加单元测试

**创建测试文件**：`packages/utils/lib/__tests__/credit-calculator.test.ts`

```typescript
import { describe, it, expect } from 'vitest';
import { 
  calculateCredits, 
  calculateTotalCredits,
  CreditCalculationError,
  type CreditCalculationInput 
} from '../credit-calculator';

describe('credit-calculator', () => {
  const mockModelConfig = {
    pricingConfig: {
      baseCredits: 10,
      multipliers: {
        videoLength: { "5": 1.0, "10": 2.0 },
        mode: { "std": 1.0, "pro": 2.0 },
        resolution: { "720P": 1.0, "1080P": 2.5 }
      }
    }
  };

  describe('calculateCredits', () => {
    it('should calculate base credits correctly', () => {
      const input: CreditCalculationInput = {
        modelConfig: mockModelConfig,
        params: {}
      };

      const result = calculateCredits(input);
      
      expect(result.totalCredits).toBe(10);
      expect(result.appliedMultipliers).toHaveLength(0);
      expect(result.calculationSteps).toEqual(['Base credits: 10']);
    });

    it('should apply video length multiplier', () => {
      const input: CreditCalculationInput = {
        modelConfig: mockModelConfig,
        params: { duration: 10 }
      };

      const result = calculateCredits(input);
      
      expect(result.totalCredits).toBe(20);
      expect(result.appliedMultipliers).toHaveLength(1);
      expect(result.appliedMultipliers[0]).toEqual({
        type: 'videoLength',
        value: '10',
        multiplier: 2.0
      });
    });

    it('should apply multiple multipliers', () => {
      const input: CreditCalculationInput = {
        modelConfig: mockModelConfig,
        params: { 
          duration: 10, 
          modeCode: 'pro', 
          resolution: '1080P' 
        }
      };

      const result = calculateCredits(input);
      
      // 10 * 2.0 * 2.0 * 2.5 = 100
      expect(result.totalCredits).toBe(100);
      expect(result.appliedMultipliers).toHaveLength(3);
    });

    it('should throw error when pricing config is missing', () => {
      const input: CreditCalculationInput = {
        modelConfig: { pricingConfig: null as any },
        params: {}
      };

      expect(() => calculateCredits(input)).toThrow(CreditCalculationError);
    });
  });

  describe('calculateTotalCredits', () => {
    it('should calculate total credits for multiple outputs', () => {
      const input: CreditCalculationInput = {
        modelConfig: mockModelConfig,
        params: { duration: 5 }
      };

      const total = calculateTotalCredits(input, 3);
      
      expect(total).toBe(30); // 10 * 1.0 * 3
    });
  });
});
```

## 兼容性考虑

### 向后兼容性

- 前端Hook接口保持不变，确保现有组件无需修改
- 后端API响应格式不变
- 错误处理逻辑保持一致

### 类型安全

- 使用严格的TypeScript类型定义
- 编译时检查确保类型一致性
- 运行时参数验证

### 错误处理

- 共享函数抛出标准Error类型
- 调用方负责转换为具体框架的错误类型
- 保持原有的错误信息和状态码

## 测试策略

### 单元测试

- 为共享函数编写完整的单元测试
- 覆盖所有计算场景和边界情况
- 测试错误处理逻辑

### 集成测试

- 验证前后端使用共享函数后计算结果一致
- 确保API响应格式不变
- 验证前端UI显示正确

### 回归测试

- 运行现有的端到端测试
- 确保重构不影响业务功能
- 验证积分扣除逻辑正确

## 部署计划

### 分阶段部署

1. **Phase 1**：创建共享函数，添加测试
2. **Phase 2**：更新后端代码，进行测试
3. **Phase 3**：更新前端代码，进行测试
4. **Phase 4**：清理旧代码，更新文档

### 回滚计划

- 保留原有代码作为备份
- 准备快速回滚脚本
- 监控关键指标确保稳定性

## 预期收益

### 代码质量

- **消除重复**：前后端共享同一套计算逻辑
- **提高一致性**：统一的类型定义和接口
- **增强可维护性**：修改逻辑只需要改一处

### 开发效率

- **减少测试工作**：只需要测试一套逻辑
- **降低Bug风险**：避免前后端计算不一致
- **便于扩展**：新增计算因子更加容易

### 技术债务

- **降低复杂度**：统一的计算逻辑
- **提高可测试性**：纯函数易于测试
- **改善文档**：集中的API文档

## 风险评估

### 技术风险

- **低风险**：纯函数重构，不改变业务逻辑
- **回滚容易**：可快速回退到原有实现
- **影响范围可控**：只涉及计算逻辑，不影响数据存储

### 业务风险

- **极低风险**：计算逻辑保持完全一致
- **测试充分**：完整的单元和集成测试覆盖
- **渐进式部署**：分阶段上线，可及时发现问题

## 总结

这个重构方案将积分计算逻辑抽离到共享工具包，解决了代码重复和维护困难的问题。通过统一的接口设计和严格的类型定义，提高了代码质量和开发效率。重构过程风险可控，预期收益明显，建议尽快实施。