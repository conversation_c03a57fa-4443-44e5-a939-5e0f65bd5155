# Favorite API 实现设计文档

## 需求概述

实现一个新的API接口：

1. **POST /api/generations/:generationId/favorite/toggle** 
   - 收藏状态切换功能（如果已收藏则取消收藏，如果未收藏则添加收藏）
   - 响应：`{favorite: true|false}` （返回切换后的状态）
   - 数据库操作：只更新generation表的favorite字段，不更新starNum
   - 权限限制：只能对自己生成的视频进行收藏操作

## 当前系统分析

### 数据库结构

`Generation` 模型已包含所需字段：
```prisma
model Generation {
  favorite    Boolean @default(false) @map("favorite")    // 收藏状态
  userId      String                                      // 生成用户ID（用于权限控制）
  // ... 其他字段
}
```

### 现有基础设施

- **API框架**: Hono + OpenAPI文档
- **认证**: `authMiddleware` 中间件  
- **数据库**: Prisma ORM + PostgreSQL
- **现有模式**: 已有like/unlike功能可参考
- **管理模式**: `GenerationsManager` 统一处理数据库操作

## 设计决策

### API设计

**端点路径:**
- `POST /api/generations/:generationId/favorite/toggle` - 切换收藏状态

**响应格式:**
```json
{
  "favorite": true|false
}
```

**设计理由:**
- 简单的布尔值响应符合需求
- 单一端点简化前端逻辑
- toggle语义明确表示状态切换
- 只允许对自己生成的视频进行操作

### 数据库操作策略

**收藏状态切换操作:**
1. 验证generation存在且未删除
2. 验证当前用户是该generation的创建者
3. 检查当前收藏状态
4. 切换 `favorite` 字段值（true变false，false变true）
5. 返回切换后的状态

## 实施步骤

### 步骤1: 扩展GenerationsManager数据库操作

**文件位置:** `/packages/jobs/src/lib/generations-manager.ts`

**需要添加的方法:**

```typescript
/**
 * 切换Generation的收藏状态
 */
static async toggleFavoriteGeneration(generationId: string, userId: string): Promise<{
  id: string;
  favorite: boolean;
} | null>

/**
 * 批量更新收藏状态（修复generation-service.ts中缺失的方法）
 */
static async batchUpdateFavoriteGenerations(
  generationIds: string[],
  userId: string,
  favorite: boolean,
  isAdmin: boolean = false
): Promise<{
  updated?: string[];
  error?: string;
}>
```

### 步骤2: 添加服务层方法

**文件位置:** `/packages/api/src/routes/generations/lib/generation-service.ts`

**需要添加的方法:**
```typescript
/**
 * 切换Generation的收藏状态
 */
static async toggleFavoriteGeneration(
  user: Session["user"], 
  generationId: string
): Promise<FavoriteApiResponse>
```

### 步骤3: 添加Schema验证定义

**文件位置:** `/packages/api/src/routes/generations/schemas.ts`

**需要添加的Schema:**
```typescript
// 收藏响应数据schema
export const FavoriteResponseDataSchema = z.object({
  favorite: z.boolean(),
});

// 收藏成功响应schema
export const FavoriteSuccessResponseSchema = z.object({
  success: z.literal(true),
  data: FavoriteResponseDataSchema,
});

// 收藏错误响应schema
export const FavoriteErrorResponseSchema = z.object({
  success: z.literal(false),
  error: z.string(),
});
```

### 步骤4: 添加类型定义

**文件位置:** `/packages/api/src/routes/generations/types.ts`

**需要添加的类型:**
```typescript
export type FavoriteApiResponse = {
  success: true;
  data: {
    favorite: boolean;
  };
} | {
  success: false;
  error: string;
};
```

### 步骤5: 添加API路由

**文件位置:** `/packages/api/src/routes/generations/router.ts`

**需要添加的路由:**
```typescript
// POST /api/generations/:generationId/favorite/toggle
.post(
  "/:generationId/favorite/toggle",
  authMiddleware,
  describeRoute({
    tags: ["Generations"],
    summary: "Toggle generation favorite status",
    description: "Toggle the favorite status of a generation. Only works for generations created by the current user.",
    // ... OpenAPI配置
  }),
  validator("param", GenerationIdParamSchema),
  async (c) => {
    // 实现逻辑
  }
)
```

## 详细实现代码

### 数据库操作实现

```typescript
/**
 * 切换Generation的收藏状态
 */
static async toggleFavoriteGeneration(generationId: string, userId: string): Promise<{
  id: string;
  favorite: boolean;
} | null> {
  try {
    // 1. 检查generation是否存在且未删除，并验证用户权限
    const generation = await db.generation.findUnique({
      where: { id: generationId },
      select: {
        id: true,
        favorite: true,
        deleted: true,
        userId: true,
      }
    });

    if (!generation || generation.deleted) {
      return null;
    }

    // 2. 验证用户只能操作自己创建的generation
    if (generation.userId !== userId) {
      throw new Error('You can only toggle favorite status for your own generations');
    }

    // 3. 切换收藏状态
    const updatedGeneration = await db.generation.update({
      where: { id: generationId },
      data: {
        favorite: !generation.favorite,
      },
      select: {
        id: true,
        favorite: true,
      }
    });

    logger.info('[GenerationsManager] Generation收藏状态切换成功:', {
      generationId,
      userId,
      newFavoriteStatus: updatedGeneration.favorite
    });

    return updatedGeneration;
  } catch (error) {
    logger.error('[GenerationsManager] 切换Generation收藏状态失败:', {
      generationId,
      userId,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}
```

### API路由实现

```typescript
// POST /api/generations/:generationId/favorite/toggle
.post(
  "/:generationId/favorite/toggle",
  authMiddleware,
  describeRoute({
    tags: ["Generations"],
    summary: "切换Generation收藏状态",
    description: "切换Generation的收藏状态。只能操作由当前用户创建的Generation。",
    parameters: [
      {
        name: "generationId",
        in: "path",
        description: "Generation ID",
        schema: { type: "string" },
        required: true,
      },
    ],
    responses: {
      200: {
        description: "收藏状态切换成功",
        content: {
          'application/json': {
            schema: RestfulFavoriteResponseSchema,
          },
        },
      },
      403: {
        description: "没有权限操作该Generation",
        content: {
          'application/json': {
            schema: RestfulErrorResponseSchema,
          },
        },
      },
      404: {
        description: "Generation不存在",
        content: {
          'application/json': {
            schema: RestfulErrorResponseSchema,
          },
        },
      },
      500: {
        description: "服务器内部错误",
        content: {
          'application/json': {
            schema: RestfulErrorResponseSchema,
          },
        },
      },
    },
  }),
  validator("param", GenerationIdParamSchema),
  async (c) => {
    const user = c.get("user");
    const { generationId } = c.req.valid("param");

    console.log(`[GenerationsAPI] 用户 ${user.id} 正在切换 generation ${generationId} 的收藏状态`);

    try {
      const result = await GenerationService.toggleFavoriteGeneration(user, generationId);
      
      if (!result) {
        return c.json({ error: 'Generation not found' }, 404);
      }

      console.log(`[GenerationsAPI] Generation ${generationId} 收藏状态切换成功，新状态: ${result.favorite}`);
      
      // RESTful风格：直接返回资源数据
      return c.json(result, 200);
      
    } catch (error) {
      console.warn(`[GenerationsAPI] 切换Generation ${generationId}收藏状态失败:`, error);
      
      if (error instanceof Error) {
        if (error.message.includes('only toggle favorite status for your own')) {
          return c.json({ error: error.message }, 403);
        }
      }
      
      return c.json({ error: 'Internal server error' }, 500);
    }
  }
)
```

## 关键设计考虑

### 1. 简化操作
- 单一toggle接口简化前端逻辑
- 只更新`favorite`字段，不影响`starNum`等其他字段
- 自动检测当前状态并切换

### 2. 安全检查
- 验证generation存在且未删除
- 严格的用户权限检查（只能操作自己的generation）
- 适当的错误处理和日志记录

### 3. 与现有代码保持一致
- 遵循现有API的相同模式
- 使用相同的日志、错误处理和响应格式
- 维护OpenAPI文档标准

### 4. 性能考虑
- 使用选择性字段查询减少数据库负载
- 单次原子操作，避免事务复杂性
- 利用现有数据库索引

### 5. 权限控制
- 通过现有中间件进行用户认证
- 严格限制只能操作自己创建的generation
- 通过userId字段进行所有者验证

## 测试策略

### 单元测试
- 独立测试数据库操作
- 模拟数据库响应
- 测试错误条件

### 集成测试
- 测试完整API流程
- 使用真实数据库测试
- 测试并发操作

### API测试
- 测试HTTP端点
- 验证请求/响应格式
- 测试认证功能

## 错误场景处理

1. **Generation不存在**: 返回404及相应消息
2. **Generation已删除**: 视为不存在处理
3. **权限不足**: 返回403及相应消息（只能操作自己的generation）
4. **数据库错误**: 返回500及通用消息
5. **并发修改**: 通过数据库级别的乐观锁处理

## 回滚计划

如果出现问题：
1. 移除toggle API路由
2. 回滚数据库操作方法
3. 移除服务层方法
4. 保留数据库schema（favorite字段已存在并在其他地方使用）

## 未来增强功能

1. **收藏列表**: 允许用户创建自定义收藏集合
2. **批量收藏操作**: 扩展现有批量操作支持toggle
3. **收藏统计**: 用户个人收藏内容的分析
4. **收藏分类**: 按标签或类别组织收藏列表
5. **导出出口**: 允许用户导出收藏列表

## 总结

此实现采用简化的toggle方法，减少了API复杂性并增强了安全性。通过严格的用户权限控制，确保用户只能操作自己创建的内容。设计优先考虑简单性、安全性和可维护性。

数据库操作封装在GenerationsManager中，API逻辑遵循现有的服务层模式，确保代码结构清晰且易于维护。单一toggle接口也简化了前端实现逻辑。

## RESTful响应格式说明

API采用RESTful风格的响应格式，直接返回资源数据而不是RPC包装格式：

### 成功响应 (200)
```json
{
  "favorite": true
}
```

### 错误响应 (4xx/5xx)
```json
{
  "error": "Error message"
}
```

### 响应示例

**成功切换为收藏:**
```http
POST /api/generations/gen123/favorite/toggle
Content-Type: application/json

HTTP/1.1 200 OK
{
  "favorite": true
}
```

**权限不足:**
```http
POST /api/generations/gen456/favorite/toggle
Content-Type: application/json

HTTP/1.1 403 Forbidden
{
  "error": "You can only toggle favorite status for your own generations"
}
```

**Generation不存在:**
```http
POST /api/generations/nonexistent/favorite/toggle
Content-Type: application/json

HTTP/1.1 404 Not Found
{
  "error": "Generation not found"
}
```

这种RESTful风格的响应格式更简洁，符合REST API设计原则，也便于前端处理。

## 前端界面集成方案

### 📋 当前界面分析

基于现有的 `GenerationItemCard` 组件，收藏功能已有基础实现：

**现有实现：**
- **位置**：右上角（hover时显示）
- **图标**：`Star` 组件（来自 lucide-react）
- **状态**：通过 `generation.favorite` 字段控制
- **样式**：已收藏时显示实心黄色，未收藏时显示空心白色
- **事件**：通过 `onFavorite` 回调处理

**组件结构：**
```typescript
// 当前的收藏按钮实现（第141-158行）
<button
  type="button"
  className={cn(
    "cursor-pointer items-center gap-x-1 rounded p-1 transition-all hover:bg-white/20 absolute right-2 top-2",
    isHovered ? "flex" : "hidden", // 当前逻辑：只在hover时显示
  )}
  onClick={handleFavorite}
  aria-label={generation.favorite ? "Remove from favorites" : "Add to favorites"}
>
  <Star
    className={cn(
      "size-4 transition-colors stroke-2",
      generation.favorite
        ? "fill-yellow-500 text-yellow-500"  // 已收藏：实心黄色
        : "text-white fill-none",            // 未收藏：空心白色
    )}
  />
</button>
```

### 🎯 新界面需求

1. **已收藏状态**：卡片上显示实心黄色五角星
2. **点击行为**：点击后取消收藏，切换到未收藏状态
3. **未收藏状态**：默认隐藏五角星
4. **Hover行为**：hover时显示空心五角星（原来的颜色）

### 🔧 具体实现方案

#### 1. 组件显示逻辑更新

**文件位置：** `apps/web/modules/marketing/shared/components/GenerationItemCard/GenerationItemCard.tsx`

```typescript
{/* 右上角收藏按钮 - 更新后的实现 */}
<button
  type="button"
  className={cn(
    "cursor-pointer items-center gap-x-1 rounded p-1 transition-all hover:bg-white/20 absolute right-2 top-2",
    // 新逻辑：已收藏时始终显示，未收藏时hover显示
    generation.favorite ? "flex" : (isHovered ? "flex" : "hidden"),
  )}
  onClick={handleFavorite}
  aria-label={generation.favorite ? "Remove from favorites" : "Add to favorites"}
>
  <Star
    className={cn(
      "size-4 transition-colors stroke-2",
      generation.favorite
        ? "fill-yellow-500 text-yellow-500"  // 已收藏：实心黄色
        : "text-white fill-none",            // 未收藏hover：空心白色
    )}
  />
</button>
```

#### 2. API集成实现

**文件位置：** `apps/web/modules/marketing/shared/components/InfiniteMediaContent.tsx`

```typescript
// 1. 添加收藏状态更新追踪
const [favoriteUpdates, setFavoriteUpdates] = useState<Record<string, boolean>>({});

// 2. 在render时合并收藏状态更新
const updatedGeneration = {
  ...generation,
  ...(likeUpdates[generation.id] && {
    isLiked: likeUpdates[generation.id].isLiked,
    starNum: likeUpdates[generation.id].starNum
  }),
  // 新增：合并收藏状态更新
  ...(favoriteUpdates[generation.id] !== undefined && {
    favorite: favoriteUpdates[generation.id]
  })
};

// 3. 实现收藏切换逻辑
onFavorite={async (id) => {
  console.log('Favorite clicked for generation:', id);
  try {
    const response = await fetch(`/api/generations/${id}/favorite/toggle`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error toggling favorite:', errorData.error);
      
      // 处理不同类型的错误
      if (response.status === 403) {
        alert('You can only favorite your own videos');
      } else if (response.status === 404) {
        alert('Video not found');
      } else {
        alert('Failed to update favorite status');
      }
      return;
    }

    const result = await response.json(); // { favorite: true|false }
    console.log('Favorite toggled successfully:', result);
    
    // 更新单个卡片的收藏状态，无需重新获取所有数据
    setFavoriteUpdates(prev => ({
      ...prev,
      [id]: result.favorite
    }));
  } catch (error) {
    console.error('Error toggling favorite:', error);
    alert('Network error occurred');
  }
}}

// 4. 筛选条件变化时重置收藏状态
useEffect(() => {
  setSelectedIds(new Set());
  setLikeUpdates({}); // 重置点赞状态
  setFavoriteUpdates({}); // 重置收藏状态
  refetch();
}, [filters, refetch]);
```

#### 3. 类型定义更新

**文件位置：** `apps/web/modules/marketing/shared/components/GenerationItemCard/types.ts`

```typescript
export interface GenerationItemCardProps {
  generation: GenerationData;
  onPublish?: (id: string) => void;
  onFavorite?: (id: string) => Promise<void>; // 更新为异步类型
  onLike?: (id: string) => Promise<void>;
  // ... 其他属性保持不变
}

export interface CardActions {
  onPublish?: (id: string) => void;
  onFavorite?: (id: string) => Promise<void>; // 更新为异步类型
  onLike?: (id: string) => Promise<void>;
  // ... 其他属性保持不变
}
```

#### 4. Hook逻辑更新

**文件位置：** `apps/web/modules/marketing/shared/components/GenerationItemCard/hooks/useCardActions.ts`

```typescript
const handleFavorite = useCallback(async () => {
  await actions.onFavorite?.(generation.id);
}, [actions.onFavorite, generation.id]);
```

### 🎨 UI/UX 行为规范

| 状态 | 显示逻辑 | 图标样式 | 触发条件 |
|------|---------|---------|---------|
| **已收藏** | 始终显示 | 实心黄色五角星 | `generation.favorite === true` |
| **未收藏 + 非hover** | 隐藏 | 不显示 | `generation.favorite === false && !isHovered` |
| **未收藏 + hover** | 显示 | 空心白色五角星 | `generation.favorite === false && isHovered` |

### 📱 交互流程

1. **初始状态**：
   - 已收藏的视频：显示实心黄色五角星
   - 未收藏的视频：不显示任何图标

2. **Hover交互**：
   - 已收藏的视频：保持实心黄色五角星显示
   - 未收藏的视频：显示空心白色五角星

3. **点击行为**：
   - 已收藏 → 未收藏：图标消失，状态更新
   - 未收藏 → 已收藏：显示实心黄色五角星，状态更新

4. **错误处理**：
   - 权限不足：提示"只能收藏自己的视频"
   - 网络错误：提示"网络连接失败"
   - 其他错误：通用错误提示

### 🔧 实施步骤

1. **[ ] 第一步：修改组件显示逻辑**
   - 更新 `GenerationItemCard.tsx` 中的收藏按钮显示条件
   - 测试不同状态下的图标显示效果

2. **[ ] 第二步：添加状态管理**
   - 在 `InfiniteMediaContent.tsx` 中添加 `favoriteUpdates` 状态
   - 更新渲染逻辑以合并收藏状态更新

3. **[ ] 第三步：实现API调用**
   - 实现收藏切换的异步处理逻辑
   - 添加完整的错误处理和用户提示

4. **[ ] 第四步：更新类型定义**
   - 将 `onFavorite` 回调更新为异步类型
   - 确保TypeScript类型检查通过

5. **[ ] 第五步：更新Hook逻辑**
   - 修改 `useCardActions` 中的 `handleFavorite` 为异步处理
   - 测试组件的事件处理

6. **[ ] 第六步：端到端测试**
   - 测试收藏/取消收藏的完整流程
   - 验证权限控制和错误处理
   - 确保UI状态与后端数据同步

### 🚀 技术优势

- **最小改动**：复用现有组件架构和设计模式
- **用户体验**：已收藏状态持久可见，符合用户期望
- **性能优化**：单卡片状态更新，避免重新加载整个列表
- **错误处理**：完善的错误提示和权限控制机制
- **一致性**：与现有点赞功能保持一致的交互和状态管理模式
- **可维护性**：遵循现有代码规范和组件设计原则

### 🧪 测试用例

#### 功能测试
- [ ] 已收藏视频显示实心黄色五角星
- [ ] 未收藏视频默认不显示图标
- [ ] Hover未收藏视频显示空心白色五角星
- [ ] 点击切换收藏状态正常工作
- [ ] 状态更新后UI立即响应

#### 权限测试
- [ ] 只能收藏自己创建的视频
- [ ] 尝试收藏他人视频显示权限错误
- [ ] 错误提示用户友好且准确

#### 边界测试
- [ ] 网络请求失败时的错误处理
- [ ] 视频不存在时的错误处理
- [ ] 并发点击时的状态一致性

#### 性能测试
- [ ] 大量卡片时的渲染性能
- [ ] 状态更新不影响其他卡片
- [ ] 内存使用合理，无内存泄漏

这个完整的前端集成方案为收藏功能提供了详细的实施指导，确保与现有系统无缝集成并提供良好的用户体验。