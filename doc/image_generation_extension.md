# 生图API提供商和Webhook扩展设计

> 再提一个问题, 以后集成生图的呢, api提供商和webhook如何扩展?

很好的问题！生图(图像生成)的扩展设计思路：

## 1. 提供商架构扩展

现有的 VideoProvider 基类可以扩展为更通用的架构：

```typescript
// packages/api/src/lib/providers/base.ts
export abstract class BaseProvider {
  abstract readonly name: string;
  abstract readonly supportedModels: string[];
  abstract readonly supportedTypes: ('video' | 'image')[];

  abstract generateContent(request: ContentGenerationRequest): Promise<ContentGenerationResponse>;
  abstract parseWebhook(payload: any): WebhookPayload;
  abstract checkStatus(taskId: string): Promise<ContentGenerationResponse>;
}

// 具体实现
export abstract class VideoProvider extends BaseProvider {
  readonly supportedTypes = ['video'];
  // video特有的方法
}

export abstract class ImageProvider extends BaseProvider {
  readonly supportedTypes = ['image'];
  // image特有的方法
}

// 多功能提供商
export abstract class MultiModalProvider extends BaseProvider {
  readonly supportedTypes = ['video', 'image'];
  // 支持多种类型的提供商
}
```

## 2. 提供商管理器扩展

```typescript
// packages/api/src/lib/providers/manager.ts
class ContentProviderManager {
  private videoProviders: Map<string, VideoProvider> = new Map();
  private imageProviders: Map<string, ImageProvider> = new Map();
  private multiModalProviders: Map<string, MultiModalProvider> = new Map();

  async getProvider(modelCode: string, type: 'video' | 'image'): Promise<BaseProvider> {
    // 根据类型和模型获取对应提供商
    const providerName = this.getProviderName(modelCode, type);

    if (type === 'video') {
      return this.videoProviders.get(providerName) ||
             this.multiModalProviders.get(providerName);
    } else {
      return this.imageProviders.get(providerName) ||
             this.multiModalProviders.get(providerName);
    }
  }
}
```

## 3. 具体提供商实现举例

```typescript
// packages/api/src/lib/providers/midjourney.ts
export class MidjourneyProvider extends ImageProvider {
  readonly name = 'midjourney';
  readonly supportedModels = [
    'midjourney-v6',
    'midjourney-niji',
  ];

  async generateContent(request: ContentGenerationRequest): Promise<ContentGenerationResponse> {
    // Midjourney API调用
  }

  parseWebhook(payload: any): WebhookPayload {
    // Midjourney webhook解析
  }
}

// packages/api/src/lib/providers/runway.ts
export class RunwayProvider extends MultiModalProvider {
  readonly name = 'runway';
  readonly supportedModels = [
    'runway-gen3-video',
    'runway-gen3-image',
  ];

  async generateContent(request: ContentGenerationRequest): Promise<ContentGenerationResponse> {
    // 根据request.type判断是视频还是图像生成
    if (request.type === 'video') {
      return this.generateVideo(request);
    } else {
      return this.generateImage(request);
    }
  }
}
```

## 4. Webhook路由扩展

```typescript
// packages/api/src/routes/webhooks.ts
export const webhooksRouter = new Hono()
  .basePath("/webhooks")

  // 视频生成完成webhook
  .post("/video", async (c) => {
    const payload = await c.req.json();
    await handleVideoWebhook(payload);
    return c.json({ success: true });
  })

  // 图像生成完成webhook
  .post("/image-complete", async (c) => {
    const payload = await c.req.json();
    await handleImageWebhook(payload);
    return c.json({ success: true });
  })

  // 通用webhook处理器
  .post("/content-complete", async (c) => {
    const payload = await c.req.json();
    const contentType = payload.type || 'video'; // 从payload判断类型

    if (contentType === 'video') {
      await handleVideoWebhook(payload);
    } else {
      await handleImageWebhook(payload);
    }

    return c.json({ success: true });
  })

  // 提供商特定webhook
  .post("/midjourney-webhook", async (c) => {
    const payload = await c.req.json();
    await handleProviderWebhook('midjourney', payload);
    return c.json({ success: true });
  });
```

## 5. 数据库扩展

现有的Job表已经通过 type 字段区分了video和image，无需修改：

```sql
-- 现有设计已经支持
model Job {
  type TaskType  -- video | image
  // ...其他字段
}

-- 如果需要更多图像特定参数
model ImageJobParam {
  jobId           String @id
  // 图像生成特有参数
  width           Int?
  height          Int?
  steps           Int?
  cfg_scale       Float?
  sampler         String?
  // ...
}
```

## 6. 使用示例

```typescript
// 生图API调用
const imageJob = await JobService.createJob(userId, {
  type: 'image',
  featureCode: 'text-to-image',
  modelParam: {
    modelCode: 'midjourney-v6',
    prompt: 'a beautiful landscape',
    width: 1024,
    height: 1024,
    // ...
  }
});

// 视频生成API调用（保持不变）
const videoJob = await JobService.createJob(userId, {
  type: 'video',
  featureCode: 'image-to-video',
  // ...
});
```

## 总结

这样的架构设计保证了：
- 向后兼容：现有视频生成功能不受影响
- 易于扩展：新增提供商只需实现对应接口
- 类型安全：TypeScript确保类型正确
- 统一管理：webhook和提供商都有统一的管理机制