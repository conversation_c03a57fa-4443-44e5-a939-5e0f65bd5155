# 工具栏集成批量API实施方案

## 概述

本文档详细说明如何将现有的批量API集成到视频管理界面的工具栏中，实现批量收藏和批量删除功能。根据UI分析和代码审查，需要改进现有的工具栏实现，使其能够调用我们的批量API端点。

## 现状分析

### UI结构
- **主界面**: `InfiniteMediaContent.tsx` - 主要的视频展示组件
- **工具栏**: `FilterBar.tsx` - 包含内嵌的选择模式工具栏（第177-218行）
- **备用工具栏**: `SelectionToolbar.tsx` - 独立的浮动工具栏（未使用）

### 现有功能状态
✅ **已实现**：
- UI工具栏结构完整
- 选择模式切换
- 单个项目选择/全选功能
- 批量删除API端点 (`POST /api/bulk/generations/delete`)
- 批量收藏API端点 (`POST /api/bulk/generations/favorite`)

⚠️ **部分实现**：
- 批量删除功能存在但使用串行单个删除API
- 工具栏按钮存在但未连接处理函数

❌ **缺失**：
- 批量收藏功能实现
- bulk API的前端集成
- 专用的批量操作hooks

## 实施计划

### 阶段1: 创建批量操作Hooks

#### 1.1 创建 `useBulkOperations` Hook

**文件位置**: `apps/web/modules/marketing/shared/hooks/useBulkOperations.ts`

**功能特性**:
- 集成批量删除和批量收藏API
- 统一的错误处理和加载状态
- 事务性操作支持
- 完整的TypeScript类型支持

```typescript
'use client';

import { useState } from 'react';
import { useSession } from '@saas/auth/hooks/use-session';

interface UseBulkOperationsReturn {
  bulkFavorite: (generationIds: string[], favorite: boolean) => Promise<boolean>;
  bulkDelete: (generationIds: string[]) => Promise<boolean>;
  loading: boolean;
  error: string | null;
}

export function useBulkOperations(): UseBulkOperationsReturn {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { session } = useSession();

  const bulkFavorite = async (generationIds: string[], favorite: boolean): Promise<boolean> => {
    if (!session?.userId) {
      setError('用户未认证');
      return false;
    }

    if (generationIds.length === 0) {
      setError('没有选择要操作的项目');
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/bulk/generations/favorite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          generationIds,
          favorite
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP错误! 状态: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.updated) {
        console.log(`成功更新 ${result.updated.length} 个项目的收藏状态`);
        return true;
      } else {
        throw new Error(result.error || '未知的响应格式');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '发生未知错误';
      console.error('批量收藏操作错误:', errorMessage);
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const bulkDelete = async (generationIds: string[]): Promise<boolean> => {
    if (!session?.userId) {
      setError('用户未认证');
      return false;
    }

    if (generationIds.length === 0) {
      setError('没有选择要删除的项目');
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/bulk/generations/delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          generationIds
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP错误! 状态: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.deleted) {
        console.log(`成功删除 ${result.deleted.length} 个项目`);
        return true;
      } else {
        throw new Error(result.error || '未知的响应格式');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '发生未知错误';
      console.error('批量删除操作错误:', errorMessage);
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    bulkFavorite,
    bulkDelete,
    loading,
    error,
  };
}
```

### 阶段2: 更新FilterBar组件

#### 2.1 添加批量操作处理函数Props

**修改文件**: `apps/web/modules/marketing/shared/components/FilterBar.tsx`

**更改内容**:
1. 添加批量操作回调props
2. 连接工具栏按钮到处理函数
3. 添加加载状态显示

```typescript
interface FilterBarProps {
  onFiltersChange: (filters: FilterState) => void;
  onSelectModeToggle: (enabled: boolean) => void;
  selectedCount: number;
  totalCount?: number;
  onSelectAll?: () => void;
  // 新增批量操作props
  onBulkFavorite?: () => void;
  onBulkDelete?: () => void;
  onBulkDownload?: () => void;
  bulkOperationLoading?: boolean;
}
```

#### 2.2 更新工具栏按钮

**修改位置**: FilterBar.tsx 第190-217行

```typescript
<div className="flex items-center space-x-1">
  <Button 
    size="sm" 
    variant="ghost" 
    onClick={onBulkFavorite}
    disabled={selectedCount === 0 || bulkOperationLoading}
    className="text-foreground hover:bg-accent hover:text-accent-foreground p-1.5"
    title="批量收藏"
  >
    <Star className="w-4 h-4" />
  </Button>

  <Button 
    size="sm" 
    variant="ghost" 
    onClick={onBulkDownload}
    disabled={selectedCount === 0 || bulkOperationLoading}
    className="text-foreground hover:bg-accent hover:text-accent-foreground p-1.5"
    title="批量下载"
  >
    <Download className="w-4 h-4" />
  </Button>

  <Button 
    size="sm" 
    variant="ghost" 
    onClick={onBulkDelete}
    disabled={selectedCount === 0 || bulkOperationLoading}
    className="text-foreground hover:bg-accent hover:text-accent-foreground p-1.5"
    title="批量删除"
  >
    <Trash2 className="w-4 h-4" />
  </Button>
</div>
```

### 阶段3: 更新InfiniteMediaContent组件

#### 3.1 集成新的批量操作Hook

**修改文件**: `apps/web/modules/marketing/shared/components/InfiniteMediaContent.tsx`

**更改内容**:
1. 导入新的hook
2. 替换现有的批量操作实现
3. 更新FilterBar props

```typescript
// 新增导入
import { useBulkOperations } from "../hooks/useBulkOperations";

// 在组件内部添加
const { 
  bulkFavorite, 
  bulkDelete,
  loading: bulkOperationLoading, 
  error: bulkOperationError 
} = useBulkOperations();

// 替换现有的handleBulkFavorite函数
const handleBulkFavorite = async () => {
  if (selectedIds.size === 0) {
    alert('请先选择要收藏的项目');
    return;
  }

  console.log('批量收藏:', Array.from(selectedIds));
  
  // 询问用户操作类型
  const shouldFavorite = confirm(`是否要收藏选中的 ${selectedIds.size} 个项目？\n\n点击确定收藏，点击取消取消收藏`);
  
  const success = await bulkFavorite(Array.from(selectedIds), shouldFavorite);
  
  if (success) {
    // 更新UI状态
    setSelectedIds(new Set());
    setSelectMode(false);
    await refetch();
    alert(`成功${shouldFavorite ? '收藏' : '取消收藏'} ${selectedIds.size} 个项目`);
  } else if (bulkOperationError) {
    alert(`批量收藏操作失败: ${bulkOperationError}`);
  }
};

// 替换现有的handleBulkDelete函数
const handleBulkDelete = async () => {
  if (selectedIds.size === 0) {
    alert('请先选择要删除的项目');
    return;
  }

  if (!confirm(`确定要删除选中的 ${selectedIds.size} 个项目吗？此操作不可撤销。`)) {
    return;
  }

  console.log('批量删除:', Array.from(selectedIds));
  
  const success = await bulkDelete(Array.from(selectedIds));
  
  if (success) {
    // 更新UI状态
    setSelectedIds(new Set());
    setSelectMode(false);
    await refetch();
    alert(`成功删除 ${selectedIds.size} 个项目`);
  } else if (bulkOperationError) {
    alert(`批量删除操作失败: ${bulkOperationError}`);
  }
};

// 保持现有的handleBulkDownload函数
const handleBulkDownload = async () => {
  console.log('批量下载:', Array.from(selectedIds));
  // TODO: 实现批量下载逻辑
  alert('批量下载功能即将推出');
};
```

#### 3.2 更新FilterBar组件使用

```typescript
// 更新FilterBar组件的props
<FilterBar
  onFiltersChange={setFilters}
  onSelectModeToggle={handleSelectModeToggle}
  selectedCount={selectedIds.size}
  totalCount={generations.length}
  onSelectAll={handleSelectAll}
  onBulkFavorite={handleBulkFavorite}
  onBulkDelete={handleBulkDelete}
  onBulkDownload={handleBulkDownload}
  bulkOperationLoading={bulkOperationLoading}
/>
```

### 阶段4: 用户体验增强

#### 4.1 添加操作确认对话框

**推荐方案**: 使用更优雅的确认对话框替代原生alert

```typescript
// 可选：创建自定义确认对话框组件
// apps/web/modules/ui/components/confirmation-dialog.tsx

interface ConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  onConfirm: () => void;
  onCancel: () => void;
  confirmText?: string;
  cancelText?: string;
  variant?: 'default' | 'destructive';
}

export function ConfirmationDialog({
  open,
  onOpenChange,
  title,
  description,
  onConfirm,
  onCancel,
  confirmText = '确认',
  cancelText = '取消',
  variant = 'default'
}: ConfirmationDialogProps) {
  // 使用shadcn/ui的Dialog组件实现
  // 实现省略...
}
```

#### 4.2 添加操作进度指示

```typescript
// 在FilterBar组件中添加加载状态显示
{bulkOperationLoading && (
  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
    <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
    <span>处理中...</span>
  </div>
)}
```

#### 4.3 操作成功/失败反馈

```typescript
// 使用toast通知替代alert
import { toast } from '@ui/components/use-toast';

// 成功示例
toast({
  title: "操作成功",
  description: `成功收藏 ${selectedIds.size} 个项目`,
});

// 错误示例
toast({
  title: "操作失败",
  description: bulkOperationError,
  variant: "destructive",
});
```

## 实施步骤

### 第一步: 创建基础Hook (优先级: 高)

1. 创建 `useBulkOperations.ts` 文件
2. 实现批量收藏和删除API调用
3. 添加完整的错误处理和加载状态
4. 编写单元测试

### 第二步: 更新FilterBar组件 (优先级: 高)

1. 添加批量操作props接口
2. 连接工具栏按钮到处理函数
3. 添加按钮禁用状态
4. 测试UI交互

### 第三步: 集成到主组件 (优先级: 高)

1. 在 `InfiniteMediaContent` 中导入新hook
2. 替换现有批量操作实现
3. 更新FilterBar props传递
4. 测试端到端功能

### 第四步: 用户体验优化 (优先级: 中)

1. 实现确认对话框
2. 添加操作进度指示
3. 优化错误和成功反馈
4. 添加键盘快捷键支持

### 第五步: 测试和优化 (优先级: 中)

1. 端到端测试
2. 性能优化
3. 错误场景测试
4. 用户反馈收集

## 技术考虑

### 性能优化

1. **防抖处理**: 防止用户快速连续点击导致重复请求
2. **请求合并**: 在合适的时间窗口内合并多个操作
3. **缓存更新**: 操作成功后智能更新本地缓存而不是完全重新获取

### 错误处理策略

1. **网络错误**: 提供重试机制
2. **权限错误**: 明确提示用户权限不足
3. **服务器错误**: 友好的错误信息和报告机制
4. **部分失败**: 显示哪些项目操作成功/失败

### 可访问性

1. **键盘导航**: 支持Tab键导航和空格键/回车键操作
2. **屏幕阅读器**: 适当的ARIA标签和角色
3. **焦点管理**: 操作完成后的焦点处理
4. **颜色对比**: 确保按钮和文字有足够的对比度

## 测试策略

### 单元测试

```typescript
// apps/web/modules/marketing/shared/hooks/__tests__/useBulkOperations.test.ts

describe('useBulkOperations', () => {
  test('批量收藏操作成功', async () => {
    // 测试成功场景
  });

  test('批量删除操作成功', async () => {
    // 测试成功场景
  });

  test('处理网络错误', async () => {
    // 测试错误处理
  });

  test('处理未认证用户', async () => {
    // 测试权限验证
  });
});
```

### 集成测试

```typescript
// e2e/bulk-operations.spec.ts

test('用户可以批量收藏视频', async ({ page }) => {
  // 登录用户
  // 选择多个视频
  // 点击收藏按钮
  // 验证操作成功
});

test('用户可以批量删除视频', async ({ page }) => {
  // 登录用户
  // 选择多个视频
  // 点击删除按钮
  // 确认删除
  // 验证视频被删除
});
```

## 监控和分析

### 操作指标

1. **成功率**: 批量操作的成功率
2. **响应时间**: API响应时间分布
3. **使用频率**: 批量操作功能的使用频率
4. **错误类型**: 常见错误类型分析

### 用户行为分析

1. **批次大小**: 用户通常选择多少个项目进行批量操作
2. **操作偏好**: 收藏vs删除的使用比例
3. **错误恢复**: 用户如何处理操作失败

## 部署和回滚计划

### 功能开关

```typescript
// config/feature-flags.ts
export const FEATURE_FLAGS = {
  BULK_OPERATIONS: process.env.ENABLE_BULK_OPERATIONS === 'true',
  // 其他功能开关...
};
```

### 渐进式部署

1. **Phase 1**: 内部测试环境
2. **Phase 2**: Beta用户群体
3. **Phase 3**: 全量发布

### 回滚策略

1. **快速回滚**: 通过功能开关立即禁用
2. **数据一致性**: 确保回滚不影响已执行的操作
3. **用户通知**: 及时通知用户功能变更

## 维护和支持

### 文档更新

1. 更新API文档
2. 更新用户手册
3. 创建故障排除指南

### 监控告警

1. **API错误率**: 超过阈值时告警
2. **响应时间**: 性能降级告警
3. **用户反馈**: 负面反馈监控

## 总结

此实施方案提供了完整的工具栏批量API集成解决方案，包括：

- ✅ 完整的技术实现方案
- ✅ 详细的实施步骤
- ✅ 全面的测试策略
- ✅ 用户体验优化建议
- ✅ 性能和可访问性考虑
- ✅ 部署和维护指南

通过遵循此方案，可以确保批量操作功能的高质量实现，提供优秀的用户体验，同时保持代码的可维护性和可扩展性。