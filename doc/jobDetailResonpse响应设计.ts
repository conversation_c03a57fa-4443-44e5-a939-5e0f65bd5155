{
    "success": true,
    "data": {
        "user": {
            "id": "string",
            "name": "string",
            "username": "string",
            "image": "string"
        },
        "job": {
            "id": "string",
            "userId": "string",
            "featureCode": "string",
            "type": "video" | "image",
            "numOutputs": number,
            "status": "waiting" | "processing" | "succeeded" | "failed",
            "credit": number,
            "apiProviderCost": number,
            "timeCostSeconds": number,
            "modelCode": "string",
            "prompt": "string",
            "image": "string",
            "imageTail": "string",
            "negativePrompt": "string",
            "promptStrength": number,
            "duration": number,
            "modeCode": "string",
            "resolution": "string",
            "aspectRatio": "string",
            "style": "string",
            "motionRange": "string",
            "seed": number,
            "processType": "string",
            "createdAt": "ISO string",
            "updatedAt": "ISO string"
        },
        "generations": [
            {
                "id": "string",
                "mediaId": "string",
                "cover": "string",
                "thumbnail": "string",
                "videoUrl": "string",
                "mediaUrl": "string",
                "status": "waiting" | "processing" | "succeeded" | "failed",
                "mediaType": "video" | "image",
                "duration": number,
                "createdAt": "ISO string",
                "updatedAt": "ISO string"
            }
        ],
        "progress": {
            "completed": number,
            "total": number
        }
    },
    "message": "Job details retrieved successfully",
    "timestamp": "ISO string"
}