# Jobs Router OpenAPI 重构设计方案

## 1. 当前状况分析

### 1.1 重复模式识别

**与 Webhooks 的差异：**
- **重复程度较低**：约 30-40% 重复（vs Webhooks 的 85%）
- **功能差异性大**：端点功能各不相同，不适合完全模板化
- **响应格式部分统一**：错误响应和成功响应有一定模式

**重复的元素：**
```typescript
// 1. 标准错误响应模式
400: { description: "Bad request - Invalid parameters" }
404: { description: "Not found" }
500: { description: "Internal server error" }

// 2. 认证相关响应  
401: { description: "Unauthorized" }
403: { description: "Forbidden" }

// 3. 成功响应结构
200: {
  description: "Success",
  content: {
    "application/json": { schema: SomeSchema }
  }
}

// 4. 测试端点的基本模式
tags: ["Jobs"]
summary: "Test xxx"
description: "Test endpoint for xxx"
```

### 1.2 重构价值评估

**适合重构的原因：**
- ✅ **标准化错误处理**：统一错误响应格式
- ✅ **提升一致性**：保持 API 文档风格统一
- ✅ **便于扩展**：为新增端点提供模板
- ✅ **减少维护成本**：修改响应格式只需改一处

**不适合完全模板化的原因：**
- ❌ **功能差异大**：业务端点功能各不相同
- ❌ **参数复杂**：每个端点的参数和逻辑差异明显
- ❌ **过度抽象风险**：可能降低代码可读性

## 2. 推荐重构方案：**响应模板 + 端点特定描述**

### 2.1 目标目录结构

```
packages/api/src/routes/jobs/
├── openapi/                      # 新增 OpenAPI 模块
│   ├── index.ts                  # 统一导出
│   ├── responses.ts              # 标准响应模板
│   ├── descriptors.ts            # 描述生成器（选择性使用）
│   └── types.ts                  # OpenAPI 相关类型
├── lib/                          # 现有：业务逻辑
├── types.ts                      # 现有：请求/响应类型
└── router.ts                     # 现有：路由定义（将被优化）
```

### 2.2 设计理念

**混合策略：**
- **重用响应模板**：标准化常见的错误和成功响应
- **保留端点特定性**：不强制模板化差异较大的端点
- **选择性抽象**：只对明显重复的部分进行抽象

## 3. 详细实现设计

### 3.1 标准响应模板 (`openapi/responses.ts`)

```typescript
import { 
  CreateJobResponseSchema, 
  GetJobResponseSchema,
  ErrorResponseSchema 
} from "../types";

/**
 * Jobs API 标准响应定义
 */
export const jobResponses = {
  /** 成功响应生成器 */
  success: (schema: any, description: string = "Operation successful") => ({
    description,
    content: {
      "application/json": { schema }
    }
  }),

  /** 客户端错误响应 */
  badRequest: {
    description: "Bad request - Invalid parameters",
    content: {
      "application/json": {
        schema: ErrorResponseSchema
      }
    }
  },

  /** 认证失败响应 */
  unauthorized: {
    description: "Unauthorized - Authentication required",
    content: {
      "application/json": {
        schema: ErrorResponseSchema
      }
    }
  },

  /** 权限不足响应 */
  forbidden: {
    description: "Forbidden - Insufficient permissions",
    content: {
      "application/json": {
        schema: ErrorResponseSchema
      }
    }
  },

  /** 支付需求响应 */
  paymentRequired: {
    description: "Payment required - Insufficient credits",
    content: {
      "application/json": {
        schema: ErrorResponseSchema
      }
    }
  },

  /** 资源未找到响应 */
  notFound: {
    description: "Not found - Resource does not exist",
    content: {
      "application/json": {
        schema: ErrorResponseSchema
      }
    }
  },

  /** 限流响应 */
  rateLimited: {
    description: "Rate limit exceeded - Too many requests",
    content: {
      "application/json": {
        schema: ErrorResponseSchema
      }
    }
  },

  /** 服务器错误响应 */
  serverError: {
    description: "Internal server error",
    content: {
      "application/json": {
        schema: ErrorResponseSchema
      }
    }
  }
};

/**
 * 常用响应组合
 */
export const jobResponseSets = {
  /** 认证端点标准响应 */
  authenticated: {
    400: jobResponses.badRequest,
    401: jobResponses.unauthorized,
    403: jobResponses.forbidden,
    500: jobResponses.serverError
  },

  /** 创建资源端点响应 */
  createResource: {
    400: jobResponses.badRequest,
    401: jobResponses.unauthorized,
    402: jobResponses.paymentRequired,
    404: jobResponses.notFound,
    429: jobResponses.rateLimited,
    500: jobResponses.serverError
  },

  /** 获取资源端点响应 */
  getResource: {
    401: jobResponses.unauthorized,
    404: jobResponses.notFound,
    500: jobResponses.serverError
  },

  /** 测试端点响应 */
  testing: {
    500: jobResponses.serverError
  }
};
```

### 3.2 选择性描述生成器 (`openapi/descriptors.ts`)

```typescript
import { jobResponses, jobResponseSets } from "./responses";
import type { JobEndpointType, JobOpenAPIDescriptor } from "./types";

/**
 * Jobs 端点描述生成器配置
 */
interface JobDescriptorConfig {
  summary: string;
  description: string;
  successResponse?: any;
  responseSet?: keyof typeof jobResponseSets;
  customResponses?: Record<string, any>;
  additionalTags?: string[];
}

/**
 * 创建 Job 端点的 OpenAPI 描述
 * 仅用于有明显模式的端点
 */
export function createJobDescriptor(config: JobDescriptorConfig): JobOpenAPIDescriptor {
  const {
    summary,
    description,
    successResponse,
    responseSet = 'authenticated',
    customResponses = {},
    additionalTags = []
  } = config;

  const baseResponses = jobResponseSets[responseSet] || {};
  
  const responses = {
    ...(successResponse ? { 200: jobResponses.success(successResponse, "Success") } : {}),
    ...baseResponses,
    ...customResponses
  };

  return {
    tags: ["Jobs", ...additionalTags],
    summary,
    description,
    responses
  };
}

/**
 * 预配置的测试端点描述生成器
 * 适用于测试相关端点
 */
export const testEndpointDescriptors = {
  /**
   * 提供商测试端点
   */
  providerTest: (providerName: string) => createJobDescriptor({
    summary: `Test ${providerName} integration`,
    description: `Test endpoint to verify ${providerName} provider integration`,
    responseSet: 'testing',
    additionalTags: ['Testing', 'Integration']
  }),

  /**
   * 通用测试端点
   */
  generic: (testName: string) => createJobDescriptor({
    summary: `Test ${testName}`,
    description: `Test endpoint for ${testName} functionality`,
    responseSet: 'testing',
    additionalTags: ['Testing']
  })
};
```

### 3.3 类型定义 (`openapi/types.ts`)

```typescript
/**
 * Job 端点类型
 */
export type JobEndpointType = 'create' | 'get' | 'test' | 'custom';

/**
 * Job OpenAPI 描述对象
 */
export interface JobOpenAPIDescriptor {
  tags: string[];
  summary: string;
  description: string;
  responses: Record<string, any>;
}
```

### 3.4 统一导出 (`openapi/index.ts`)

```typescript
/**
 * Jobs OpenAPI 模块统一导出
 */

// 响应模板
export {
  jobResponses,
  jobResponseSets
} from "./responses";

// 描述生成器（选择性使用）
export {
  createJobDescriptor,
  testEndpointDescriptors
} from "./descriptors";

// 类型定义
export type {
  JobEndpointType,
  JobOpenAPIDescriptor
} from "./types";
```

### 3.5 重构后的路由定义 (`router.ts`)

```typescript
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { HTTPException } from "hono/http-exception";
import { authMiddleware } from "../../middleware/auth";
import { JobService } from "./lib/job-service";
import { jobResponses, jobResponseSets, testEndpointDescriptors } from "./openapi";
import {
  CreateJobRequestSchema,
  CreateJobResponseSchema,
  GetJobResponseSchema,
} from "./types";

export const jobsRouter = new Hono()
  .basePath("/jobs")
  
  // POST /api/jobs - 创建视频生成任务
  .post(
    "/",
    authMiddleware,
    describeRoute({
      tags: ["Jobs"],
      summary: "Create video generation job",
      description: "Create a new video or image generation job",
      responses: {
        200: jobResponses.success(CreateJobResponseSchema, "Job created successfully"),
        ...jobResponseSets.createResource
      },
    }),
    validator("json", CreateJobRequestSchema),
    async (c) => {
      // ... 现有逻辑保持不变
    }
  )

  // GET /api/jobs/details/:id - 获取任务详情
  .get(
    "/details/:id",
    authMiddleware,
    describeRoute({
      tags: ["Jobs"],
      summary: "Get job details",
      description: "Get details of a specific job including progress and results",
      responses: {
        200: jobResponses.success(GetJobResponseSchema, "Job details retrieved successfully"),
        ...jobResponseSets.getResource
      },
    }),
    async (c) => {
      // ... 现有逻辑保持不变
    }
  )
  
  // 测试端点 - 使用模板化描述
  .get(
    "/test-providers",
    describeRoute(testEndpointDescriptors.generic("video providers integration")),
    async (c) => {
      // ... 现有逻辑保持不变
    }
  )

  // 专门的 Replicate 测试端点 - 使用模板化描述
  .get(
    "/test-replicate",
    describeRoute(testEndpointDescriptors.providerTest("Replicate")),
    async (c) => {
      // ... 现有逻辑保持不变
    }
  );
```

## 4. 对比 Webhooks 重构的差异

### 4.1 相似之处
- ✅ **模块化组织**：openapi/ 目录结构
- ✅ **响应标准化**：统一的错误响应格式
- ✅ **类型安全**：完整的 TypeScript 支持

### 4.2 关键差异

| 维度 | Webhooks 重构 | Jobs 重构 |
|------|---------------|-----------|
| **重复程度** | 85% 重复 | 30-40% 重复 |
| **抽象策略** | 完全模板化 | 选择性抽象 |
| **处理逻辑** | 统一处理函数 | 保持端点特定逻辑 |
| **适用场景** | 类似端点大量重复 | 响应格式标准化 |
| **重构收益** | 显著减少代码量 | 提升一致性和维护性 |

## 5. 实施建议

### 5.1 分阶段实施

**阶段1：响应标准化**
1. 创建 `openapi/responses.ts`
2. 更新现有端点使用标准响应
3. 验证文档生成正确

**阶段2：测试端点模板化**
1. 创建测试端点描述生成器
2. 重构 `/test-*` 端点
3. 验证功能无回归

**阶段3：选择性扩展**
1. 识别其他可模板化的模式
2. 创建对应的描述生成器
3. 逐步应用到合适的端点

### 5.2 判断标准

**适合模板化的端点特征：**
- ✅ 响应格式相似
- ✅ 错误处理模式一致
- ✅ 功能类别相同（如测试端点）

**不适合模板化的端点特征：**
- ❌ 业务逻辑复杂且独特
- ❌ 参数结构差异很大
- ❌ 响应格式特殊

## 6. 预期收益

### 6.1 维护性提升
- **响应格式一致性**：所有端点使用标准化错误响应
- **文档质量提升**：统一的描述风格和错误说明
- **修改便利性**：响应格式修改只需要改一处

### 6.2 开发效率
- **新端点开发**：可以快速复用响应模板
- **测试端点标准化**：测试相关端点有统一的描述模式
- **文档维护**：减少重复的文档配置工作

## 7. 总结

Jobs Router 的重构方案采用**响应模板 + 选择性抽象**策略：

- ✅ **保持务实**：不过度抽象，保持代码可读性
- ✅ **标准化收益**：统一响应格式和错误处理
- ✅ **渐进式改进**：可以分阶段实施，风险可控
- ✅ **为未来准备**：为新增端点提供模板和最佳实践

这个方案在保持代码简洁性的同时，提升了 API 的一致性和可维护性，是一个平衡的重构方案。