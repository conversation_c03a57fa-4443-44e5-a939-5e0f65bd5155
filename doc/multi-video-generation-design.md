# 🎯 完整的多视频生成与显示方案

## 核心设计理念

- **一个作业批次 = 一个 MediaPreviewCard**
- **按作业分组显示，而非按单个视频显示**
- **所有状态覆盖层都支持多视频网格布局**

## 数据结构设计

### TempId机制（Creating阶段）
```javascript
// 单个tempId支持多个generation
const tempId = generateTempId(); // "${Date.now()}_${randomString}"

const creatingTask = {
  id: `creating-${tempId}`,
  tempId: tempId,
  status: "creating",
  generations: [
    { id: `temp-gen-${tempId}-0`, tempId: tempId, status: "creating" },
    { id: `temp-gen-${tempId}-1`, tempId: tempId, status: "creating" },
    { id: `temp-gen-${tempId}-2`, tempId: tempId, status: "creating" },
    { id: `temp-gen-${tempId}-3`, tempId: tempId, status: "creating" }
    // 根据outputNumber(1-4)动态生成
  ]
}
```

### API返回数据结构（后续阶段）
```javascript
const jobTask = {
  id: "job_xxx",
  jobId: "job_xxx", 
  status: "processing", // 作业整体状态
  generations: [
    { id: "gen_1", generationId: "gen_1", status: "processing", inputImage: "...", videoUrl: null },
    { id: "gen_2", generationId: "gen_2", status: "succeeded", inputImage: "...", videoUrl: "..." },
    { id: "gen_3", generationId: "gen_3", status: "failed", inputImage: "...", error: "..." },
    { id: "gen_4", generationId: "gen_4", status: "waiting", inputImage: "...", videoUrl: null }
  ]
}
```

## MediaPreviewCard ID策略

- **容器ID**: `item-${第一个generation的ID}`
  - Creating阶段: `item-temp-gen-${tempId}-0`
  - API返回后: `item-gen_1`
- **兼容现有逻辑**: VideoPreviewSection.tsx:50-51的ID计算逻辑无需修改

## 完整状态流转与显示

### MediaPreviewCard结构
```
MediaPreviewCard (作业批次容器)
├── 容器ID: item-${第一个generationId}
├── 顶部信息栏: 作业信息（模型、提示词、时间戳、用户信息）
├── 输入信息栏: 输入图片缩略图 + 提示词
└── 媒体显示区域: 2列网格布局
    ├── Creating状态: CreatingTaskOverlay × N
    ├── Submitting状态: SubmittingTaskOverlay × N  
    ├── Processing状态: ProgressOverlay × N
    ├── Succeeded状态: Video × N
    └── Failed状态: ErrorOverlay × N
```

## 各状态组件的多视频处理方案

### 1. CreatingTaskOverlay
- **输入**: 无需修改，保持现有简单结构
- **显示**: N个"Create a job for generating"加载动画
- **布局**: 2列网格，每个占位区域显示旋转加载图标
- **优势**: 沿用现有tempId机制，无需复杂修改

### 2. SubmittingTaskOverlay  
- **输入**: 接收generations数组
- **显示**: N个红色波形动画
- **布局**: 2列网格，每个区域显示"Submitting your task..."
- **状态**: 每个generation状态为"waiting"

### 3. ProgressOverlay
- **输入**: 接收generations数组
- **显示**: N个独立进度条和背景图片
- **布局**: 2列网格，每个进度条独立计算百分比
- **状态**: 每个generation状态为"processing"

### 4. Video组件（成功状态）
- **输入**: 接收generations数组
- **显示**: N个视频播放器
- **布局**: 2列网格，每个视频独立播放
- **状态**: generation状态为"succeeded"

### 5. ErrorOverlay（失败状态）
- **输入**: 接收generations数组  
- **显示**: N个错误提示信息
- **布局**: 2列网格，显示具体错误原因
- **状态**: generation状态为"failed"

## 关键实现要点

### 1. 组件接口统一化
所有状态组件需要支持：
- 接收`generations`数组而不是单个`generation`
- 内部循环渲染多个子组件
- 使用统一的2列网格布局(`grid grid-cols-2 gap-4`)

### 2. 状态判断逻辑
MediaPreviewCard根据generations数组的状态分布决定显示哪种覆盖层：
- 全部"creating" → CreatingTaskOverlay
- 全部"waiting" → SubmittingTaskOverlay  
- 包含"processing" → ProgressOverlay
- 混合状态 → 按individual generation状态显示

### 3. 布局一致性
- **统一网格**: 所有状态都使用2列网格布局
- **响应式**: 移动端可能改为1列布局
- **间距统一**: 使用`gap-4`保持视觉一致性

### 4. ID和引用管理
- **容器ID**: 使用第一个generation的ID
- **tempId传承**: Creating → Waiting阶段的ID转换
- **轮询兼容**: 现有pollJobStatus逻辑无需修改

## 数据流转示例

```
用户选择outputNumber=4 → 点击Generate
   ↓
创建creatingTask (1个tempId, 4个temp-gen-${tempId}-N)
   ↓  
显示CreatingTaskOverlay × 4 (2×2网格)
   ↓
API返回job数据 → 替换为waitingTask  
   ↓
显示SubmittingTaskOverlay × 4 (2×2网格)
   ↓
开始processing → 显示ProgressOverlay × 4 (2×2网格)
   ↓
完成后 → 显示Video × 4 (2×2网格)
```

## 实现优势

这个方案**完全兼容现有架构**，只需要扩展各个状态组件支持多generation渲染，而核心的ID策略、tempId机制、API交互逻辑都保持不变。

### 兼容性保证
- ✅ 现有单视频生成流程完全不受影响
- ✅ VideoPreviewSection的ID计算逻辑无需修改
- ✅ VideoGenerationContainer的tempId生成机制可直接复用
- ✅ API轮询和状态更新逻辑保持不变

### 扩展性优势
- 🎯 统一的网格布局系统，易于维护
- 🎯 模块化的状态组件设计，便于单独测试
- 🎯 灵活的generation数量支持（1-4个）
- 🎯 清晰的数据流转，便于调试和排错

## 下一步实现计划

1. **MediaPreviewCard**: 修改为支持多generation渲染
2. **各状态组件**: 扩展为接收generations数组
3. **VideoGenerationContainer**: 调整creating task的generation数组生成
4. **网格布局**: 实现统一的2列响应式布局
5. **测试验证**: 确保单视频和多视频场景都正常工作