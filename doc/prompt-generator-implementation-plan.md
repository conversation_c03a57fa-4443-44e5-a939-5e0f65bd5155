# Prompt Generator 功能实施方案

## 需求概述

在 image-to-video 页面的 Prompt (Optional) 输入框中添加 Generate 按钮，实现以下交互流程：

1. 点击 Generate 按钮 → 弹出创意输入模态框
2. 输入创意(最多300字符) → 点击 Continue 按钮 → 调用 POST /api/prompts 生成3个提示词
3. 显示提示词选择界面 → 可点击 Generate more 再次调用 API 生成更多提示词
4. 选中提示词 → 点击 Confirm → 将提示词填入原始输入框

## 技术架构分析

### 推荐组件结构

```
apps/web/modules/marketing/shared/components/
├── prompt-generator/
│   ├── PromptIdeaInputModal.tsx            # 创意输入模态框(新建/移动)
│   ├── PromptSelectionModal.tsx            # 提示词选择模态框(新建/移动)
│   └── PromptGeneratorButton.tsx           # Generate按钮组件(新建)

apps/web/modules/marketing/image-to-video/
├── components/
│   └── form/
│       └── PromptInputSection.tsx          # Prompt输入区域(需要修改)
```

### 设计优势

**共享组件的好处**:
- ✅ 可复用于文生视频、图生图、文生图等多个场景
- ✅ 统一的交互体验和视觉风格
- ✅ 集中维护，减少重复代码
- ✅ 便于后续功能扩展和优化

### 现有API结构

```
packages/api/src/routes/prompts/
├── index.ts                                # 路由定义
├── handlers.ts                             # 业务逻辑
├── schemas.ts                              # 数据验证
└── types.ts                                # 类型定义
```

## 详细实施步骤

### 第一阶段：优化模态框组件

#### 1. 创建 PromptIdeaInputModal 共享组件

**文件**: `apps/web/modules/marketing/shared/components/prompt-generator/PromptIdeaInputModal.tsx`

**需要改进的功能**:
- ✅ 增加300字符限制和计数显示
- ✅ 改进UI样式匹配设计图
- ✅ 优化按钮状态和交互反馈
- ✅ 添加适当的加载状态

**主要修改**:
```typescript
// 新增状态管理
const [promptText, setPromptText] = useState("");
const [isLoading, setIsLoading] = useState(false);
const MAX_CHARS = 300;

// 字符限制处理
const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
  const newText = e.target.value;
  if (newText.length <= MAX_CHARS) {
    setPromptText(newText);
  }
};

// 提交处理
const handleSubmit = async () => {
  if (promptText.trim() && !isLoading) {
    setIsLoading(true);
    try {
      await onSubmit(promptText.trim());
      setPromptText("");
      onClose();
    } finally {
      setIsLoading(false);
    }
  }
};
```

#### 2. 创建 PromptSelectionModal 共享组件

**文件**: `apps/web/modules/marketing/shared/components/prompt-generator/PromptSelectionModal.tsx`

**需要改进的功能**:
- ✅ 实现真实的API调用替换硬编码数据
- ✅ 添加Generate more功能的累积逻辑
- ✅ 改进选择和确认交互
- ✅ 添加复制功能
- ✅ 优化加载和错误状态

**主要修改**:
```typescript
interface PromptSelectionModalProps {
  open: boolean;
  prompts: string[];
  onClose: () => void;
  onSelect: (prompt: string) => void;
  onGenerateMore: () => Promise<void>; // 新增
  isGeneratingMore?: boolean; // 新增
}

// 新增状态
const [selectedPrompt, setSelectedPrompt] = useState<string | null>(null);
const [isGeneratingMore, setIsGeneratingMore] = useState(false);

// Generate more 处理
const handleGenerateMore = async () => {
  setIsGeneratingMore(true);
  try {
    await onGenerateMore();
  } finally {
    setIsGeneratingMore(false);
  }
};
```

### 第二阶段：创建API调用服务

#### 3. 创建前端API调用函数

**文件**: `apps/web/modules/marketing/shared/lib/prompts-api.ts` (新建)

```typescript
// API调用接口
interface PromptGenerationRequest {
  idea: string;
}

interface PromptGenerationResponse {
  prompts: string[];
}

export async function generatePrompts(
  idea: string
): Promise<PromptGenerationResponse> {
  const response = await fetch('/api/prompts', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ idea }),
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(error || 'Failed to generate prompts');
  }

  return response.json();
}
```

### 第三阶段：集成到主表单

#### 4. 修改 PromptInputSection 组件

**文件**: `apps/web/modules/marketing/image-to-video/components/form/PromptInputSection.tsx`

**核心功能集成**:
```typescript
import { useState } from 'react';
import { PromptIdeaInputModal } from '@marketing/shared/components/prompt-generator/PromptIdeaInputModal';
import { PromptSelectionModal } from '@marketing/shared/components/prompt-generator/PromptSelectionModal';
import { generatePrompts } from '@marketing/shared/lib/prompts-api';

export function PromptInputSection({ value, onChange }: PromptInputSectionProps) {
  // 模态框状态管理
  const [isIdeaModalOpen, setIsIdeaModalOpen] = useState(false);
  const [isSelectionModalOpen, setIsSelectionModalOpen] = useState(false);
  const [generatedPrompts, setGeneratedPrompts] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  // 处理Generate按钮点击
  const handleGenerateClick = () => {
    setIsIdeaModalOpen(true);
  };

  // 处理创意提交
  const handleIdeaSubmit = async (idea: string) => {
    setIsIdeaModalOpen(false);
    setIsGenerating(true);
    
    try {
      const response = await generatePrompts(idea);
      setGeneratedPrompts(response.prompts);
      setIsSelectionModalOpen(true);
    } catch (error) {
      // 错误处理
      console.error('Failed to generate prompts:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // 处理Generate more
  const handleGenerateMore = async () => {
    // 这里需要保存原始idea或者要求用户重新输入
    // 当前实现：重新打开idea输入框
    setIsSelectionModalOpen(false);
    setIsIdeaModalOpen(true);
  };

  // 处理提示词选择
  const handlePromptSelect = (prompt: string) => {
    onChange(prompt);
    setIsSelectionModalOpen(false);
    setGeneratedPrompts([]);
  };

  return (
    <div className="space-y-2">
      {/* 原有的输入框 */}
      <div className="relative bg-white rounded-lg shadow-sm border border-gray-100">
        <textarea
          value={value}
          onChange={(e) => onChange(e.target.value)}
          // ... 其他属性
        />
        
        {/* Generate 按钮 */}
        <button
          onClick={handleGenerateClick}
          className="absolute right-2 top-2 flex items-center gap-1.5 px-3 py-1.5 text-sm text-gray-500 bg-gray-100 hover:bg-gray-200 rounded-md"
          disabled={isGenerating}
        >
          <Sparkles className="h-4 w-4" />
          {isGenerating ? 'Generating...' : 'Generate'}
        </button>
      </div>

      {/* 模态框组件 */}
      <PromptIdeaInputModal
        open={isIdeaModalOpen}
        onClose={() => setIsIdeaModalOpen(false)}
        onSubmit={handleIdeaSubmit}
      />

      <PromptSelectionModal
        open={isSelectionModalOpen}
        prompts={generatedPrompts}
        onClose={() => setIsSelectionModalOpen(false)}
        onSelect={handlePromptSelect}
        onGenerateMore={handleGenerateMore}
      />
    </div>
  );
}
```

## 数据流程图

```mermaid
graph TD
    A[用户点击Generate按钮] --> B[弹出PromptIdeaInputModal]
    B --> C[用户输入创意idea]
    C --> D[点击Continue按钮]
    D --> E[调用POST /api/prompts]
    E --> F[显示PromptSelectionModal]
    F --> G{用户操作}
    G -->|选择提示词| H[点击Confirm]
    G -->|需要更多选项| I[点击Generate more]
    H --> J[填入原始输入框]
    I --> E
    F --> K[关闭模态框]
```

## 用户体验设计

### 交互状态设计

1. **Generate按钮状态**:
   - 默认: 可点击，显示"Generate"
   - 生成中: 禁用，显示"Generating..."
   - 完成: 恢复默认状态

2. **模态框加载状态**:
   - 创意输入: Continue按钮在API调用时显示loading
   - 提示词选择: Generate more按钮显示loading状态

3. **错误处理**:
   - API调用失败: Toast提示错误信息
   - 网络错误: 重试机制
   - 输入验证: 实时字符计数和限制

### 响应式设计

- 模态框在移动端自适应宽度
- 提示词列表支持滚动
- 按钮大小适配触摸操作

## API 设计规范

### 请求格式

```typescript
POST /api/prompts
Content-Type: application/json

{
  "idea": "用户输入的创意描述，最多300字符"
}
```

### 响应格式

```typescript
// 成功响应
{
  "prompts": [
    "生成的提示词1",
    "生成的提示词2", 
    "生成的提示词3"
  ]
}

// 错误响应
{
  "error": "错误描述",
  "code": "ERROR_CODE"
}
```

### 错误码定义

- `IDEA_TOO_LONG`: 创意描述超过300字符
- `IDEA_EMPTY`: 创意描述为空
- `RATE_LIMIT_EXCEEDED`: API调用频率超限
- `GENERATION_FAILED`: AI生成失败

## 测试策略

### 单元测试

1. **组件测试**:
   - PromptIdeaInputModal: 字符限制、提交逻辑
   - PromptSelectionModal: 选择逻辑、Generate more功能
   - PromptInputSection: 模态框状态管理

2. **API测试**:
   - generatePrompts函数: 请求格式、错误处理
   - 后端handlers: 输入验证、AI服务集成

### 集成测试

1. **用户流程测试**:
   - 完整的Generate → 输入 → 生成 → 选择流程
   - Generate more功能的累积效果
   - 错误场景的恢复处理

2. **性能测试**:
   - API响应时间监控
   - 大量提示词的渲染性能
   - 模态框动画流畅度

## 部署和监控

### 部署检查清单

- [ ] API endpoint 正确配置
- [ ] 环境变量设置(REPLICATE_API_TOKEN等)
- [ ] 国际化文本添加
- [ ] 错误监控配置

### 监控指标

1. **业务指标**:
   - Generate按钮点击率
   - 提示词生成成功率
   - 用户选择提示词的比例

2. **技术指标**:
   - API响应时间
   - 错误率统计
   - 用户行为路径分析

## 组件复用场景

### 1. 文生视频 (Text-to-Video)
```typescript
// apps/web/modules/marketing/text-to-video/components/form/PromptInputSection.tsx
import { PromptIdeaInputModal } from '@marketing/shared/components/prompt-generator/PromptIdeaInputModal';
// 复用相同的prompt生成逻辑
```

### 2. 图生图 (Image-to-Image) 
```typescript
// apps/web/modules/marketing/image-to-image/components/form/PromptInputSection.tsx
import { PromptSelectionModal } from '@marketing/shared/components/prompt-generator/PromptSelectionModal';
// 适配图像编辑的提示词需求
```

### 3. 文生图 (Text-to-Image)
```typescript
// apps/web/modules/marketing/text-to-image/components/form/PromptInputSection.tsx
// 可能需要特定的图像风格提示词
```

## 后续优化方向

1. **功能增强**:
   - 提示词收藏功能
   - 历史记录查看
   - 自定义提示词模板
   - 按场景分类的提示词（视频/图像）

2. **AI优化**:
   - 根据用户反馈优化提示词质量
   - 支持不同风格的提示词生成
   - 多语言提示词支持
   - 针对不同AI模型优化提示词格式

3. **用户体验**:
   - 提示词预览功能
   - 拖拽排序
   - 批量操作支持
   - 智能推荐相关提示词

---

## 总结

本实施方案提供了完整的Prompt Generator功能开发指导，包含了从组件设计到API集成的详细步骤。通过模块化的开发方式和清晰的数据流程，确保功能的可维护性和扩展性。同时注重用户体验和错误处理，为用户提供流畅的交互体验。