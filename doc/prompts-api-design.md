# Prompts API 设计文档与实施指南

## 概述

本文档描述了用于生成视频提示词的API端点设计，该API将调用Replicate上的OpenAI GPT-4o模型来优化和扩展用户提供的创意想法。

## API 规格

### 端点信息
- **路径**: `POST /api/prompts`
- **用途**: 基于用户输入的创意想法生成多个优化的视频生成提示词
- **认证**: 可选（根据项目需求决定是否需要认证）

### 请求结构

#### Request Body
```json
{
  "idea": "A long-haired beauty briskly walks along a tree-lined avenue, her hair flowing in the wind."
}
```

#### 参数说明
| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `idea` | string | ✅ | - | 用户的创意想法，长度1-300字符 |

### 响应结构

#### 成功响应 (200)
```json
{
  "prompts": [
    "A long-haired beauty walks briskly down a tree-lined avenue, her hair dancing in the wind, sunlight filtering through the leaves, creating a cinematic atmosphere with shallow depth of field.",
    "The elegance of a long-haired woman as she strides along a shaded path, her hair flowing freely like a river, with golden hour lighting casting warm shadows through the canopy above.",
    "A stunning woman with flowing hair briskly walks past vibrant trees, leaves rustling softly in the breeze around her, captured in slow motion with professional cinematography."
  ]
}
```

#### 错误响应 (400) - 参数验证错误
```json
{
  "error": "Idea is required"
}
```

#### 错误响应 (500) - 服务器错误
```json
{
  "error": "Failed to generate prompts"
}
```

## 技术实现

### 1. 依赖项
- **Hono**: Web框架
- **Zod**: 数据验证  
- **HTTPException**: RESTful错误处理
- **Replicate**: AI模型调用
- **OpenAI GPT-4o**: 通过Replicate调用

### 2. RESTful设计原则
- **依赖HTTP状态码**：200=成功，400=客户端错误，500=服务器错误
- **简洁响应体**：直接返回有用数据，无需封装
- **标准错误处理**：使用HTTPException统一处理

### 3. 核心逻辑流程

```mermaid
graph TD
    A[接收请求] --> B[Zod参数验证]
    B --> C[构建系统提示词]
    C --> D[调用Replicate GPT-4o]
    D --> E[解析AI响应]
    E --> F[返回JSON数据]
    
    B --> G[验证失败] --> H[HTTPException 400]
    D --> I[API调用失败] --> J[HTTPException 500]
```

### 4. 提示词工程

#### 系统提示词模板
```
You are a professional video generation prompt optimization expert. Based on the user's idea, generate 3 high-quality video generation prompts. Requirements:
1. Each prompt should describe visual scenes in detail
2. Include details about actions, environment, lighting, atmosphere, etc.
3. Suitable for AI video generation models
4. Output in English
5. Each prompt should have some variation while maintaining the core theme
Please return only the prompt list, one per line, without any other content.
```

## 详细实施步骤

### 时间估算
- **预计总时间**: 4-6小时
- **开发**: 2-3小时
- **测试**: 1-2小时  
- **文档和部署**: 1小时

### 步骤1: 环境检查和准备 (30分钟)

#### 1.1 验证现有依赖
```bash
# 检查当前工作目录
pwd

# 查看package.json确认依赖
cat packages/api/package.json | grep -E "(hono|zod|replicate)"

# 检查replicate包是否已安装
ls packages/api/node_modules/ | grep replicate
```

#### 1.2 环境变量验证
```bash
# 检查环境变量配置
echo $REPLICATE_API_TOKEN

# 如果没有设置，需要添加到.env文件
# REPLICATE_API_TOKEN=your_token_here
```

#### 1.3 项目结构确认
```
packages/api/src/routes/
├── auth.ts
├── ai.ts
├── health.ts
└── prompts.ts  # 新建这个文件
```

### 步骤2: 创建Prompts路由 (60分钟)

#### 2.1 创建类型定义
在 `packages/api/src/routes/prompts/` 目录创建：

```
prompts/
├── index.ts          # 主路由文件
├── types.ts          # 类型定义
├── schemas.ts        # Zod验证模式
└── handlers.ts       # 业务逻辑处理
```

#### 2.2 实现验证模式 (`schemas.ts`)
```typescript
import { z } from "zod";

export const promptGenerationSchema = z.object({
  idea: z.string()
    .min(1, "创意想法不能为空")
    .max(300, "创意想法过长，请控制在300字符内"),
});

export const promptResponseSchema = z.object({
  prompts: z.array(z.string()),
});
```

#### 2.3 实现类型定义 (`types.ts`)
```typescript
import { z } from "zod";
import { promptGenerationSchema, promptResponseSchema } from "./schemas";

export type PromptGenerationRequest = z.infer<typeof promptGenerationSchema>;
export type PromptGenerationResponse = z.infer<typeof promptResponseSchema>;
```

#### 2.4 实现业务逻辑 (`handlers.ts`)
```typescript
import Replicate from "replicate";
import type { PromptGenerationRequest, PromptGenerationResponse } from "./types";

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

export async function generatePrompts(
  request: PromptGenerationRequest
): Promise<PromptGenerationResponse> {
  const { idea } = request;
  const count = 3; // 固定生成3条
  const language = "en"; // 固定使用英语
  const startTime = Date.now();

  try {
    console.log(`[Prompts API] 开始生成提示词: ${JSON.stringify(request)}`);

    // 构建系统提示词
    const systemPrompt = buildSystemPrompt(count, language);
    const userPrompt = `Original idea: ${idea}\n\nPlease generate ${count} enhanced video generation prompts based on this idea.`;

    // 调用Replicate GPT-4o
    const output = await replicate.run("openai/gpt-4o", {
      input: {
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: userPrompt }
        ],
        max_tokens: 1000,
        temperature: 0.8,
      }
    });

    // 解析输出
    const generatedText = parseReplicateOutput(output);
    const prompts = parsePromptsFromText(generatedText, count);

    const duration = Date.now() - startTime;
    console.log(`[Prompts API] 生成完成，耗时: ${duration}ms`);

    return {
      prompts,
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[Prompts API] 生成失败，耗时: ${duration}ms`, error);
    
    // 根据错误类型返回不同的错误信息
    if (error instanceof Error) {
      if (error.message.includes("rate limit")) {
        throw new Error("API调用频率过高，请稍后重试");
      } else if (error.message.includes("auth")) {
        throw new Error("API认证失败，请联系管理员");
      }
    }
    
    throw error;
  }
}

function buildSystemPrompt(count: number, language: string): string {
  return `You are a professional video generation prompt optimization expert. Based on the user's idea, generate ${count} high-quality video generation prompts. Requirements:
1. Each prompt should describe visual scenes in detail
2. Include details about actions, environment, lighting, atmosphere, etc.
3. Suitable for AI video generation models
4. Output in English
5. Each prompt should have some variation while maintaining the core theme
Please return only the prompt list, one per line, without any other content.`;
}

function parseReplicateOutput(output: any): string {
  if (Array.isArray(output)) {
    return output.join("");
  } else if (typeof output === "string") {
    return output;
  } else {
    throw new Error("Replicate输出格式异常");
  }
}

function parsePromptsFromText(text: string, count: number): string[] {
  const prompts = text
    .split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0 && !line.match(/^\d+\./)) // 过滤数字编号
    .slice(0, count);

  // 如果生成数量不足，进行补充
  while (prompts.length < count) {
    prompts.push(`Enhanced cinematic prompt variation ${prompts.length + 1}`);
  }

  return prompts;
}
```

#### 2.5 实现主路由文件 (`index.ts`)
```typescript
import { Hono } from "hono";
import { HTTPException } from "hono/http-exception";
import { zValidator } from "@hono/zod-validator";
import { promptGenerationSchema } from "./schemas";
import { generatePrompts } from "./handlers";

const promptsRouter = new Hono();

promptsRouter.post(
  "/prompts",
  zValidator("json", promptGenerationSchema),
  async (c) => {
    try {
      const validatedData = c.req.valid("json");
      const result = await generatePrompts(validatedData);
      return c.json(result);
    } catch (error) {
      console.error("Prompts API错误:", error);
      
      const message = error instanceof Error ? error.message : "Failed to generate prompts";
      throw new HTTPException(500, { message });
    }
  }
);

export { promptsRouter };
```

### 步骤3: 集成到主应用 (15分钟)

#### 3.1 更新app.ts
```typescript
// 在 packages/api/src/app.ts 中添加
import { promptsRouter } from "./routes/prompts";

// 在路由配置中添加
const appRouter = app
  .route("/", authRouter)
  .route("/", webhooksRouter)
  .route("/", aiRouter)
  .route("/", promptsRouter)  // 添加这行
  .route("/", uploadsRouter)
  // ... 其他路由
```

### 步骤4: 创建测试用例 (45分钟)

#### 4.1 创建测试文件
```typescript
// packages/api/src/routes/prompts/prompts.test.ts
import { describe, it, expect, beforeAll } from '@jest/globals';
import { testClient } from 'hono/testing';
import { promptsRouter } from './index';

describe('Prompts API', () => {
  const client = testClient(promptsRouter);

  it('应该成功生成提示词', async () => {
    const response = await client.prompts.$post({
      json: {
        idea: "A cat walking in the garden"
      }
    });

    expect(response.status).toBe(200);
    
    const data = await response.json();
    expect(data.prompts).toHaveLength(3);
  });

  it('应该验证必需参数', async () => {
    const response = await client.prompts.$post({
      json: {
        // 缺少idea参数
      }
    });

    expect(response.status).toBe(400);
  });

  it('应该处理字符长度验证', async () => {
    const response = await client.prompts.$post({
      json: {
        idea: "A".repeat(301)  // 超出最大长度300
      }
    });

    expect(response.status).toBe(400);
  });
});
```

### 步骤5: 手动测试 (30分钟)

#### 5.1 启动开发服务器
```bash
pnpm dev
```

#### 5.2 使用curl测试
```bash
# 基本测试
curl -X POST http://localhost:3000/api/prompts \
  -H "Content-Type: application/json" \
  -d '{
    "idea": "A long-haired beauty briskly walks along a tree-lined avenue, her hair flowing in the wind."
  }'

# 测试参数验证
curl -X POST http://localhost:3000/api/prompts \
  -H "Content-Type: application/json" \
  -d '{
    "idea": ""
  }'
```

#### 5.3 使用Postman或其他API测试工具
创建测试集合，包含：
- 正常请求测试
- 参数验证测试  
- 错误处理测试

### 步骤6: 代码质量检查 (15分钟)

#### 6.1 运行代码检查
```bash
# 类型检查
pnpm type-check

# 代码格式化
pnpm format

# 代码检查
pnpm lint
```

#### 6.2 安全检查
- 确保没有硬编码的敏感信息
- 验证输入参数限制
- 检查错误信息不泄露内部信息

### 步骤7: 文档更新 (15分钟)

#### 7.1 更新API文档
在现有OpenAPI配置中添加新端点的文档

#### 7.2 更新README
如果需要，在项目README中添加新API的使用说明

## 验收标准

### 功能验收
- ✅ API能够成功接收请求并返回正确格式的响应
- ✅ 参数验证正常工作
- ✅ 错误处理完善
- ✅ 日志记录清晰

### 性能验收
- ✅ API响应时间在可接受范围内（通常<10秒）
- ✅ 并发请求处理正常

### 质量验收  
- ✅ 代码通过所有检查（lint, type-check）
- ✅ 测试用例覆盖主要场景
- ✅ 文档完整清晰

## 部署清单

### 部署前检查
- [ ] 环境变量已配置
- [ ] 代码已通过所有测试
- [ ] 文档已更新
- [ ] 监控配置已设置

### 部署后验证
- [ ] API端点可访问
- [ ] 功能正常工作
- [ ] 监控指标正常
- [ ] 日志记录正常

## 故障排除

### 常见问题
1. **Replicate API认证失败**
   - 检查REPLICATE_API_TOKEN环境变量
   - 验证token有效性

2. **API响应超时**
   - 检查网络连接
   - 调整超时设置

3. **生成质量不佳**
   - 优化系统提示词
   - 调整temperature参数

### 调试工具
- 使用console.log记录关键步骤
- 查看Replicate API调用日志
- 监控系统资源使用情况

## 安全考虑

### 1. 输入验证
- 严格的参数类型验证
- 字符串长度限制
- 数值范围限制

### 2. 速率限制
- 建议添加速率限制中间件
- 防止API滥用

### 3. 错误处理
- 不暴露敏感信息
- 统一错误格式
- 详细日志记录

## 监控指标

### 1. 性能指标
- 响应时间
- 吞吐量
- 成功率

### 2. 业务指标
- API调用次数
- 生成提示词质量
- 用户满意度

### 3. 成本指标
- Replicate API使用量
- 成本分析

## 扩展计划

### 短期优化
- 缓存常见请求
- 批量生成优化
- 多语言支持扩展

### 长期规划
- 自定义模型训练
- 个性化提示词生成
- 用户偏好学习

## 依赖服务

### 外部服务
- **Replicate API**: GPT-4o模型调用
- **OpenAI**: 通过Replicate间接使用

### 内部服务
- **认证服务**: 用户身份验证（可选）
- **日志服务**: 请求记录和监控
- **配置服务**: 环境变量管理

## 故障恢复

### 1. Replicate API故障
- 实现重试机制
- 降级策略（使用本地模板）
- 故障通知

### 2. 超时处理
- 设置合理超时时间
- 异步处理机制
- 用户友好提示

### 3. 数据备份
- 请求日志备份
- 配置备份
- 恢复流程文档