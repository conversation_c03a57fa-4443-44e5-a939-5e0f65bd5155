# 真实API集成测试指南

## 前置条件

1. **环境变量配置**
   - 复制 `.env.example` 到 `.env`
   - 填入真实的API密钥和配置
   - 确保数据库连接正常

2. **数据库准备**
   ```bash
   pnpm db:push
   ```

## 测试步骤

### 1. 启动开发服务器

```bash
pnpm dev
```

### 2. 基础功能测试

1. **访问 Image-to-Video 页面**
   - URL: `http://localhost:3000/image-to-video`
   - 确认页面正常加载

2. **测试视频生成流程**
   - 上传一张测试图片
   - 填写提示词（可选）
   - 选择模型和参数
   - 点击 "Generate with AI" 按钮

3. **观察控制台日志**
   
   前端日志（浏览器控制台）：
   ```
   🚀 createVideoJob called with request: {...}
   📡 Sending POST request to /api/jobs...
   📥 Received response: {...}
   ✅ Job created successfully: {...}
   ```

   后端日志（终端）：
   ```
   ========== CREATE JOB REQUEST ==========
   User ID: xxx
   Request Body: {...}
   🚀 Processing job outputs: job_xxx
   📡 Using Replicate/Fal for model xxx
   ```

### 3. 状态轮询测试

- 观察任务状态变化：creating → waiting → processing → succeeded
- 检查进度更新是否正常
- 确认视频生成完成后能正常播放

### 4. 错误处理测试

1. **无效图片测试**
   - 不上传图片直接点击生成
   - 应显示 "Please upload an image first"

2. **API错误测试**
   - 暂时修改环境变量为无效值
   - 应显示友好的错误提示

### 5. Webhook测试

1. **检查Webhook接收**
   ```bash
   # 查看webhook事件
   curl http://localhost:3000/api/webhooks/stats
   ```

2. **手动触发处理**
   ```bash
   curl -X POST http://localhost:3000/api/webhooks/process
   ```

### 6. 性能和并发测试

1. **多任务并发**
   - 快速创建多个任务
   - 观察限流是否生效

2. **长时间运行**
   - 创建任务后等待完成
   - 确认超时处理正常

## 常见问题排查

### 1. API调用失败

检查：
- 环境变量是否正确设置
- API密钥是否有效
- 网络连接是否正常

### 2. Webhook未触发

检查：
- `WEBHOOK_PROCESSOR_ENABLED=true`
- Provider的webhook URL配置
- 签名密钥是否匹配

### 3. 视频未生成

检查：
- 查看数据库中Job和Generation记录
- 检查后端日志中的错误信息
- 确认CDN配置正确

## 监控命令

```bash
# 查看Job状态
pnpm db:studio

# 查看webhook健康状态
curl http://localhost:3000/api/webhooks/health

# 查看失败的webhook事件
curl http://localhost:3000/api/webhooks/failed
```

## 回滚方案

如需快速回滚到Mock模式：

1. 修改 `apps/web/modules/shared/lib/jobs-api.ts`
2. 将真实API调用代码注释，恢复Mock代码
3. 重启开发服务器