# Replicate Webhook 视频处理实施指南

## 快速概览

本指南提供了实现 Replicate webhook 视频处理的核心步骤，包括视频上传到 R2 和状态更新。

## 核心实现步骤

### 1. 创建视频上传服务

在 `packages/api/src/lib/services/video-upload.service.ts` 中实现：

```typescript
export class VideoUploadService {
  // 从 URL 下载视频到 R2
  async uploadFromUrl(sourceUrl: string, options: UploadOptions): Promise<string>
  
  // 生成 CDN URL
  generateCdnUrl(key: string): string
  
  // 验证视频文件
  validateVideo(url: string): Promise<boolean>
}
```

主要功能：
- 流式下载避免内存溢出
- 自动重试机制
- 返回 CDN URL

### 2. 扩展 Webhook Processor

修改 `packages/api/src/routes/webhooks/shared/processor.ts`：

#### 2.1 增强成功状态处理

```typescript
if (payload.status === 'succeed' && payload.resultUrl) {
  // 1. 上传视频到 R2
  const cdnUrl = await videoUploadService.uploadFromUrl(
    payload.resultUrl,
    {
      userId: generation.userId,
      generationId: generation.id,
      filename: `${Date.now()}-${generateId()}.mp4`
    }
  );
  
  // 2. 更新 Generation
  await updateGeneration({
    videoUrl: cdnUrl,
    status: 'succeed'
  });
  
  // 3. 检查并更新 Job 状态
  await checkAndUpdateJobStatus(generation.jobId);
}
```

#### 2.2 Job 状态更新逻辑

```typescript
async function checkAndUpdateJobStatus(jobId: string) {
  const stats = await getJobCompletionStats(jobId);
  
  if (stats.allCompleted) {
    if (stats.successRate > 0) {
      await updateJob(jobId, { status: 'succeed' });
    } else {
      await updateJob(jobId, { status: 'failed' });
      await refundCredits(jobId, 1.0); // 100% refund
    }
  }
}
```

### 3. 配置 R2 存储

#### 3.1 存储路径结构

```
web-cdn/fluxfly/production/{userId}/{generationId}/ori/{filename}
```

#### 3.2 使用现有 Storage 包

利用 `@repo/storage` 中的现有功能：

```typescript
import { S3Service } from '@repo/storage';

const storage = new S3Service({
  bucketName: process.env.R2_BUCKET_NAME,
  region: 'auto',
  endpoint: `https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`
});
```

### 4. 错误处理增强

#### 4.1 添加错误分类

```typescript
enum VideoProcessingError {
  DOWNLOAD_FAILED = 'DOWNLOAD_FAILED',
  UPLOAD_FAILED = 'UPLOAD_FAILED',
  INVALID_VIDEO = 'INVALID_VIDEO',
  STORAGE_QUOTA_EXCEEDED = 'STORAGE_QUOTA_EXCEEDED'
}
```

#### 4.2 错误恢复策略

- 下载失败：3次重试，指数退避
- 上传失败：记录原始URL，支持手动重试
- 无效视频：标记错误，不重试

### 5. 监控和日志

#### 5.1 关键日志点

```typescript
// Webhook 接收
logger.info('[Webhook] Received', { provider: 'replicate', taskId, status });

// 视频处理
logger.info('[Video] Processing started', { url, generationId });
logger.info('[Video] Upload completed', { cdnUrl, duration, size });

// 状态更新
logger.info('[Status] Generation updated', { generationId, status });
logger.info('[Status] Job completed', { jobId, successRate });
```

#### 5.2 性能指标

- 视频下载时间
- 上传速度 (MB/s)
- 总处理时间
- 成功率

### 6. 数据库事务保证

使用 Prisma 事务确保数据一致性：

```typescript
await prisma.$transaction(async (tx) => {
  // 1. 更新 Generation
  await tx.generation.update(...);
  
  // 2. 检查并更新 Job
  const jobStats = await tx.generation.groupBy(...);
  if (allCompleted) {
    await tx.job.update(...);
  }
  
  // 3. 记录处理日志
  await tx.processingLog.create(...);
});
```

## 部署清单

### 环境变量

```bash
# R2 配置
R2_BUCKET_NAME=fluxfly
R2_ACCOUNT_ID=your-account-id
R2_ACCESS_KEY_ID=your-access-key
R2_SECRET_ACCESS_KEY=your-secret-key

# CDN 配置
VIDEO_CDN_BASE_URL=https://videocdn.fluxfly.ai

# 处理限制
MAX_VIDEO_SIZE_MB=500
VIDEO_DOWNLOAD_TIMEOUT_MS=300000
```

### 权限配置

确保 R2 bucket 有正确的 CORS 配置：

```json
{
  "AllowedOrigins": ["https://fluxfly.ai"],
  "AllowedMethods": ["GET", "PUT"],
  "AllowedHeaders": ["*"],
  "MaxAgeSeconds": 3600
}
```

## 测试建议

### 1. 本地测试

使用 ngrok 或类似工具测试 webhook：

```bash
ngrok http 3001
# 更新 Replicate webhook URL 为 ngrok URL
```

### 2. 测试用例

- ✅ 正常视频上传流程
- ✅ 大文件处理（>100MB）
- ✅ 网络中断恢复
- ✅ 并发处理多个视频
- ✅ Job 状态正确更新

### 3. 压力测试

```bash
# 模拟并发 webhook
artillery quick -c 10 -n 100 http://localhost:3001/webhooks/replicate
```

## 常见问题

### Q: 视频上传超时怎么办？
A: 增加 `VIDEO_DOWNLOAD_TIMEOUT_MS`，或实现分片上传

### Q: 如何处理重复的 webhook？
A: 使用 `externalTaskId` 做幂等性检查

### Q: R2 存储满了怎么办？
A: 实现定期清理策略，删除超过 30 天的临时文件

### Q: 如何验证视频完整性？
A: 比较文件大小，可选实现 MD5 校验

## 下一步

1. **Phase 1**: 实现基础上传功能
2. **Phase 2**: 添加重试和错误处理
3. **Phase 3**: 优化性能和监控
4. **Phase 4**: 实现高级功能（缩略图、预览等）