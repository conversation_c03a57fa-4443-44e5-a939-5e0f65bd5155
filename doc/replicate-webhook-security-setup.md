# Replicate Webhook Security Setup

## 概述

为了增强 Replicate webhook 的安全性，我们实现了基于 HMAC 的安全 token 机制，防止重放攻击和恶意请求。该方案在调用 Replicate API 时生成带时间戳的安全 token，并在 webhook 回调时验证其有效性。

## 安全机制

### 1. HMAC Token 生成
- 使用 SHA256 算法生成 HMAC 签名
- Token 包含：用户ID、任务ID、时间戳
- 使用 base64url 编码，确保 URL 安全

### 2. 时间戳验证
- 默认有效期：20分钟（考虑到视频生成耗时）
- 允许 1 分钟的时钟偏差
- 防止重放攻击

### 3. 双重验证
- 支持传统的 webhook 签名验证（可选）
- 新增安全 token 验证（推荐）
- 两种方式可同时使用或独立使用

## 环境配置

### 必需环境变量

```bash
# Replicate API 基础配置
REPLICATE_API_TOKEN="r8_xxxxxxxxxxxxxxxxxxxxxxxxxxxxx"

# 安全 Token 密钥（推荐）- 用于生成和验证 HMAC token
REPLICATE_WEBHOOK_SECRET_KEY="your-super-secret-webhook-signing-key-here"
```

### 可选环境变量

```bash
# 传统 Webhook 签名验证（可选）
REPLICATE_WEBHOOK_SECRET="your-webhook-secret"

# IP 白名单（可选）
REPLICATE_ALLOWED_IPS="***********,********"
```

### 生成安全密钥

推荐使用以下方法生成强密钥：

```bash
# 方法 1: 使用 openssl
openssl rand -base64 32

# 方法 2: 使用 Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"

# 方法 3: 使用在线工具（不推荐用于生产环境）
# https://passwordsgenerator.net/
```

## 实现原理

### 1. Token 生成流程

当创建 Replicate prediction 时：

```typescript
// 1. 生成安全参数
const { token, timestamp } = generateWebhookToken({
  userId: "user123",
  taskId: "gen456", 
  secretKey: process.env.REPLICATE_WEBHOOK_SECRET_KEY!
});

// 2. 添加到 input 参数中
const secureInput = {
  ...originalInput,
  webhook_user_id: "user123",
  webhook_task_id: "gen456", 
  webhook_timestamp: timestamp,
  webhook_token: token
};

// 3. 调用 Replicate API
await replicate.predictions.create({
  model: modelEndpoint,
  input: secureInput,
  webhook: "https://yoursite.com/webhooks/replicate"
});
```

### 2. Token 验证流程

当接收到 webhook 时：

```typescript
// 1. 从 webhook payload 的 input 中提取安全信息
const tokenData = extractTokenFromInput(payload.input);

// 2. 验证 token
const verification = verifyWebhookToken({
  userId: tokenData.userId,
  taskId: tokenData.taskId,
  timestamp: tokenData.timestamp,
  receivedToken: payload.input.webhook_token,
  secretKey: process.env.REPLICATE_WEBHOOK_SECRET_KEY!,
  maxAgeMinutes: 20
});

// 3. 检查验证结果
if (!verification.valid) {
  console.error(`Token validation failed: ${verification.reason}`);
  return false;
}
```

## 部署说明

### 1. 环境变量设置

确保在生产环境中正确设置环境变量：

```bash
# .env.production
REPLICATE_WEBHOOK_SECRET_KEY="your-production-secret-key"
```

### 2. 密钥管理

- **开发环境**: 可使用相对简单的密钥
- **生产环境**: 必须使用强随机密钥
- **密钥轮换**: 定期更换密钥以提高安全性
- **安全存储**: 使用密钥管理服务（如 AWS Secrets Manager、Hashicorp Vault）

### 3. 监控和日志

系统会自动记录以下日志：

```
✅ [Replicate] Security token validated for user user123, task gen456
❌ [Replicate] Security token validation failed: Token expired
⚠️  [Replicate] Security token expected but not found in webhook payload
```

## 故障排查

### 常见问题

1. **Token 验证失败**
   ```
   Error: Token signature mismatch
   ```
   - 检查 `REPLICATE_WEBHOOK_SECRET_KEY` 是否正确
   - 确保生成和验证时使用相同的密钥

2. **Token 过期**
   ```
   Error: Token expired. Age: 1800s, Max age: 1200s
   ```
   - 视频生成时间超过 20 分钟（默认）
   - 可考虑增加 `maxAgeMinutes` 参数

3. **缺少安全参数**
   ```
   Warning: Security token expected but not found
   ```
   - 检查 `userId` 和 `taskId` 是否正确传递给 provider
   - 确保环境变量配置正确

### 调试模式

启用详细日志以排查问题：

```bash
# 开发环境
DEBUG=webhook:* npm run dev

# 生产环境
LOG_LEVEL=debug npm start
```

## 性能考虑

### 1. Token 生成开销
- HMAC SHA256 计算非常快（< 1ms）
- 对系统性能影响可忽略不计

### 2. Token 验证开销
- 每个 webhook 额外验证时间 < 1ms
- 使用时间安全比较防止计时攻击

### 3. 内存使用
- 无需维护 token 存储
- 仅在验证时临时计算和比较

## 安全最佳实践

### 1. 密钥管理
- ✅ 使用强随机密钥（至少 32 字节）
- ✅ 定期轮换密钥
- ✅ 安全存储密钥
- ❌ 不要在代码中硬编码密钥

### 2. 网络安全
- ✅ 使用 HTTPS 传输 webhook
- ✅ 配置 IP 白名单（如果可能）
- ✅ 启用防火墙和网络访问控制

### 3. 监控和审计
- ✅ 记录所有验证失败的尝试
- ✅ 设置异常告警
- ✅ 定期审计访问日志

## 迁移指南

### 从无安全验证迁移

1. **阶段 1**: 配置环境变量
   ```bash
   REPLICATE_WEBHOOK_SECRET_KEY="your-secret-key"
   ```

2. **阶段 2**: 部署代码更新
   - 新的 webhook 将包含安全 token
   - 旧的 webhook 仍然接受（兼容性）

3. **阶段 3**: 验证和监控
   - 观察日志确保新 webhook 正常工作
   - 检查是否有验证失败的情况

### 从传统签名验证迁移

可以同时使用两种验证方式：

```bash
# 传统验证（保持不变）
REPLICATE_WEBHOOK_SECRET="old-webhook-secret"

# 新的安全 token（添加）
REPLICATE_WEBHOOK_SECRET_KEY="new-hmac-secret-key"
```

## API 参考

### generateWebhookToken

```typescript
function generateWebhookToken(options: {
  userId: string;
  taskId: string;
  secretKey: string;
}): { token: string; timestamp: number }
```

### verifyWebhookToken

```typescript
function verifyWebhookToken(options: {
  userId: string;
  taskId: string;
  timestamp: number;
  receivedToken: string;
  secretKey: string;
  maxAgeMinutes?: number; // 默认 20 分钟
}): { valid: boolean; reason?: string }
```

### addSecurityToInput

```typescript
function addSecurityToInput(
  input: any, 
  options: GenerateTokenOptions
): any
```

### extractTokenFromInput

```typescript
function extractTokenFromInput(input: any): WebhookTokenData | null
```

## 技术支持

如遇到问题，请检查：

1. **环境变量配置**是否正确
2. **密钥格式**是否符合要求
3. **网络连接**是否正常
4. **日志输出**中的错误信息

更多帮助请参考：
- 项目 Issues: https://github.com/your-project/issues
- 技术文档: https://docs.your-project.com
- Replicate 官方文档: https://replicate.com/docs