# Replicate Webhook 测试指南

## 快速测试

### 1. 环境准备

确保以下环境变量已配置：

```bash
# 必需
REPLICATE_WEBHOOK_SECRET_KEY="your-secret-key"
DATABASE_URL="your-database-url"
S3_ENDPOINT="your-r2-endpoint"
S3_ACCESS_KEY_ID="your-access-key"
S3_SECRET_ACCESS_KEY="your-secret-key"

# 可选
WEBHOOK_TEST_URL="http://localhost:3001/api/webhooks/replicate"
NEXT_PUBLIC_VIDEO_CDN_BASE_URL="https://videocdn.fluxfly.ai"
```

### 2. 运行测试

```bash
# 在 packages/api 目录下
npm run dev  # 启动开发服务器

# 在另一个终端运行测试
npx tsx test-webhook-replicate.ts
```

### 3. 测试用例

脚本会自动测试以下场景：

1. **视频生成成功**: 测试完整的视频下载、上传、状态更新流程
2. **视频生成失败**: 测试错误处理和积分返还
3. **视频生成进行中**: 测试进度更新

## 手动测试

### 使用 curl 发送 webhook

```bash
# 测试成功状态
curl -X POST http://localhost:3001/api/webhooks/replicate \
  -H "Content-Type: application/json" \
  -d '{
    "id": "test-prediction-123",
    "status": "succeeded",
    "output": ["https://replicate.delivery/pbxt/test/video.mp4"],
    "input": {
      "prompt": "Test video",
      "webhook_user_id": "test-user",
      "webhook_task_id": "test-generation",
      "webhook_timestamp": 1703123456,
      "webhook_token": "test-token"
    }
  }'
```

### 使用 ngrok 测试外部 webhook

```bash
# 安装 ngrok
npm install -g ngrok

# 暴露本地端口
ngrok http 3001

# 复制 ngrok URL 并更新 Replicate webhook 设置
# 例如: https://abc123.ngrok.io/api/webhooks/replicate
```

## 预期行为

### 成功流程

1. ✅ Webhook 安全验证通过
2. ✅ 从 Replicate URL 下载视频
3. ✅ 上传视频到 R2/CDN
4. ✅ 更新 Generation 记录（videoUrl = CDN URL）
5. ✅ 检查并更新 Job 状态
6. ✅ 清除相关缓存

### 失败处理

1. ❌ 下载失败：重试机制，最终使用原始 URL
2. ❌ 上传失败：回退到原始 URL，记录错误
3. ❌ 数据库错误：事务回滚，重试操作
4. ❌ 全部失败：积分返还，Job 状态更新为 failed

## 监控日志

关键日志标识符：

```
[Webhook] - 主流程日志
[VideoUpload] - 视频上传相关
[JobStatus] - Job 状态管理
[Retry] - 重试机制
```

### 重要日志示例

```
✅ [Webhook] Video uploaded to CDN
📈 [JobStatus] Job status updated
🔄 [Retry] Operation succeeded after 2 attempts
💰 [JobStatus] Credit refunded: 10 credits
```

## 故障排查

### 常见问题

1. **Token 验证失败**
   ```
   Error: Security token validation failed: Token expired
   ```
   - 检查系统时间
   - 确认 SECRET_KEY 一致

2. **视频下载超时**
   ```
   Error: Failed to download video after retries
   ```
   - 检查网络连接
   - 增加超时时间

3. **S3 上传失败**
   ```
   Error: Failed to upload video to S3
   ```
   - 检查 S3 凭据
   - 确认存储桶权限

4. **数据库连接错误**
   ```
   Error: Database operation failed
   ```
   - 检查 DATABASE_URL
   - 确认数据库运行状态

### 调试模式

```bash
# 启用详细日志
DEBUG=webhook:*,video:*,job:* npm run dev

# 或设置环境变量
LOG_LEVEL=debug npm run dev
```

## 性能基准

### 预期处理时间

- **小视频** (< 50MB): 30-60 秒
- **中等视频** (50-200MB): 1-3 分钟  
- **大视频** (> 200MB): 3-10 分钟

### 并发处理

- 支持同时处理多个 webhook
- 每个上传使用独立的临时文件
- 自动清理临时资源

## 生产环境注意事项

1. **监控设置**
   - 设置 webhook 处理失败告警
   - 监控存储空间使用量
   - 跟踪平均处理时间

2. **性能优化**
   - 考虑使用 CDN 加速下载
   - 实施队列系统处理高并发
   - 定期清理过期文件

3. **安全考虑**
   - 定期轮换 SECRET_KEY
   - 监控异常访问模式
   - 实施速率限制

## 扩展功能

### 将来可以添加的功能

1. **视频处理**
   - 自动生成缩略图
   - 创建预览片段
   - 转换视频格式

2. **通知系统**
   - 完成后发送邮件/推送
   - WebSocket 实时状态更新
   - Slack/Discord 集成

3. **分析统计**
   - 处理时间分析
   - 成功率统计
   - 存储使用情况