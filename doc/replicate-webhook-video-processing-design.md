# Replicate Webhook 视频处理设计方案

## 1. 概述

本方案设计了一个完整的 Replicate webhook 回调处理系统，实现视频生成完成后的自动化处理流程，包括视频下载、上传到 CDN、数据库状态更新等核心功能。

## 2. 系统架构

### 2.1 核心组件

```
┌─────────────────┐     ┌──────────────────┐     ┌─────────────────┐
│   Replicate     │────▶│  Webhook Router  │────▶│  Webhook Handler│
│   Callback      │     │   /webhooks/     │     │   (Validator)   │
└─────────────────┘     └──────────────────┘     └────────┬────────┘
                                                           │
                                                           ▼
┌─────────────────┐     ┌──────────────────┐     ┌─────────────────┐
│   Job Status    │◀────│ Webhook Processor│◀────│  Video Uploader │
│   Manager       │     │  (Core Logic)    │     │   (R2 Upload)   │
└─────────────────┘     └──────────────────┘     └─────────────────┘
```

### 2.2 数据流

1. **Webhook 接收**: Replicate 发送 webhook 到 `/webhooks/replicate`
2. **安全验证**: 验证 HMAC token 和签名
3. **状态分发**: 根据状态执行不同的处理逻辑
4. **视频处理**: 成功状态下载并上传视频到 R2
5. **数据更新**: 更新 Generation 和 Job 记录
6. **通知机制**: 可选的完成通知

## 3. 详细设计

### 3.1 Webhook 处理流程

#### 状态处理策略

| 状态 | 处理动作 | 数据更新 |
|------|---------|----------|
| `starting` | 记录开始时间 | Generation.status = 'processing' |
| `processing` | 更新进度 | Generation.progress = x% |
| `succeeded` | 完整处理流程 | 上传视频 + 更新URL + 状态 |
| `failed` | 记录错误 | Generation.status = 'failed' + errorMessage |
| `canceled` | 标记取消 | Generation.status = 'failed' |

#### 成功状态处理流程

提供两种处理模式：

**模式1：端到端流式处理**（推荐，默认启用）
```
1. 提取视频 URL
   └─> payload.output[0] 或 payload.output.video_url

2. 流式传输（零内存占用）
   ├─> Replicate URL → fetch() response.body
   ├─> Readable.fromWeb() → 直接上传到 R2
   ├─> 并发下载+上传，节省30-50%时间
   └─> 支持任意大小文件，无内存限制

3. 生成 CDN URL
   ├─> 路径: web-cdn/fluxfly/production/{userId}/{generationId}/ori/
   └─> URL: https://videocdn.fluxfly.ai/...

4. 更新 Generation 记录
   ├─> videoUrl = CDN URL
   ├─> status = 'succeed'
   └─> 记录文件大小和处理时间

5. 检查 Job 完成状态
   └─> 所有 Generation 完成则更新 Job 状态
```

**模式2：临时文件模式**（兼容模式）
```
1-2. 流式下载到临时文件 → 流式上传到 R2 → 清理临时文件
3-5. 同上
```

**配置控制**：
- `VIDEO_UPLOAD_STREAMING=true` - 端到端流式（推荐）
- `VIDEO_UPLOAD_STREAMING=false` - 临时文件模式

### 3.2 视频上传器设计

#### 核心功能

1. **流式处理**: 避免大文件占用过多内存
2. **断点续传**: 大文件上传失败后可继续
3. **并发控制**: 限制同时上传数量
4. **进度跟踪**: 实时更新上传进度

#### 文件组织结构

```
web-cdn/
└── fluxfly/
    └── production/
        └── {userId}/
            └── {generationId}/
                ├── ori/          # 原始视频
                │   └── {timestamp}-{uuid}.mp4
                ├── thumb/        # 缩略图（可选）
                │   └── {timestamp}-{uuid}.jpg
                └── preview/      # 预览视频（可选）
                    └── {timestamp}-{uuid}_preview.mp4
```

### 3.3 Job 状态管理器

#### 状态计算逻辑

```typescript
interface JobStatusCalculation {
  totalGenerations: number;
  completedGenerations: number;
  successfulGenerations: number;
  failedGenerations: number;
}

// 状态判定规则
- 全部成功 → Job.status = 'succeed'
- 部分成功 → Job.status = 'succeed' (with warning)
- 全部失败 → Job.status = 'failed'
- 仍在处理 → Job.status = 'processing'
```

#### 积分处理

- **全部失败**: 100% 积分返还
- **部分失败**: 按失败比例返还积分
- **计算公式**: `refundAmount = job.credit * (failedCount / totalCount)`

### 3.4 错误处理和重试机制

#### 重试策略

1. **网络错误**: 指数退避重试（1s, 2s, 4s, 8s）
2. **上传失败**: 最多重试 3 次
3. **数据库错误**: 使用事务保证一致性

#### 错误分类

| 错误类型 | 处理方式 | 重试策略 |
|----------|---------|----------|
| 网络超时 | 自动重试 | 指数退避 |
| 文件过大 | 分片上传 | 断点续传 |
| 权限错误 | 记录日志 | 不重试 |
| 数据不一致 | 事务回滚 | 人工介入 |

### 3.5 监控和日志

#### 关键监控指标

1. **性能指标**
   - Webhook 处理延迟
   - 视频上传速度
   - 队列积压情况

2. **业务指标**
   - 成功率统计
   - 平均处理时间
   - 存储使用量

#### 日志级别

```
INFO:  正常流程日志
WARN:  部分失败或异常但可恢复
ERROR: 需要人工介入的错误
DEBUG: 详细调试信息（开发环境）
```

## 4. 扩展性设计

### 4.1 队列系统（可选）

对于高并发场景，可引入队列系统：

```
Webhook → Queue → Workers → Storage
```

优势：
- 削峰填谷
- 失败重试
- 横向扩展

### 4.2 缓存策略

1. **元数据缓存**: 缓存 Generation 查询结果
2. **URL 缓存**: 缓存 CDN URL 映射
3. **状态缓存**: 缓存 Job 完成状态

### 4.3 多存储支持

支持多种存储后端：
- Cloudflare R2（主要）
- AWS S3（备份）
- 本地存储（开发）

## 5. 安全考虑

### 5.1 访问控制

1. **Webhook 验证**: HMAC token + IP 白名单
2. **存储权限**: 最小权限原则
3. **URL 签名**: 私有文件使用签名 URL

### 5.2 数据保护

1. **传输加密**: HTTPS 传输
2. **存储加密**: R2 存储加密
3. **备份策略**: 定期备份关键数据

## 6. 实施计划

### Phase 1: 基础功能（1-2天）
- [x] Webhook 安全验证
- [ ] 视频下载和上传
- [ ] Generation 状态更新
- [ ] Job 状态计算

### Phase 2: 优化增强（2-3天）
- [ ] 流式处理优化
- [ ] 重试机制完善
- [ ] 监控日志系统
- [ ] 性能优化

### Phase 3: 扩展功能（可选）
- [ ] 队列系统集成
- [ ] 多存储支持
- [ ] 视频处理（缩略图、预览）
- [ ] 完成通知机制

## 7. 配置参数

### 环境变量

```bash
# 存储配置
R2_BUCKET_NAME=fluxfly
R2_ACCOUNT_ID=xxx
R2_ACCESS_KEY_ID=xxx
R2_SECRET_ACCESS_KEY=xxx

# CDN 配置
VIDEO_CDN_BASE_URL=https://videocdn.fluxfly.ai

# 处理配置
VIDEO_DOWNLOAD_TIMEOUT=300000  # 5分钟
VIDEO_UPLOAD_TIMEOUT=600000    # 10分钟
MAX_CONCURRENT_UPLOADS=5       # 最大并发上传数

# 重试配置
MAX_RETRY_ATTEMPTS=3
RETRY_DELAY_BASE=1000         # 1秒
```

### 功能开关

```typescript
const features = {
  enableVideoUpload: true,      // 启用视频上传
  enableThumbnail: false,       // 启用缩略图生成
  enableNotification: false,    // 启用完成通知
  enableQueue: false,           // 启用队列系统
};
```

## 8. 测试策略

### 8.1 单元测试
- Token 验证逻辑
- 状态计算逻辑
- URL 生成逻辑

### 8.2 集成测试
- Webhook 端到端流程
- 上传功能测试
- 错误处理测试

### 8.3 性能测试
- 大文件上传
- 并发处理能力
- 内存使用情况

## 9. 运维指南

### 9.1 部署检查清单
- [ ] 环境变量配置完整
- [ ] 存储权限正确设置
- [ ] 监控告警已配置
- [ ] 日志收集正常

### 9.2 故障排查
1. **Webhook 未触发**: 检查 Replicate 配置和网络
2. **上传失败**: 检查存储权限和配额
3. **状态不一致**: 检查数据库事务和并发

### 9.3 性能调优
- 调整并发上传数
- 优化数据库查询
- 使用 CDN 加速

## 10. 总结

本方案提供了一个完整的 Replicate webhook 视频处理系统设计，涵盖了从接收 webhook 到视频存储的完整流程。通过模块化设计和清晰的错误处理机制，确保系统的可靠性和可维护性。后续可根据实际需求逐步实现各项功能，并持续优化性能。