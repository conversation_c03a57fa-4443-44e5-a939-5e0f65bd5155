# 基于 Upstash Redis 的图片转视频系统完整优化方案

## 🎯 方案概述

使用 Upstash Redis 作为限流存储，结合 rate-limiter-flexible 实现专业的分布式限流，同时修复现有架构问题。

## 📋 当前问题分析

1. **图片重复上传**: 用户选择时已上传到videocdn，API中重复上传
2. **缺少异步处理**: job-service.ts 中 processJobAsync 未实现
3. **并发保护缺失**: 无专业限流机制
4. **状态管理**: 需要webhook接收API提供商回调

## 🚀 完整解决方案

### 1. Upstash Redis 配置

#### 1.1 安装依赖

```bash
pnpm add rate-limiter-flexible @upstash/redis
```

#### 1.2 环境变量配置

```env
# .env
UPSTASH_REDIS_URL=https://your-endpoint.upstash.io
UPSTASH_REDIS_TOKEN=your-token
WEBHOOK_BASE_URL=https://your-app.vercel.app
VIDEO_API_KEY=your-video-api-key
```

#### 1.3 Redis 连接配置

**新建**: `packages/api/src/lib/redis.ts`

```typescript
import { Redis } from '@upstash/redis';

// Upstash Redis 连接
export const redis = new Redis({
  url: process.env.UPSTASH_REDIS_URL!,
  token: process.env.UPSTASH_REDIS_TOKEN!,
});

// 连接测试
export async function testRedisConnection() {
  try {
    await redis.ping();
    console.log('✅ Redis connected successfully');
    return true;
  } catch (error) {
    console.error('❌ Redis connection failed:', error);
    return false;
  }
}
```

### 2. 专业限流中间件

#### 2.1 创建限流器

**新建**: `packages/api/src/middleware/rate-limiter.ts`

```typescript
import { RateLimiterRedis } from 'rate-limiter-flexible';
import { HTTPException } from 'hono/http-exception';
import { redis } from '../lib/redis';
import type { Context, Next } from 'hono';

// 将 Upstash Redis 适配为 rate-limiter-flexible 格式
const redisAdapter = {
  async get(key: string) {
    const result = await redis.get(key);
    return result;
  },

  async set(key: string, value: any, ttl?: number) {
    if (ttl) {
      await redis.setex(key, ttl, value);
    } else {
      await redis.set(key, value);
    }
  },

  async del(key: string) {
    await redis.del(key);
  },

  async eval(script: string, numKeys: number, ...args: any[]) {
    return await redis.eval(script, numKeys, ...args);
  }
};

// 视频生成限流器 - 基于用户等级
const createVideoLimiter = (userPlan: string = 'free') => {
  const limits = {
    free: { points: 3, duration: 300, blockDuration: 300 },      // 免费：3次/5分钟
    pro: { points: 10, duration: 300, blockDuration: 180 },      // Pro：10次/5分钟
    enterprise: { points: 50, duration: 300, blockDuration: 60 }, // 企业：50次/5分钟
  };

  const config = limits[userPlan] || limits.free;

  return new RateLimiterRedis({
    storeClient: redisAdapter,
    keyPrefix: `video_gen_${userPlan}`,
    points: config.points,
    duration: config.duration,
    blockDuration: config.blockDuration,
  });
};

// 通用API限流器
const generalApiLimiter = new RateLimiterRedis({
  storeClient: redisAdapter,
  keyPrefix: 'api_general',
  points: 100,        // 100次
  duration: 60,       // 每分钟
  blockDuration: 60,  // 阻塞60秒
});

// 创建限流中间件
export const videoGenerationRateLimit = async (c: Context, next: Next) => {
  const user = c.get('user');
  if (!user) {
    throw new HTTPException(401, { message: 'Unauthorized' });
  }

  const limiter = createVideoLimiter(user.plan || 'free');

  try {
    const resRateLimiter = await limiter.consume(user.id);

    // 设置响应头显示限流信息
    c.header('X-RateLimit-Limit', String(limiter.points));
    c.header('X-RateLimit-Remaining', String(resRateLimiter.remainingPoints));
    c.header('X-RateLimit-Reset', String(new Date(Date.now() + resRateLimiter.msBeforeNext)));

    await next();
  } catch (rejRes) {
    const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;

    c.header('Retry-After', String(secs));
    c.header('X-RateLimit-Limit', String(limiter.points));
    c.header('X-RateLimit-Remaining', '0');
    c.header('X-RateLimit-Reset', String(new Date(Date.now() + rejRes.msBeforeNext)));

    throw new HTTPException(429, {
      message: `Rate limit exceeded. Try again in ${secs} seconds.`,
    });
  }
};

// 通用API限流中间件
export const generalApiRateLimit = async (c: Context, next: Next) => {
  const user = c.get('user');
  const key = user?.id || c.req.header('x-forwarded-for') || 'anonymous';

  try {
    await generalApiLimiter.consume(key);
    await next();
  } catch (rejRes) {
    const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;

    c.header('Retry-After', String(secs));
    throw new HTTPException(429, {
      message: `API rate limit exceeded. Try again in ${secs} seconds.`,
    });
  }
};
```

### 3. 修复图片上传问题

#### 3.1 修改图片处理逻辑

**修改**: `apps/web/modules/marketing/image-to-video/lib/job-api.ts`

```typescript
/**
 * 将表单数据转换为Job API请求格式
 */
export async function convertFormDataToJobRequest(formData: any): Promise<CreateJobRequest> {
  // 映射配置保持不变
  const aspectRatioMap: Record<string, string> = {
    "16:9": "ASPECT_16_9",
    "9:16": "ASPECT_9_16",
    "1:1": "ASPECT_1_1",
    "4:3": "ASPECT_4_3",
    "3:4": "ASPECT_3_4",
    "21:9": "ASPECT_21_9",
  };

  const styleMap: Record<string, string> = {
    "auto": "auto",
    "anime": "anime",
    "3d_animation": "animation_3d",
    "comic": "comic",
    "clay": "clay",
    "cyberpunk": "cyberpunk",
  };

  const motionRangeMap: Record<string, string> = {
    "auto": "auto",
    "small": "small",
    "medium": "medium",
    "large": "large",
  };

  // 🔧 直接使用已上传的图片URL，不再重复上传
  const imageUrl = formData.image?.accessURL || formData.imageUrl || "";
  const imageTailUrl = formData.imageTail?.accessURL || formData.imageTailUrl || "";

  // 🗑️ 删除 uploadImage 相关代码

  return {
    featureCode: "video_generation",
    type: "video",
    numOutputs: formData.outputNumber || 1,
    modelParam: {
      modelCode: formData.model?.code || formData.model?.value || "",
      prompt: formData.prompt || "",
      image: imageUrl,
      imageTail: imageTailUrl || undefined,
      negativePrompt: formData.negativePrompt || undefined,
      strength: formData.promptStrength ? formData.promptStrength / 100 : undefined,
      duration: formData.videoLength || 5,
      modeCode: formData.mode || "fast",
      resolution: formData.resolution || undefined,
      aspectRatio: aspectRatioMap[formData.aspectRatio] || "ASPECT_16_9",
      style: styleMap[formData.style] || "auto",
      motionRange: motionRangeMap[formData.motionRange] || "auto",
      seed: formData.seed || undefined,
      processType: "image" as const,
    },
    options: {
      enableMagicPrompt: false,
      enableTranslatePrompt: false,
      enableTranslateNegativePrompt: false,
      protectionMode: formData.copyProtection || false,
      published: formData.publicVisibility || false,
    },
  };
}

// 🗑️ 删除 uploadImage 函数 (不再需要)
```

### 4. 实现异步任务处理

#### 4.1 修改Job服务

**修改**: `packages/api/src/routes/jobs/lib/job-service.ts`

```typescript
import { db } from "@repo/database";
import { createId } from "@paralleldrive/cuid2";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { HTTPException } from "hono/http-exception";
import type { CreateJobRequest } from "../types";

dayjs.extend(utc);

export class JobService {
  /**
   * 创建新的任务
   */
  static async createJob(userId: string, request: CreateJobRequest) {
    const now = dayjs().utc().toDate();

    // 验证逻辑保持不变...
    const user = await db.user.findUnique({
      where: { id: userId },
      select: { id: true }
    });

    if (!user) {
      throw new HTTPException(404, { message: "User not found" });
    }

    // 验证功能代码、模型、定价等逻辑保持不变...
    const feature = await db.feature.findUnique({
      where: { code: request.featureCode },
      select: { id: true, code: true }
    });

    if (!feature) {
      throw new HTTPException(400, { message: "Invalid feature code" });
    }

    const model = await db.model.findUnique({
      where: { code: request.modelParam.modelCode },
      select: {
        id: true,
        code: true,
        apiProviderCode: true,
        isActive: true
      }
    });

    if (!model || !model.isActive) {
      throw new HTTPException(400, { message: "Invalid or inactive model" });
    }

    const pricingEntry = await db.pricingEntry.findFirst({
      where: {
        featureCode: request.featureCode,
        modelCode: request.modelParam.modelCode,
        modeCode: request.modelParam.modeCode,
        ...(request.modelParam.duration && { duration: request.modelParam.duration }),
        ...(request.modelParam.resolution && { resolution: request.modelParam.resolution }),
      },
      select: {
        id: true,
        code: true,
        credits: true
      }
    });

    if (!pricingEntry) {
      throw new HTTPException(400, { message: "No pricing found for this configuration" });
    }

    const apiProviderPrice = await db.apiProviderPrice.findFirst({
      where: {
        modelCode: request.modelParam.modelCode,
        modelType: request.type === "video" ? "video" : "image"
      },
      select: { id: true, pricePerUnit: true }
    });

    if (!apiProviderPrice) {
      throw new HTTPException(400, { message: "No API provider price found" });
    }

    const totalCredit = pricingEntry.credits * request.numOutputs;

    const creditUsage = await db.creditUsage.findFirst({
      where: { userId },
      select: { balance: true }
    });

    if (!creditUsage || creditUsage.balance < totalCredit) {
      throw new HTTPException(402, { message: "Insufficient credits" });
    }

    // 🔧 修改：创建任务记录，状态为 queued
    const result = await db.$transaction(async (tx) => {
      const jobId = createId();
      const job = await tx.job.create({
        data: {
          id: jobId,
          userId,
          featureCode: request.featureCode,
          type: request.type,
          numOutputs: request.numOutputs,
          credit: totalCredit,
          status: "queued", // 🔧 改为 queued
          createdAt: now,
          updatedAt: now,
        }
      });

      // 创建任务参数记录...
      if (request.type === "video") {
        await tx.videoJobParam.create({
          data: {
            jobId,
            userId,
            modelId: model.id,
            modelCode: model.code,
            apiProviderCode: model.apiProviderCode,
            prompt: request.modelParam.prompt,
            image: request.modelParam.image,
            imageTail: request.modelParam.imageTail,
            negativePrompt: request.modelParam.negativePrompt,
            strength: request.modelParam.strength,
            duration: request.modelParam.duration,
            modeCode: request.modelParam.modeCode,
            resolution: request.modelParam.resolution,
            aspectRatio: request.modelParam.aspectRatio,
            style: request.modelParam.style,
            motionRange: request.modelParam.motionRange,
            seed: request.modelParam.seed,
            cameraType: request.modelParam.cameraType,
            cameraConfig: request.modelParam.cameraConfig,
            cameraFixed: request.modelParam.cameraFixed,
            video: request.modelParam.video,
            processType: request.modelParam.processType,
            enableMagicPrompt: request.options?.enableMagicPrompt ?? false,
            enableTranslatePrompt: request.options?.enableTranslatePrompt ?? false,
            enableTranslateNegativePrompt: request.options?.enableTranslateNegativePrompt ?? false,
            protectionMode: request.options?.protectionMode ?? false,
            published: request.options?.published ?? false,
            pricingEntryCode: pricingEntry.code,
            apiProviderPriceId: apiProviderPrice.id,
            createdAt: now,
            updatedAt: now,
          }
        });
      }

      // 创建Generation占位记录...
      for (let i = 0; i < request.numOutputs; i++) {
        await tx.generation.create({
          data: {
            id: createId(),
            userId,
            jobId,
            mediaId: createId(),
            mediaType: request.type,
            status: "waiting",
            createdAt: now,
            updatedAt: now,
          }
        });
      }

      // 扣除积分...
      await tx.creditUsage.update({
        where: { userId },
        data: {
          balance: { decrement: totalCredit },
          used: { increment: totalCredit },
          updatedAt: now,
        }
      });

      return job;
    });

    // ✅ 异步处理任务 - 不等待结果
    processJobAsync(result.id).catch(error => {
      console.error(`Failed to process job ${result.id}:`, error);
    });

    const estimatedTime = this.calculateEstimatedTime(request.modelParam.modelCode, request.numOutputs);

    return {
      jobId: result.id,
      status: "queued" as const,
      credit: totalCredit,
      estimatedTime,
      numOutputs: request.numOutputs,
    };
  }

  // 其他方法保持不变...
  static async getJobDetails(jobId: string, userId?: string) {
    // 保持原有逻辑不变
    const job = await db.job.findUnique({
      where: { id: jobId },
      include: {
        videoJobParam: true,
        imageJobParam: true,
        generations: {
          orderBy: { createdAt: "asc" },
          select: {
            id: true,
            mediaId: true,
            cover: true,
            thumbnail: true,
            videoUrl: true,
            mediaUrl: true,
            status: true,
            mediaType: true,
            duration: true,
            createdAt: true,
            updatedAt: true,
          }
        }
      }
    });

    if (!job) {
      throw new HTTPException(404, { message: "Job not found" });
    }

    if (userId && job.userId !== userId) {
      throw new HTTPException(403, { message: "Access denied" });
    }

    const completedCount = job.generations.filter(g => g.status === "succeed").length;
    const progress = {
      completed: completedCount,
      total: job.numOutputs,
      percentage: Math.round((completedCount / job.numOutputs) * 100)
    };

    const params = job.type === "video"
      ? job.videoJobParam
      : job.imageJobParam;

    return {
      job: {
        id: job.id,
        userId: job.userId,
        featureCode: job.featureCode,
        type: job.type,
        numOutputs: job.numOutputs,
        status: job.status,
        credit: job.credit,
        apiProviderCost: job.apiProviderCost ? Number(job.apiProviderCost) : undefined,
        timeCostSeconds: job.timeCostSeconds,
        createdAt: job.createdAt.toISOString(),
        updatedAt: job.updatedAt.toISOString(),
      },
      params: params ? {
        ...params,
        createdAt: params.createdAt.toISOString(),
        updatedAt: params.updatedAt.toISOString(),
      } : null,
      generations: job.generations.map(g => ({
        ...g,
        createdAt: g.createdAt.toISOString(),
        updatedAt: g.updatedAt.toISOString(),
      })),
      progress,
    };
  }

  private static calculateEstimatedTime(modelCode: string, numOutputs: number): number {
    const baseTimeMap: Record<string, number> = {
      "runway-gen2": 120,
      "runway-gen3": 180,
      "luma-dream-machine": 150,
    };

    const baseTime = baseTimeMap[modelCode] || 120;
    return baseTime * numOutputs;
  }
}

// ✅ 新增：异步任务处理函数
async function processJobAsync(jobId: string) {
  try {
    console.log(`🚀 Starting to process job: ${jobId}`);

    // 1. 获取任务详情
    const job = await db.job.findUnique({
      where: { id: jobId },
      include: {
        videoJobParam: true,
        imageJobParam: true,
      }
    });

    if (!job) {
      console.error(`❌ Job not found: ${jobId}`);
      return;
    }

    // 2. 更新状态为处理中
    await db.job.update({
      where: { id: jobId },
      data: {
        status: "processing",
        updatedAt: new Date(),
      }
    });

    // 3. 准备API参数
    const jobParams = job.type === "video" ? job.videoJobParam : job.imageJobParam;
    if (!jobParams) {
      throw new Error("Job parameters not found");
    }

    // 4. 调用API提供商
    const apiResponse = await callVideoProviderAPI({
      ...jobParams,
      webhook_url: `${process.env.WEBHOOK_BASE_URL}/api/webhooks/video`
    });

    // 5. 保存外部任务ID
    await db.job.update({
      where: { id: jobId },
      data: {
        externalTaskId: apiResponse.task_id,
        status: "processing",
        updatedAt: new Date(),
      }
    });

    console.log(`✅ Job ${jobId} submitted to provider with task_id: ${apiResponse.task_id}`);

  } catch (error) {
    console.error(`❌ Error processing job ${jobId}:`, error);

    // 标记任务失败
    await db.job.update({
      where: { id: jobId },
      data: {
        status: "failed",
        updatedAt: new Date(),
      }
    });

    // 返还积分
    await refundCredits(jobId);
  }
}

// ✅ 新增：调用视频提供商API
async function callVideoProviderAPI(jobParams: any) {
  const response = await fetch('https://api.runway.ml/v1/generate', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${process.env.VIDEO_API_KEY}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      prompt: jobParams.prompt,
      image: jobParams.image,
      duration: jobParams.duration,
      aspect_ratio: jobParams.aspectRatio,
      style: jobParams.style,
      motion_range: jobParams.motionRange,
      webhook_url: jobParams.webhook_url,
      // 根据具体API提供商调整参数格式
    })
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`API Provider Error: ${response.status} - ${errorText}`);
  }

  const result = await response.json();
  return result;
}

// ✅ 新增：返还积分函数
async function refundCredits(jobId: string) {
  try {
    const job = await db.job.findUnique({
      where: { id: jobId },
      select: { userId: true, credit: true }
    });

    if (job) {
      await db.creditUsage.update({
        where: { userId: job.userId },
        data: {
          balance: { increment: job.credit },
          used: { decrement: job.credit },
          updatedAt: new Date(),
        }
      });
    }
  } catch (error) {
    console.error(`Failed to refund credits for job ${jobId}:`, error);
  }
}
```

### 5. 创建Webhook处理端点

#### 5.1 创建Webhook路由

**新建**: `packages/api/src/routes/webhooks/router.ts`

```typescript
import { Hono } from "hono";
import { db } from "@repo/database";

export const webhooksRouter = new Hono()
  .basePath("/webhooks")

  // POST /api/webhooks/video
  .post("/video", async (c) => {
    try {
      const payload = await c.req.json();
      console.log('📥 Received webhook:', payload);

      const { task_id, status, result_url, error_message } = payload;

      if (!task_id) {
        return c.json({ error: "Missing task_id" }, 400);
      }

      // 根据external_task_id找到任务
      const job = await db.job.findFirst({
        where: { externalTaskId: task_id },
        select: { id: true, userId: true, numOutputs: true }
      });

      if (!job) {
        console.error(`❌ Job not found for task_id: ${task_id}`);
        return c.json({ error: "Job not found" }, 404);
      }

      // 更新任务状态
      const jobStatus = status === "completed" ? "succeed" : "failed";
      await db.job.update({
        where: { id: job.id },
        data: {
          status: jobStatus,
          updatedAt: new Date(),
          ...(status === "failed" && { errorMessage: error_message })
        }
      });

      // 更新generation记录
      if (status === "completed" && result_url) {
        await db.generation.updateMany({
          where: { jobId: job.id },
          data: {
            status: "succeed",
            videoUrl: result_url,
            mediaUrl: result_url,
            updatedAt: new Date()
          }
        });
      } else if (status === "failed") {
        await db.generation.updateMany({
          where: { jobId: job.id },
          data: {
            status: "failed",
            updatedAt: new Date()
          }
        });
      }

      console.log(`✅ Job ${job.id} updated to ${jobStatus}`);

      return c.json({
        success: true,
        message: `Job ${job.id} updated successfully`
      });

    } catch (error) {
      console.error("❌ Webhook processing error:", error);
      return c.json({
        error: "Internal server error",
        details: error.message
      }, 500);
    }
  });
```

#### 5.2 集成到主应用

**修改**: `packages/api/src/app.ts`

```typescript
import { Hono } from "hono";
import { jobsRouter } from "./routes/jobs/router";
import { webhooksRouter } from "./routes/webhooks/router";
import { generalApiRateLimit } from "./middleware/rate-limiter";

const app = new Hono()
  .basePath("/api")

  // 通用限流 (可选)
  .use(generalApiRateLimit)

  // 路由
  .route("/jobs", jobsRouter)
  .route("/webhooks", webhooksRouter); // ✅ 新增

export default app;
```

### 6. 应用限流到Jobs路由

#### 6.1 修改Jobs路由

**修改**: `packages/api/src/routes/jobs/router.ts`

```typescript
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { HTTPException } from "hono/http-exception";
import { authMiddleware } from "../../middleware/auth";
import { videoGenerationRateLimit } from "../../middleware/rate-limiter";
import { JobService } from "./lib/job-service";
import {
  CreateJobRequestSchema,
  CreateJobResponseSchema,
  GetJobResponseSchema,
} from "./types";

export const jobsRouter = new Hono()
  .basePath("/jobs")
  .use(authMiddleware)

  // POST /api/jobs - 创建视频生成任务
  .post(
    "/",
    videoGenerationRateLimit, // ✅ 添加限流中间件
    describeRoute({
      tags: ["Jobs"],
      summary: "Create video generation job",
      description: "Create a new video or image generation job",
      responses: {
        200: {
          description: "Job created successfully",
          content: {
            "application/json": {
              schema: CreateJobResponseSchema,
            },
          },
        },
        400: {
          description: "Bad request - Invalid parameters",
        },
        402: {
          description: "Payment required - Insufficient credits",
        },
        404: {
          description: "Not found - Invalid feature or model",
        },
        429: {
          description: "Rate limit exceeded",
        },
      },
    }),
    validator("json", CreateJobRequestSchema),
    async (c) => {
      const user = c.get("user");
      const request = c.req.valid("json");

      try {
        const result = await JobService.createJob(user.id, request);

        return c.json({
          success: true,
          data: result,
        });
      } catch (error) {
        console.error("Error creating job:", error);

        if (error instanceof HTTPException) {
          throw error;
        }

        throw new HTTPException(500, {
          message: "Failed to create job",
        });
      }
    }
  )

  // GET /api/jobs/:id - 获取任务详情 (不需要严格限流)
  .get(
    "/:id",
    describeRoute({
      tags: ["Jobs"],
      summary: "Get job details",
      description: "Get details of a specific job including progress and results",
      responses: {
        200: {
          description: "Job details",
          content: {
            "application/json": {
              schema: GetJobResponseSchema,
            },
          },
        },
        403: {
          description: "Forbidden - Access denied",
        },
        404: {
          description: "Not found - Job not found",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      const jobId = c.req.param("id");

      try {
        const result = await JobService.getJobDetails(jobId, user.id);

        return c.json({
          success: true,
          data: result,
        });
      } catch (error) {
        console.error("Error getting job details:", error);

        if (error instanceof HTTPException) {
          throw error;
        }

        throw new HTTPException(500, {
          message: "Failed to get job details",
        });
      }
    }
  );
```

### 7. 前端错误处理优化

#### 7.1 优化错误处理和用户提示

**修改**: `apps/web/modules/marketing/image-to-video/components/VideoGenerationContainer.tsx`

```typescript
// 在 handleCreateTask 函数中增强错误处理
const handleCreateTask = async (formData: any) => {
  setIsProcessing(true);

  try {
    const jobRequest = await convertFormDataToJobRequest(formData);
    const response = await createVideoJob(jobRequest);

    if (response.success && response.data) {
      const newTask = {
        id: `task-${Date.now()}`,
        jobId: response.data.jobId,
        prompt: formData.prompt,
        model: formData.model?.name || "Unknown Model",
        status: "queued", // ✅ 改为 queued
        progress: 0,
        timestamp: new Date().toISOString(),
        estimatedTime: response.data.estimatedTime,
        credit: response.data.credit,
        numOutputs: response.data.numOutputs,
        generations: [],
      };

      setTasks((prev) => [newTask, ...prev]);

      toast.success("任务创建成功", {
        description: `已消耗 ${response.data.credit} 积分，预计等待时间 ${Math.round(response.data.estimatedTime / 60)} 分钟`,
      });

      pollJobStatus(response.data.jobId);
    } else {
      setIsProcessing(false);

      // ✅ 增强错误处理
      const errorMessage = response.error || "未知错误";

      if (errorMessage.includes('Rate limit exceeded')) {
        toast.error("请求过于频繁", {
          description: "请稍后再试，或考虑升级账户获得更高限额",
        });
      } else if (errorMessage.includes('Insufficient credits')) {
        toast.error("积分不足", {
          description: "请充值积分后重试",
        });
      } else if (errorMessage.includes('Invalid')) {
        toast.error("参数错误", {
          description: "请检查您的输入参数",
        });
      } else {
        toast.error("任务创建失败", {
          description: errorMessage,
        });
      }
    }
  } catch (error) {
    console.error("Error creating video job:", error);
    setIsProcessing(false);

    // ✅ 网络错误处理
    if (error.name === 'AbortError') {
      toast.error("请求超时", {
        description: "网络连接超时，请重试",
      });
    } else {
      toast.error("任务创建失败", {
        description: error instanceof Error ? error.message : "网络错误",
      });
    }
  }
};
```

#### 7.2 添加前端防抖保护

**修改**: `apps/web/modules/marketing/image-to-video/components/VideoGenerationForm.tsx`

```typescript
import { useCallback, useState } from "react";
import { debounce } from "lodash-es";

export function VideoGenerationForm({ onSubmit, isProcessing }: Props) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // ✅ 防抖提交函数
  const debouncedSubmit = useCallback(
    debounce(async (formData: any) => {
      setIsSubmitting(true);
      try {
        await onSubmit(formData);
      } finally {
        setIsSubmitting(false);
      }
    }, 2000), // 2秒防抖
    [onSubmit]
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!image || isProcessing || isSubmitting) return;

    const formData = {
      prompt,
      image,
      imageTail,
      model,
      aspectRatio,
      videoLength,
      style,
      motionRange,
      promptStrength,
      negativePrompt,
      seed,
      outputNumber,
      copyProtection,
      publicVisibility,
      mode,
      resolution,
      endFrame,
    };

    await debouncedSubmit(formData);
  };

  // ✅ 按钮状态管理
  const isDisabled = !image || isProcessing || isSubmitting;
  const buttonText = isSubmitting ? "提交中..." : isProcessing ? "处理中..." : t("form.generateWithAI");

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 表单内容保持不变 */}

      <Button
        type="submit"
        disabled={isDisabled}
        className="w-full"
      >
        {buttonText}
      </Button>
    </form>
  );
}
```

## 📋 完整实施步骤

### 第一阶段：基础设施准备 (1-2天)

1. **设置 Upstash Redis**
   ```bash
   # 1. 注册 Upstash 账户
   # 2. 创建 Redis 数据库
   # 3. 获取 URL 和 Token
   ```

2. **安装依赖**
   ```bash
   pnpm add rate-limiter-flexible @upstash/redis lodash-es
   pnpm add -D @types/lodash-es
   ```

3. **配置环境变量**
   ```env
   UPSTASH_REDIS_URL=https://your-redis.upstash.io
   UPSTASH_REDIS_TOKEN=your-token
   WEBHOOK_BASE_URL=https://your-app.vercel.app
   VIDEO_API_KEY=your-video-api-key
   ```

### 第二阶段：核心功能实现 (2-3天)

1. **创建Redis连接** (`packages/api/src/lib/redis.ts`)
2. **实现限流中间件** (`packages/api/src/middleware/rate-limiter.ts`)
3. **修复图片上传逻辑** (`apps/web/modules/marketing/image-to-video/lib/job-api.ts`)
4. **实现异步任务处理** (`packages/api/src/routes/jobs/lib/job-service.ts`)
5. **创建Webhook端点** (`packages/api/src/routes/webhooks/router.ts`)

### 第三阶段：集成和优化 (1-2天)

1. **应用限流到路由** (`packages/api/src/routes/jobs/router.ts`)
2. **前端错误处理优化** (`VideoGenerationContainer.tsx`)
3. **前端防抖保护** (`VideoGenerationForm.tsx`)
4. **测试和调试**

### 第四阶段：监控和部署 (1天)

1. **添加日志监控**
2. **部署到生产环境**
3. **监控限流效果**
4. **根据实际情况调整限流参数**

## 🎯 预期效果

### 性能优化
- ✅ 消除图片重复上传，减少延迟
- ✅ 异步任务处理，立即响应用户
- ✅ 专业限流保护，防止系统过载

### 用户体验
- ✅ 更快的响应速度
- ✅ 清晰的限流提示
- ✅ 更好的错误处理

### 系统稳定性
- ✅ 分布式限流，支持多实例
- ✅ 积分保护机制
- ✅ 完善的错误恢复

### 可扩展性
- ✅ 基于用户等级的分级限流
- ✅ 易于调整的限流参数
- ✅ 监控和分析能力

这个方案完全基于 Upstash Redis 的 serverless 友好架构，适合在 Vercel 或 Cloudflare 等平台部署，既解决了当前问题，又为未来扩展奠定了基础。