# Video Detail View 组件复刻方案

## 概述

基于竞争对手Pollo.ai的视频详情页面，设计并实现一个完整的视频详情展示组件。该组件采用左右分栏布局，左侧为视频播放器，右侧为视频信息和操作区域。

## 设计分析

### 整体布局结构
```
┌─────────────────────────────────────────────────────────────┐
│                    VideoDetailView                          │
├──────────────────────────────┬──────────────────────────────┤
│                              │        Header Area           │
│                              ├──────────────────────────────┤
│         Video Player         │                              │
│      (SimpleVideoPlayer)     │       Video Info Area        │
│                              │      (VideoInfoPanel)        │
│                              │                              │
│                              ├──────────────────────────────┤
│                              │     Action Buttons Area      │
│                              │   (VideoActionsPanel)        │
└──────────────────────────────┴──────────────────────────────┘
```

### 核心组件分解

1. **VideoDetailView** - 主容器组件
2. **SimpleVideoPlayer** - 左侧视频播放器
3. **VideoInfoPanel** - 右侧视频信息面板
4. **VideoActionsPanel** - 右侧功能按钮面板

## 技术规格

### 技术栈选择
- **框架**: React 19 + TypeScript
- **样式**: Tailwind CSS
- **视频播放器**: Video.js (已安装v8.23.3)
- **UI组件**: Shadcn/ui
- **图标**: 自定义图标库 (基于现有的i-cus前缀系统)

### 响应式设计
- **桌面端**: 左右分栏布局 (lg:flex-row)
- **移动端**: 上下堆叠布局 (flex-col)
- **断点**: 使用Tailwind的lg断点 (1024px)

## 详细实施方案

### 1. 主组件 VideoDetailView

#### 接口设计
```typescript
interface VideoDetailViewProps {
  video: VideoData
  onRegenerate?: () => void
  onUpscale?: () => void
  onVideoToVideo?: () => void
  onCanvas?: () => void
  onAddSoundEffects?: () => void
  onLipSync?: () => void
  onLike?: (liked: boolean) => void
  onShare?: () => void
  onCreateSimilar?: () => void
}

interface VideoData {
  id: string
  title: string
  videoUrl: string
  posterUrl: string
  duration: number
  author: UserProfile
  createdAt: string
  status: 'published' | 'unpublished'
  type: 'image-to-video' | 'text-to-video'
  originalImage?: string
  prompt: string
  model: string
  resolution: string  
  seed: string
  outputDimension: string
  project: string
  likes: number
  isLiked: boolean
}
```

#### 组件结构
```tsx
export function VideoDetailView({ video, ...handlers }: VideoDetailViewProps) {
  return (
    <div className="bg-card relative p-0 lg:rounded-lg lg:p-6">
      <div className="relative flex flex-col lg:flex-row lg:gap-6 lg:rounded-lg">
        {/* 左侧视频播放器 */}
        <div className="flex-1">
          <SimpleVideoPlayer 
            videoUrl={video.videoUrl}
            posterUrl={video.posterUrl}
            duration={video.duration}
          />
        </div>
        
        {/* 右侧信息面板 */}
        <div className="flex flex-col justify-between gap-y-3 p-6 lg:h-[80vh] lg:w-[408px] lg:p-0">
          <VideoInfoPanel video={video} />
          <VideoActionsPanel 
            video={video}
            onRegenerate={handlers.onRegenerate}
            onUpscale={handlers.onUpscale}
            // ... 其他handlers
          />
        </div>
      </div>
    </div>
  )
}
```

### 2. SimpleVideoPlayer 组件

#### 功能要求
- 支持视频播放、暂停、进度控制
- 音量控制、全屏功能
- 倍速播放 (0.5x, 1x, 1.5x, 2x)
- 响应式尺寸适配
- 背景模糊效果

#### 技术实现
```tsx
'use client'

import { useEffect, useRef } from 'react'
import videojs from 'video.js'
import 'video.js/dist/video-js.css'

interface SimpleVideoPlayerProps {
  videoUrl: string
  posterUrl: string
  duration: number
  autoPlay?: boolean
  muted?: boolean
  loop?: boolean
  onReady?: (player: videojs.Player) => void
}

export function SimpleVideoPlayer({ 
  videoUrl, 
  posterUrl, 
  duration,
  autoPlay = false,
  muted = true,
  loop = true,
  onReady
}: SimpleVideoPlayerProps) {
  const videoRef = useRef<HTMLDivElement>(null)
  const playerRef = useRef<videojs.Player | null>(null)

  useEffect(() => {
    // 确保 Video.js 播放器只初始化一次
    if (!playerRef.current) {
      const videoElement = document.createElement('video-js')
      
      videoElement.classList.add('vjs-big-play-centered')
      videoRef.current?.appendChild(videoElement)

      const player = videojs(videoElement, {
        autoplay: autoPlay,
        controls: true,
        responsive: true,
        fluid: true,
        muted: muted,
        loop: loop,
        poster: posterUrl,
        preload: 'metadata',
        playbackRates: [0.5, 1, 1.5, 2],
        sources: [{
          src: videoUrl,
          type: 'video/mp4'
        }]
      }, () => {
        onReady?.(player)
      })

      playerRef.current = player
    }

    return () => {
      const player = playerRef.current
      if (player && !player.isDisposed()) {
        player.dispose()
        playerRef.current = null
      }
    }
  }, [videoUrl, posterUrl, autoPlay, muted, loop, onReady])

  return (
    <div className="bg-muted relative size-[100vw] w-full lg:h-[80vh] lg:flex-1">
      <div className="relative size-full">
        {/* 背景模糊层 */}
        <div className="absolute inset-0 rounded-2xl bg-cover bg-center bg-no-repeat" 
             style={{ backgroundImage: `url(${posterUrl})` }} />
        <div className="absolute inset-0 backdrop-blur-lg" />
        
        {/* Video.js 播放器容器 */}
        <div className="flex size-full flex-col items-center justify-center hover:cursor-pointer [&_.video-js]:size-full">
          <div className="relative aspect-video w-full flex-1 transition-shadow hover:shadow-lg">
            <div 
              ref={videoRef} 
              className="video-js vjs-default-skin !bg-transparent vjs-big-play-button vjs-fill"
              data-vjs-player
            />
          </div>
        </div>
        
        {/* 收藏按钮 */}
        <FavoriteButton className="absolute right-2 top-2 lg:right-2" />
      </div>
    </div>
  )
}
```

### 3. VideoInfoPanel 组件

#### 功能要求
- 显示作者信息和创建时间
- 显示视频类型和状态标签
- 显示原始图片预览
- 显示prompt和生成参数
- 支持复制prompt功能

#### 实现结构
```tsx
interface VideoInfoPanelProps {
  video: VideoData
}

export function VideoInfoPanel({ video }: VideoInfoPanelProps) {
  return (
    <div className="flex max-h-fit min-h-0 flex-1 flex-col gap-y-3">
      {/* 头部信息 */}
      <VideoHeader video={video} />
      
      {/* 内容区域 */}
      <div className="scrollbar bg-background flex-1 overflow-y-auto rounded-md p-3">
        {/* 标签区域 */}
        <VideoLabels type={video.type} status={video.status} />
        
        {/* 原始图片 */}
        {video.originalImage && (
          <OriginalImageSection imageUrl={video.originalImage} />
        )}
        
        {/* Prompt区域 */}
        <PromptSection prompt={video.prompt} />
        
        {/* 参数信息 */}
        <VideoParameters video={video} />
      </div>
    </div>
  )
}
```

### 4. VideoActionsPanel 组件

#### 功能要求
- 6个主要操作按钮 (2x3网格布局)
- 底部交互区域 (点赞、分享、更多操作)
- 主要CTA按钮 (Create Similar Video)

#### 实现结构
```tsx
interface VideoActionsPanelProps {
  video: VideoData
  onRegenerate?: () => void
  onUpscale?: () => void
  onVideoToVideo?: () => void
  onCanvas?: () => void
  onAddSoundEffects?: () => void
  onLipSync?: () => void
  onLike?: (liked: boolean) => void
  onShare?: () => void
  onCreateSimilar?: () => void
}

export function VideoActionsPanel({ video, ...handlers }: VideoActionsPanelProps) {
  const actionButtons = [
    { icon: 'regenerate', label: 'Regenerate', action: handlers.onRegenerate },
    { icon: 'upscale', label: 'Upscale', action: handlers.onUpscale },
    { icon: 'video-to-video', label: 'Video to Video', action: handlers.onVideoToVideo },
    { icon: 'canvas', label: 'Canvas', action: handlers.onCanvas },
    { icon: 'sound', label: 'Add Sound Effects', action: handlers.onAddSoundEffects },
    { icon: 'lip-sync', label: 'Lip Sync', action: handlers.onLipSync },
  ]

  return (
    <div className="flex flex-col gap-y-5">
      {/* 功能按钮网格 */}
      <div className="grid grid-cols-2 gap-2">
        {actionButtons.map((button) => (
          <ActionButton
            key={button.label}
            icon={button.icon}
            label={button.label}
            onClick={button.action}
          />
        ))}
      </div>
      
      {/* 底部交互区域 */}
      <VideoInteractionBar 
        likes={video.likes}
        isLiked={video.isLiked}
        onLike={handlers.onLike}
        onShare={handlers.onShare}
        onCreateSimilar={handlers.onCreateSimilar}
      />
    </div>
  )
}
```

## 实施步骤

### 第一阶段：基础架构搭建
1. 创建主组件文件结构
2. 定义TypeScript接口
3. 搭建基础布局框架
4. 集成Tailwind样式系统

### 第二阶段：核心功能实现
1. 实现SimpleVideoPlayer组件
   - 集成React Player或Video.js
   - 添加自定义控制器
   - 实现响应式设计
2. 实现VideoInfoPanel组件
   - 构建信息展示区域
   - 添加复制功能
   - 优化滚动体验

### 第三阶段：交互功能完善
1. 实现VideoActionsPanel组件
   - 创建操作按钮组件
   - 实现点赞、分享功能
   - 添加主CTA按钮
2. 添加状态管理和事件处理

### 第四阶段：样式优化和测试
1. 细化样式匹配
2. 响应式适配测试
3. 交互体验优化
4. 性能优化

## 样式规范

### 颜色系统
```css
/* 基于现有的f-前缀系统适配到Tailwind */
.bg-f-bg-container → bg-card
.bg-f-bg-layout → bg-background  
.text-f-text-tertiary → text-muted-foreground
.text-f-text-secondary → text-foreground/80
.bg-f-bg-hover → hover:bg-accent
```

### 组件样式规范
- 圆角统一使用 `rounded-lg`
- 间距采用 `gap-3`, `gap-6` 等
- 阴影使用 `shadow-lg`
- 过渡效果使用 `transition-all`

## 文件结构

```
components/video-detail/
├── VideoDetailView.tsx          # 主组件
├── SimpleVideoPlayer.tsx        # 视频播放器
├── VideoInfoPanel/
│   ├── index.tsx               # 信息面板主组件
│   ├── VideoHeader.tsx         # 头部信息
│   ├── VideoLabels.tsx         # 标签组件
│   ├── OriginalImageSection.tsx # 原始图片区域
│   ├── PromptSection.tsx       # Prompt区域
│   └── VideoParameters.tsx     # 参数信息
├── VideoActionsPanel/
│   ├── index.tsx               # 操作面板主组件
│   ├── ActionButton.tsx        # 操作按钮
│   └── VideoInteractionBar.tsx # 交互栏
├── shared/
│   ├── FavoriteButton.tsx      # 收藏按钮
│   └── types.ts                # 类型定义
└── index.ts                    # 导出文件
```

## 总结

该复刻方案完整涵盖了竞争对手产品的核心功能和设计特点，采用现代React开发最佳实践，确保代码的可维护性和扩展性。通过模块化设计，各个组件职责明确，便于后续功能迭代和优化。