# 视频生成功能真实API集成方案

## 概述

本文档详细说明从用户点击Generate按钮到视频生成完成的整个流程，以及将现有Mock数据替换为真实API调用的具体改造方案。

## 当前系统架构

### 1. 前端流程

```
用户操作 → VideoGenerationForm → handleCreateTask → createVideoJob API → 轮询状态更新
```

### 2. 后端流程

```
API接收请求 → JobService创建任务 → 异步调用Provider → Webhook接收回调 → 更新数据库 → 前端轮询获取最新状态
```

## 详细流程分析

### 1. 用户点击Generate按钮

**位置**: `apps/web/modules/marketing/image-to-video/components/VideoGenerationForm.tsx:329`

用户填写表单并点击Generate按钮后，触发`handleSubmit`函数，调用父组件的`onSubmit`回调。

### 2. 创建任务请求

**位置**: `apps/web/modules/marketing/image-to-video/components/VideoGenerationContainer.tsx:97-220`

`handleCreateTask`函数执行以下步骤：

1. **立即显示creating状态** - 创建临时任务，提升用户体验
2. **转换表单数据** - 调用`convertFormDataToJobRequest`转换为API格式
3. **调用API创建任务** - 调用`createVideoJob`发送请求
4. **更新任务状态** - 用API返回的真实数据替换临时任务
5. **开始轮询** - 调用`pollJobStatus`定期查询任务状态

### 3. API处理流程

**位置**: `packages/api/src/routes/jobs/router.ts` & `packages/api/src/routes/jobs/lib/job-service.ts`

#### 3.1 创建任务 (POST /api/jobs)

```typescript
JobService.createJob(userId, request)
├── 验证请求参数
├── 计算积分消耗
├── 创建Job记录
├── 创建Generation记录（状态：waiting）
├── 异步调用Provider API
└── 返回任务信息
```

#### 3.2 异步处理 (processJobOutputsAsync)

```typescript
processJobOutputsAsync(jobId, jobData)
├── 更新任务状态为processing
├── 为每个Generation调用Provider API
│   ├── 获取Provider实例（Replicate/Fal）
│   ├── 准备参数（包含metadata）
│   ├── 调用generateVideo
│   └── 更新Generation的externalTaskId
└── 处理失败时返还积分
```

### 4. Webhook处理流程

**位置**: `packages/api/src/routes/webhooks/router.ts`

#### 4.1 接收Webhook

```typescript
handleProviderWebhook(provider, handler)
├── 验证签名
├── 提取metadata（generationId, userId）
├── 存储到VideoWebhookEvent表
└── 立即返回200（异步处理）
```

#### 4.2 异步处理Webhook

```typescript
VideoWebhookProcessor.processEvent(event)
├── 查找对应Generation记录
├── 更新Generation状态
├── 如果成功，下载并上传视频到CDN
└── 更新Job总体状态
```

### 5. 前端轮询更新

**位置**: `apps/web/modules/marketing/image-to-video/components/VideoGenerationContainer.tsx:39-94`

```typescript
pollJobStatus(jobId)
├── 每2秒调用getJobDetails API
├── 更新任务和Generation状态
├── 更新进度百分比
└── 任务完成后停止轮询
```

## Mock数据移除方案

### 1. 前端改造

#### 1.1 移除Mock逻辑

**文件**: `apps/web/modules/shared/lib/jobs-api.ts`

```diff
export async function createVideoJob(request: CreateJobRequest): Promise<CreateJobResponse> {
-  // 🧪 Mock返回数据用于测试页面逻辑
-  console.log("📡 Using MOCK response for testing...");
-  
-  // 模拟网络延迟
-  await new Promise(resolve => setTimeout(resolve, 1000));
-  
-  const jobId = `job_${Date.now()}`;
-  // ... mock数据生成逻辑
-  
-  return mockResponse;
  
-  /* 原始代码，暂时注释
+  // 真实API调用
  try {
    console.log("📡 Sending POST request to /api/jobs...");
    const response = await apiClient.jobs.$post({
      json: request,
    });
    console.log("📥 Received response:", response);

    // 直接返回响应数据，响应格式已经符合CreateJobResponse
    console.log("✅ Job created successfully:", response);
    return response as CreateJobResponse;
  } catch (error) {
    console.error("💥 Error creating video job:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
-  */
}

export async function getJobDetails(jobId: string): Promise<GetJobResponse> {
-  // 🧪 Mock返回数据用于测试页面逻辑
-  // ... mock逻辑
-  
-  /* 原始代码，暂时注释
+  // 真实API调用
  try {
    const response = await apiClient.jobs.details[":id"].$get({
      param: {
        id: jobId,
      },
    });

    // 直接返回响应数据，响应格式已经符合GetJobResponse
    console.log("✅ Job details retrieved:", response);
    return response as GetJobResponse;
  } catch (error) {
    console.error("Error getting job details:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
-  */
}
```

### 2. 后端改造

#### 2.1 启用限流中间件

**文件**: `packages/api/src/routes/jobs/router.ts`

```diff
.post(
  "/",
  authMiddleware, // 认证中间件
-  // videoGenerationRateLimit, // 🔥 临时禁用限流中间件用于开发测试
+  videoGenerationRateLimit, // 限流中间件
  // ...
)
```

#### 2.2 启用用户积分检查

**文件**: `packages/api/src/routes/jobs/lib/job-service.ts`

```diff
// 5. 检查用户积分（暂时注释掉）
-// const creditUsage = await db.creditUsage.findFirst({
-//   where: { userId },
-//   select: { balance: true }
-// });
-
-// if (!creditUsage || creditUsage.balance < totalCredit) {
-//   throw new HTTPException(402, { message: "Insufficient credits" });
-// }
+const creditUsage = await db.creditUsage.findFirst({
+  where: { userId },
+  select: { balance: true }
+});
+
+if (!creditUsage || creditUsage.balance < totalCredit) {
+  return JobResponseBuilder.insufficientCredits(totalCredit, creditUsage?.balance || 0);
+}
```

#### 2.3 启用Webhook处理器

**文件**: `packages/api/src/app.ts`

```diff
// 🎛️ 智能启动后台 webhook 处理器
// 在Vercel环境中，依赖Job轮询触发；在其他环境中使用定时器
-// 暂时注释掉定时器
-/*
if (process.env.WEBHOOK_PROCESSOR_ENABLED !== 'false') {
  if (process.env.VERCEL === '1') {
    console.log('🚀 [App] Running on Vercel: Webhook processing via Job polling triggers');
    console.log('ℹ️  [App] Set WEBHOOK_TRIGGER_ON_JOB_POLL=false to disable job polling triggers');
  } else {
    console.log('🚀 [App] Starting background webhook processor with timer');
    startGlobalWebhookProcessor();
  }
} else {
  console.log('⏸️  [App] Webhook processor disabled via WEBHOOK_PROCESSOR_ENABLED=false');
}
-*/
```

### 3. 环境变量配置

确保以下环境变量已正确配置：

```env
# API Provider配置
REPLICATE_API_TOKEN=your_replicate_token
REPLICATE_WEBHOOK_SIGNING_KEY=your_webhook_key
FAL_API_KEY=your_fal_key

# Webhook配置
WEBHOOK_PROCESSOR_ENABLED=true
WEBHOOK_TRIGGER_ON_JOB_POLL=true  # Vercel环境建议开启

# CDN配置
R2_ACCESS_KEY_ID=your_r2_access_key
R2_SECRET_ACCESS_KEY=your_r2_secret_key
R2_ENDPOINT=your_r2_endpoint
R2_BUCKET_NAME=your_bucket_name
```

### 4. 数据库准备

确保数据库已执行最新迁移：

```bash
pnpm db:push
```

需要的表：
- `Job` - 存储任务信息
- `Generation` - 存储每个生成的视频信息
- `VideoWebhookEvent` - 存储webhook事件
- `CreditUsage` - 用户积分信息

## 测试计划

### 1. 单元测试

- [ ] API请求格式转换测试
- [ ] 积分计算逻辑测试
- [ ] Webhook签名验证测试

### 2. 集成测试

- [ ] 完整的视频生成流程测试
- [ ] 积分扣除和返还测试
- [ ] 多输出视频生成测试
- [ ] 失败重试机制测试

### 3. 端到端测试

- [ ] 用户完整操作流程
- [ ] 不同模型和参数组合
- [ ] 错误处理和用户提示
- [ ] 并发请求处理

## 监控和日志

### 1. 关键指标

- 任务创建成功率
- 平均处理时间
- Webhook处理延迟
- 失败率和重试次数

### 2. 日志位置

- API请求日志：`[JobService]`前缀
- Webhook处理：`[VideoWebhookProcessor]`前缀
- Provider调用：`[Provider]`前缀

## 回滚方案

如果集成出现问题，可以快速回滚：

1. 前端：恢复Mock数据逻辑
2. 后端：注释掉真实API调用
3. 环境变量：设置`WEBHOOK_PROCESSOR_ENABLED=false`

## 时间线

1. **第一阶段**：移除Mock，启用真实API（1天）
2. **第二阶段**：测试和问题修复（2天）
3. **第三阶段**：性能优化和监控（1天）
4. **第四阶段**：生产环境部署（1天）

## 风险和注意事项

1. **API限流**：确保Provider API限额充足
2. **积分系统**：确保积分扣除和返还逻辑正确
3. **并发处理**：注意数据库事务和锁
4. **错误处理**：完善的错误提示和降级方案
5. **Webhook安全**：验证签名，防止恶意请求

## 附录：关键代码位置

- 前端表单：`apps/web/modules/marketing/image-to-video/components/VideoGenerationForm.tsx`
- 前端容器：`apps/web/modules/marketing/image-to-video/components/VideoGenerationContainer.tsx`
- API客户端：`apps/web/modules/shared/lib/jobs-api.ts`
- API路由：`packages/api/src/routes/jobs/router.ts`
- 业务逻辑：`packages/api/src/routes/jobs/lib/job-service.ts`
- Webhook路由：`packages/api/src/routes/webhooks/router.ts`
- Webhook处理器：`packages/api/src/lib/services/video-webhook-processor.ts`
- 数据库Schema：`packages/database/prisma/schema.prisma`