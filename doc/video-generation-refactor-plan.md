# 视频生成流程重构方案

## 📋 需求背景

当前视频生成流程存在以下问题：
1. 用户点击生成按钮后没有立即反馈
2. 状态切换不够流畅，用户体验不佳
3. API响应结构过于简单，缺少必要信息
4. 业务逻辑同步阻塞，等待时间过长

## 🎯 重构目标

### 新的流程设计
1. 点击"Generate"时，立即显示MediaPreviewCard组件（creating状态）
2. POST /api/jobs响应后，显示waiting状态
3. 每2秒轮询任务状态，根据返回结果切换UI显示
4. 任务完成后显示生成的视频

### 状态流转图
```
Creating (loading图标) → Waiting (红色波形) → Processing (进度条) → Succeeded (视频播放)
```

## 🔧 详细设计方案

### 1. API响应结构重构

#### 新的CreateJobResponse接口定义
```typescript
export interface CreateJobResponse {
  success: boolean;
  data?: {
    user: {
      id: string;
      name: string;
      username: string;
      image: string;
    };
    job: {
      id: string;
      userId: string;
      featureCode: string;
      type: "video" | "image";
      numOutputs: number;
      status: "waiting" | "processing" | "succeeded" | "failed";
      credit: number;
      apiProviderCost: number;
      timeCostSeconds: number;
      modelCode: string;
      prompt: string;
      image: string;
      imageTail: string;
      negativePrompt: string;
      promptStrength: number;
      duration: number;
      modeCode: string;
      resolution: string;
      aspectRatio: string;
      style: string;
      motionRange: string;
      seed: number;
      processType: string;
      createdAt: string;
      updatedAt: string;
    };
    generations: Array<{
      id: string;
      mediaId: string;
      cover: string;
      thumbnail: string;
      videoUrl: string;
      mediaUrl: string;
      status: "waiting" | "processing" | "succeeded" | "failed";
      mediaType: "video" | "image";
      duration: number;
      createdAt: string;
      updatedAt: string;
    }>;
    progress: {
      completed: number;
      total: number;
    };
  };
  message: string;
  timestamp: string;
}
```

### 2. 前端组件重构

#### 2.1 新增CreatingTaskOverlay组件
创建一个新的覆盖层组件，用于显示"Generating..."状态：

```typescript
// CreatingTaskOverlay.tsx
export function CreatingTaskOverlay() {
  return (
    <div className="absolute inset-0 z-10 bg-slate-800/90 backdrop-blur-sm flex items-center justify-center">
      <div className="text-center text-white">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
        <p className="text-lg font-medium">Generating...</p>
      </div>
    </div>
  );
}
```

#### 2.2 MediaPreviewCard状态扩展
支持5种状态：creating、waiting、processing、succeeded、failed

```typescript
interface Generation {
  id: string;
  status: "creating" | "waiting" | "processing" | "succeeded" | "failed";
  inputImage?: string;
  outputVideo?: string;
  error?: string;
  createdAt: string;
  estimatedDuration?: number;
}
```

状态与UI组件映射：
- `creating` → `CreatingTaskOverlay` (Generating... loading图标)
- `waiting` → `SubmittingTaskOverlay` (Submitting your task... 红色波形)
- `processing` → `ProgressOverlay` (图片背景+进度条，最高99%)
- `succeeded` → `Video` (视频播放器)
- `failed` → `ErrorOverlay` (错误提示)

#### 2.3 VideoGenerationContainer流程重构

```typescript
const handleCreateTask = async (formData: any) => {
  // 第1步：立即显示creating状态
  const creatingTask = {
    id: `creating-${Date.now()}`,
    status: "creating",
    inputImage: formData.imageFile,
    prompt: formData.prompt,
    timestamp: new Date().toISOString()
  };
  setTasks([creatingTask, ...tasks]);

  try {
    // 第2步：调用API创建任务
    const response = await createVideoJob(formData);
    
    if (response.success && response.data) {
      // 第3步：使用API返回的数据替换creating状态
      const { job, generations } = response.data;
      const waitingTask = {
        id: job.id,
        jobId: job.id,
        status: "waiting",
        inputImage: job.image,
        prompt: job.prompt,
        generations: generations,
        estimatedTime: 60,
        timestamp: job.createdAt
      };
      
      // 替换临时任务
      setTasks(prev => prev.map(t => 
        t.id === creatingTask.id ? waitingTask : t
      ));

      // 第4步：开始轮询任务状态
      startPolling(job.id);
    }
  } catch (error) {
    // 错误处理：移除creating任务
    setTasks(prev => prev.filter(t => t.id !== creatingTask.id));
    toast.error("创建任务失败");
  }
};
```

### 3. 业务逻辑重构

#### 3.1 后端处理流程优化

当前流程（同步）：
```
用户请求 → 调用Replicate API → 等待响应 → 插入数据库 → 返回结果
```

新流程（异步）：
```
用户请求 → 立即插入数据库(waiting状态) → 返回结果 → 异步调用Replicate API
```

#### 3.2 数据库事务设计

```typescript
// POST /api/jobs 业务逻辑
export async function createJob(request: CreateJobRequest) {
  const transaction = await db.transaction();
  
  try {
    // 1. 立即创建job和generation记录
    const job = await transaction.job.create({
      data: {
        ...request,
        status: 'waiting',
        createdAt: new Date()
      }
    });
    
    const generation = await transaction.generation.create({
      data: {
        jobId: job.id,
        status: 'waiting',
        createdAt: new Date()
      }
    });
    
    await transaction.commit();
    
    // 2. 构建完整响应
    const response = {
      success: true,
      data: {
        user: await getCurrentUser(),
        job: job,
        generations: [generation],
        progress: { completed: 0, total: 1 }
      },
      message: "Job created successfully",
      timestamp: new Date().toISOString()
    };
    
    // 3. 异步处理Replicate调用
    processReplicateAsync(job.id).catch(console.error);
    
    return response;
    
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}

// 异步处理函数
async function processReplicateAsync(jobId: string) {
  try {
    // 调用Replicate API
    const prediction = await replicate.predictions.create({...});
    
    // 更新状态为processing
    await db.job.update({
      where: { id: jobId },
      data: { status: 'processing' }
    });
    
    await db.generation.update({
      where: { jobId: jobId },
      data: { status: 'processing' }
    });
    
  } catch (error) {
    // 更新为失败状态
    await db.job.update({
      where: { id: jobId },
      data: { status: 'failed' }
    });
  }
}
```

### 4. 轮询机制优化

```typescript
const pollJobStatus = useCallback(async (jobId: string) => {
  const pollInterval = setInterval(async () => {
    try {
      const response = await getJobDetails(jobId);
      
      if (response.success && response.data) {
        const { job, generations, progress } = response.data;
        const generation = generations[0];
        
        // 更新任务状态
        setTasks(prev => prev.map(task => {
          if (task.jobId === jobId) {
            return {
              ...task,
              status: generation.status,
              progress: progress.percentage,
              outputVideo: generation.videoUrl,
              error: generation.status === 'failed' ? 'Generation failed' : undefined
            };
          }
          return task;
        }));
        
        // 任务完成或失败时停止轮询
        if (generation.status === 'succeeded' || generation.status === 'failed') {
          clearInterval(pollInterval);
          
          if (generation.status === 'succeeded') {
            toast.success("视频生成完成！");
          } else {
            toast.error("视频生成失败");
          }
        }
      }
    } catch (error) {
      console.error("轮询出错:", error);
    }
  }, 2000); // 每2秒轮询一次
  
  // 设置最大轮询时间（10分钟）
  setTimeout(() => {
    clearInterval(pollInterval);
  }, 10 * 60 * 1000);
  
  return pollInterval;
}, []);
```

## 🚀 实施计划

### Phase 1：前端改造（立即实施）
1. 创建CreatingTaskOverlay组件
2. 修改MediaPreviewCard支持5种状态
3. 更新VideoGenerationContainer流程
4. 实现新的Mock数据结构

### Phase 2：API接口升级（本周内）
1. 定义新的API响应接口
2. 更新Mock实现以匹配新结构
3. 测试完整的状态切换流程

### Phase 3：后端优化（后续迭代）
1. 实现数据库事务处理
2. 异步化Replicate API调用
3. 优化错误处理和重试机制

## ⚠️ 风险评估

### 低风险
- 前端组件重构，向后兼容
- Mock数据结构调整

### 中风险
- API接口结构变更，需要前后端协调
- 状态管理逻辑重构，需要充分测试

### 高风险
- 后端业务逻辑异步化，涉及数据一致性
- 生产环境部署需要灰度发布

## 📊 预期效果

1. **用户体验提升**
   - 点击后立即有视觉反馈
   - 状态切换流畅自然
   - 进度展示更加准确

2. **性能优化**
   - API响应时间从3-5秒降至100ms以内
   - 减少前端等待时间
   - 提高系统并发处理能力

3. **可维护性增强**
   - 状态管理更加清晰
   - 组件职责单一
   - 易于扩展新功能

## 🔍 测试要点

1. **功能测试**
   - 各状态UI显示正确性
   - 状态切换流畅性
   - 错误处理完整性

2. **性能测试**
   - API响应时间
   - 轮询稳定性
   - 内存泄漏检查

3. **兼容性测试**
   - 不同浏览器表现
   - 移动端适配
   - 网络异常处理

## 📝 总结

这次重构将从根本上改善视频生成功能的用户体验，通过异步化处理和优化的状态管理，用户将获得更流畅、更直观的交互体验。建议按照实施计划分阶段推进，确保每个阶段都经过充分测试后再进入下一阶段。