# 视频生成系统架构图

## 1. 整体系统架构

```mermaid
graph TB
    subgraph "前端层"
        U[用户浏览器]
        F[Next.js前端应用]
        CDN[VideoCDN]
    end

    subgraph "API层"
        AG[API网关]
        RL[限流中间件]
        API[Hono API服务]
    end

    subgraph "缓存层"
        Redis[(Redis/Upstash)]
    end

    subgraph "数据层"
        DB[(PostgreSQL)]
    end

    subgraph "提供商层"
        PM[提供商管理器]
        FAL[Fal.ai]
        REP[Replicate]
        MJ[Midjourney<br/>未来扩展]
        SD[StabilityAI<br/>未来扩展]
    end

    subgraph "处理层"
        WH[Webhook处理器]
        JQ[任务队列]
    end

    U --> F
    F --> CDN
    F --> AG
    AG --> RL
    RL --> API
    API --> Redis
    API --> DB
    API --> PM
    PM --> FAL
    PM --> REP
    PM -.-> MJ
    PM -.-> SD
    FAL --> WH
    REP --> WH
    WH --> API
    API --> JQ
```

## 2. 视频生成完整流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant CDN as CDN
    participant API as API服务
    participant R as Redis
    participant DB as 数据库
    participant PM as 提供商管理器
    participant P as API提供商
    participant WH as Webhook

    U->>F: 1. 选择图片
    F->>CDN: 2. 上传图片
    CDN-->>F: 3. 返回URL
    U->>F: 4. 设置参数
    U->>F: 5. 点击生成
    F->>API: 6. POST /jobs
    
    rect rgb(200, 230, 255)
        Note over API,R: 限流检查
        API->>R: 7. 检查限流
        R-->>API: 8. 通过/拒绝
    end
    
    API->>DB: 9. 创建Job(queued)
    API->>PM: 10. 获取提供商
    PM->>P: 11. 调用生成API
    P-->>PM: 12. 返回taskId
    PM-->>API: 13. 返回结果
    API->>DB: 14. 更新Job(processing)
    API-->>F: 15. 返回jobId
    
    rect rgb(255, 230, 200)
        Note over P,WH: 异步处理
        P->>WH: 16. 完成回调
        WH->>DB: 17. 更新状态
        WH->>DB: 18. 保存结果
    end
    
    loop 轮询状态
        F->>API: 19. GET /jobs/:id
        API->>DB: 20. 查询状态
        API-->>F: 21. 返回状态
    end
    
    F->>U: 22. 显示结果
```

## 3. 提供商管理架构

```mermaid
classDiagram
    class BaseProvider {
        <<abstract>>
        +name: string
        +supportedModels: string[]
        +generateContent()
        +parseWebhook()
        +checkStatus()
    }
    
    class VideoProvider {
        <<abstract>>
        +generateVideo()
    }
    
    class ImageProvider {
        <<abstract>>
        +generateImage()
    }
    
    class FalProvider {
        +name: "fal"
        +supportedModels: string[]
        +generateVideo()
        +parseWebhook()
        +checkStatus()
    }
    
    class ReplicateProvider {
        +name: "replicate"
        +supportedModels: string[]
        +generateVideo()
        +parseWebhook()
        +checkStatus()
    }
    
    class ProviderManager {
        -providers: Map
        -modelToProvider: Map
        +getProvider(model)
        +registerProvider()
        +healthCheck()
    }
    
    BaseProvider <|-- VideoProvider
    BaseProvider <|-- ImageProvider
    VideoProvider <|-- FalProvider
    VideoProvider <|-- ReplicateProvider
    ProviderManager o-- BaseProvider
```

## 4. 限流策略

```mermaid
graph LR
    subgraph "用户请求"
        U1[免费用户]
        U2[Pro用户]
        U3[企业用户]
    end
    
    subgraph "限流规则"
        R1[3次/5分钟]
        R2[15次/5分钟]
        R3[60次/5分钟]
    end
    
    subgraph "Redis存储"
        K1[rl:free:userId]
        K2[rl:pro:userId]
        K3[rl:enterprise:userId]
    end
    
    U1 --> R1
    U2 --> R2
    U3 --> R3
    
    R1 --> K1
    R2 --> K2
    R3 --> K3
    
    K1 --> D{检查}
    K2 --> D
    K3 --> D
    
    D -->|通过| API[继续处理]
    D -->|超限| E[返回429]
```

## 5. 数据库关系图

```mermaid
erDiagram
    User ||--o{ Job : creates
    Job ||--o| VideoJobParam : has
    Job ||--o{ Generation : produces
    
    User {
        string id PK
        string email
        string name
        string tier
    }
    
    Job {
        string id PK
        string userId FK
        string featureCode
        enum type
        enum status
        int credit
        string externalTaskId
        string apiProviderCode
        string errorMessage
        datetime createdAt
        datetime updatedAt
    }
    
    VideoJobParam {
        string jobId PK,FK
        string modelCode
        string prompt
        string imageUrl
        string imageTailUrl
        int duration
        string aspectRatio
    }
    
    Generation {
        string id PK
        string jobId FK
        string generationUrl
        json metadata
        datetime createdAt
    }
```

## 6. 状态转换图

```mermaid
stateDiagram-v2
    [*] --> Queued: 创建任务
    Queued --> Processing: 调用API
    Processing --> Succeeded: 生成成功
    Processing --> Failed: 生成失败
    Failed --> [*]: 结束
    Succeeded --> [*]: 结束
    
    Processing --> Processing: 处理中
    Failed --> Queued: 重试
```

## 7. Webhook处理流程

```mermaid
flowchart TD
    A[接收Webhook] --> B{识别提供商}
    B -->|Fal.ai| C[Fal解析器]
    B -->|Replicate| D[Replicate解析器]
    B -->|未知| E[错误处理]
    
    C --> F[提取数据]
    D --> F
    
    F --> G{检查状态}
    G -->|完成| H[更新Job为成功]
    G -->|失败| I[更新Job为失败]
    G -->|处理中| J[忽略]
    
    H --> K[创建Generation记录]
    I --> L[记录错误信息]
    I --> M[退还用户额度]
    
    K --> N[计算耗时]
    L --> N
    N --> O[返回成功]
    
    E --> P[返回错误]
```

## 8. 错误处理流程

```mermaid
flowchart TD
    A[用户操作] --> B{错误类型}
    
    B -->|输入错误| C[参数验证失败]
    B -->|限流错误| D[超过请求限制]
    B -->|额度错误| E[额度不足]
    B -->|API错误| F[提供商调用失败]
    B -->|系统错误| G[内部错误]
    
    C --> H[返回400<br/>提示修正输入]
    D --> I[返回429<br/>显示重试时间]
    E --> J[返回402<br/>引导充值]
    F --> K[返回503<br/>尝试其他提供商]
    G --> L[返回500<br/>通用错误信息]
    
    K --> M{有备用提供商?}
    M -->|是| N[自动切换]
    M -->|否| O[任务失败]
    
    N --> P[重新处理]
    O --> Q[退还额度]
```

## 9. 缓存策略

```mermaid
graph TD
    A[请求] --> B{缓存检查}
    B -->|命中| C[返回缓存]
    B -->|未命中| D[查询数据库]
    
    D --> E[更新缓存]
    E --> F[返回数据]
    
    subgraph "缓存内容"
        G[用户配额]
        H[Job状态]
        I[提供商健康状态]
        J[模型配置]
    end
    
    subgraph "缓存时间"
        K[配额: 60s]
        L[状态: 10s]
        M[健康: 300s]
        N[配置: 3600s]
    end
    
    G -.-> K
    H -.-> L
    I -.-> M
    J -.-> N
```

## 10. 多租户隔离

```mermaid
graph TB
    subgraph "组织A"
        U1[用户1]
        U2[用户2]
        J1[任务池A]
        Q1[配额A]
    end
    
    subgraph "组织B"
        U3[用户3]
        U4[用户4]
        J2[任务池B]
        Q2[配额B]
    end
    
    subgraph "共享资源"
        API[API服务]
        PM[提供商池]
        Cache[缓存层]
    end
    
    U1 --> J1
    U2 --> J1
    U3 --> J2
    U4 --> J2
    
    J1 --> API
    J2 --> API
    
    API --> PM
    API --> Cache
    
    Q1 -.->|隔离| Q2
    J1 -.->|隔离| J2
```