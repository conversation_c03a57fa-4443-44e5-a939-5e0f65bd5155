# 🧪 Vitest配置方案 - 全面测试架构

基于项目分析，我为你设计了一个分阶段的Vitest配置方案：

## 📊 测试策略（3个层次）

### 1. 单元测试 (Unit Tests)
- **工具函数** - `/packages/utils/`, `/packages/storage/`
- **服务层** - `/packages/credits/`, `/packages/database/queries/`
- **配置验证** - `/config/` (已有测试基础)
- **React Hooks** - `/apps/web/modules/*/hooks/`

### 2. 集成测试 (Integration Tests)
- **API路由** - `/packages/api/src/routes/`
- **数据库查询** - Prisma queries with test DB
- **认证流程** - Better Auth集成
- **支付处理** - Payment providers

### 3. E2E测试 (现有Playwright)
- **用户流程** - 注册、登录、视频生成
- **跨浏览器兼容性**

## ⚙️ 配置结构

### 根级配置
```
vitest.config.ts              # 工作区配置
vitest.shared.ts              # 共享测试配置
```

### 包级配置
```
packages/*/vitest.config.ts   # 包特定配置
apps/web/vitest.config.ts     # Web应用配置
```

### 测试工具
```
tests/
├── setup/
│   ├── global-setup.ts      # 全局设置
│   ├── test-db.ts          # 测试数据库
│   └── mocks/              # 共享mock
├── utils/
│   ├── test-helpers.ts     # 测试助手
│   ├── factories.ts        # 数据工厂
│   └── custom-matchers.ts  # 自定义匹配器
└── fixtures/               # 测试数据
```

## 📦 依赖清单

### 核心测试框架
```json
{
  "vitest": "^2.1.8",
  "@vitest/ui": "^2.1.8",
  "happy-dom": "^15.11.6"
}
```

### React测试
```json
{
  "@testing-library/react": "^16.1.0",
  "@testing-library/jest-dom": "^6.6.3",
  "@testing-library/user-event": "^14.5.2"
}
```

### API & 数据库测试
```json
{
  "supertest": "^7.0.0",
  "prisma-test-environment": "^0.4.0",
  "node-mocks-http": "^1.16.1"
}
```

### Mock & 工具
```json
{
  "msw": "^2.6.4",
  "faker": "^6.6.6",
  "@types/supertest": "^6.0.2"
}
```

## 🔧 集成方案

### 1. Turbo集成
```json
// turbo.json
{
  "tasks": {
    "test": {
      "dependsOn": ["build"],
      "outputs": ["coverage/**"]
    },
    "test:unit": {
      "cache": false
    }
  }
}
```

### 2. TypeScript支持
- 路径别名 (`@repo/*`) 完全支持
- 类型检查集成到测试流程
- 与现有tsconfig.json兼容

### 3. Next.js集成
- Server Components测试
- App Router支持
- 中间件测试

## 📂 文件组织结构

```
project/
├── packages/
│   ├── utils/
│   │   ├── lib/
│   │   │   ├── base-url.ts
│   │   │   └── base-url.test.ts
│   │   └── vitest.config.ts
│   ├── database/
│   │   ├── prisma/queries/
│   │   │   ├── users.ts
│   │   │   └── users.test.ts
│   │   └── vitest.config.ts
│   └── api/
│       ├── src/routes/
│       │   ├── credits/
│       │   │   ├── router.ts
│       │   │   └── router.test.ts
│       │   └── __tests__/
│       └── vitest.config.ts
├── apps/web/
│   ├── modules/
│   │   ├── shared/hooks/
│   │   │   ├── form-errors.ts
│   │   │   └── form-errors.test.tsx
│   │   └── marketing/
│   │       └── components/
│   │           ├── VideoCard.tsx
│   │           └── VideoCard.test.tsx
│   └── vitest.config.ts
├── config/
│   ├── sample-video/
│   │   ├── manager.ts
│   │   └── manager.test.ts        # 已存在
│   └── vitest.config.ts
└── tests/                        # 共享测试工具
    ├── setup/
    ├── utils/
    └── fixtures/
```

## 🚀 实施阶段

### 阶段1：基础设施 (1-2天)
1. 配置根级Vitest
2. 设置测试工具和helpers
3. 配置覆盖率报告
4. 集成Turbo缓存

### 阶段2：核心功能 (3-5天)
1. 测试工具函数 (`/packages/utils/`)
2. 测试配置管理 (`/config/`)
3. 测试核心服务 (`/packages/credits/`, `/packages/database/`)
4. 设置数据库测试环境

### 阶段3：组件和API (5-7天)
1. React组件测试
2. API路由测试
3. 认证流程测试
4. 集成测试

### 阶段4：优化 (2-3天)
1. 性能优化
2. 并行测试
3. CI/CD集成
4. 测试覆盖率目标

## 📈 预期收益

- **开发速度** - 快速反馈，减少调试时间
- **代码质量** - 自动化质量检查
- **重构信心** - 安全的代码重构
- **文档价值** - 测试即文档
- **团队协作** - 统一的测试标准

---

这个方案充分考虑了你的monorepo结构，与现有工具链完美集成，并提供了渐进式的实施路径。需要我开始实施吗？