# Webhook 回调流程完整文档

## 概述

本文档详细描述了从接收 webhook 回调请求到整个视频生成流程完成的完整调用链路。该系统支持多个 AI 视频生成提供商（Replicate、FAL 等），具备完整的安全验证、错误处理和积分管理功能。

## 整体架构图

```mermaid
graph TB
    A[AI Provider<br/>Replicate/FAL] -->|Webhook Callback| B[webhooks/router.ts<br/>handleProviderWebhook]
    B --> C[handlers/replicate.ts<br/>ReplicateWebhookHandler.handle]
    C --> D[shared/processor.ts<br/>processWebhookPayload]
    D --> E{Status Check}
    E -->|succeed| F[handleGenerationCompletion]
    E -->|failed| G[handleGenerationFailure] 
    E -->|processing| H[updateGenerationProgress]
    F --> I[VideoUploadService<br/>uploadFromUrl]
    I --> J[JobsManager<br/>update Generation]
    F --> J
    G --> J
    H --> J
    J --> K[JobStatusService<br/>checkAndUpdateJobStatus]
    K --> L[Credit Refund<br/>handleCreditRefund]
    K --> M[Redis Cache<br/>clearJobCache]
    M --> N[Response<br/>Success/Error]
```

## 详细流程分析

### 第一阶段：Webhook 接收与路由

#### 1. Webhook Router (`packages/api/src/routes/webhooks/router.ts`)

**入口函数：`handleProviderWebhook`**
- **位置**：`router.ts:20-54`
- **职责**：通用 webhook 请求处理器
- **调用链**：

```typescript
// 1. 接收 webhook 请求
POST /webhooks/replicate → handleProviderWebhook(c, "replicate", replicateHandler)

// 2. 解析请求体
const body = await c.req.json() // router.ts:28

// 3. 构造 WebhookRequest 对象
const webhookRequest: WebhookRequest = {
  headers: Object.fromEntries([...c.req.raw.headers.entries()]),
  body,
  url: c.req.url
} // router.ts:32-36

// 4. 调用对应提供商处理器
const payload = await handler.handle(webhookRequest) // router.ts:39

// 5. 处理 webhook 载荷
const result = await processWebhookPayload(payload, provider) // router.ts:42
```

### 第二阶段：提供商特定处理

#### 2. Replicate Handler (`packages/api/src/routes/webhooks/handlers/replicate.ts`)

**核心类：`ReplicateWebhookHandler`**
- **继承**：`BaseWebhookHandler`
- **主要方法**：

```typescript
// 1. 签名验证
async validateSignature(req: WebhookRequest): Promise<boolean>
// 位置：replicate.ts:39-89
// 验证传统签名 + 安全 token 双重验证

// 2. 载荷解析
parsePayload(body: any): WebhookPayload 
// 位置：replicate.ts:91-105
// 提取任务ID、状态映射、结果URL、错误信息

// 3. 状态映射
private mapReplicateStatus(status: string)
// 位置：replicate.ts:114-124
// starting → waiting
// processing → processing  
// succeeded → succeed
// failed/canceled → failed
```

**安全验证流程**：
```typescript
// 1. 传统 HMAC-SHA256 签名验证
const signatureValid = await this.validator.validate(req, payload)

// 2. 自定义安全 token 验证
const verification = verifyWebhookToken({
  userId: tokenData.userId,
  taskId: tokenData.taskId, 
  timestamp: tokenData.timestamp,
  receivedToken: req.body.input.webhook_token,
  secretKey: webhookSecretKey,
  maxAgeMinutes: 20
})
```

### 第三阶段：核心业务逻辑处理

#### 3. Webhook Processor (`packages/api/src/routes/webhooks/shared/processor.ts`)

**主函数：`processWebhookPayload`**
- **位置**：`processor.ts:10-56`
- **调用流程**：

```typescript
// 1. 查找 Generation 记录
const generation = await JobsManager.findGenerationByExternalTaskId(
  payload.taskId, 
  true // includeJob
) // processor.ts:16-19

// 2. 计算处理时间
const processingTime = Math.round((Date.now() - generation.createdAt.getTime()) / 1000)

// 3. 状态分发处理
switch (payload.status) {
  case "succeed":
    await handleGenerationCompletion(generation, job, payload, processingTime)
    break;
  case "failed": 
    await handleGenerationFailure(generation, job, payload, processingTime)
    break;
  case "processing":
    await updateGenerationProgress(generation.id, payload)
    break;
}

// 4. 清除缓存
await redisUtils.clearJobCache(job.id) // processor.ts:45
```

### 第四阶段：具体状态处理

#### 4A. 成功处理 (`handleGenerationCompletion`)

**位置**：`processor.ts:61-152`
**关键步骤**：

```typescript
// 1. 初始化视频上传服务
const videoUploadService = new VideoUploadService()

// 2. 视频上传到 CDN（双策略）
try {
  // 优先：流式上传
  uploadResult = await videoUploadService.uploadFromUrlStreaming(
    payload.resultUrl, { userId, generationId }
  )
} catch (streamingError) {
  // 回退：临时文件上传  
  uploadResult = await videoUploadService.uploadFromUrl(
    payload.resultUrl, { userId, generationId }
  )
}

// 3. 数据库事务更新
await JobsManager.transaction(async (tx) => {
  // 3.1 更新 Generation 状态
  await tx.generation.update({
    where: { id: generation.id },
    data: {
      status: "succeed",
      videoUrl: cdnUrl,
      mediaUrl: cdnUrl,
      updatedAt: new Date()
    }
  })
  
  // 3.2 检查并更新 Job 状态
  const statusResult = await JobStatusService.checkAndUpdateJobStatus(
    job.id, processingTime, tx
  )
})
```

#### 4B. 失败处理 (`handleGenerationFailure`)

**位置**：`processor.ts:157-196`
**关键步骤**：

```typescript
await JobsManager.transaction(async (tx) => {
  // 1. 更新 Generation 为失败状态
  await tx.generation.update({
    where: { id: generation.id },
    data: {
      status: "failed",
      updatedAt: new Date()
    }
  })

  // 2. 检查并更新 Job 状态  
  const statusResult = await JobStatusService.checkAndUpdateJobStatus(
    job.id, processingTime, tx
  )

  // 3. 如果整个 Job 失败，更新错误信息
  if (statusResult.finalStatus === "failed") {
    await tx.job.update({
      where: { id: job.id },
      data: {
        errorMessage: payload.errorMessage,
        updatedAt: new Date()
      }
    })
  }
})
```

#### 4C. 进度更新 (`updateGenerationProgress`)

**位置**：`processor.ts:201-223`
**关键步骤**：

```typescript
// 使用重试机制更新进度
const result = await RetryService.execute(
  async () => {
    await JobsManager.updateGeneration(generationId, {
      status: "processing"
    })
  },
  RetryService.createDatabaseRetry(),
  `updateGenerationProgress-${generationId}`
)
```

### 第五阶段：Job 状态管理

#### 5. Job Status Service (`packages/api/src/lib/services/job-status.service.ts`)

**核心方法：`checkAndUpdateJobStatus`**
- **位置**：`job-status.service.ts:57-117`
- **调用流程**：

```typescript
// 1. 获取 Job 统计信息
const stats = await this.getJobStatusStats(jobId, tx) // job-status.service.ts:62

// 2. 检查是否所有 Generation 都完成
if (!stats.allCompleted) {
  return { updated: false }
}

// 3. 确定最终状态
const finalStatus = stats.successfulGenerations > 0 ? "succeed" : "failed"

// 4. 更新 Job 状态
await database.job.update({
  where: { id: jobId },
  data: {
    status: finalStatus,
    timeCostSeconds: processingTime,
    updatedAt: new Date()
  }
})

// 5. 处理积分返还
const creditRefund = await this.handleCreditRefund(job, stats, tx)
```

**积分返还逻辑 (`handleCreditRefund`)**
- **位置**：`job-status.service.ts:122-196`

```typescript
// 1. 计算返还金额
if (stats.successfulGenerations === 0) {
  // 全部失败，100% 返还
  refundAmount = job.credit
} else if (stats.failedGenerations > 0) {
  // 部分失败，按比例返还
  refundAmount = Math.floor(job.credit * (stats.failedGenerations / stats.totalGenerations))
}

// 2. 执行积分返还
await database.creditUsage.update({
  where: { userId: job.userId },
  data: {
    balance: { increment: refundAmount },
    used: { decrement: refundAmount },
    updatedAt: new Date()
  }
})
```

### 第六阶段：支持服务

#### 6. Video Upload Service

**主要上传策略**：

```typescript
// 1. uploadFromUrlStreaming (优先)
// packages/api/src/lib/services/video-upload.service.ts
// 适用：Edge Runtime，内存友好

// 2. uploadFromUrl (回退) 
// 使用临时文件，稳定性最高

// 3. uploadViaDirectStream (备选)
// 直接流式传输，适用于 Node.js Runtime
```

## 错误处理机制

### 1. 分层错误处理

```typescript
// 第一层：Router 级别
try {
  const result = await processWebhookPayload(payload, provider)
  return c.json(result)
} catch (error) {
  return c.json({ error: "Internal server error" }, 500)
}

// 第二层：Processor 级别  
if (!generation) {
  throw new Error(`Generation not found for task_id: ${payload.taskId}`)
}

// 第三层：Service 级别
const result = await RetryService.execute(
  async () => { /* 数据库操作 */ },
  retryOptions,
  `operation-${id}`
)
```

### 2. 重试机制

```typescript
// 数据库操作重试配置
RetryService.createDatabaseRetry() = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 5000,
  backoffFactor: 2
}
```

## 安全机制

### 1. 多重验证

```mermaid
graph LR
    A[Webhook Request] --> B[IP 白名单检查]
    B --> C[HMAC-SHA256 签名验证]
    C --> D[自定义 Token 验证]
    D --> E[时间戳有效性检查]
    E --> F[处理请求]
```

### 2. Token 生成与验证

```typescript
// Token 生成（发起请求时）
const token = generateWebhookToken({
  userId, taskId, timestamp, secretKey
})

// Token 验证（接收回调时）
const verification = verifyWebhookToken({
  userId, taskId, timestamp, receivedToken, secretKey,
  maxAgeMinutes: 20
})
```

## 性能优化

### 1. 数据库优化

- **事务使用**：确保数据一致性
- **批量查询**：`findGenerationByExternalTaskId` 包含 Job 信息
- **索引优化**：通过 `externalTaskId` 快速查找

### 2. 缓存机制

```typescript
// 缓存清理
await redisUtils.clearJobCache(job.id)

// 避免重复处理
// 通过状态检查防止重复更新
```

### 3. 文件上传优化

```typescript
// 智能上传策略选择
try {
  // 优先使用内存友好的流式上传
  result = await videoUploadService.uploadFromUrlStreaming()
} catch (error) {
  // 失败时回退到稳定的临时文件模式
  result = await videoUploadService.uploadFromUrl()
}
```

## 监控与日志

### 1. 关键日志点

```typescript
// 请求开始
console.log(`📥 [${provider}] Received webhook:`, body)

// 处理完成  
console.log(`✅ [${provider}] Webhook processed in ${processingTime}ms`)

// 状态更新
logger.info("[JobStatus] Job status updated", { jobId, finalStatus })

// 积分返还
logger.info("[JobStatus] Credit refunded", { userId, amount, reason })
```

### 2. 错误监控

```typescript
// 签名验证失败
console.error('[Replicate] Traditional signature validation failed')

// 上传失败
logger.error("[Webhook] Failed to upload video to CDN", { generationId, error })

// 积分返还失败
logger.error("[JobStatus] Credit refund failed", { jobId, error })
```

## 总结

这个 webhook 回调系统具备以下特点：

1. **模块化设计**：清晰的分层架构，职责分离
2. **多提供商支持**：统一的接口，易于扩展
3. **安全可靠**：多重验证机制，防止恶意请求
4. **高可用性**：完善的错误处理和重试机制
5. **数据一致性**：事务确保数据完整性
6. **积分管理**：自动化的积分返还机制
7. **性能优化**：智能上传策略和缓存机制
8. **可观测性**：详细的日志记录和监控

整个流程从 webhook 接收到最终完成，涉及 10+ 个核心文件和 20+ 个关键方法，形成了一个完整、可靠的视频生成回调处理系统。