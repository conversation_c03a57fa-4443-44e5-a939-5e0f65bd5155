# Webhook OpenAPI 模块重构设计文档

## 1. 重构背景

### 1.1 当前问题
- `createWebhookOpenAPIDesc` 函数放在 `router.ts` 中，违反单一职责原则
- OpenAPI 相关代码缺乏统一组织，难以维护和扩展
- 工具函数与路由逻辑耦合，无法独立测试

### 1.2 重构目标
- ✅ 创建模块内 OpenAPI 目录结构
- ✅ 提取 OpenAPI 相关工具函数
- ✅ 支持配置化的描述生成
- ✅ 保持模块内聚性
- ✅ 提高可测试性和可维护性

## 2. 设计方案

### 2.1 目标目录结构

```
packages/api/src/routes/webhooks/
├── openapi/                      # 新增 OpenAPI 模块
│   ├── index.ts                  # 统一导出接口
│   ├── descriptors.ts            # 描述生成器
│   ├── responses.ts              # 标准响应定义
│   └── types.ts                  # OpenAPI 相关类型定义
├── handlers/                     # 现有：webhook 处理器
├── shared/                       # 现有：共享工具
├── schemas.ts                    # 现有：数据 schema
└── router.ts                     # 现有：路由定义（将被简化）
```

### 2.2 核心设计理念

#### 2.2.1 模块内聚原则
- OpenAPI 相关代码保持在 webhooks 模块内
- 避免创建全局 openapi/ 目录
- 功能相关的代码保持就近原则

#### 2.2.2 配置化设计
- 使用工厂函数生成 OpenAPI 描述
- 支持灵活的配置和扩展
- 标准化常用的响应模式

#### 2.2.3 类型安全
- 完整的 TypeScript 类型定义
- 编译期错误检测
- 良好的 IDE 支持

## 3. 详细实现设计

### 3.1 类型定义 (`openapi/types.ts`)

```typescript
/**
 * Webhook 描述生成器配置
 */
export interface WebhookDescriptorConfig {
  /** 提供商名称 */
  provider: string;
  /** 请求体 schema */
  requestSchema: any;
  /** 自定义响应（可选） */
  customResponses?: Record<string, any>;
  /** 自定义描述（可选） */
  customDescription?: string;
  /** 额外的标签（可选） */
  additionalTags?: string[];
}

/**
 * 预定义的 Webhook 提供商类型
 */
export type WebhookProvider = 'replicate' | 'fal' | 'runway' | 'stable-diffusion';

/**
 * Webhook 描述生成器返回类型
 */
export interface WebhookOpenAPIDescriptor {
  summary: string;
  description: string;
  tags: string[];
  requestBody: {
    content: {
      "application/json": {
        schema: any;
      };
    };
  };
  responses: Record<string, any>;
}
```

### 3.2 标准响应定义 (`openapi/responses.ts`)

```typescript
import { openAPISchemas } from "../schemas";

/**
 * Webhook 端点的标准响应定义
 * 遵循 OpenAPI 3.0 规范
 */
export const webhookResponses = {
  /** 成功响应 */
  success: {
    description: "Webhook 处理成功",
    content: {
      "application/json": {
        schema: openAPISchemas.webhookSuccessResponse
      }
    }
  },

  /** 客户端错误响应 */
  badRequest: {
    description: "请求参数错误",
    content: {
      "application/json": {
        schema: openAPISchemas.webhookErrorResponse
      }
    }
  },

  /** 资源未找到响应 */
  notFound: {
    description: "任务未找到",
    content: {
      "application/json": {
        schema: openAPISchemas.webhookErrorResponse
      }
    }
  },

  /** 服务器错误响应 */
  serverError: {
    description: "服务器内部错误",
    content: {
      "application/json": {
        schema: openAPISchemas.webhookErrorResponse
      }
    }
  },

  /** 认证失败响应 */
  unauthorized: {
    description: "签名验证失败",
    content: {
      "application/json": {
        schema: openAPISchemas.webhookErrorResponse
      }
    }
  }
};

/**
 * 标准 Webhook 响应集合
 * 适用于大部分 webhook 端点
 */
export const standardWebhookResponses = {
  200: webhookResponses.success,
  400: webhookResponses.badRequest,
  401: webhookResponses.unauthorized,
  404: webhookResponses.notFound,
  500: webhookResponses.serverError
};

/**
 * 公开 Webhook 响应集合（无需认证）
 */
export const publicWebhookResponses = {
  200: webhookResponses.success,
  400: webhookResponses.badRequest,
  404: webhookResponses.notFound,
  500: webhookResponses.serverError
};
```

### 3.3 描述生成器 (`openapi/descriptors.ts`)

```typescript
import { standardWebhookResponses } from "./responses";
import { openAPISchemas } from "../schemas";
import type { WebhookDescriptorConfig, WebhookOpenAPIDescriptor, WebhookProvider } from "./types";

/**
 * 字符串首字母大写工具函数
 */
function capitalizeFirst(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * 通用 Webhook OpenAPI 描述生成器
 * 
 * @param config - 生成器配置
 * @returns OpenAPI 描述对象
 * 
 * @example
 * ```typescript
 * const desc = createWebhookDescriptor({
 *   provider: 'replicate',
 *   requestSchema: openAPISchemas.replicateWebhookBody
 * });
 * ```
 */
export function createWebhookDescriptor(config: WebhookDescriptorConfig): WebhookOpenAPIDescriptor {
  const {
    provider,
    requestSchema,
    customResponses = {},
    customDescription,
    additionalTags = []
  } = config;

  const providerName = capitalizeFirst(provider);
  
  return {
    summary: `${providerName} Webhook Handler`,
    description: customDescription || `处理来自 ${providerName} API 的视频生成完成通知`,
    tags: ["Webhooks", ...additionalTags],
    requestBody: {
      content: {
        "application/json": {
          schema: requestSchema
        }
      }
    },
    responses: {
      ...standardWebhookResponses,
      ...customResponses
    }
  };
}

/**
 * 预配置的 Webhook 描述生成器
 * 为常用提供商提供便捷的描述生成
 */
export const webhookDescriptors = {
  /**
   * Replicate webhook 描述
   */
  replicate: (): WebhookOpenAPIDescriptor => createWebhookDescriptor({
    provider: 'replicate',
    requestSchema: openAPISchemas.replicateWebhookBody,
    additionalTags: ['AI', 'Video Generation']
  }),

  /**
   * Fal webhook 描述
   */
  fal: (): WebhookOpenAPIDescriptor => createWebhookDescriptor({
    provider: 'fal',
    requestSchema: openAPISchemas.falWebhookBody,
    additionalTags: ['AI', 'Video Generation']
  }),

  /**
   * 通用 webhook 描述（用于向后兼容的 /video 端点）
   */
  generic: (): WebhookOpenAPIDescriptor => createWebhookDescriptor({
    provider: 'video',
    requestSchema: openAPISchemas.genericWebhookBody || openAPISchemas.replicateWebhookBody,
    customDescription: '通用视频生成 webhook 处理器，支持多种 AI 提供商',
    additionalTags: ['Legacy', 'Compatibility']
  })
};

/**
 * 动态创建新提供商的 webhook 描述
 * 
 * @param provider - 提供商名称
 * @param requestSchema - 请求体 schema
 * @returns webhook 描述生成函数
 * 
 * @example
 * ```typescript
 * const runwayDescriptor = createProviderDescriptor('runway', runwaySchema);
 * ```
 */
export function createProviderDescriptor(
  provider: WebhookProvider | string,
  requestSchema: any
) {
  return (): WebhookOpenAPIDescriptor => createWebhookDescriptor({
    provider,
    requestSchema,
    additionalTags: ['AI', 'Video Generation']
  });
}
```

### 3.4 统一导出接口 (`openapi/index.ts`)

```typescript
/**
 * Webhook OpenAPI 模块统一导出
 * 
 * 提供所有 OpenAPI 相关的工具函数、类型定义和预配置
 */

// 核心工具函数
export {
  createWebhookDescriptor,
  webhookDescriptors,
  createProviderDescriptor
} from "./descriptors";

// 响应定义
export {
  webhookResponses,
  standardWebhookResponses,
  publicWebhookResponses
} from "./responses";

// 类型定义
export type {
  WebhookDescriptorConfig,
  WebhookProvider,
  WebhookOpenAPIDescriptor
} from "./types";

// 便捷的重导出
export const { replicate, fal, generic } = webhookDescriptors;
```

### 3.5 更新后的路由定义 (`router.ts`)

```typescript
import { webhookHandler as paymentsWebhookHandler } from "@repo/payments";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { processWebhookPayload } from "./shared/processor";
import { createReplicateHandler } from "./handlers/replicate";
import { createFalHandler } from "./handlers/fal";
import { webhookDescriptors } from "./openapi";  // 新的导入
import type { WebhookRequest, BaseWebhookHandler } from "./shared/types";
import type { Context } from "hono";

// 创建 webhook 处理器实例
const replicateHandler = createReplicateHandler();
const falHandler = createFalHandler();

/**
 * 通用 webhook 端点处理器
 * 抽取重复的处理逻辑，减少代码重复
 */
async function handleProviderWebhook(
  c: Context,
  provider: string,
  handler: BaseWebhookHandler
): Promise<Response> {
  const startTime = Date.now();
  
  try {
    const body = await c.req.json();
    console.log(`📥 [${provider}] Received webhook:`, JSON.stringify(body, null, 2));
    
    // 构造请求对象
    const webhookRequest: WebhookRequest = {
      headers: Object.fromEntries([...c.req.raw.headers.entries()]),
      body,
      url: c.req.url
    };
    
    // 使用对应的处理器处理请求
    const payload = await handler.handle(webhookRequest);
    
    // 处理 webhook 载荷
    const result = await processWebhookPayload(payload, provider);
    
    console.log(`✅ [${provider}] Webhook processed in ${Date.now() - startTime}ms`);
    return c.json(result);
    
  } catch (error) {
    console.error(`❌ [${provider}] Webhook processing error:`, error);
    return c.json({ 
      error: "Internal server error",
      details: error instanceof Error ? error.message : String(error) 
    }, 500);
  }
}

export const webhooksRouter = new Hono()
  .basePath("/webhooks")
  
  // Payments webhook（保持不变）
  .post("/payments", 
    describeRoute({
      tags: ["Webhooks"],
      summary: "Handle payments webhook",
    }),
    (c) => paymentsWebhookHandler(c.req.raw)
  )
  
  // Replicate 端点（使用新的描述生成器）
  .post("/replicate", 
    describeRoute(webhookDescriptors.replicate()),
    (c) => handleProviderWebhook(c, "replicate", replicateHandler)
  )
  
  // Fal 端点（使用新的描述生成器）
  .post("/fal",
    describeRoute(webhookDescriptors.fal()),
    (c) => handleProviderWebhook(c, "fal", falHandler)
  )
  
  // 保留统一的 webhook 端点（向后兼容）
  .post("/video",
    describeRoute(webhookDescriptors.generic()),
    async (c) => {
      // 保持现有的处理逻辑不变
      // ... 现有代码
    }
  )
  
  // 其他端点保持不变...
```

## 4. 扩展性设计

### 4.1 添加新提供商的便利性

**添加新提供商只需要 2 步：**

```typescript
// 1. 在 descriptors.ts 中添加预配置（可选）
export const webhookDescriptors = {
  // ... 现有配置
  runway: (): WebhookOpenAPIDescriptor => createWebhookDescriptor({
    provider: 'runway',
    requestSchema: openAPISchemas.runwayWebhookBody,
    additionalTags: ['AI', 'Video Generation']
  })
};

// 2. 在 router.ts 中添加路由
.post("/runway",
  describeRoute(webhookDescriptors.runway()),
  (c) => handleProviderWebhook(c, "runway", runwayHandler)
)
```

**或者使用动态创建：**

```typescript
// 无需修改 descriptors.ts
.post("/runway",
  describeRoute(createProviderDescriptor('runway', runwaySchema)()),
  (c) => handleProviderWebhook(c, "runway", runwayHandler)
)
```

### 4.2 自定义响应的支持

```typescript
// 为特定提供商自定义响应
const customDescriptor = createWebhookDescriptor({
  provider: 'special-provider',
  requestSchema: specialSchema,
  customResponses: {
    202: {
      description: "请求已接受，正在异步处理",
      content: {
        "application/json": {
          schema: { $ref: "#/components/schemas/AsyncResponse" }
        }
      }
    }
  }
});
```

## 5. 实施计划

### 5.1 第一阶段：创建 OpenAPI 目录结构
1. 创建 `openapi/` 目录
2. 创建 `types.ts`、`responses.ts`、`descriptors.ts`、`index.ts`
3. 实现核心工具函数

### 5.2 第二阶段：迁移现有代码
1. 从 `router.ts` 移除 `createWebhookOpenAPIDesc` 函数
2. 更新 `router.ts` 的导入语句
3. 更新路由定义使用新的描述生成器

### 5.3 第三阶段：测试验证
1. 运行 TypeScript 类型检查
2. 运行代码格式化和 lint
3. 验证 OpenAPI 文档生成正确
4. 测试所有 webhook 端点功能

## 6. 预期收益

### 6.1 代码组织改善
- **模块内聚**：OpenAPI 相关代码集中管理
- **职责分离**：router.ts 专注于路由定义
- **易于维护**：统一的文件结构和命名规范

### 6.2 开发效率提升
- **快速扩展**：添加新提供商变得极其简单
- **复用性强**：工具函数可在多个场景使用
- **类型安全**：完整的 TypeScript 支持

### 6.3 质量提升
- **独立测试**：OpenAPI 工具函数可以独立测试
- **标准化**：统一的响应格式和文档风格
- **可配置**：灵活的自定义选项

## 7. 风险评估

### 7.1 潜在风险
- **学习成本**：开发者需要了解新的目录结构
- **回归风险**：重构可能引入新的 bug

### 7.2 缓解措施
- **渐进式迁移**：先创建新结构，再逐步迁移
- **充分测试**：确保所有现有功能正常工作
- **文档完善**：提供清晰的使用指南和示例

## 8. 总结

这个重构方案遵循了现代 API 项目的最佳实践：

- ✅ **符合 Hono 生态**：与 hono-openapi 使用模式一致
- ✅ **模块化设计**：清晰的目录结构和职责分工
- ✅ **类型安全**：完整的 TypeScript 支持
- ✅ **易于扩展**：配置化的设计模式
- ✅ **向后兼容**：不影响现有功能

通过这次重构，我们将获得一个更加清晰、可维护、可扩展的 webhook OpenAPI 架构。