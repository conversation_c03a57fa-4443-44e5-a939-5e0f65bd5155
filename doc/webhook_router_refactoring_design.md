# Webhook Router 重构设计方案

## 1. 背景与问题分析

### 1.1 当前代码重复问题

当前 `packages/api/src/routes/webhooks/router.ts` 中的 `replicate` 和 `fal` 端点存在大量重复代码（约85%相同）：

**重复的部分：**
- 时间记录逻辑 (`const startTime = Date.now()`)
- 请求体解析 (`await c.req.json()`)
- 日志记录格式
- WebhookRequest 对象构造
- 错误处理逻辑和响应格式
- 处理流程完全相同

**差异的部分：**
- Provider 名称 (`'replicate'` vs `'fal'`)
- Handler 实例 (`replicateHandler` vs `falHandler`)
- OpenAPI schema 定义 (`replicateWebhookBody` vs `falWebhookBody`)

### 1.2 当前架构优势

现有的分层架构设计良好：
- **BaseWebhookHandler**: 处理请求解析和验证
- **processWebhookPayload**: 处理业务逻辑（数据库更新、积分退还）
- **分离关注点**: 保持了HTTP层和业务逻辑层的分离

## 2. 重构目标

- ✅ **消除重复代码**: 减少路由器中的样板代码
- ✅ **保持架构优势**: 不破坏现有的分层设计
- ✅ **提高可维护性**: 统一错误处理和日志记录
- ✅ **易于扩展**: 便于添加新的提供商端点
- ✅ **保持向后兼容**: 不影响现有功能

## 3. 详细设计方案

### 3.1 通用处理器函数设计

创建一个通用的 webhook 处理器函数，抽取重复逻辑：

```typescript
/**
 * 通用 webhook 端点处理器
 * @param c - Hono Context 对象
 * @param provider - 提供商名称（用于日志和处理）
 * @param handler - 对应的 webhook 处理器实例
 * @returns Promise<Response>
 */
async function handleProviderWebhook(
  c: Context,
  provider: string,
  handler: BaseWebhookHandler
): Promise<Response> {
  const startTime = Date.now();
  
  try {
    // 1. 解析请求体
    const body = await c.req.json();
    console.log(`📥 [${provider}] Received webhook:`, JSON.stringify(body, null, 2));
    
    // 2. 构造 WebhookRequest 对象
    const webhookRequest: WebhookRequest = {
      headers: Object.fromEntries([...c.req.raw.headers.entries()]),
      body,
      url: c.req.url
    };
    
    // 3. 使用对应的处理器处理请求
    const payload = await handler.handle(webhookRequest);
    
    // 4. 处理 webhook 载荷（业务逻辑）
    const result = await processWebhookPayload(payload, provider);
    
    // 5. 记录成功日志并返回结果
    console.log(`✅ [${provider}] Webhook processed in ${Date.now() - startTime}ms`);
    return c.json(result);
    
  } catch (error) {
    // 6. 统一错误处理
    console.error(`❌ [${provider}] Webhook processing error:`, error);
    return c.json({ 
      error: "Internal server error",
      details: error instanceof Error ? error.message : String(error) 
    }, 500);
  }
}
```

### 3.2 OpenAPI 描述生成器设计

创建一个函数来生成标准化的 OpenAPI 描述，减少配置重复：

```typescript
/**
 * 生成 webhook 端点的 OpenAPI 描述
 * @param provider - 提供商名称
 * @param bodySchema - 请求体 schema
 * @returns OpenAPI 路由描述对象
 */
function createWebhookOpenAPIDesc(provider: string, bodySchema: any) {
  const capitalizedProvider = provider.charAt(0).toUpperCase() + provider.slice(1);
  
  return {
    summary: `${capitalizedProvider} Webhook Handler`,
    description: `处理来自 ${capitalizedProvider} API 的视频生成完成通知`,
    tags: ["Webhooks"],
    requestBody: {
      content: {
        "application/json": {
          schema: bodySchema
        }
      }
    },
    responses: {
      200: {
        description: "Webhook 处理成功",
        content: {
          "application/json": {
            schema: openAPISchemas.webhookSuccessResponse
          }
        }
      },
      400: {
        description: "请求参数错误",
        content: {
          "application/json": {
            schema: openAPISchemas.webhookErrorResponse
          }
        }
      },
      404: {
        description: "任务未找到",
        content: {
          "application/json": {
            schema: openAPISchemas.webhookErrorResponse
          }
        }
      },
      500: {
        description: "服务器内部错误",
        content: {
          "application/json": {
            schema: openAPISchemas.webhookErrorResponse
          }
        }
      }
    }
  };
}
```

### 3.3 重构后的路由定义

使用新的通用函数简化路由定义：

```typescript
export const webhooksRouter = new Hono()
  .basePath("/webhooks")
  
  // Payments webhook（保持不变）
  .post("/payments", 
    describeRoute({
      tags: ["Webhooks"],
      summary: "Handle payments webhook",
    }),
    (c) => paymentsWebhookHandler(c.req.raw)
  )
  
  // Replicate 端点（重构后）
  .post("/replicate", 
    describeRoute(createWebhookOpenAPIDesc("replicate", openAPISchemas.replicateWebhookBody)),
    (c) => handleProviderWebhook(c, "replicate", replicateHandler)
  )
  
  // Fal 端点（重构后） 
  .post("/fal",
    describeRoute(createWebhookOpenAPIDesc("fal", openAPISchemas.falWebhookBody)),
    (c) => handleProviderWebhook(c, "fal", falHandler)
  )
  
  // 其他端点保持不变...
```

### 3.4 新增提供商的便利性

添加新提供商变得非常简单：

```typescript
// 1. 创建处理器
const newProviderHandler = createNewProviderHandler();

// 2. 添加路由（只需一行）
.post("/newprovider",
  describeRoute(createWebhookOpenAPIDesc("newprovider", openAPISchemas.newProviderWebhookBody)),
  (c) => handleProviderWebhook(c, "newprovider", newProviderHandler)
)
```

## 4. 实施计划

### 4.1 第一阶段：创建通用函数
1. 在 `router.ts` 顶部添加 `handleProviderWebhook` 函数
2. 在 `router.ts` 顶部添加 `createWebhookOpenAPIDesc` 函数
3. 更新必要的类型导入

### 4.2 第二阶段：重构现有端点
1. 重构 `/replicate` 端点使用新的通用函数
2. 重构 `/fal` 端点使用新的通用函数
3. 保持所有其他端点不变

### 4.3 第三阶段：测试验证
1. 运行现有测试确保功能不变
2. 测试错误处理流程
3. 验证日志记录格式
4. 检查 OpenAPI 文档生成

## 5. 预期收益

### 5.1 代码指标改善
- **减少重复代码**: 从 ~60 行重复代码减少到 ~6 行
- **代码行数减少**: 整体减少约 40-50 行代码
- **维护复杂度降低**: 统一的错误处理和日志记录

### 5.2 开发效率提升
- **新增提供商**: 从 ~30 行代码减少到 ~3 行代码
- **维护成本**: 修改错误处理逻辑只需改一处
- **一致性**: 所有提供商的处理流程完全一致

### 5.3 质量提升
- **统一错误处理**: 所有端点使用相同的错误响应格式
- **统一日志格式**: 便于监控和调试
- **类型安全**: 通过 TypeScript 保证参数类型正确

## 6. 风险评估与缓解

### 6.1 潜在风险
- **回归风险**: 重构可能引入新的 bug
- **性能影响**: 额外的函数调用层级

### 6.2 缓解措施
- **保持向后兼容**: 不修改现有的处理逻辑，只是重新组织
- **充分测试**: 确保所有现有功能正常工作
- **逐步重构**: 先重构一个端点，验证后再重构其他
- **性能测试**: 验证重构后性能无明显影响

## 7. 替代方案比较

### 7.1 方案A：将 processWebhookPayload 集成到 handle 方法
- ❌ **紧耦合**: Handler 与业务逻辑耦合
- ❌ **违反单一职责**: 混合 HTTP 和业务关注点
- ❌ **降低可重用性**: 无法在其他上下文使用 processor

### 7.2 方案B：当前设计方案（推荐）
- ✅ **保持分离**: HTTP 层和业务层分离
- ✅ **提高复用**: 统一的处理流程
- ✅ **易于维护**: 清晰的职责划分
- ✅ **向后兼容**: 不破坏现有架构

## 8. 结论

推荐使用**方案B（通用处理器函数）**进行重构，这个方案：

1. **完美解决代码重复问题**，同时保持良好的架构设计
2. **显著提升开发效率**，添加新提供商变得极其简单
3. **提高代码质量**，统一错误处理和日志记录
4. **保持向后兼容**，不影响现有功能和测试

这个重构既实用又优雅，是在保持代码质量的前提下解决重复代码问题的最佳方案。