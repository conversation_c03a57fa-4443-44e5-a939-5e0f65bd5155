api get /api/jobs/:id, 会返回generations列表,每个generation有状态字段, '/home/<USER>/app/video-generator/video-generator-web/doc/6.png',看图,帮我设计一个组件,媒体预  │
│   览卡片组件,可以取名为MediaPreviewCard,要设计成嵌套子的形式,例如中间红色部分可以单独设计成Video组件,可以根据任务的api返回的完成状态显示不同的子组件,例如为完成就显示进  │
│   度条提示组件,进度条提示组件用上传后的图片作为背景,进度条提示组件半透明如图'/home/<USER>/app/video-generator/video-generator-web/doc/8.png',完成了就显示视频组件,      │
│   请说出你的理解,是否完整理解需求,然后设计一个详细方案.ultrathink

现在来梳理流程,用户点击/image-to-video页面的Generate->显示submit task 提示框(apps/web/modules/marketing/image-to-video/components/SubmittingTaskCard.tsx)->call  │
│   post/jobs 创建作业->作业返回后,立马定时(每间隔2秒)call                                                                                                                 │
│       /jobs/detail/:jobid,返回这个作业下的task的状态数据后,显示带进度条的视频卡片(MediaPreviewCard),任务完成后显示带视频组件的MediaPreviewCard; 请检查,ultrathink   


流程需要重构:
1.点击用户点击/image-to-video页面的Generate时候,call POST /api/jobs,并立即显示MediaPreviewCard组件, 但是内容显示的是laoding,提示Create a job for generating,如图:
2./api/jobs 响应回来后,job和其中的generation的状态应该是waiting状态, 这时候MediaPreviewCard的中的内容子组件显示Submitting you task...,如图:, 随后每间隔2秒发起 GET /api/jobs/detail/:jobid请求poll 任务状态;
3./api/jobs/detail/:jobid 请求返回的generation的任务状态是processing的时候,MediaPreviewCard的中的内容子组件显示的是以上传的图片为背景的进度条组件,状态不是succeded,状态条就永远不要100%,如图
4.最后,/api/jobs/detail/:jobid 请求返回的generation的任务状态是succeeded的时候,MediaPreviewCard的中的内容子组件显示的就是视频组件;
5.POST /api/jobs 响应结构需要改造, 以适应现在这个流程:
{
    "success": true,
    "data": {
        "user": {
            "id": "string",
            "name": "string",
            "username": "string",
            "image": "string"
        },
        "job": {
            "id": "string",
            "userId": "string",
            "featureCode": "string",
            "type": "video" | "image",
            "numOutputs": number,
            "status": "waiting" | "processing" | "succeeded" | "failed",
            "credit": number,
            "apiProviderCost": number,
            "timeCostSeconds": number,
            "modelCode": "string",
            "prompt": "string",
            "image": "string",
            "imageTail": "string",
            "negativePrompt": "string",
            "promptStrength": number,
            "duration": number,
            "modeCode": "string",
            "resolution": "string",
            "aspectRatio": "string",
            "style": "string",
            "motionRange": "string",
            "seed": number,
            "processType": "string",
            "createdAt": "ISO string",
            "updatedAt": "ISO string"
        },
        "generations": [
            {
                "id": "string",
                "mediaId": "string",
                "cover": "string",
                "thumbnail": "string",
                "videoUrl": "string",
                "mediaUrl": "string",
                "status": "waiting" | "processing" | "succeeded" | "failed",
                "mediaType": "video" | "image",
                "duration": number,
                "createdAt": "ISO string",
                "updatedAt": "ISO string"
            }
        ],
        "progress": {
            "completed": number,
            "total": number
        }
    },
    "message": "Job details retrieved successfully",
    "timestamp": "ISO string"
}

6./api/jobs里面的业务逻辑需要修改,不用等到call replicate 的prediction create回来后再插入Generation记录;
而是job和generation做一个事务插入,都是waiting状态,立马返回,同时异步call replicate接口, call接口回来后,再update job和generation记录为processing状态;

你仔细检查阅读已有代码,给出一个详细设计方案和改造计划,ultrathink


优化:
1.去掉  @@unique([provider, eventId]) // 防重复
2.status                VideoWebhookEventStatus @default(pending) 改成processStatus,VideoWebhookEventStatus 改成VideoWebhookEventProcessStatus,skipped的处理状态去掉,不需要了
3.添加status字段,        值是webhooks原始报文中的status;
4.判断事件有没有重复用provider,eventId,webhooks原始报文中的status,也就是新增加的status字段;如果检测到重复打印warn日志,提示duplicate webhookevent skipped
5.对于新加的status字段状态是processing的,就更新job和generation就可以;是succeded状态的还要完成上传和状态更新;
你先阅读嗲吗,详细分析范围,给出方案和改造计划,ultrathink




我需要开发一个api,需求如下:
1. POST  /api/prompts
request body:
{
    "idea": "A long-haired beauty briskly walks along a tree-lined avenue, her hair flowing in the wind."
}

response body:
{
    "prompts": [
               "A long-haired beauty walks briskly down a tree-lined avenue, her hair dancing in the wind, sunlight filtering through the leaves.",
                "The elegance of a long-haired woman as she strides along a shaded path, her hair flowing freely like a river.",
                "A stunning woman with flowing hair briskly walks past vibrant trees, leaves rustling softly in the breeze around her."
                ]
}, 大概是这样, 你也可以建议或者给出request和response更好的结构.一次只返回3条,返回英文的;

2.call replicate上的openai/gpt-4o 模型;


新需求:
image-to-video页面有个Prompt (Optional) 输入框里面有个Generate按钮,当用户点击这个按钮的时候弹出一个模态框如图1:
用户输入idea(限制300个字符)后,如图2:,点击Continue按钮,就会call POST /api/prompts,生成三个提示词,如图3:, 如果点击Generate more会再次call POST /api/prompts
再生成三条提示词添加到这个窗口中,选中某条提示词,点击Confirm, 就把这条提示词带到表单区域的Prompt (Optional) 输入框里面; 请给出详细设计方案和实施步骤,并写成一份markdown文档,ultrathink


 新需求:                                                                                                     │
│   image-to-video页面有个Prompt (Optional) 输入框里面有个Generate按钮,当用户点击这个按钮的时候弹出一个模态框   │
│   如图1:'/home/<USER>/app/video-generator/video-generator-web/doc/21.png'                                    │
│   用户输入idea(限制300个字符)后,如图2:'/home/<USER>/app/video-generator/video-generator-web/doc/22.png',点   │
│   击Continue按钮,就会call POST                                                                                │
│   /api/prompts,生成三个提示词,如图3:'/home/<USER>/app/video-generator/video-generator-web/doc/23.png' ,      │
│   如果点击Generate more会再次call POST /api/prompts                                                           │
│   再生成三条提示词添加到这个窗口中,选中某条提示词,点击Confirm, 就把这条提示词带到表单区域的Prompt (Optional)  │
│   输入框里面; 请给出详细设计方案和实施步骤,并写成一份markdown文档,ultrathink    



 需要设计一个GenerationItemCard UI组件,                                                           │
│   将来在/my-generations页面列表中使用.将根据Generation中的记录数据渲染这个卡片                     │
│   设计图如图1:'/home/<USER>/app/video-generator/video-generator-web/doc/31.png',                  │
│   当鼠标移动上去的时候,如图2:'/home/<USER>/app/video-generator/video-generator-web/doc/32.png',   │
│   当点击下面...图标的时候,如图3:'/home/<USER>/app/video-generator/video-generator-web/doc/33.png  │
│   ',                                                                                               │
│   我给你一个html片段,你克隆一个一模一样的time卡片组件,html代码片段如下:                            │
│   [Pasted text #1]                                                                                 │
│                                                                                                    │
│   请给出详细设计方案和实施步骤,写成一个markdwon文件,ultrathink   

我需要设计一个api GET /api/generations

request参数:
userId
generationType
pageSize
page
publishStatus
mediaType
favorite
没有出现的或者值为null或者undifne的,查全部值的;如果传了userid的话, jobs,generation都需要查询这个userid的,
generationType用来跟Job的type对应,MediaType用来跟Generation的MediaType对应

response body:
{
"generations": [
    {
        所有Generation的字段
    }
],
"jobs":[{所有jobs字段}],
"users":[{id,name,image}],
}

请给出详细设计方案和实施步骤,写成一个markdown文档.ultrathink


需要设计开发一个页面,如图:
有Videos,Images,两个tab页面,/my-videos,/my-images
缺省/my-videos
里面显示的卡片项目, 就用GenerationItemCard组件, 进入这个页面会call /api/generations,这个用户下面的所有生成的媒体;

参考图中 Favorites, select,publish status , 另外起一行作为表头,类似图:

如果点击Favorites复选框则,添加查询条件过滤一次;
如果点击video tab的话,call /api/generations 重新过滤一次,用mediaType和generationType为video的条件,和其他条件call一次,渲染列表,其他类似


需要设计开发一个页面,如图:'/home/<USER>/app/video-generator/video-generator-web/doc/41.png'
有Videos,Images,两个tab页面,/my-videos,/my-images
缺省/my-videos

里面显示的卡片项目, 就用GenerationItemCard组件, 进入这个页面会call /api/generations,这个用户下面的所有生成的媒体;

参考图中 Favorites, select,publish status , 另外起一行作为表头,类似图2:'/home/<USER>/app/video-generator/video-generator-web/doc/42.png, 点击select的时候会在select左侧显示一个工具条,
如图3:'/home/<USER>/app/video-generator/video-generator-web/doc/44.png'

如果点击Favorites复选框则,添加查询条件过滤一次;
如果点击video tab的话,call /api/generations 重新过滤一次,用mediaType和generationType为video的条件,和其他条件call一次,渲染列表,其他类似
请给出详细设计方案和实施步骤,写入一个markdown文件,ultrathink



实现一个新的api:

1. POST /api/generations/:generationId/likes

response body:

{
   "generationId": "cmdhbk9g403519ympfzaeafsp",
   "likeCount": 1
}

更新generation表的字段:starNum+1

2. DELETE /api/generations/:generationId/likes


{
   "generationId": "cmdhbk9g403519ympfzaeafsp",
   "likeCount": 0
}

更新generation表的字段:starNum-1

请给出详细设案和实施步骤,写入一个markdown文件中,ultrathink


实现一个新的api:
  GET /:generationId/download?media=video&type=watermark
  GET /:generationId/download?media=video&type=no-watermark
  GET /:generationId/download?media=image&type=thumbnail

{
    "downloadUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm84hl47d01qnkvxudh1tv25y/wm/1753365172799-4afe14dc-a17d-4833-af1d-63765142b8f1.mp4"
}

/api/generations/videos/:generationId/downloads/no-watermark

{
    "downloadUrl": "https://videocdn.pollo.ai/web-cdn/pollo/production/cm84hl47d01qnkvxudh1tv25y/ori/1753365172799-6aae0a0f-b4db-47e3-9a75-c25108e9ee3c.mp4"
}


实现一个新的api:
DELETE /api/generations/:generationId

数据库有关的操作要封装到packages/jobs/src/lib/generations-manager.ts中, 请给出详细方案和实施步骤, 写入一个markdwon文件中,ultrathink

response body;
{
  "deleted": "gen_001",
  "failed": {
    "gen_003": "In use by job #xyz"
  }
}

实现一个新的api:

1. POST /api/generations/:generationId/favorite

response body:

{
   "favorite": true
}

更新generation表的字段:starNum+1

2. DELETE /api/generations/:generationId/favorite


{
   "favorite": false
}

数据库的操作要封装到generations-manager.ts,请给出详细设案和实施步骤,写入一个markdown文件中,ultrathink



我想开发一个ui组件,如图1:'/home/<USER>/app/video-generator/video-generator-web/doc/81.png', 
这个组件分成左右两部分,左边是一个子组件SimpleVideoPlayer,右边分成上下两部分,上部分是视频信息部分子组件VideoInfo,下面的功能按钮区;                                                             │
我给你竞争对手的代码,你帮我复刻一个这样的ui组件,给出详细方案和实施步骤,写成一个markdown文件,ultrathink      

竞争对手的html代码如下:
<div class="coco-spin-nested-loading css-1neak7x"><div class="coco-spin-container"><div class="bg-f-bg-container relative p-0 lg:rounded-lg lg:p-6"><div class="relative flex flex-col lg:flex-row lg:gap-6 lg:rounded-lg"><div class="flex-1"><div class="flex w-full items-center justify-center"></div><div class="bg-f-other-5 relative size-[100vw] w-full lg:h-[80vh] lg:flex-1"><div class="relative size-full"><div class="absolute inset-0 rounded-2xl bg-cover bg-center bg-no-repeat"></div><div class="absolute inset-0 backdrop-blur-lg"></div><div class="flex size-full flex-col items-center justify-center hover:cursor-pointer [&amp;_.video-js]:size-full"><div class="relative aspect-video w-full flex-1 transition-shadow hover:shadow-lg"><div id="vjs_video_3" class="video-js vjs-default-skin !bg-transparent vjs-big-play-button vjs-fill vjs_video_3-dimensions vjs-controls-enabled vjs-touch-enabled vjs-workinghover vjs-v8 vjs-has-started vjs-paused vjs-user-inactive" tabindex="-1" role="region" lang="en-us" translate="no" aria-label="Video Player"><video class="vjs-tech" id="vjs_video_3_html5_api" tabindex="-1" role="application" loop="" muted="muted" src="https://videocdn.pollo.ai/web-cdn/pollo/production/cm84hl47d01qnkvxudh1tv25y/wm/1753357108102-7d65ca48-f97e-4186-af13-7df65b3ffc9f.mp4"></video><div class="vjs-poster" aria-disabled="false"><picture class="vjs-poster" tabindex="-1"><img loading="lazy" alt="" src="https://videocdn.pollo.ai/web-cdn/pollo/production/cm84hl47d01qnkvxudh1tv25y/cover/1753357108102-e5a0aaa8-ba82-4b95-af16-dba07e82ecd4.webp"></picture></div><div class="vjs-title-bar vjs-hidden"><div class="vjs-title-bar-title" id="vjs-title-bar-title-27"></div><div class="vjs-title-bar-description" id="vjs-title-bar-description-28"></div></div><div class="vjs-text-track-display" translate="yes" aria-live="off" aria-atomic="true" style="inset-block: 124px;"><div style="position: absolute; inset: 0px; margin: 1.5%;"></div></div><div class="vjs-loading-spinner" dir="ltr"><span class="vjs-control-text">Video Player is loading.</span></div><button class="vjs-big-play-button vjs-hidden" type="button" title="Play Video" aria-disabled="false"><span class="vjs-icon-placeholder" aria-hidden="true"></span><span class="vjs-control-text" aria-live="polite">Play Video</span></button><div class="vjs-control-bar" dir="ltr"><button class="vjs-play-control vjs-control vjs-button vjs-paused" type="button" title="Play" aria-disabled="false"><span class="vjs-icon-placeholder" aria-hidden="true"></span><span class="vjs-control-text" aria-live="polite">Play</span></button><button class="vjs-skip-backward-undefined vjs-control vjs-button vjs-hidden" type="button" title="Skip Backward" aria-disabled="false"><span class="vjs-icon-placeholder" aria-hidden="true"></span><span class="vjs-control-text" aria-live="polite">Skip Backward</span></button><button class="vjs-skip-forward-undefined vjs-control vjs-button vjs-hidden" type="button" title="Skip Forward" aria-disabled="false"><span class="vjs-icon-placeholder" aria-hidden="true"></span><span class="vjs-control-text" aria-live="polite">Skip Forward</span></button><div class="vjs-volume-panel vjs-control vjs-volume-panel-vertical"><button class="vjs-mute-control vjs-control vjs-button vjs-vol-0" type="button" title="Unmute" aria-disabled="false"><span class="vjs-icon-placeholder" aria-hidden="true"></span><span class="vjs-control-text" aria-live="polite">Unmute</span></button><div class="vjs-volume-control vjs-control vjs-volume-vertical"><div tabindex="0" class="vjs-volume-bar vjs-slider-bar vjs-slider vjs-slider-vertical" role="slider" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" aria-label="Volume Level" aria-live="polite" aria-valuetext="0%"><div class="vjs-mouse-display"><div class="vjs-volume-tooltip" aria-hidden="true"></div></div><div class="vjs-volume-level"><span class="vjs-control-text"></span></div></div></div></div><div class="vjs-current-time vjs-time-control vjs-control"><span class="vjs-control-text" role="presentation">Current Time&nbsp;</span><span class="vjs-current-time-display" role="presentation">0:02</span></div><div class="vjs-time-control vjs-time-divider" aria-hidden="true"><div><span>/</span></div></div><div class="vjs-duration vjs-time-control vjs-control"><span class="vjs-control-text" role="presentation">Duration&nbsp;</span><span class="vjs-duration-display" role="presentation">0:05</span></div><div class="vjs-progress-control vjs-control"><div tabindex="0" class="vjs-progress-holder vjs-slider vjs-slider-horizontal" role="slider" aria-valuenow="42.70" aria-valuemin="0" aria-valuemax="100" aria-label="Progress Bar" aria-valuetext="0:02 of 0:05"><div class="vjs-load-progress" style="width: 100%;"><span class="vjs-control-text"><span>Loaded</span>: <span class="vjs-control-text-loaded-percentage">100.00%</span></span><div data-start="0" data-end="5.041667" style="left: 0%; width: 100%;"></div></div><div class="vjs-mouse-display" style="left: 287px;"><div class="vjs-time-tooltip" aria-hidden="true" style="right: -8px;">0:03</div></div><div class="vjs-play-progress vjs-slider-bar" aria-hidden="true" style="width: 42.7%;"><div class="vjs-time-tooltip" aria-hidden="true" style="right: -18px;">0:02</div></div></div></div><div class="vjs-live-control vjs-control vjs-hidden"><div class="vjs-live-display" aria-live="off"><span class="vjs-control-text">Stream Type&nbsp;</span>LIVE</div></div><button class="vjs-seek-to-live-control vjs-control" type="button" title="Seek to live, currently behind live" aria-disabled="false"><span class="vjs-icon-placeholder" aria-hidden="true"></span><span class="vjs-control-text" aria-live="polite">Seek to live, currently behind live</span><span class="vjs-seek-to-live-text" aria-hidden="true">LIVE</span></button><div class="vjs-remaining-time vjs-time-control vjs-control"><span class="vjs-control-text" role="presentation">Remaining Time&nbsp;</span><span aria-hidden="true">-</span><span class="vjs-remaining-time-display" role="presentation">0:03</span></div><div class="vjs-custom-control-spacer vjs-spacer ">&nbsp;</div><div class="vjs-playback-rate vjs-menu-button vjs-menu-button-popup vjs-control vjs-button" style="z-index: 1000;"><div class="vjs-playback-rate-value" id="vjs-playback-rate-value-label-vjs_video_3_component_285">1x</div><button class="vjs-playback-rate vjs-menu-button vjs-menu-button-popup vjs-button" type="button" aria-disabled="false" title="Playback Rate" aria-haspopup="true" aria-expanded="false" aria-describedby="vjs-playback-rate-value-label-vjs_video_3_component_285"><span class="vjs-icon-placeholder" aria-hidden="true"></span><span class="vjs-control-text" aria-live="polite">Playback Rate</span></button><div class="vjs-menu"><ul class="vjs-menu-content" role="menu"><li class="vjs-menu-item" tabindex="-1" role="menuitemradio" aria-disabled="false" aria-checked="false"><span class="vjs-menu-item-text">2x</span><span class="vjs-control-text" aria-live="polite"></span></li><li class="vjs-menu-item" tabindex="-1" role="menuitemradio" aria-disabled="false" aria-checked="false"><span class="vjs-menu-item-text">1.5x</span><span class="vjs-control-text" aria-live="polite"></span></li><li class="vjs-menu-item vjs-selected" tabindex="-1" role="menuitemradio" aria-disabled="false" aria-checked="true"><span class="vjs-menu-item-text">1x</span><span class="vjs-control-text" aria-live="polite">, selected</span></li><li class="vjs-menu-item" tabindex="-1" role="menuitemradio" aria-disabled="false" aria-checked="false"><span class="vjs-menu-item-text">0.5x</span><span class="vjs-control-text" aria-live="polite"></span></li></ul></div></div><div class="vjs-chapters-button vjs-menu-button vjs-menu-button-popup vjs-control vjs-button vjs-hidden"><button class="vjs-chapters-button vjs-menu-button vjs-menu-button-popup vjs-button" type="button" aria-disabled="false" title="Chapters" aria-haspopup="true" aria-expanded="false"><span class="vjs-icon-placeholder" aria-hidden="true"></span><span class="vjs-control-text" aria-live="polite">Chapters</span></button><div class="vjs-menu"><ul class="vjs-menu-content"><li class="vjs-menu-title" tabindex="-1">Chapters</li></ul></div></div><div class="vjs-descriptions-button vjs-menu-button vjs-menu-button-popup vjs-control vjs-button vjs-hidden"><button class="vjs-descriptions-button vjs-menu-button vjs-menu-button-popup vjs-button" type="button" aria-disabled="false" title="Descriptions" aria-haspopup="true" aria-expanded="false"><span class="vjs-icon-placeholder" aria-hidden="true"></span><span class="vjs-control-text" aria-live="polite">Descriptions</span></button><div class="vjs-menu"><ul class="vjs-menu-content"><li class="vjs-menu-item vjs-selected" tabindex="-1" role="menuitemradio" aria-disabled="false" aria-checked="true"><span class="vjs-menu-item-text">descriptions off</span><span class="vjs-control-text" aria-live="polite">, selected</span></li></ul></div></div><div class="vjs-subs-caps-button vjs-menu-button vjs-menu-button-popup vjs-control vjs-button vjs-hidden"><button class="vjs-subs-caps-button vjs-menu-button vjs-menu-button-popup vjs-button" type="button" aria-disabled="false" title="Captions" aria-haspopup="true" aria-expanded="false"><span class="vjs-icon-placeholder" aria-hidden="true"></span><span class="vjs-control-text" aria-live="polite">Captions</span></button><div class="vjs-menu"><ul class="vjs-menu-content"><li class="vjs-menu-item vjs-texttrack-settings" tabindex="-1" role="menuitem" aria-disabled="false"><span class="vjs-menu-item-text">captions settings</span><span class="vjs-control-text" aria-live="polite">, opens captions settings dialog</span></li><li class="vjs-menu-item vjs-selected" tabindex="-1" role="menuitemradio" aria-disabled="false" aria-checked="true"><span class="vjs-menu-item-text">captions off</span><span class="vjs-control-text" aria-live="polite">, selected</span></li></ul></div></div><div class="vjs-audio-button vjs-menu-button vjs-menu-button-popup vjs-control vjs-button vjs-hidden"><button class="vjs-audio-button vjs-menu-button vjs-menu-button-popup vjs-button" type="button" aria-disabled="false" title="Audio Track" aria-haspopup="true" aria-expanded="false"><span class="vjs-icon-placeholder" aria-hidden="true"></span><span class="vjs-control-text" aria-live="polite">Audio Track</span></button><div class="vjs-menu"><ul class="vjs-menu-content"></ul></div></div><button class="vjs-picture-in-picture-control vjs-control vjs-button" type="button" title="Picture-in-Picture" aria-disabled="false"><span class="vjs-icon-placeholder" aria-hidden="true"></span><span class="vjs-control-text" aria-live="polite">Picture-in-Picture</span></button><button class="vjs-fullscreen-control vjs-control vjs-button" type="button" title="Fullscreen" aria-disabled="false"><span class="vjs-icon-placeholder" aria-hidden="true"></span><span class="vjs-control-text" aria-live="polite">Fullscreen</span></button></div><div class="vjs-error-display vjs-modal-dialog vjs-hidden " tabindex="-1" aria-describedby="vjs_video_3_component_553_description" aria-hidden="true" aria-label="Modal Window" role="dialog" aria-live="polite"><p class="vjs-modal-dialog-description vjs-control-text" id="vjs_video_3_component_553_description">This is a modal window.</p><div class="vjs-modal-dialog-content" role="document"></div></div><div class="vjs-modal-dialog vjs-hidden  vjs-text-track-settings" tabindex="-1" aria-describedby="vjs_video_3_component_559_description" aria-hidden="true" aria-label="Caption Settings Dialog" role="dialog" aria-live="polite"><p class="vjs-modal-dialog-description vjs-control-text" id="vjs_video_3_component_559_description">Beginning of dialog window. Escape will cancel and close the window.</p><div class="vjs-modal-dialog-content" role="document"><div class="vjs-track-settings-colors"><fieldset class="vjs-fg vjs-track-setting"><legend id="captions-text-legend-vjs_video_3_component_559">Text</legend><span class="vjs-text-color"><label id="captions-foreground-color-vjs_video_3_component_559" class="vjs-label" for="vjs_select_586">Color</label><select id="vjs_select_586" aria-labelledby="captions-text-legend-vjs_video_3_component_559 captions-foreground-color-vjs_video_3_component_559"><option id="captions-foreground-color-vjs_video_3_component_559-White" value="#FFF" aria-labelledby="captions-text-legend-vjs_video_3_component_559 captions-foreground-color-vjs_video_3_component_559 captions-foreground-color-vjs_video_3_component_559-White">White</option><option id="captions-foreground-color-vjs_video_3_component_559-Black" value="#000" aria-labelledby="captions-text-legend-vjs_video_3_component_559 captions-foreground-color-vjs_video_3_component_559 captions-foreground-color-vjs_video_3_component_559-Black">Black</option><option id="captions-foreground-color-vjs_video_3_component_559-Red" value="#F00" aria-labelledby="captions-text-legend-vjs_video_3_component_559 captions-foreground-color-vjs_video_3_component_559 captions-foreground-color-vjs_video_3_component_559-Red">Red</option><option id="captions-foreground-color-vjs_video_3_component_559-Green" value="#0F0" aria-labelledby="captions-text-legend-vjs_video_3_component_559 captions-foreground-color-vjs_video_3_component_559 captions-foreground-color-vjs_video_3_component_559-Green">Green</option><option id="captions-foreground-color-vjs_video_3_component_559-Blue" value="#00F" aria-labelledby="captions-text-legend-vjs_video_3_component_559 captions-foreground-color-vjs_video_3_component_559 captions-foreground-color-vjs_video_3_component_559-Blue">Blue</option><option id="captions-foreground-color-vjs_video_3_component_559-Yellow" value="#FF0" aria-labelledby="captions-text-legend-vjs_video_3_component_559 captions-foreground-color-vjs_video_3_component_559 captions-foreground-color-vjs_video_3_component_559-Yellow">Yellow</option><option id="captions-foreground-color-vjs_video_3_component_559-Magenta" value="#F0F" aria-labelledby="captions-text-legend-vjs_video_3_component_559 captions-foreground-color-vjs_video_3_component_559 captions-foreground-color-vjs_video_3_component_559-Magenta">Magenta</option><option id="captions-foreground-color-vjs_video_3_component_559-Cyan" value="#0FF" aria-labelledby="captions-text-legend-vjs_video_3_component_559 captions-foreground-color-vjs_video_3_component_559 captions-foreground-color-vjs_video_3_component_559-Cyan">Cyan</option></select></span><span class="vjs-text-opacity vjs-opacity"><label id="captions-foreground-opacity-vjs_video_3_component_559" class="vjs-label" for="vjs_select_591">Opacity</label><select id="vjs_select_591" aria-labelledby="captions-text-legend-vjs_video_3_component_559 captions-foreground-opacity-vjs_video_3_component_559"><option id="captions-foreground-opacity-vjs_video_3_component_559-Opaque" value="1" aria-labelledby="captions-text-legend-vjs_video_3_component_559 captions-foreground-opacity-vjs_video_3_component_559 captions-foreground-opacity-vjs_video_3_component_559-Opaque">Opaque</option><option id="captions-foreground-opacity-vjs_video_3_component_559-SemiTransparent" value="0.5" aria-labelledby="captions-text-legend-vjs_video_3_component_559 captions-foreground-opacity-vjs_video_3_component_559 captions-foreground-opacity-vjs_video_3_component_559-SemiTransparent">Semi-Transparent</option></select></span></fieldset><fieldset class="vjs-bg vjs-track-setting"><legend id="captions-background-vjs_video_3_component_559">Text Background</legend><span class="vjs-bg-color"><label id="captions-background-color-vjs_video_3_component_559" class="vjs-label" for="vjs_select_601">Color</label><select id="vjs_select_601" aria-labelledby="captions-background-vjs_video_3_component_559 captions-background-color-vjs_video_3_component_559"><option id="captions-background-color-vjs_video_3_component_559-Black" value="#000" aria-labelledby="captions-background-vjs_video_3_component_559 captions-background-color-vjs_video_3_component_559 captions-background-color-vjs_video_3_component_559-Black">Black</option><option id="captions-background-color-vjs_video_3_component_559-White" value="#FFF" aria-labelledby="captions-background-vjs_video_3_component_559 captions-background-color-vjs_video_3_component_559 captions-background-color-vjs_video_3_component_559-White">White</option><option id="captions-background-color-vjs_video_3_component_559-Red" value="#F00" aria-labelledby="captions-background-vjs_video_3_component_559 captions-background-color-vjs_video_3_component_559 captions-background-color-vjs_video_3_component_559-Red">Red</option><option id="captions-background-color-vjs_video_3_component_559-Green" value="#0F0" aria-labelledby="captions-background-vjs_video_3_component_559 captions-background-color-vjs_video_3_component_559 captions-background-color-vjs_video_3_component_559-Green">Green</option><option id="captions-background-color-vjs_video_3_component_559-Blue" value="#00F" aria-labelledby="captions-background-vjs_video_3_component_559 captions-background-color-vjs_video_3_component_559 captions-background-color-vjs_video_3_component_559-Blue">Blue</option><option id="captions-background-color-vjs_video_3_component_559-Yellow" value="#FF0" aria-labelledby="captions-background-vjs_video_3_component_559 captions-background-color-vjs_video_3_component_559 captions-background-color-vjs_video_3_component_559-Yellow">Yellow</option><option id="captions-background-color-vjs_video_3_component_559-Magenta" value="#F0F" aria-labelledby="captions-background-vjs_video_3_component_559 captions-background-color-vjs_video_3_component_559 captions-background-color-vjs_video_3_component_559-Magenta">Magenta</option><option id="captions-background-color-vjs_video_3_component_559-Cyan" value="#0FF" aria-labelledby="captions-background-vjs_video_3_component_559 captions-background-color-vjs_video_3_component_559 captions-background-color-vjs_video_3_component_559-Cyan">Cyan</option></select></span><span class="vjs-bg-opacity vjs-opacity"><label id="captions-background-opacity-vjs_video_3_component_559" class="vjs-label" for="vjs_select_606">Opacity</label><select id="vjs_select_606" aria-labelledby="captions-background-vjs_video_3_component_559 captions-background-opacity-vjs_video_3_component_559"><option id="captions-background-opacity-vjs_video_3_component_559-Opaque" value="1" aria-labelledby="captions-background-vjs_video_3_component_559 captions-background-opacity-vjs_video_3_component_559 captions-background-opacity-vjs_video_3_component_559-Opaque">Opaque</option><option id="captions-background-opacity-vjs_video_3_component_559-SemiTransparent" value="0.5" aria-labelledby="captions-background-vjs_video_3_component_559 captions-background-opacity-vjs_video_3_component_559 captions-background-opacity-vjs_video_3_component_559-SemiTransparent">Semi-Transparent</option><option id="captions-background-opacity-vjs_video_3_component_559-Transparent" value="0" aria-labelledby="captions-background-vjs_video_3_component_559 captions-background-opacity-vjs_video_3_component_559 captions-background-opacity-vjs_video_3_component_559-Transparent">Transparent</option></select></span></fieldset><fieldset class="vjs-window vjs-track-setting"><legend id="captions-window-vjs_video_3_component_559">Caption Area Background</legend><span class="vjs-window-color"><label id="captions-window-color-vjs_video_3_component_559" class="vjs-label" for="vjs_select_616">Color</label><select id="vjs_select_616" aria-labelledby="captions-window-vjs_video_3_component_559 captions-window-color-vjs_video_3_component_559"><option id="captions-window-color-vjs_video_3_component_559-Black" value="#000" aria-labelledby="captions-window-vjs_video_3_component_559 captions-window-color-vjs_video_3_component_559 captions-window-color-vjs_video_3_component_559-Black">Black</option><option id="captions-window-color-vjs_video_3_component_559-White" value="#FFF" aria-labelledby="captions-window-vjs_video_3_component_559 captions-window-color-vjs_video_3_component_559 captions-window-color-vjs_video_3_component_559-White">White</option><option id="captions-window-color-vjs_video_3_component_559-Red" value="#F00" aria-labelledby="captions-window-vjs_video_3_component_559 captions-window-color-vjs_video_3_component_559 captions-window-color-vjs_video_3_component_559-Red">Red</option><option id="captions-window-color-vjs_video_3_component_559-Green" value="#0F0" aria-labelledby="captions-window-vjs_video_3_component_559 captions-window-color-vjs_video_3_component_559 captions-window-color-vjs_video_3_component_559-Green">Green</option><option id="captions-window-color-vjs_video_3_component_559-Blue" value="#00F" aria-labelledby="captions-window-vjs_video_3_component_559 captions-window-color-vjs_video_3_component_559 captions-window-color-vjs_video_3_component_559-Blue">Blue</option><option id="captions-window-color-vjs_video_3_component_559-Yellow" value="#FF0" aria-labelledby="captions-window-vjs_video_3_component_559 captions-window-color-vjs_video_3_component_559 captions-window-color-vjs_video_3_component_559-Yellow">Yellow</option><option id="captions-window-color-vjs_video_3_component_559-Magenta" value="#F0F" aria-labelledby="captions-window-vjs_video_3_component_559 captions-window-color-vjs_video_3_component_559 captions-window-color-vjs_video_3_component_559-Magenta">Magenta</option><option id="captions-window-color-vjs_video_3_component_559-Cyan" value="#0FF" aria-labelledby="captions-window-vjs_video_3_component_559 captions-window-color-vjs_video_3_component_559 captions-window-color-vjs_video_3_component_559-Cyan">Cyan</option></select></span><span class="vjs-window-opacity vjs-opacity"><label id="captions-window-opacity-vjs_video_3_component_559" class="vjs-label" for="vjs_select_621">Opacity</label><select id="vjs_select_621" aria-labelledby="captions-window-vjs_video_3_component_559 captions-window-opacity-vjs_video_3_component_559"><option id="captions-window-opacity-vjs_video_3_component_559-Transparent" value="0" aria-labelledby="captions-window-vjs_video_3_component_559 captions-window-opacity-vjs_video_3_component_559 captions-window-opacity-vjs_video_3_component_559-Transparent">Transparent</option><option id="captions-window-opacity-vjs_video_3_component_559-SemiTransparent" value="0.5" aria-labelledby="captions-window-vjs_video_3_component_559 captions-window-opacity-vjs_video_3_component_559 captions-window-opacity-vjs_video_3_component_559-SemiTransparent">Semi-Transparent</option><option id="captions-window-opacity-vjs_video_3_component_559-Opaque" value="1" aria-labelledby="captions-window-vjs_video_3_component_559 captions-window-opacity-vjs_video_3_component_559 captions-window-opacity-vjs_video_3_component_559-Opaque">Opaque</option></select></span></fieldset></div><div class="vjs-track-settings-font"><fieldset class="vjs-font-percent vjs-track-setting"><legend id="captions-font-size-vjs_video_3_component_559">Font Size</legend><select id="vjs_select_636" aria-labelledby="captions-font-size-vjs_video_3_component_559"><option id="vjs-track-option-637-50" value="0.50" aria-labelledby="captions-font-size-vjs_video_3_component_559 vjs-track-option-637-50">50%</option><option id="vjs-track-option-638-75" value="0.75" aria-labelledby="captions-font-size-vjs_video_3_component_559 vjs-track-option-638-75">75%</option><option id="vjs-track-option-639-100" value="1.00" aria-labelledby="captions-font-size-vjs_video_3_component_559 vjs-track-option-639-100">100%</option><option id="vjs-track-option-640-125" value="1.25" aria-labelledby="captions-font-size-vjs_video_3_component_559 vjs-track-option-640-125">125%</option><option id="vjs-track-option-641-150" value="1.50" aria-labelledby="captions-font-size-vjs_video_3_component_559 vjs-track-option-641-150">150%</option><option id="vjs-track-option-642-175" value="1.75" aria-labelledby="captions-font-size-vjs_video_3_component_559 vjs-track-option-642-175">175%</option><option id="vjs-track-option-643-200" value="2.00" aria-labelledby="captions-font-size-vjs_video_3_component_559 vjs-track-option-643-200">200%</option><option id="vjs-track-option-644-300" value="3.00" aria-labelledby="captions-font-size-vjs_video_3_component_559 vjs-track-option-644-300">300%</option><option id="vjs-track-option-645-400" value="4.00" aria-labelledby="captions-font-size-vjs_video_3_component_559 vjs-track-option-645-400">400%</option></select></fieldset><fieldset class="vjs-edge-style vjs-track-setting"><legend id="captions-edge-style-vjs_video_3_component_559">Text Edge Style</legend><select id="vjs_select_655" aria-labelledby="captions-edge-style-vjs_video_3_component_559"><option id="vjs-track-option-656-None" value="none" aria-labelledby="captions-edge-style-vjs_video_3_component_559 vjs-track-option-656-None">None</option><option id="vjs-track-option-657-Raised" value="raised" aria-labelledby="captions-edge-style-vjs_video_3_component_559 vjs-track-option-657-Raised">Raised</option><option id="vjs-track-option-658-Depressed" value="depressed" aria-labelledby="captions-edge-style-vjs_video_3_component_559 vjs-track-option-658-Depressed">Depressed</option><option id="vjs-track-option-659-Uniform" value="uniform" aria-labelledby="captions-edge-style-vjs_video_3_component_559 vjs-track-option-659-Uniform">Uniform</option><option id="vjs-track-option-660-Dropshadow" value="dropshadow" aria-labelledby="captions-edge-style-vjs_video_3_component_559 vjs-track-option-660-Dropshadow">Drop shadow</option></select></fieldset><fieldset class="vjs-font-family vjs-track-setting"><legend id="captions-font-family-vjs_video_3_component_559">Font Family</legend><select id="vjs_select_670" aria-labelledby="captions-font-family-vjs_video_3_component_559"><option id="vjs-track-option-671-ProportionalSansSerif" value="proportionalSansSerif" aria-labelledby="captions-font-family-vjs_video_3_component_559 vjs-track-option-671-ProportionalSansSerif">Proportional Sans-Serif</option><option id="vjs-track-option-672-MonospaceSansSerif" value="monospaceSansSerif" aria-labelledby="captions-font-family-vjs_video_3_component_559 vjs-track-option-672-MonospaceSansSerif">Monospace Sans-Serif</option><option id="vjs-track-option-673-ProportionalSerif" value="proportionalSerif" aria-labelledby="captions-font-family-vjs_video_3_component_559 vjs-track-option-673-ProportionalSerif">Proportional Serif</option><option id="vjs-track-option-674-MonospaceSerif" value="monospaceSerif" aria-labelledby="captions-font-family-vjs_video_3_component_559 vjs-track-option-674-MonospaceSerif">Monospace Serif</option><option id="vjs-track-option-675-Casual" value="casual" aria-labelledby="captions-font-family-vjs_video_3_component_559 vjs-track-option-675-Casual">Casual</option><option id="vjs-track-option-676-Script" value="script" aria-labelledby="captions-font-family-vjs_video_3_component_559 vjs-track-option-676-Script">Script</option><option id="vjs-track-option-677-SmallCaps" value="small-caps" aria-labelledby="captions-font-family-vjs_video_3_component_559 vjs-track-option-677-SmallCaps">Small Caps</option></select></fieldset></div><div class="vjs-track-settings-controls"><button class="vjs-default-button" type="button" title="restore all settings to the default values" aria-disabled="false">Reset</button><button class="vjs-done-button" type="button" title="restore all settings to the default values" aria-disabled="false">Done</button></div></div><button class="vjs-close-button vjs-control vjs-button" type="button" title="Close Modal Dialog" aria-disabled="false"><span class="vjs-icon-placeholder" aria-hidden="true"></span><span class="vjs-control-text" aria-live="polite">Close Modal Dialog</span></button><p class="vjs-control-text">End of dialog window.</p></div><div><button type="button" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; border-radius: 50%; width: 48px; height: 48px; cursor: pointer; display: none; justify-content: center; align-items: center; z-index: 2; background-color: rgb(43, 41, 55); opacity: 0.8;"><span class="i-cus--pol-play text-f-tertiary size-12"></span></button></div></div></div></div></div><div class="flex cursor-pointer items-center gap-x-1 rounded p-1 transition-all text-f-text hover:bg-f-other-2 absolute right-[calc(100%-32px)] lg:right-2 top-2"><span class="inline-flex justify-center items-center size-4 i-cus--pol-favorite-02"></span></div></div></div><div class="flex flex-col justify-between gap-y-3 p-6 lg:h-[80vh] lg:w-[408px] lg:p-0"><div class="flex max-h-fit min-h-0 flex-1 flex-col gap-y-3"><div class="flex items-center justify-between"><div class="flex"><div class="hover:text-f-primary inline-flex cursor-pointer items-center gap-x-2"><div class="bg-f-orange-6 relative flex size-6 items-center justify-center rounded-full text-white hover:cursor-pointer"><figure class="relative size-full overflow-hidden rounded-full"><img alt="Shane Wu (Shane)" loading="lazy" decoding="async" data-nimg="fill" class="size-full" src="https://lh3.googleusercontent.com/a/ACg8ocLJtsM1rekLdP8Z3OD3w79xTtlsftvPqcfQETfgV5VhgLR76rI=s96-c" style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;"></figure></div><span class="line-clamp-1 max-w-[200px] text-sm">Shane Wu (Shane)</span></div></div><div class="text-f-text-quaternary text-xs font-normal">2025-07-24 19:37</div></div><div class="scrollbar bg-f-bg-layout flex-1 overflow-y-auto rounded-md p-3"><div class="mb-3 flex items-center justify-between"><div class="bg-f-bg-layout border-f-border text-f-text-secondary flex h-[24px] items-center rounded border px-2 text-xs">Image to Video</div><div class="flex min-w-[120px] justify-end"><button type="button" class="hover:text-f-primary flex w-fit items-center justify-center gap-x-1 border-none py-[2px] text-xs leading-4 text-f-text-secondary bg-f-bg-active border-f-border hover:bg-f-bg-hover h-[24px] rounded border px-2"><span class="inline-flex justify-center items-center i-com--hide group-hover:i-com--show !size-4"></span><span class="scale-90">Unpublished</span></button></div></div><div class="[&amp;_.coco-form-item-control-input]:min-h-[auto] [&amp;_.coco-form-item-label&gt;label:after]:ms-0 [&amp;_.coco-form-item-label&gt;label:after]:text-xs"><form autocomplete="off" class="coco-form coco-form-vertical css-1oa2iw1 coco-pro-form css-1oa2iw1"><input type="text" style="display: none;"><div class="mb-3"><div class="mb-2"><div class="inline-flex items-center text-sm font-semibold leading-5">Original image</div></div><div class="flex h-20 gap-2"><div class="w-fit"><div class="group size-full"><span class="coco-upload-wrapper bg-f-bg-layout hover:border-f-border text-f-text-tertiary flex size-full flex-col items-center justify-center overflow-hidden rounded-lg border border-transparent [&amp;_.coco-upload-btn]:!flex [&amp;_.coco-upload-btn]:justify-center [&amp;_.coco-upload-drag-container]:size-full [&amp;_.coco-upload-drag]:border-0 [&amp;_.coco-upload-drag]:bg-transparent css-1oa2iw1"><div class="css-1oa2iw1 coco-upload coco-upload-drag coco-upload-disabled"><span class="coco-upload coco-upload-disabled coco-upload-btn" role="button"><input name="file" disabled="" type="file" accept=".jpeg,.jpg,.png,.webp" style="display: none;"><div class="coco-upload-drag-container"><div class="group relative inline-flex size-full cursor-pointer"><div class="flex size-full items-center justify-center group-hover:opacity-20"><img alt="upload img" loading="eager" width="140" height="104" decoding="async" data-nimg="1" class="size-auto max-h-full max-w-full object-contain bg-checkered" src="https://videocdn.pollo.ai/web-cdn/pollo/production/cm84hl47d01qnkvxudh1tv25y/image/1753356961026-eeeb6c6b-b8b9-4651-ac5b-5736b3ca64f7.jpg" style="color: transparent;"></div><div class="absolute left-1/2 top-1/2 z-[1] hidden -translate-x-1/2 -translate-y-1/2 group-hover:flex"><span class="inline-flex justify-center items-center hover:text-f-primary cursor-pointer px-2 py-1 group-hover:block group-hover:text-f-primary"><span class="inline-flex justify-center items-center i-cus--pol-magnifer size-5"></span></span></div></div></div></span></div></span></div></div></div></div><div data-name="prompt" class="coco-row mb-[2px] flex-col css-1oa2iw1"><div class="coco-col coco-col-24 css-1oa2iw1"><div class="inline-flex items-center w-full text-xs font-semibold"><div class="flex w-full items-center justify-between"><div class="inline-flex items-center text-sm font-semibold leading-5"><div class="flex items-center gap-x-1">Prompt</div></div><span class="inline-flex items-center justify-center"><span class="inline-flex justify-center items-center i-cus--pol-copy-solid text-f-text-tertiary size-5 cursor-pointer"></span></span></div></div></div><div class="coco-col css-1oa2iw1" style="flex: 1 1 auto;"><div class="flex h-full items-center gap-3"><span class="text-f-text-tertiary text-xs"><div class=""><span class="text-f-text-tertiary text-xs scrollbar flex max-h-[150px] overflow-y-auto">开盒视频</span></div></span></div></div><div class="coco-col css-1oa2iw1"><div class="coco-divider css-1oa2iw1 coco-divider-horizontal border-f-border my-3" role="separator"></div></div></div><div data-name="videoModelLabel" class="coco-row mb-[2px] flex-row css-1oa2iw1"><div class="coco-col coco-col-15 css-1oa2iw1"><div class="inline-flex items-center after:content-[':'] text-xs font-semibold">Model</div></div><div class="coco-col css-1oa2iw1" style="flex: 1 1 auto;"><div class="flex h-full items-center gap-3"><span class="text-f-text-tertiary text-xs">Pollo 1.6</span></div></div></div><div data-name="resolution" class="coco-row mb-[2px] flex-row css-1oa2iw1"><div class="coco-col coco-col-15 css-1oa2iw1"><div class="inline-flex items-center after:content-[':'] text-xs font-semibold">Resolution</div></div><div class="coco-col css-1oa2iw1" style="flex: 1 1 auto;"><div class="flex h-full items-center gap-3"><span class="text-f-text-tertiary text-xs"><span>480P</span></span></div></div></div><div data-name="length" class="coco-row mb-[2px] flex-row css-1oa2iw1"><div class="coco-col coco-col-15 css-1oa2iw1"><div class="inline-flex items-center after:content-[':'] text-xs font-semibold">Video Length</div></div><div class="coco-col css-1oa2iw1" style="flex: 1 1 auto;"><div class="flex h-full items-center gap-3"><span class="text-f-text-tertiary text-xs">5s</span></div></div></div><div data-name="seed" class="coco-row mb-[2px] flex-row css-1oa2iw1"><div class="coco-col coco-col-15 css-1oa2iw1"><div class="inline-flex items-center after:content-[':'] text-xs font-semibold">Seed</div></div><div class="coco-col css-1oa2iw1" style="flex: 1 1 auto;"><div class="flex h-full items-center gap-3"><span class="text-f-text-tertiary text-xs">140269222</span></div></div></div></form></div><div class="coco-row mb-[2px] flex-row text-f-text-secondary css-1neak7x"><div class="coco-col coco-col-15 css-1neak7x"><div class="inline-flex items-center after:content-[':'] text-xs font-semibold">Output Dimension</div></div><div class="coco-col css-1neak7x" style="flex: 1 1 auto;"><div class="flex h-full items-center gap-3"><span class="text-f-text-tertiary text-xs"><span>736 x 544</span></span></div></div></div><div><div class="coco-row coco-row-no-wrap my-px flex items-center transition-all duration-300 ease-in-out css-1neak7x"><div class="coco-col coco-col-15 css-1neak7x"><div class="text-f-text-secondary text-xs font-semibold">Created in:</div></div><div class="coco-col min-w-0 css-1neak7x" style="flex: 1 1 auto; min-width: 0px;"><div class="text-f-text-tertiary truncate text-xs bg-f-bg-active break-all rounded px-1.5 py-0.5" title="Default Project">Default Project</div></div></div></div></div></div><div class="flex flex-col gap-y-5"><div class="flex-wrap items-center grid grid-cols-2 gap-2"><button type="button" class="coco-btn css-1neak7x coco-btn-default coco-btn-color-default coco-btn-variant-outlined !bg-f-bg-layout text-f-text-tertiary flex items-center gap-x-1 !border-none !px-2 hover:!bg-f-bg-hover h-8 justify-start text-xs"><span class="coco-btn-icon"><span class="inline-flex justify-center items-center i-cus--pol-change size-4"></span></span><div class="flex w-full items-center gap-x-1"><span>Regenerate</span></div></button><button type="button" class="coco-btn css-1neak7x coco-btn-default coco-btn-color-default coco-btn-variant-outlined !bg-f-bg-layout text-f-text-tertiary flex items-center gap-x-1 !border-none !px-2 hover:!bg-f-bg-hover h-8 justify-start text-xs"><span class="coco-btn-icon"><span class="inline-flex justify-center items-center i-cus--pol-upscale size-4"></span></span><div class="flex w-full items-center gap-x-1"><span>Upscale</span></div></button><button type="button" class="coco-btn css-1neak7x coco-btn-default coco-btn-color-default coco-btn-variant-outlined !bg-f-bg-layout text-f-text-tertiary flex items-center gap-x-1 !border-none !px-2 hover:!bg-f-bg-hover h-8 justify-start text-xs"><span class="coco-btn-icon"><span class="inline-flex justify-center items-center i-cus--pol-video-to-video size-4"></span></span><div class="flex w-full items-center gap-x-1"><span>Video to Video</span></div></button><button type="button" class="coco-btn css-1neak7x coco-btn-default coco-btn-color-default coco-btn-variant-outlined !bg-f-bg-layout text-f-text-tertiary flex items-center gap-x-1 !border-none !px-2 hover:!bg-f-bg-hover h-8 justify-start text-xs"><span class="coco-btn-icon"><span class="inline-flex justify-center items-center i-cus--pol-canvas-line size-4"></span></span><div class="flex w-full items-center gap-x-1"><span>Canvas</span></div></button><button type="button" class="coco-btn css-1neak7x coco-btn-default coco-btn-color-default coco-btn-variant-outlined !bg-f-bg-layout text-f-text-tertiary flex items-center gap-x-1 !border-none !px-2 hover:!bg-f-bg-hover h-8 justify-start text-xs"><span class="coco-btn-icon"><span class="inline-flex justify-center items-center i-cus--pol-sound size-4"></span></span><div class="flex w-full items-center gap-x-1"><span>Add Sound Effects</span></div></button><button type="button" class="coco-btn css-1neak7x coco-btn-default coco-btn-color-default coco-btn-variant-outlined !bg-f-bg-layout text-f-text-tertiary flex items-center gap-x-1 !border-none !px-2 hover:!bg-f-bg-hover h-8 justify-start text-xs"><span class="coco-btn-icon"><span class="inline-flex justify-center items-center i-cus--pol-lip-sync size-4"></span></span><div class="flex w-full items-center gap-x-1"><span>Lip Sync</span></div></button></div><div class="flex w-full items-center gap-2"><div class="flex cursor-pointer items-center justify-center gap-x-1 rounded-full bg-opacity-15 px-2 py-1 text-xs hover:bg-opacity-40 bg-f-bg-layout hover:bg-f-bg-hover text-f-text-tertiary hover:text-f-primary group"><span class="order-2">1</span><span class="inline-flex justify-center items-center size-[18px] i-cus--pol-heart-solid text-f-primary order-1 group-hover:text-f-primary"></span></div><div class="flex flex-wrap items-center gap-2 gap-x-1.5"><div class="text-f-text-tertiary hover:bg-f-bg-hover hover:text-f-primary flex cursor-pointer items-center gap-x-1 rounded p-1 transition-all"><span class="inline-flex justify-center items-center size-6 md:size-4 i-cus--pol-link !size-[18px]"></span></div><div class="coco-dropdown-trigger [&amp;&gt;*:first-child]:w-full"><div class="text-f-text-tertiary hover:bg-f-bg-hover hover:text-f-primary flex cursor-pointer items-center gap-x-1 rounded p-1 transition-all"><span class="inline-flex justify-center items-center size-6 md:size-4 i-cus--pol-share-line !size-[18px]"></span></div></div><div class="coco-dropdown-trigger [&amp;&gt;*:first-child]:w-full"><div class="text-f-text-tertiary hover:bg-f-bg-hover hover:text-f-primary flex cursor-pointer items-center gap-x-1 rounded p-1 transition-all"><span class="inline-flex justify-center items-center size-6 md:size-4 i-com--more !size-[18px]"></span></div></div></div><button type="button" class="coco-btn css-1neak7x coco-btn-primary coco-btn-color-primary coco-btn-variant-solid ms-auto flex h-7 items-center justify-center gap-x-1 px-3 text-sm font-semibold md:px-3"><span>Create Similar Video</span></button></div></div></div></div></div></div></div>


我给你竞争对手的ui交互流程:
1.用户去/my-creations页面,显示一些列视频(卡片形式展示);如图1:'/home/<USER>/app/video-generator/video-generator-web/doc/82.png'
2.用户单击第一个视频卡片,打开如图所示模态ui:'/home/<USER>/app/video-generator/video-generator-web/doc/83.png', 打开这个页面是没有去call api的,因为数据在列表的时候都拿到了,用不着再去call api;另外浏览器的地址变为https://pollo.ai/v/cmdqztsph014mh26j9bkookl7?from=my-creations,但是其实可以看到背后的页面还是原来的/my-creations页面; 这个模态对话框的左侧还有一对上下按钮,可以上下翻看不同的视频纤细信息,浏览器url中的v/后面的视频id也跟着变化;
3.有点奇怪的是,直接复制https://pollo.ai/v/cmdqztsph014mh26j9bkookl7?from=my-creations,在浏览器中打开的页面却是如图:'/home/<USER>/app/video-generator/video-generator-web/doc/85.png'所示的样子,多了顶部导航栏;这样访问是call了api 去拿了视频详细信息了.

4.参数除了有?from=my-creations外,还有这种?source=share;

你帮我分析竞争对手是怎么实现的,详细分析,给出方案和实施计划,写入一个markdown文件,不要动代码,ultrathink.


我给你竞争对手的ui交互流程:                                                        │
│   1.用户去/my-creations页面,显示一些列视频(卡片形式展示);如图1:'/home/<USER>/app/v  │
│   ideo-generator/video-generator-web/doc/82.png'                                     │
│   2.用户单击第一个视频卡片,打开如图所示模态ui:'/home/<USER>/app/video-generator/vi  │
│   deo-generator-web/doc/83.png' , 打开这个页面是没有去call                           │
│   api的,因为数据在列表的时候都拿到了,用不着再去call                                  │
│   api;另外浏览器的地址变为https://pollo.ai/v/cmdqztsph014mh26j9bkookl7?from=my-crea  │
│   tions,但是其实可以看到背后的页面还是原来的/my-creations页面;                       │
│   这个模态对话框的左侧还有一对上下按钮,可以上下翻看不同的视频纤细信息,浏览器url中的  │
│   v/后面的视频id也跟着变化;                                                          │
│   3.有点奇怪的是,直接复制https://pollo.ai/v/cmdqztsph014mh26j9bkookl7?from=my-creat  │
│   ions,在浏览器中打开的页面却是如图:'/home/<USER>/app/video-generator/video-genera  │
│   tor-web/doc/85.png' 所示的样子,多了顶部导航栏;这样访问是call了api 去拿了视频详细信息了.                                     │
│                                                                                      │
│   你帮我分析竞争对手是怎么实现的,详细分析,给出方案和实施计划,写入一个markdown文件,   │
│   不要动代码,ultrathink.   


现在来优化模块款内容区域的左侧视频组件部分:    
1.竞争对手的行为是,鼠标hover视频的时候开始播放,并显示视频控制栏;鼠标移出去就停止播放,隐藏控制栏;再次hover的时候从上次暂停的地方开始播放;
