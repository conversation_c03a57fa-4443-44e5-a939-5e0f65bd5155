// 模型api提供商价格
model ApiPricingEntry {
  id              String            @id
  apiProviderCode   String
  modelId       String
  unit            PricingUnit
  pricePerUnit    Decimal          @db.Decimal(10, 4)
  createdAt       DateTime         
  updatedAt       DateTime         

  @@index([apiProviderCode])
  @@index([modelId])
  @@map("c_api_pricing_entry")
}

enum PricingUnit {
  second
  video
  megapixel
  image
}