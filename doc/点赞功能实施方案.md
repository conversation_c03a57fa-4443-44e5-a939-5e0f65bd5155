# 点赞功能集成实施方案

## 概述

本文档详细分析了在 GenerationItemCard 界面集成点赞功能的技术方案和实施步骤。实现心形图标的点击点赞功能，仅关注点赞计数，不追踪具体用户。

## 问题分析

### 现状调研

1. **现有UI结构**
   - `GenerationItemCard` 组件位于：`apps/web/modules/marketing/shared/components/GenerationItemCard/GenerationItemCard.tsx`
   - 组件中已有两个图标：心形图标（Heart）和星形图标（Star）
   - 目前心形图标绑定的是 `handleFavorite` 函数，对应收藏功能

2. **现有API结构**
   - 已存在点赞API：
     - `POST /api/generations/:generationId/likes` - 点赞（将改为toggle）
     - `DELETE /api/generations/:generationId/likes` - 取消点赞（将废弃）
   - 已存在收藏API：
     - `POST /api/generations/:generationId/favorite` - 收藏
     - `DELETE /api/generations/:generationId/favorite` - 取消收藏

3. **数据库模型**
   - `Generation` 模型包含：
     - `starNum: Int` - 点赞总数
     - `isLike: Boolean` - 点赞状态（存在用户状态问题，见下文分析）
     - `favorite: Boolean` - 收藏状态（用户个人收藏标志）

### 核心问题识别

**现有`isLike`字段的局限性**：
- `isLike`是Generation表中的单个布尔值，无法区分不同用户的点赞状态
- 如果用户A点赞了某个视频，用户B看到的也是已点赞状态
- 无法实现"用户点赞过的视频再次查看时显示红色心形图标"
- 点击心形图标时无法判断应该执行点赞还是取消点赞操作

### 功能需求明确

1. **点赞功能**：心形图标 - 用户个人点赞状态切换
   - 未点赞时点击：创建点赞关系 + `starNum+1` + 显示红色心形
   - 已点赞时点击：删除点赞关系 + `starNum-1` + 显示白色心形
   - 用户下次查看时：根据个人点赞状态显示对应颜色

2. **收藏功能**：星形图标 - 个人收藏功能，点击切换 `favorite` 布尔值

### 🎯 关键需求

1. **初始状态显示**：GenerationItemCard需要知道`isLikedByUser`状态来正确显示心形图标颜色
2. **智能切换**：使用`POST /likes/toggle`端点，系统自动判断执行点赞还是取消点赞
3. **状态持久化**：用户点赞状态在数据库中持久保存，刷新页面后状态不丢失

## 解决方案设计

### 技术方案

**推荐方案：创建用户点赞关联表 + 智能单一API端点**

优势：
- 支持用户个人点赞状态追踪
- 实现正确的UI状态显示
- 支持点赞状态持久化
- **API设计极简**：只需一个POST端点处理所有操作
- **智能判断**：系统自动检测用户状态并执行相应操作
- 为未来功能扩展奠定基础

缺点：
- 需要数据库结构变更
- API逻辑需要调整

### 🎯 核心设计亮点：智能Toggle端点

```typescript
// 使用语义明确的toggle端点
POST /api/generations/{id}/likes/toggle

// 前端调用非常简单 - 无需判断当前状态
onClick={() => toggleLike(generationId)}

// 后端智能处理：
// 1. 检查用户是否已点赞
// 2. 如果已点赞 → 取消点赞 (删除记录 + starNum-1)  
// 3. 如果未点赞 → 执行点赞 (创建记录 + starNum+1)
// 4. 返回最新状态供前端更新UI
```

**优势**：
- ✅ **语义明确**：`toggle`清楚表达切换操作的意图
- ✅ **前端简单**：无需维护点赞状态逻辑
- ✅ **后端统一**：避免状态不一致问题
- ✅ **RESTful友好**：POST执行操作是标准实践
- ✅ **减少请求**：无需先查询状态再操作

### 架构设计

```
用户点赞功能架构：

1. 数据层：新增 GenerationLike 关联表
   ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
   │     User        │    │ GenerationLike  │    │   Generation    │
   │                 │    │                 │    │                 │
   │ id (PK)         │◄──►│ userId (FK)     │    │ id (PK)         │
   │ ...             │    │ generationId(FK)│◄──►│ starNum (Int)   │
   └─────────────────┘    │ createdAt       │    │ favorite (Bool) │
                          └─────────────────┘    │ ...             │
                                                 └─────────────────┘

2. API层：智能点赞切换（Toggle端点）
   - POST /likes/toggle：自动检查用户状态 → 智能切换点赞/取消点赞
   - 语义明确的toggle操作，符合RESTful实践
   - 返回用户的点赞状态和总计数

3. UI层：状态驱动显示
   - 心形图标：根据 isLikedByUser 显示红色/白色
   - 显示 starNum 作为点赞总数
   - 星形图标：收藏功能（个人favorite状态）
```

## 实施步骤

### 第一阶段：数据库结构扩展

#### 1.1 创建 GenerationLike 关联表

**文件**：`packages/database/prisma/schema.prisma`

```prisma
model GenerationLike {
  id           String      @id @default(cuid())
  userId       String
  generationId String
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  
  // 关联关系
  user         User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  generation   Generation  @relation(fields: [generationId], references: [id], onDelete: Cascade)
  
  // 确保用户对同一个generation只能点赞一次
  @@unique([userId, generationId])
  @@index([userId])
  @@index([generationId])
  @@map("generation_like")
}
```

#### 1.2 更新现有模型

```prisma
// 更新 Generation 模型
model Generation {
  // ... 现有字段 ...
  
  // 新增关联关系
  likes        GenerationLike[]
  
  // ... 现有关联关系 ...
}

// 更新 User 模型  
model User {
  // ... 现有字段 ...
  
  // 新增关联关系
  generationLikes GenerationLike[]
  
  // ... 现有关联关系 ...
}
```

#### 1.3 执行数据库迁移

```bash
# 生成迁移文件
pnpm prisma migrate dev --name add-generation-like-table

# 应用迁移
pnpm prisma generate
```

### 第二阶段：后端API更新

#### 2.1 更新 GenerationsManager

**文件**：`packages/jobs/src/lib/generations-manager.ts`

```typescript
/**
 * 用户点赞切换（智能判断点赞/取消点赞）
 */
static async toggleLikeGeneration(
  generationId: string, 
  userId: string
): Promise<{
  id: string;
  starNum: number;
  isLiked: boolean;
  action: 'liked' | 'unliked';
} | null> {
  try {
    // 1. 检查Generation是否存在且未删除
    const generation = await db.generation.findUnique({
      where: { id: generationId },
      select: { id: true, starNum: true, deleted: true }
    });

    if (!generation || generation.deleted) {
      return null;
    }

    // 2. 检查用户是否已经点赞
    const existingLike = await db.generationLike.findUnique({
      where: {
        userId_generationId: { userId, generationId }
      }
    });

    if (existingLike) {
      // 用户已点赞，执行取消点赞
      const result = await db.$transaction(async (tx) => {
        // 删除点赞记录
        await tx.generationLike.delete({
          where: { id: existingLike.id }
        });

        // starNum-1
        const updatedGeneration = await tx.generation.update({
          where: { id: generationId },
          data: { starNum: { decrement: 1 } },
          select: { id: true, starNum: true }
        });

        return updatedGeneration;
      });

      logger.info('[GenerationsManager] User unliked generation:', {
        userId, generationId, newStarNum: result.starNum
      });

      return {
        ...result,
        isLiked: false,
        action: 'unliked'
      };

    } else {
      // 用户未点赞，执行点赞
      const result = await db.$transaction(async (tx) => {
        // 创建点赞记录
        await tx.generationLike.create({
          data: { userId, generationId }
        });

        // starNum+1
        const updatedGeneration = await tx.generation.update({
          where: { id: generationId },
          data: { starNum: { increment: 1 } },
          select: { id: true, starNum: true }
        });

        return updatedGeneration;
      });

      logger.info('[GenerationsManager] User liked generation:', {
        userId, generationId, newStarNum: result.starNum
      });

      return {
        ...result,
        isLiked: true,
        action: 'liked'
      };
    }

  } catch (error) {
    logger.error('[GenerationsManager] Error toggling like:', {
      userId, generationId,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

/**
 * 获取Generations列表（包含用户点赞状态）
 * 🔑 关键方法：为GenerationItemCard提供正确的心形图标显示状态
 */
static async getGenerationsWithUserLikeStatus(
  whereCondition: any,
  pagination: { page: number; pageSize: number },
  userId: string
): Promise<{
  generations: any[];
  total: number;
}> {
  const [generations, total] = await Promise.all([
    db.generation.findMany({
      where: whereCondition,
      include: {
        user: { select: { id: true, name: true, image: true } },
        job: { select: { id: true, type: true, status: true } },
        // 🎯 核心：JOIN查询当前用户的点赞状态
        likes: {
          where: { userId }, // 只查询当前用户是否点赞过
          select: { id: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip: (pagination.page - 1) * pagination.pageSize,
      take: pagination.pageSize,
    }),
    db.generation.count({ where: whereCondition })
  ]);

  // 转换数据：为每个generation添加isLikedByUser字段
  const generationsWithLikeStatus = generations.map(gen => ({
    ...gen,
    isLikedByUser: gen.likes.length > 0, // 🔴 有记录=已点赞，⚪ 无记录=未点赞
    likes: undefined // 清除敏感信息，只保留isLikedByUser
  }));

  return {
    generations: generationsWithLikeStatus,
    total
  };
}
```

#### 2.2 更新 GenerationService

**文件**：`packages/api/src/routes/generations/lib/generation-service.ts`

```typescript
/**
 * 用户点赞切换（RESTful风格）
 */
static async toggleLikeGeneration(
  user: Session["user"], 
  generationId: string
): Promise<{
  generationId: string;
  likeCount: number;
  isLiked: boolean;
} | null> {
  try {
    const result = await GenerationsManager.toggleLikeGeneration(generationId, user.id);

    if (!result) {
      return null;
    }

    console.log(`[GenerationService] User ${user.id} ${result.isLiked ? 'liked' : 'unliked'} generation ${generationId}, new count: ${result.starNum}`);

    return {
      generationId: result.id,
      likeCount: result.starNum,
      isLiked: result.isLiked
    };

  } catch (error) {
    console.error('[GenerationService] Error toggling like:', error);
    throw error;
  }
}

/**
 * 获取Generations列表（包含用户点赞状态）
 */
static async getGenerations(
  user: Session["user"], 
  params: GetGenerationsRequest
): Promise<GetGenerationsResponse | GetGenerationsErrorResponse> {
  try {
    // 1. 权限检查
    GenerationsManager.checkPermissions(user, params);
    
    // 2. 构建查询条件
    const whereCondition = GenerationsManager.buildWhereCondition(params, user);
    
    // 3. 🔑 执行查询（包含用户点赞状态）
    const { generations, total } = await GenerationsManager.getGenerations(
      whereCondition,
      { 
        page: params.page, 
        pageSize: params.pageSize 
      },
      user.id // 传递当前用户ID以获取点赞状态
    );

    // 4. 处理数据
    const uniqueJobs = GenerationsManager.extractUniqueJobs(generations);
    const uniqueUsers = GenerationsManager.extractUniqueUsers(generations);
    const cleanGenerations = GenerationsManager.cleanGenerationsData(generations);

    // 5. 构建分页信息
    const pagination = GenerationsManager.buildPaginationInfo(params.page, params.pageSize, total);

    return {
      success: true,
      data: {
        generations: cleanGenerations, // 包含isLiked字段
        jobs: uniqueJobs,
        users: uniqueUsers,
        pagination
      }
    };

  } catch (error) {
    console.error('[GenerationService] Error fetching generations:', error);
    
    if (error instanceof Error) {
      if (error.message === 'Access denied') {
        return { success: false, error: 'Access denied' };
      }
      if (error.message.includes('Invalid')) {
        return { success: false, error: error.message };
      }
    }
    
    return { success: false, error: 'Internal server error' };
  }
}
```

#### 2.3 更新 API 路由

**文件**：`packages/api/src/routes/generations/router.ts`

```typescript
// GET /api/generations - 获取生成内容列表（更新以包含用户点赞状态）
.get(
  "/",
  authMiddleware,
  describeRoute({
    tags: ["Generations"],
    summary: "Get generations list with user like status",
    description: "Get paginated list of generations with user's like status. Each generation includes isLikedByUser field for proper heart icon display.",
    // ... 参数描述保持不变 ...
  }),
  validator("query", GetGenerationsRequestSchema),
  async (c) => {
    const user = c.get("user");
    const params = c.req.valid("query");

    console.log(`[GenerationsAPI] User ${user.id} requesting generations with like status:`, {
      params,
      userRole: user.role,
    });

    // 🔑 使用更新后的getGenerations方法，包含用户点赞状态
    const result = await GenerationService.getGenerations(user, params);
    
    if (result.success) {
      console.log(`[GenerationsAPI] Retrieved ${result.data.generations.length} generations with like status for user ${user.id}`);
      return c.json(result, 200);
    } else {
      console.warn(`[GenerationsAPI] Error for user ${user.id}:`, result.error);
      
      if (result.error === 'Access denied') {
        return c.json(result, 403);
      } else if (result.error.includes('Invalid')) {
        return c.json(result, 400);
      } else {
        return c.json(result, 500);
      }
    }
  }
)

// POST /api/generations/:generationId/likes/toggle - 点赞切换（RESTful风格）
.post(
  "/:generationId/likes/toggle",
  authMiddleware,
  describeRoute({
    tags: ["Generations"],
    summary: "Toggle like status for a generation",
    description: "Toggle like status for a specific generation. If the user has already liked the generation, it will be unliked. If not liked, it will be liked. This is a unified endpoint that replaces separate like/unlike operations.",
    parameters: [
      {
        name: "generationId",
        in: "path",
        description: "Generation ID",
        schema: { type: "string" },
        required: true,
      },
    ],
    responses: {
      200: {
        description: "Like status toggled successfully",
      },
      400: {
        description: "Bad request - Invalid generation ID",
      },
      401: {
        description: "Unauthorized - Authentication required",
      },
      403: {
        description: "Forbidden - Access denied",
      },
      404: {
        description: "Generation not found",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  validator("param", GenerationIdParamSchema),
  async (c) => {
    const user = c.get("user");
    const { generationId } = c.req.valid("param");

    console.log(`[GenerationsAPI] User ${user.id} toggling like for generation ${generationId}`);

    try {
      const result = await GenerationService.toggleLikeGeneration(user, generationId);
      
      if (!result) {
        return c.json({ error: 'Generation not found' }, 404);
      }

      console.log(`[GenerationsAPI] Generation ${generationId} like toggled successfully, new state: ${result.isLiked ? 'liked' : 'unliked'}, count: ${result.likeCount}`);
      
      // RESTful 风格：直接返回资源数据
      return c.json(result, 200);
      
    } catch (error) {
      console.warn(`[GenerationsAPI] Error toggling like for generation ${generationId}:`, error);
      
      if (error instanceof Error) {
        if (error.message.includes('Cannot like') || error.message.includes('Cannot unlike')) {
          return c.json({ error: error.message }, 400);
        }
      }
      
      return c.json({ error: 'Internal server error' }, 500);
    }
  }
)

// 🗑️ DELETE端点已废弃
// POST /likes/toggle 端点已经处理了所有点赞/取消点赞逻辑
```

### 第三阶段：前端UI更新

#### 3.1 更新 GenerationItemCard 组件

**文件**：`apps/web/modules/marketing/shared/components/GenerationItemCard/GenerationItemCard.tsx`

```typescript
export interface GenerationItemCardProps {
  generation: GenerationData;
  // ... 现有props ...
  onToggleLike?: (id: string) => void;  // 更新：点赞切换回调
  // ... 其他props ...
}

export function GenerationItemCard({
  generation,
  onPublish,
  onFavorite,
  onToggleLike,  // 更新
  // ... 其他props ...
}: GenerationItemCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  const actions = {
    onPublish,
    onFavorite,
    onToggleLike,  // 更新
    // ... 其他actions ...
  };

  const { 
    handleFavorite, 
    handleToggleLike,  // 更新
    handleCreateSimilar 
  } = useCardActions(generation, actions);

  // ... 其他逻辑保持不变 ...

  return (
    <div className="...">
      {/* ... 其他内容 ... */}
      
      {/* 右侧：三个点菜单 + 心形图标并排 */}
      <div className="flex items-center gap-x-2">
        <ActionMenu
          generation={generation}
          actions={actions}
          visible={isHovered}
        />

        {/* 心形点赞按钮 - 根据用户点赞状态显示颜色 */}
        <button
          type="button"
          className="flex cursor-pointer items-center justify-center p-1 transition-all hover:bg-white/20 rounded"
          onClick={handleLike}
          aria-label={generation.isLiked ? "Unlike" : "Like"}
        >
          <Heart
            className={cn(
              "size-5 transition-colors stroke-2",
              generation.isLiked
                ? "fill-red-500 text-red-500"    // 已点赞：红色填充
                : "text-white fill-none",        // 未点赞：白色轮廓
            )}
          />
          {/* 显示点赞数 */}
          {generation.starNum > 0 && (
            <span className="ml-1 text-xs text-white">
              {generation.starNum}
            </span>
          )}
        </button>
      </div>

      {/* 右上角收藏按钮保持不变 */}
      <button
        type="button"
        className={cn(
          "cursor-pointer items-center gap-x-1 rounded p-1 transition-all hover:bg-white/20 absolute right-2 top-2",
          isHovered ? "flex" : "hidden",
        )}
        onClick={handleFavorite}
        aria-label={generation.favorite ? "Remove from favorites" : "Add to favorites"}
      >
        <Star
          className={cn(
            "size-4 transition-colors stroke-2",
            generation.favorite
              ? "fill-yellow-500 text-yellow-500"
              : "text-white fill-none",
          )}
        />
      </button>

      {/* ... 其他内容 ... */}
    </div>
  );
}
```

#### 3.2 更新 useCardActions Hook

**文件**：`apps/web/modules/marketing/shared/components/GenerationItemCard/hooks/useCardActions.ts`

```typescript
export function useCardActions(
  generation: GenerationData,
  actions: CardActions,
) {
  const handleFavorite = useCallback(() => {
    actions.onFavorite?.(generation.id);
  }, [actions.onFavorite, generation.id]);

  // 更新：处理点赞切换逻辑
  const handleToggleLike = useCallback(() => {
    actions.onToggleLike?.(generation.id);
  }, [actions.onToggleLike, generation.id]);

  // ... 其他handlers保持不变 ...

  return {
    handleFavorite,
    handleToggleLike,  // 更新
    // ... 其他handlers ...
  };
}
```

#### 3.3 更新类型定义

**文件**：`apps/web/modules/marketing/shared/components/GenerationItemCard/types.ts`

```typescript
export interface GenerationData {
  id: string;
  userId: string;
  // ... 现有字段 ...
  starNum: number;           // 点赞总数
  favorite: boolean;         // 收藏状态  
  isLiked: boolean;          // 当前用户是否点赞过此generation
  // ... 其他字段 ...
}

export interface CardActions {
  // ... 现有actions ...
  onLike?: (id: string) => void;  // 点赞切换回调
  // ... 其他actions ...
}
```

#### 3.4 创建点赞API Hook

**文件**：`apps/web/modules/shared/hooks/useGenerationToggleLike.ts`

```typescript
import { useState, useCallback } from 'react';

interface ToggleLikeResponse {
  generationId: string;
  likeCount: number;
  isLiked: boolean;
}

export function useGenerationToggleLike() {
  const [loading, setLoading] = useState(false);

  const toggleLike = useCallback(async (generationId: string): Promise<ToggleLikeResponse | null> => {
    setLoading(true);
    try {
      // 🔄 使用RESTful toggle端点
      const response = await fetch(`/api/generations/${generationId}/likes/toggle`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error toggling like:', errorData.error);
        return null;
      }

      const result = await response.json();
      return result; // 直接返回资源数据 {generationId, likeCount, isLiked}
    } catch (error) {
      console.error('Error toggling like:', error);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    toggleLike,
    loading
  };
}
```

### 第四阶段：父组件集成

#### 4.1 在父组件中集成点赞切换功能

```typescript
import { useGenerationToggleLike } from '@/modules/shared/hooks/useGenerationToggleLike';

export function GenerationsList() {
  const [generations, setGenerations] = useState<GenerationData[]>([]);
  const { toggleLike, loading } = useGenerationToggleLike();

  // 处理点赞切换
  const handleToggleLike = useCallback(async (generationId: string) => {
    const result = await toggleLike(generationId);
    
    if (result) {
      // 更新本地状态
      setGenerations(prev => 
        prev.map(gen => 
          gen.id === generationId 
            ? { 
                ...gen, 
                starNum: result.likeCount,
                isLiked: result.isLiked 
              }
            : gen
        )
      );
    } else {
      console.error('Failed to toggle like');
    }
  }, [toggleLike]);

  return (
    <div>
      {generations.map(generation => (
        <GenerationItemCard
          key={generation.id}
          generation={generation}
          onLike={handleToggleLike}
          onFavorite={handleFavorite} // 收藏功能保持不变
          // ... 其他props ...
        />
      ))}
    </div>
  );
}
```

## 测试计划

### 功能测试

1. **点赞功能测试**
   - 点击心形图标，验证starNum数值增加
   - 验证点赞数显示在心形图标旁边
   - 测试API调用和响应

2. **收藏功能测试**
   - 点击星形图标，验证favorite状态切换
   - 验证收藏状态的视觉反馈（填充颜色变化）

3. **交互测试**
   - 验证hover状态下按钮的显示
   - 测试loading状态
   - 测试错误处理

### 集成测试

1. **UI更新测试**
   - 点赞后验证本地状态更新
   - 刷新页面验证数据持久化
   - 多用户同时点赞同一个generation

2. **API测试**
   - 测试认证中间件
   - 测试错误响应
   - 测试网络异常处理

## 部署注意事项

### 无数据库迁移

由于使用现有API和数据结构，无需数据库迁移，降低了部署风险。

### 性能考虑

1. **防止重复点击**：
   - 在API调用期间禁用按钮
   - 实现简单的防抖机制

2. **乐观更新**：
   - 点击时立即更新UI
   - API失败时回滚状态

### 监控

- 监控点赞API的调用频率
- 记录点赞失败的情况
- 监控starNum字段的异常变化

## 数据迁移注意事项

### 现有isLike字段处理

由于现有的`Generation.isLike`字段无法提供用户级别的状态信息，建议：

1. **保留字段**：暂时保留`isLike`字段以确保向后兼容
2. **数据清理**：如果需要，可以基于业务逻辑清理现有的isLike数据
3. **逐步迁移**：新功能使用GenerationLike表，旧字段逐步废弃

### 性能考虑

1. **查询优化**：
   - 为GenerationLike表的复合键添加索引
   - 使用批量查询减少数据库访问
   - 考虑使用Redis缓存热门内容的点赞状态

2. **数据库连接**：
   - 监控数据库连接池使用情况
   - 优化JOIN查询性能

## 后续优化建议

### 用户体验优化

1. **视觉反馈**：
   - 点击时添加心形图标的动画效果（如放大、颜色渐变）
   - 显示点赞数的动态变化动画
   - 添加点赞成功的微交互反馈

2. **防滥用机制**：
   - 添加点赞频率限制（如每分钟最多点赞10次）
   - 实现简单的防抖机制避免重复点击
   - 考虑IP或设备限制防止恶意刷赞

3. **离线支持**：
   - 实现乐观更新，点击立即更新UI
   - 网络恢复时同步点赞状态
   - 处理离线期间的操作冲突

### 功能扩展

1. **实时更新**：
   - 使用WebSocket实现点赞数的实时同步
   - 当其他用户点赞时实时更新计数
   - 支持多设备间的状态同步

2. **点赞历史**：
   - 提供用户查看自己点赞历史的功能
   - 支持按时间、类型筛选点赞记录
   - 实现点赞内容的快速访问

3. **社交功能**：
   - 显示"谁点赞了这个视频"
   - 实现点赞排行榜
   - 添加点赞通知功能

### 性能优化

1. **缓存策略**：
   - 使用Redis缓存用户最近的点赞状态
   - 缓存热门内容的点赞数和状态
   - 实现缓存预热和失效机制

2. **数据库优化**：
   - 定期分析查询性能
   - 考虑读写分离
   - 实现数据归档策略

3. **API优化**：
   - 实现GraphQL支持，减少数据传输
   - 支持批量点赞操作
   - 添加API限流机制

## RESTful API设计升级

在初始实现的基础上，我们进一步将API设计从RPC风格升级为RESTful风格，以提供更好的API体验和标准化：

### API响应格式对比

#### 升级前（RPC风格）
```typescript
// 成功响应
{
  success: true,
  data: {
    generationId: "gen_123",
    likeCount: 42,
    isLiked: true,
    action: "liked"
  }
}

// 错误响应
{
  success: false,
  error: "Generation not found"
}
```

#### 升级后（RESTful风格）
```typescript
// 成功响应 (HTTP 200)
{
  generationId: "gen_123",
  likeCount: 42,
  isLiked: true
}

// 错误响应 (HTTP 404/500)
{
  error: "Generation not found"
}
```

### 主要改进

1. **响应格式简化**：
   - 移除了`success`包装字段
   - 直接返回资源数据
   - 使用HTTP状态码表示成功/失败

2. **错误处理标准化**：
   - 通过HTTP状态码传递错误类型
   - 简化的错误响应格式
   - 符合REST API设计原则

3. **类型定义优化**：
   - 移除RPC风格的联合类型
   - 使用更直接的类型定义
   - 提高代码可读性和维护性

### 服务层更新

```typescript
// 升级后的GenerationService.toggleLikeGeneration方法
static async toggleLikeGeneration(
  user: Session["user"], 
  generationId: string
): Promise<{
  generationId: string;
  likeCount: number;
  isLiked: boolean;
} | null> {
  // 直接返回资源数据或null，抛出异常而非返回错误对象
}
```

### 前端适配

```typescript
// 升级后的API调用处理
const result = await toggleLike(generationId);

if (result) {
  // 直接使用资源数据，无需检查success字段
  setGenerations(prev => 
    prev.map(gen => 
      gen.id === generationId 
        ? { 
            ...gen, 
            starNum: result.likeCount,
            isLiked: result.isLiked 
          }
        : gen
    )
  );
} else {
  console.error('Failed to toggle like');
}
```

## 总结

**完整方案特点**：

1. **用户状态隔离**：每个用户的点赞状态独立，解决了isLike字段的用户状态问题
2. **正确的UI反馈**：
   - 已点赞：心形图标显示红色填充
   - 未点赞：心形图标显示白色轮廓
   - 用户下次查看时状态持久化显示
3. **智能切换逻辑**：**仅需一个API端点**，系统自动判断用户是否已点赞并执行相应操作
4. **数据完整性**：通过数据库约束确保用户不能重复点赞
5. **功能清晰**：
   - 心形图标 = 点赞功能（用户个人状态 + 总计数）
   - 星形图标 = 收藏功能（用户个人状态）
6. **RESTful API设计**：符合REST标准的API响应格式和错误处理

**核心改进**：
1. **解决了用户状态问题**：通过GenerationLike关联表追踪每个用户的点赞状态
2. **实现了状态持久化**：用户点赞过的视频再次查看时显示红色心形
3. **提供了智能切换**：使用`POST /likes/toggle`端点，系统自动判断用户状态并执行点赞/取消点赞
4. **API设计现代化**：从RPC风格升级为RESTful风格，使用HTTP状态码和标准错误处理
5. **列表查询优化**：GET接口JOIN查询用户点赞状态，为UI提供`isLiked`字段

**实施重点**：
- 创建GenerationLike关联表追踪用户点赞关系
- 更新GET `/api/generations`接口，JOIN查询用户点赞状态
- 实现POST `/api/generations/:id/likes/toggle`智能切换端点（RESTful风格）
- 修改前端UI根据`isLiked`正确显示心形图标颜色
- 实现点赞切换后的本地状态更新

此方案完全解决了你提出的核心需求：**点赞过的视频下次看到时心形图是红色的**，同时提供了完整的点赞功能体验和现代化的RESTful API设计。