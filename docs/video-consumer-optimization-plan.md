# Video Consumer 优化详细设计方案

## 📋 方案概述

基于现有 `apps/consumer` 代码分析，设计分阶段的性能优化方案，主要针对视频上传处理流程的瓶颈进行优化。

## 🎯 阶段1：立即实施优化 (1-2天)

### 1. **动态Worker并发控制**

#### 现状分析
- 当前固定并发数为2 (`WORKER_CONCURRENCY || '2'`)
- 没有根据系统资源动态调整
- 处理能力受限

#### 优化方案
```typescript
// apps/consumer/src/config/worker-config.ts
export class WorkerConfigManager {
  getOptimalConfig(): WorkerConfig {
    const cpuCount = cpus().length;
    const availableMemory = this.getAvailableMemory();
    
    // 基于CPU和内存计算最优并发数
    const cpuBasedConcurrency = Math.floor(cpuCount * 0.75); // 75%利用率
    const memoryBasedConcurrency = Math.floor(availableMemory / (200 * 1024 * 1024)); // 每任务200MB
    
    return {
      concurrency: Math.min(cpuBasedConcurrency, memoryBasedConcurrency, 6), // 最大6
      removeOnComplete: Math.max(20, concurrency * 4),
      removeOnFail: Math.max(10, concurrency * 2),
      stalledInterval: Math.max(10000, 30000 - (concurrency * 2000))
    };
  }
}
```

#### 配置变更
```typescript
// apps/consumer/src/workers/video-processor.ts
constructor() {
  const config = WorkerConfigManager.getInstance().getOptimalConfig();
  
  this.worker = new Worker('video-processing', this.processJob.bind(this), {
    connection: createRedisConnection(),
    concurrency: config.concurrency, // 动态并发数 4-6
    removeOnComplete: config.removeOnComplete,
    removeOnFail: config.removeOnFail,
    stalledInterval: config.stalledInterval,
    maxStalledCount: 1
  });
}
```

#### 预期效果
- **吞吐量提升 150-200%**：并发从2提升到4-6
- **资源利用率提升**：根据系统资源动态调整

---

### 2. **HEAD预检优化上传策略**

#### 现状分析
- 当前直接下载完整视频文件
- 无法提前知道文件大小选择最优策略
- 大文件和小文件使用相同处理流程

#### 优化方案
```typescript
// packages/api/src/lib/services/video-upload.ts
export class VideoUploadService {
  
  /**
   * 预检文件信息
   */
  async preCheckFile(url: string): Promise<FileInfo> {
    const { statusCode, headers } = await request(url, {
      method: 'HEAD',
      headers: { 'User-Agent': 'VideoProcessor/1.0' },
      headersTimeout: 10000,
      dispatcher: this.undiciAgent,
    });

    return {
      size: parseInt(headers['content-length'] || '0'),
      contentType: headers['content-type'] || 'video/mp4',
      isValid: statusCode >= 200 && statusCode < 300,
      supportsRangeRequests: headers['accept-ranges'] === 'bytes'
    };
  }

  /**
   * 智能策略选择
   */
  selectUploadStrategy(fileInfo: FileInfo): UploadStrategy {
    const { size } = fileInfo;
    
    if (size < 10 * 1024 * 1024) {        // < 10MB
      return {
        method: 'direct-stream',
        chunkSize: 0,
        concurrency: 1,
        useMultipart: false
      };
    } else if (size < 100 * 1024 * 1024) { // 10-100MB
      return {
        method: 'chunked-stream',
        chunkSize: 5 * 1024 * 1024,        // 5MB chunks
        concurrency: 2,
        useMultipart: true
      };
    } else {                               // > 100MB
      return {
        method: 'parallel-multipart',
        chunkSize: 10 * 1024 * 1024,       // 10MB chunks
        concurrency: 4,
        useMultipart: true
      };
    }
  }
}
```

#### 流程优化
```typescript
// apps/consumer/src/workers/video-processor.ts
private async processJob(job: Job<VideoProcessingJobData>) {
  const { event } = job.data;
  
  try {
    // 1. 并行执行：预检文件 + 加载处理器
    const [fileInfo, { VideoWebhookProcessor }] = await Promise.all([
      this.preCheckVideoFile(event.resultUrl),
      import('@repo/api/src/lib/services/video-webhook-processor')
    ]);
    
    // 2. 根据文件信息选择最优策略
    const strategy = this.selectOptimalStrategy(fileInfo);
    
    // 3. 使用优化策略处理事件
    const processor = new VideoWebhookProcessor();
    await processor.processEventWithStrategy(event, strategy);
    
  } catch (error) {
    // 错误处理
  }
}
```

#### 预期效果
- **小文件处理提速 60%**：跳过临时文件存储
- **大文件稳定性提升 40%**：提前选择最优策略
- **网络效率提升**：减少无效下载尝试

---

### 3. **Redis连接复用优化**

#### 现状分析
- 每次创建新的Redis连接
- 没有连接池管理
- 连接创建开销较大

#### 优化方案
```typescript
// packages/queue/src/redis-pool.ts
export class RedisConnectionPool {
  private static instance: RedisConnectionPool;
  private connectionPool: Redis[] = [];
  private readonly poolSize: number;
  
  constructor() {
    this.poolSize = parseInt(process.env.REDIS_POOL_SIZE || '8');
    this.initializePool();
  }
  
  private initializePool() {
    for (let i = 0; i < this.poolSize; i++) {
      const redis = this.createConnection();
      this.connectionPool.push(redis);
    }
  }
  
  getConnection(): Redis {
    // 轮询分配连接
    const index = Math.floor(Math.random() * this.connectionPool.length);
    return this.connectionPool[index];
  }
  
  async healthCheck(): Promise<boolean> {
    try {
      await Promise.all(
        this.connectionPool.map(redis => redis.ping())
      );
      return true;
    } catch (error) {
      return false;
    }
  }
}
```

#### Worker改造
```typescript
// apps/consumer/src/workers/video-processor.ts
constructor() {
  const redisPool = RedisConnectionPool.getInstance();
  
  this.worker = new Worker('video-processing', this.processJob.bind(this), {
    connection: redisPool.getConnection(), // 使用池化连接
    // ... 其他配置
  });
}
```

#### 预期效果
- **连接开销减少 80%**：复用现有连接
- **并发性能提升 30%**：减少连接等待时间
- **稳定性提升**：连接池健康检查

---

## 🔄 阶段2：中期实施优化 (3-5天)

### 1. **流式上传消除临时文件**

#### 现状分析
- 当前使用临时文件方式：下载→存储→上传
- 占用大量磁盘空间
- 增加网络延迟

#### 优化方案
```typescript
// packages/api/src/lib/services/video-upload-stream.ts
export class StreamingUploadService extends VideoUploadService {
  
  /**
   * 零拷贝流式上传
   */
  async uploadViaZeroCopyStream(
    sourceUrl: string,
    options: UploadOptions
  ): Promise<VideoUploadResult> {
    
    // 1. 创建Transform流进行实时处理
    const transformStream = new Transform({
      highWaterMark: 64 * 1024, // 64KB缓冲
      transform(chunk, encoding, callback) {
        // 可以在这里添加实时处理逻辑（如压缩、格式转换）
        callback(null, chunk);
      }
    });
    
    // 2. 创建并行流水线
    const downloadStream = await this.createDownloadStream(sourceUrl);
    const uploadStream = this.createUploadStream(s3Key, options);
    
    // 3. 流水线处理：下载→转换→上传
    await pipeline(
      downloadStream,
      transformStream,
      uploadStream
    );
    
    return result;
  }
  
  /**
   * 分片并行流式上传
   */
  async uploadViaParallelChunks(
    sourceUrl: string,
    fileInfo: FileInfo,
    options: UploadOptions
  ): Promise<VideoUploadResult> {
    
    const chunkSize = 10 * 1024 * 1024; // 10MB
    const totalChunks = Math.ceil(fileInfo.size / chunkSize);
    
    // 并行下载和上传分片
    const uploadPromises = [];
    for (let i = 0; i < totalChunks; i++) {
      const start = i * chunkSize;
      const end = Math.min(start + chunkSize - 1, fileInfo.size - 1);
      
      uploadPromises.push(
        this.uploadChunk(sourceUrl, start, end, i, options)
      );
    }
    
    // 控制并发数，避免过载
    const results = await this.executeWithConcurrencyLimit(uploadPromises, 4);
    
    return this.combineChunkResults(results);
  }
}
```

#### 预期效果
- **内存使用减少 70%**：消除临时文件存储
- **处理速度提升 50%**：并行下载上传
- **磁盘IO减少 90%**：直接流式处理

---

### 2. **基于文件大小的批处理**

#### 优化方案
```typescript
// apps/consumer/src/services/batch-processor.ts
export class BatchVideoProcessor {
  
  async processBatchBySize(events: VideoWebhookEvent[]): Promise<BatchResult> {
    
    // 1. 预检所有文件信息
    const fileInfos = await Promise.all(
      events.map(event => this.preCheckFile(event.resultUrl))
    );
    
    // 2. 按文件大小分组
    const grouped = this.groupEventsByFileSize(events, fileInfos);
    
    // 3. 分层处理策略
    const results = await Promise.allSettled([
      // 小文件：高并发批处理
      this.processSmallFilesBatch(grouped.small, { concurrency: 8 }),
      
      // 中等文件：中等并发
      this.processMediumFilesBatch(grouped.medium, { concurrency: 4 }),
      
      // 大文件：低并发，高稳定性
      this.processLargeFilesBatch(grouped.large, { concurrency: 2 })
    ]);
    
    return this.aggregateResults(results);
  }
  
  private groupEventsByFileSize(events: VideoWebhookEvent[], fileInfos: FileInfo[]) {
    const small = [], medium = [], large = [];
    
    events.forEach((event, index) => {
      const size = fileInfos[index].size;
      if (size < 20 * 1024 * 1024) {
        small.push({ event, fileInfo: fileInfos[index] });
      } else if (size < 100 * 1024 * 1024) {
        medium.push({ event, fileInfo: fileInfos[index] });
      } else {
        large.push({ event, fileInfo: fileInfos[index] });
      }
    });
    
    return { small, medium, large };
  }
}
```

#### Worker集成
```typescript
// apps/consumer/src/workers/video-processor.ts
private async processJob(job: Job<VideoProcessingJobData>) {
  
  // 检查是否为批处理任务
  if (job.data.batchMode && job.data.events?.length > 1) {
    const batchProcessor = new BatchVideoProcessor();
    return await batchProcessor.processBatchBySize(job.data.events);
  }
  
  // 单个任务处理
  return await this.processSingleEvent(job.data.event);
}
```

#### 预期效果
- **整体吞吐量提升 200%**：批处理优化
- **资源利用率提升 60%**：分层处理策略
- **小文件处理提速 300%**：高并发批处理

---

### 3. **S3客户端池化**

#### 优化方案
```typescript
// packages/api/src/lib/services/s3-pool.ts
export class S3ClientPool {
  private static instance: S3ClientPool;
  private clientPool: S3Client[] = [];
  private roundRobinIndex = 0;
  
  constructor() {
    const poolSize = parseInt(process.env.S3_POOL_SIZE || '4');
    this.initializePool(poolSize);
  }
  
  private initializePool(size: number) {
    for (let i = 0; i < size; i++) {
      const client = new S3Client({
        endpoint: process.env.S3_ENDPOINT,
        region: process.env.S3_REGION || 'auto',
        credentials: {
          accessKeyId: process.env.S3_ACCESS_KEY_ID!,
          secretAccessKey: process.env.S3_SECRET_ACCESS_KEY!,
        },
        // 每个客户端独立配置
        maxAttempts: 3,
        requestTimeout: 300000,
        retryMode: 'adaptive'
      });
      
      this.clientPool.push(client);
    }
  }
  
  getClient(): S3Client {
    // 轮询分配
    const client = this.clientPool[this.roundRobinIndex];
    this.roundRobinIndex = (this.roundRobinIndex + 1) % this.clientPool.length;
    return client;
  }
  
  async executeWithOptimalClient<T>(
    operation: (client: S3Client) => Promise<T>
  ): Promise<T> {
    let lastError: Error;
    
    // 尝试所有客户端
    for (const client of this.clientPool) {
      try {
        return await operation(client);
      } catch (error) {
        lastError = error as Error;
        continue;
      }
    }
    
    throw lastError!;
  }
}
```

#### 集成到上传服务
```typescript
// packages/api/src/lib/services/video-upload.ts
export class VideoUploadService {
  private s3Pool: S3ClientPool;
  
  constructor() {
    this.s3Pool = S3ClientPool.getInstance();
  }
  
  private async uploadStreamToR2(stream: Readable, s3Key: string, contentType?: string) {
    return await this.s3Pool.executeWithOptimalClient(async (client) => {
      const putCommand = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: s3Key,
        Body: stream,
        ContentType: contentType,
      });
      
      return await client.send(putCommand);
    });
  }
}
```

#### 预期效果
- **并发上传能力提升 300%**：多客户端并行
- **失败恢复能力提升**：客户端故障转移
- **连接复用效率提升 80%**：优化连接管理

---

## 📊 整体性能预期

### 关键指标提升
- **处理吞吐量**: 当前 2 videos/min → 优化后 10-15 videos/min (**400-650%提升**)
- **平均处理时间**: 当前 120s → 优化后 30-45s (**60-75%减少**)
- **内存使用**: 减少 50-70%
- **磁盘IO**: 减少 80-90%
- **错误率**: 减少 40-60%

### 资源使用优化
- **CPU利用率**: 从40%提升到75%
- **网络带宽**: 提升50%利用效率  
- **内存稳定性**: 消除内存泄漏风险

## 🛠️ 实施计划

### 阶段1实施步骤 (1-2天)
1. **Day 1 上午**: 实现动态Worker配置管理
2. **Day 1 下午**: 添加HEAD预检和策略选择
3. **Day 2 上午**: 实现Redis连接池
4. **Day 2 下午**: 测试和性能验证

### 阶段2实施步骤 (3-5天)  
1. **Day 3-4**: 实现流式上传和零拷贝处理
2. **Day 5**: 实现批处理和S3池化
3. **Day 6**: 集成测试和性能调优
4. **Day 7**: 生产环境部署和监控

## 🔍 监控和测试

### 性能监控指标
```typescript
// 关键监控指标
interface PerformanceMetrics {
  avgProcessingTime: number;    // 平均处理时间
  throughputPerMin: number;     // 每分钟处理量  
  errorRate: number;            // 错误率
  memoryUsage: number;          // 内存使用
  queueLength: number;          // 队列长度
  concurrentJobs: number;       // 并发任务数
}
```

### A/B测试计划
- **Phase 1**: 20%流量使用新系统
- **Phase 2**: 50%流量验证稳定性
- **Phase 3**: 100%流量全面切换

这个优化方案将显著提升video consumer的处理能力和稳定性，特别是在高负载场景下的表现。