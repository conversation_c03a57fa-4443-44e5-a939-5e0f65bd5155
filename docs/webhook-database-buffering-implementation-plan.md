# 视频生成 Webhook 数据库缓冲模式实施方案

## 概述

基于业界最佳实践，采用数据库缓冲模式（Database Buffering Pattern）来处理 Replicate 和 Fal 等 AI 服务商的 webhook 回调，确保高可靠性和可扩展性。

## 架构设计

### 核心思想
- **立即响应**: Webhook 接收后立即返回 200，避免服务商重试
- **数据库缓冲**: 将 webhook 事件存储到数据库中
- **异步处理**: 后台进程异步处理存储的事件
- **重试机制**: 处理失败时自动重试，确保最终一致性

### 性能对比

| 指标 | 当前同步模式 | 数据库缓冲模式 |
|------|-------------|---------------|
| Webhook响应时间 | 20-60秒 | ~100ms |
| 可靠性 | 中等 | 高 |
| 可扩展性 | 差 | 优 |
| 错误恢复 | 困难 | 自动重试 |
| 监控能力 | 有限 | 完整 |

## 数据库设计

### VideoWebhookEvent 表

```sql
model VideoWebhookEvent {
  id             String    @id @default(cuid())
  
  // 基本信息
  eventId        String    // 提供商的事件ID（幂等性）
  provider       String    // 提供商名称: replicate, fal
  eventType      String    // 事件类型: generation.completed, generation.failed
  
  // 业务数据
  externalTaskId String    // 提供商的任务ID
  generationId   String?   // 我们的生成ID（可能为空，需要查找）
  userId         String?   // 用户ID（处理时填充）
  
  // Webhook 原始数据
  rawPayload     Json      // 原始 webhook 载荷
  headers        Json      // 请求头信息
  signature      String?   // 签名信息
  
  // 处理状态
  status         VideoWebhookEventStatus @default(pending)
  processedAt    DateTime?
  retryCount     Int       @default(0)
  maxRetries     Int       @default(5)
  
  // 错误信息
  lastError      String?   @db.Text
  errorDetails   Json?     // 详细错误信息
  
  // 时间戳
  receivedAt     DateTime  @default(now())
  nextRetryAt    DateTime?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  
  // 索引优化
  @@unique([provider, eventId]) // 防重复
  @@index([status, nextRetryAt]) // 处理器查询优化
  @@index([provider, externalTaskId]) // 业务查询优化
  @@index([generationId]) // 业务关联查询
  @@index([receivedAt]) // 时间范围查询
  @@index([retryCount, status]) // 重试监控
  @@map("video_webhook_event")
}

enum VideoWebhookEventStatus {
  pending     // 待处理
  processing  // 处理中
  completed   // 已完成
  failed      // 永久失败
  skipped     // 已跳过（如重复事件）
}
```

## 核心服务设计

### 1. VideoWebhookEventManager（事件管理器）

```typescript
export class VideoWebhookEventManager {
  /**
   * 创建新的 webhook 事件记录
   */
  async createEvent(data: CreateVideoWebhookEventData): Promise<VideoWebhookEvent> {
    // 1. 检查是否重复事件（基于 provider + eventId）
    // 2. 提取基本信息（externalTaskId, generationId）
    // 3. 存储到数据库
    // 4. 返回事件记录
  }
  
  /**
   * 获取待处理的事件（供处理器调用）
   */
  async getPendingEvents(limit: number = 10): Promise<VideoWebhookEvent[]> {
    // 查询 status = pending 或 (status = failed AND nextRetryAt <= now)
    // 按 receivedAt ASC 排序，处理最早的事件
  }
  
  /**
   * 标记事件为处理中
   */
  async markAsProcessing(eventId: string): Promise<void> {
    // 原子性更新状态为 processing
  }
  
  /**
   * 标记事件处理成功
   */
  async markAsCompleted(eventId: string): Promise<void> {
    // 更新状态为 completed，设置 processedAt
  }
  
  /**
   * 标记事件处理失败
   */
  async markAsFailed(eventId: string, error: Error): Promise<void> {
    // 1. 增加重试计数
    // 2. 如果未达到最大重试次数，计算下次重试时间（指数退避）
    // 3. 如果达到最大重试次数，标记为永久失败
    // 4. 记录错误信息
  }
  
  /**
   * 清理旧事件（定期任务）
   */
  async cleanupOldEvents(olderThanDays: number = 30): Promise<number> {
    // 删除 30 天前且已完成的事件
  }
}
```

### 2. VideoWebhookProcessor（异步处理器）

```typescript
export class VideoWebhookProcessor {
  private eventManager: VideoWebhookEventManager;
  private uploadService: VideoUploadService;
  private jobService: JobsManager;
  
  /**
   * 处理单个 webhook 事件
   */
  async processEvent(event: VideoWebhookEvent): Promise<void> {
    try {
      // 1. 标记为处理中
      await this.eventManager.markAsProcessing(event.id);
      
      // 2. 解析载荷
      const payload = this.parseEventPayload(event);
      
      // 3. 查找对应的生成记录
      const generation = await this.findGeneration(payload.externalTaskId);
      
      if (!generation) {
        throw new Error(`Generation not found for external task ID: ${payload.externalTaskId}`);
      }
      
      // 4. 更新生成状态
      await this.updateGenerationStatus(generation, payload);
      
      // 5. 如果成功完成，处理视频上传
      if (payload.status === 'succeeded' && payload.resultUrl) {
        await this.handleVideoUpload(generation, payload.resultUrl);
      }
      
      // 6. 标记事件处理成功
      await this.eventManager.markAsCompleted(event.id);
      
      logger.info('[WebhookProcessor] Event processed successfully', {
        eventId: event.id,
        generationId: generation.id,
        status: payload.status
      });
      
    } catch (error) {
      logger.error('[WebhookProcessor] Event processing failed', {
        eventId: event.id,
        error: error.message
      });
      
      // 标记失败，会自动计算重试
      await this.eventManager.markAsFailed(event.id, error);
      throw error;
    }
  }
  
  /**
   * 批量处理待处理事件
   */
  async processPendingEvents(): Promise<void> {
    const events = await this.eventManager.getPendingEvents(10);
    
    // 并行处理多个事件（控制并发数）
    await Promise.allSettled(
      events.map(event => this.processEvent(event))
    );
  }
}
```

### 3. VideoWebhookRetryService（重试服务）

```typescript
export class VideoWebhookRetryService {
  /**
   * 计算下次重试时间（指数退避 + 抖动）
   */
  static calculateNextRetry(retryCount: number): Date {
    // 指数退避：1s, 2s, 4s, 8s, 16s, 32s, 60s (最长1分钟)
    const baseDelay = Math.min(Math.pow(2, retryCount), 60);
    
    // 添加随机抖动，避免雷群效应
    const jitter = Math.random() * 0.3; // ±30%
    const delay = baseDelay * (1 + jitter);
    
    return new Date(Date.now() + delay * 1000);
  }
  
  /**
   * 判断是否应该重试
   */
  static shouldRetry(retryCount: number, maxRetries: number = 5): boolean {
    return retryCount < maxRetries;
  }
  
  /**
   * 获取重试策略配置
   */
  static getRetryConfig(): RetryConfig {
    return {
      maxRetries: 5,
      baseDelay: 1000, // 1秒
      maxDelay: 60000, // 60秒
      jitterFactor: 0.3 // 30%抖动
    };
  }
}
```

## 实施步骤

### 1. 数据库迁移

```typescript
// 添加到 packages/database/prisma/schema.prisma
model VideoWebhookEvent {
  // ... (如上所示)
}

enum VideoWebhookEventStatus {
  pending
  processing
  completed
  failed
  skipped
}
```

### 2. 更新 Webhook Router

```typescript
// packages/api/src/routes/webhooks/router.ts
async function handleProviderWebhook(
  c: Context,
  provider: string,
  handler: BaseWebhookHandler
): Promise<Response> {
  const startTime = Date.now();
  
  try {
    // 1. 解析请求
    let rawBody: string | undefined;
    let body: any;
    
    if (provider === 'replicate' && process.env.REPLICATE_WEBHOOK_SIGNING_KEY) {
      rawBody = await c.req.text();
      body = JSON.parse(rawBody);
    } else {
      body = await c.req.json();
    }
    
    // 2. 构造请求对象
    const headers: Record<string, string> = {};
    c.req.raw.headers.forEach((value, key) => {
      headers[key.toLowerCase()] = value;
    });
    
    const webhookRequest: WebhookRequest = {
      headers,
      body: rawBody || body,
      url: c.req.url
    };
    
    // 3. 验证签名
    const isValidSignature = await handler.validateSignature(webhookRequest);
    if (!isValidSignature) {
      logger.warn(`[${provider}] Invalid webhook signature`);
      return c.json({ error: "Invalid signature" }, 401);
    }
    
    // 4. 存储事件到数据库（异步处理）
    const eventManager = new VideoWebhookEventManager();
    await eventManager.createEvent({
      eventId: this.extractEventId(body, provider),
      provider,
      eventType: this.extractEventType(body, provider),
      externalTaskId: this.extractExternalTaskId(body, provider),
      rawPayload: body,
      headers,
      signature: this.extractSignature(headers, provider)
    });
    
    // 5. 立即返回成功响应
    const responseTime = Date.now() - startTime;
    logger.info(`[${provider}] Webhook received and queued in ${responseTime}ms`);
    
    return c.json({ 
      success: true, 
      message: "Webhook received and queued for processing",
      processingTime: responseTime 
    });
    
  } catch (error) {
    logger.error(`[${provider}] Webhook processing error:`, error);
    return c.json({ 
      error: "Internal server error",
      details: error instanceof Error ? error.message : String(error) 
    }, 500);
  }
}
```

### 3. 后台处理器

```typescript
// packages/api/src/lib/services/video-webhook-processor.ts
export class VideoWebhookProcessorService {
  private processor: VideoWebhookProcessor;
  private isRunning: boolean = false;
  private intervalId: NodeJS.Timeout | null = null;
  
  constructor() {
    this.processor = new VideoWebhookProcessor();
  }
  
  /**
   * 启动后台处理器
   */
  start(intervalMs: number = 5000): void {
    if (this.isRunning) return;
    
    this.isRunning = true;
    logger.info('[WebhookProcessor] Starting background processor', { intervalMs });
    
    this.intervalId = setInterval(async () => {
      try {
        await this.processor.processPendingEvents();
      } catch (error) {
        logger.error('[WebhookProcessor] Background processing error:', error);
      }
    }, intervalMs);
  }
  
  /**
   * 停止后台处理器
   */
  stop(): void {
    if (!this.isRunning) return;
    
    this.isRunning = false;
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    
    logger.info('[WebhookProcessor] Background processor stopped');
  }
}

// 在应用启动时启动处理器
// packages/api/src/app.ts
import { VideoWebhookProcessorService } from './lib/services/video-webhook-processor';

const webhookProcessor = new VideoWebhookProcessorService();

// 启动后台处理器
webhookProcessor.start(5000); // 每5秒检查一次

// 优雅关闭
process.on('SIGINT', () => {
  webhookProcessor.stop();
  process.exit(0);
});
```

### 4. 监控和观测

```typescript
// packages/api/src/lib/services/video-webhook-monitor.ts
export class VideoWebhookMonitor {
  /**
   * 获取处理统计
   */
  async getProcessingStats(timeRangeHours: number = 24): Promise<ProcessingStats> {
    const since = new Date(Date.now() - timeRangeHours * 60 * 60 * 1000);
    
    return {
      total: await this.countEvents(since),
      completed: await this.countEventsByStatus('completed', since),
      failed: await this.countEventsByStatus('failed', since),
      pending: await this.countEventsByStatus('pending'),
      processing: await this.countEventsByStatus('processing'),
      avgProcessingTime: await this.getAverageProcessingTime(since),
      retryRate: await this.getRetryRate(since)
    };
  }
  
  /**
   * 获取失败事件详情
   */
  async getFailedEvents(limit: number = 50): Promise<VideoWebhookEvent[]> {
    // 返回最近的失败事件，用于排查问题
  }
  
  /**
   * 健康检查
   */
  async healthCheck(): Promise<HealthStatus> {
    const stats = await this.getProcessingStats(1); // 最近1小时
    
    return {
      status: this.calculateHealthStatus(stats),
      pendingCount: stats.pending,
      failedCount: stats.failed,
      avgProcessingTime: stats.avgProcessingTime,
      lastProcessedAt: await this.getLastProcessedTime()
    };
  }
}
```

## 部署配置

### 环境变量

```bash
# Webhook 处理配置
WEBHOOK_PROCESSOR_ENABLED=true
WEBHOOK_PROCESSOR_INTERVAL=5000  # 5秒
WEBHOOK_MAX_RETRIES=5
WEBHOOK_CLEANUP_DAYS=30

# 性能调优
WEBHOOK_BATCH_SIZE=10
WEBHOOK_CONCURRENT_LIMIT=5
```

### Docker/Kubernetes 配置

```yaml
# kubernetes/video-webhook-processor-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: video-webhook-processor
spec:
  replicas: 2  # 多实例提高可用性
  template:
    spec:
      containers:
      - name: processor
        env:
        - name: WEBHOOK_PROCESSOR_ENABLED
          value: "true"
        - name: WEBHOOK_PROCESSOR_INTERVAL
          value: "5000"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi" 
            cpu: "500m"
        readinessProbe:
          httpGet:
            path: /webhooks/health
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 5
```

## 测试策略

### 1. 单元测试

```typescript
// tests/webhook-event-manager.test.ts
describe('VideoWebhookEventManager', () => {
  it('should create webhook event with unique constraint', async () => {
    // 测试幂等性
  });
  
  it('should handle retry logic correctly', async () => {
    // 测试重试机制
  });
  
  it('should calculate exponential backoff', async () => {
    // 测试退避算法
  });
});
```

### 2. 集成测试

```typescript
// tests/webhook-integration.test.ts
describe('Webhook Integration', () => {
  it('should process webhook end-to-end', async () => {
    // 1. 发送 webhook 请求
    // 2. 验证立即响应
    // 3. 验证数据库记录创建
    // 4. 验证异步处理完成
  });
});
```

### 3. 负载测试

```typescript
// tests/webhook-load.test.ts
describe('Webhook Load Test', () => {
  it('should handle 1000 concurrent webhooks', async () => {
    // 并发 webhook 测试
  });
});
```

## 性能指标

### 关键指标
- **响应时间**: Webhook 响应时间 < 200ms
- **处理延迟**: 事件处理延迟 < 30秒
- **成功率**: 处理成功率 > 99.5%
- **重试率**: 重试事件比例 < 5%

### 容量规划
- **日处理量**: 支持 100,000+ webhook/天
- **并发处理**: 支持 10+ 并发处理
- **数据保留**: 30天内的事件记录

## 监控告警

### Prometheus 指标

```typescript
// 自定义指标
webhook_events_total{provider, status}
webhook_processing_duration_seconds
webhook_retry_attempts_total
webhook_queue_size
```

### 告警规则

```yaml
# 队列积压告警
- alert: WebhookQueueBacklog
  expr: webhook_queue_size > 100
  for: 5m
  
# 处理失败率过高
- alert: WebhookHighFailureRate  
  expr: rate(webhook_events_total{status="failed"}[5m]) > 0.05
  for: 2m
```

## 总结

通过实施数据库缓冲模式，我们将获得：

1. **高可靠性**: 即使处理失败也能自动重试，确保最终一致性
2. **快速响应**: Webhook响应时间从20-60秒降到100ms以内
3. **可扩展性**: 支持高并发和大量webhook处理
4. **可观测性**: 完整的监控、日志和告警体系
5. **运维友好**: 支持优雅关闭、健康检查和故障恢复

这套方案已经在各大互联网公司得到验证，是处理高频webhook的业界标准做法。