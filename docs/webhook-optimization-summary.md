# Webhook 优化改造总结

## 改造概述

本次改造优化了视频生成 Webhook 事件处理系统，主要改进了重复检测机制和差异化处理策略。

## 主要变更

### 1. 数据库结构优化

#### 枚举重命名
- `VideoWebhookEventStatus` → `VideoWebhookEventProcessStatus`
- 移除了 `skipped` 状态

#### 字段变更
- 原 `status` 字段重命名为 `processStatus`（内部处理状态）
- 新增 `status` 字段（存储 webhook 原始报文中的状态）

#### 约束变更
- 删除：`@@unique([provider, eventId])`
- 新增：`@@unique([provider, eventType, status])`

### 2. 重复检测逻辑优化

**旧逻辑**：基于 `provider + eventId` 判断重复

**新逻辑**：基于 `provider + eventType + status` 判断重复

**优势**：
- 更精确的重复检测
- 允许同一任务的不同状态事件（如 processing → succeeded）
- 避免误判重复事件

### 3. 差异化处理策略

根据 webhook 原始状态（`status` 字段）执行不同操作：

- **processing 状态**：仅更新 job 和 generation 状态
- **succeeded 状态**：更新状态 + 上传视频 + 更新 URL
- **其他状态**（如 failed）：仅更新状态

### 4. 日志优化

- 重复事件检测时输出 `warn` 级别日志
- 明确标识 "Duplicate webhook event skipped"
- 包含完整的上下文信息（provider、eventType、status）

## 影响范围

### 修改的文件

1. **数据库 Schema**
   - `packages/database/prisma/schema.prisma`

2. **核心服务**
   - `packages/api/src/lib/services/video-webhook-event-manager.ts`
   - `packages/api/src/lib/services/video-webhook-processor.ts`

3. **API 路由**
   - `packages/api/src/routes/webhooks/router.ts`

### 自动生成文件
- `packages/database/prisma/zod/index.ts`
- `packages/database/prisma/generated/client/`

## 测试验证

### 测试脚本
```bash
npx tsx packages/api/test-webhook-optimization.ts
```

### 测试场景
1. Replicate processing 状态处理
2. Replicate succeeded 状态处理
3. Fal IN_PROGRESS 状态处理
4. Fal COMPLETED 状态处理
5. 重复事件检测
6. 处理统计验证
7. 健康检查验证

## 部署注意事项

### 数据库迁移
1. **备份数据库**（重要！）
2. 执行迁移脚本
3. 验证数据完整性

### 代码部署
1. 生成 Prisma 客户端：`pnpm prisma generate`
2. 重启应用服务
3. 监控日志确保正常运行

### 回滚预案
如需回滚，执行反向迁移脚本恢复原始结构。

## 性能优化建议

1. **索引优化**
   ```sql
   CREATE INDEX CONCURRENTLY "idx_video_webhook_event_provider_eventType_status" 
   ON "video_webhook_event" ("provider", "eventType", "status");
   ```

2. **监控指标**
   - 重复事件比率
   - 处理延迟
   - 错误率

## 后续优化方向

1. 添加更细粒度的状态处理策略
2. 实现可配置的处理规则
3. 增强监控和告警机制
4. 优化批量处理性能