# Webhook Smart Trigger Architecture

## 📋 概述

为了解决在 Vercel Serverless 环境中无法运行持续定时器的问题，我们实施了**智能触发架构**，利用前端轮询 Job 状态的机制来触发 webhook 事件处理。

## 🏗️ 架构设计

### 传统架构 vs 智能触发架构

```mermaid
graph TD
    subgraph "传统架构（本地/VPS环境）"
        A1[Webhook接收] --> B1[存储到数据库]
        B1 --> C1[定时器每5秒查询]
        C1 --> D1[批量处理事件]
        D1 --> E1[更新状态]
    end
    
    subgraph "智能触发架构（Vercel环境）"
        A2[Webhook接收] --> B2[存储到数据库]
        B2 --> F2[前端轮询Job状态]
        F2 --> G2[API调用时触发处理]
        G2 --> H2[异步处理相关事件]
        H2 --> I2[更新状态]
    end
```

### 核心优势

1. **🚀 Vercel 兼容**：完全兼容 Serverless 环境
2. **⚡ 响应更快**：利用用户活跃时的 API 调用触发处理
3. **💰 成本效益**：只在有用户活动时消耗资源
4. **🔄 自动降频**：没有用户活动时自然停止处理
5. **🛡️ 防重复**：内置防重复处理机制

## 🎛️ 环境变量配置

### 核心环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `WEBHOOK_PROCESSOR_ENABLED` | `true` | 是否启用webhook处理器 |
| `WEBHOOK_TRIGGER_ON_JOB_POLL` | Vercel环境为`true` | 是否启用Job轮询触发 |
| `VERCEL` | 自动设置 | Vercel环境标识 |

### 不同环境的配置

#### Vercel 生产环境
```bash
# .env.production
WEBHOOK_PROCESSOR_ENABLED=true
WEBHOOK_TRIGGER_ON_JOB_POLL=true
# VERCEL=1 (自动设置)
```

#### 本地开发环境
```bash
# .env.local
WEBHOOK_PROCESSOR_ENABLED=true
WEBHOOK_TRIGGER_ON_JOB_POLL=false  # 使用定时器
```

#### 禁用处理（调试用）
```bash
# 完全禁用
WEBHOOK_PROCESSOR_ENABLED=false

# 或者只禁用轮询触发
WEBHOOK_TRIGGER_ON_JOB_POLL=false
```

## 🔧 工作原理

### 1. Webhook 接收流程

```typescript
// router.ts - webhook接收端点
POST /api/webhooks/replicate
├── 验证签名
├── 提取metadata
├── 存储到VideoWebhookEvent表 (status='pending')
└── 立即返回200 OK
```

### 2. 智能触发流程

```typescript
// job-service.ts - Job状态查询API
GET /api/jobs/details/:id
├── 🔍 查找相关的pending webhook事件
├── 🚀 异步触发处理（不阻塞API响应）
├── 📊 查询并返回最新Job状态
└── 前端获得实时更新
```

### 3. 防重复处理机制

```typescript
class JobService {
    private static processingJobs = new Set<string>();
    
    // 处理前检查
    if (this.processingJobs.has(jobId)) {
        return; // 跳过重复处理
    }
    
    // 标记为处理中
    this.processingJobs.add(jobId);
    
    // 5秒后清理标记
    setTimeout(() => {
        this.processingJobs.delete(jobId);
    }, 5000);
}
```

## 📊 监控和调试

### 日志输出

应用启动时会输出不同环境的配置信息：

```bash
# Vercel环境
🚀 [App] Running on Vercel: Webhook processing via Job polling triggers
ℹ️  [App] Set WEBHOOK_TRIGGER_ON_JOB_POLL=false to disable job polling triggers

# 本地环境
🚀 [App] Starting background webhook processor with timer

# 禁用状态
⏸️  [App] Webhook processor disabled via WEBHOOK_PROCESSOR_ENABLED=false
```

### API监控端点

保持原有的监控API可用：

- `GET /api/webhooks/health` - 系统健康状态
- `GET /api/webhooks/stats` - 处理统计
- `POST /api/webhooks/process` - 手动触发处理
- `GET /api/webhooks/failed` - 查看失败事件

### 处理日志

```bash
[JobService] Triggering 3 pending webhook events for job job_abc123
[JobService] Webhook processing completed for job job_abc123: 3 succeeded, 0 failed
[JobService] Job job_def456 is already being processed, skipping
```

## 🎯 性能优化

### 1. 查询优化

只查询相关的pending事件：
```sql
SELECT * FROM VideoWebhookEvent 
WHERE status = 'pending' 
  AND externalTaskId IN (
    SELECT externalTaskId FROM Generation WHERE jobId = ?
  )
ORDER BY receivedAt ASC 
LIMIT 10;
```

### 2. 批量处理

限制并发数，避免系统过载：
```typescript
const batchSize = 3; // 最多同时处理3个事件
for (let i = 0; i < events.length; i += batchSize) {
    const batch = events.slice(i, i + batchSize);
    await Promise.allSettled(batch.map(processor.processEvent));
}
```

### 3. 异步执行

使用 `setImmediate` 确保不阻塞 API 响应：
```typescript
setImmediate(async () => {
    // webhook处理逻辑
    await processWebhookEvents();
});
```

## 🚀 部署注意事项

### Vercel 部署

1. 确保环境变量正确设置
2. 监控 Function 执行时间（避免超时）
3. 观察日志确认智能触发正常工作

### 回退机制

如果智能触发出现问题，可以：

1. **临时禁用**：设置 `WEBHOOK_TRIGGER_ON_JOB_POLL=false`
2. **手动处理**：调用 `POST /api/webhooks/process`
3. **查看失败事件**：`GET /api/webhooks/failed`

## 📈 预期效果

- **响应时间**：用户能在3-6秒内看到状态更新（之前可能需要等待定时器周期）
- **资源利用**：只在有用户活动时处理，节省服务器资源
- **可靠性**：保持原有的重试机制和错误处理
- **可扩展性**：支持多用户并发，自动负载均衡

## 🔄 迁移步骤

1. **代码部署**：部署包含智能触发的新版本
2. **环境变量**：在 Vercel 中设置相应环境变量
3. **监控观察**：观察日志和webhook处理情况
4. **性能调优**：根据实际情况调整批量大小等参数

这个架构既保持了原有功能的完整性，又完美适配了 Vercel 的 Serverless 环境！