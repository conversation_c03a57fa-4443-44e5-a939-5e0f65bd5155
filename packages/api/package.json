{"dependencies": {"@aws-sdk/client-s3": "^3.850.0", "@aws-sdk/lib-storage": "^3.850.0", "@fal-ai/client": "^1.6.0", "@hono/zod-validator": "^0.7.0", "@paralleldrive/cuid2": "^2.2.2", "@repo/ai": "workspace:*", "@repo/auth": "workspace:*", "@repo/config": "workspace:*", "@repo/database": "workspace:*", "@repo/i18n": "workspace:*", "@repo/jobs": "workspace:*", "@repo/logs": "workspace:*", "@repo/mail": "workspace:*", "@repo/payments": "workspace:*", "@repo/queue": "workspace:*", "@repo/redis": "workspace:*", "@repo/storage": "workspace:*", "@repo/utils": "workspace:*", "@scalar/hono-api-reference": "^0.9.3", "@sindresorhus/slugify": "^2.2.1", "@upstash/redis": "^1.35.1", "dayjs": "^1.11.7", "hono": "^4.8.4", "hono-openapi": "^0.4.8", "nanoid": "^5.1.5", "openai": "^5.1.1", "openapi-merge": "^1.3.3", "rate-limiter-flexible": "^7.1.1", "replicate": "^1.0.1", "undici": "^7.12.0", "zod": "^3.25.76", "zod-openapi": "^5.0.1"}, "devDependencies": {"@biomejs/biome": "2.1.1", "@repo/tsconfig": "workspace:*", "encoding": "^0.1.13", "typescript": "5.8.3"}, "main": "./index.ts", "name": "@repo/api", "scripts": {"type-check": "tsc --noEmit"}, "version": "0.0.0"}