import { auth } from "@repo/auth";
import { config } from "@repo/config";
import { getBaseUrl } from "@repo/utils";
import { <PERSON>alar } from "@scalar/hono-api-reference";
import { Hono } from "hono";
import { openAPISpecs } from "hono-openapi";
import { mergeOpenApiSchemas } from "./lib/openapi-schema";
import { corsMiddleware } from "./middleware/cors";
import { loggerMiddleware } from "./middleware/logger";
import { adminRouter } from "./routes/admin/router";
import { aiRouter } from "./routes/ai";
import { authRouter } from "./routes/auth";
import { contactRouter } from "./routes/contact/router";
import { healthRouter } from "./routes/health";
import { newsletterRouter } from "./routes/newsletter";
import { organizationsRouter } from "./routes/organizations/router";
import { paymentsRouter } from "./routes/payments/router";
import { promptsRouter } from "./routes/prompts";
import { uploadsRouter } from "./routes/uploads";
import { uploadRouter } from "./routes/upload";
import { webhooksRouter } from "./routes/webhooks/router";
import { jobsRouter } from "./routes/jobs/router";
import { generationsRouter } from "./routes/generations";
import { bulkRouter } from "./routes/bulk/router";
// 移除内置webhook处理器 - 现在由Consumer处理

export const app = new Hono().basePath("/api");

app.use(loggerMiddleware);
app.use(corsMiddleware);

// 🚀 Webhook处理现在由专门的Consumer服务处理
console.log('ℹ️  [App] Webhook processing delegated to Consumer service');

const appRouter = app
	.route("/", authRouter)
	.route("/", webhooksRouter)
	.route("/", aiRouter)
	.route("/", promptsRouter)
	.route("/", uploadsRouter)
	.route("/", uploadRouter)
	.route("/", paymentsRouter)
	.route("/", contactRouter)
	.route("/", newsletterRouter)
	.route("/", organizationsRouter)
	.route("/", adminRouter)
	.route("/", healthRouter)
	.route("/", jobsRouter)
	.route("/", generationsRouter)
	.route("/", bulkRouter);

app.get(
	"/app-openapi",
	openAPISpecs(app, {
		documentation: {
			info: {
				title: `${config.appName} API`,
				version: "1.0.0",
			},
			servers: [
				{
					url: getBaseUrl(),
					description: "API server",
				},
			],
		},
	}),
);

app.get("/openapi", async (c) => {
	const authSchema = await auth.api.generateOpenAPISchema();
	const appSchema = await (
		app.request("/api/app-openapi") as Promise<Response>
	).then((res) => res.json());

	const mergedSchema = mergeOpenApiSchemas({
		appSchema,
		authSchema: authSchema as any,
	});

	return c.json(mergedSchema);
});

app.get(
	"/docs",
	Scalar({
		theme: "saturn",
		url: "/api/openapi",
	}),
);

export type AppRouter = typeof appRouter;
