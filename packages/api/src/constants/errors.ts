/**
 * 应用错误类型枚举
 * 定义应用中可能出现的所有错误类型
 */
export enum ErrorType {
  // 通用错误
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  BAD_REQUEST = 'BAD_REQUEST',
  
  // 用户相关错误
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  
  // 积分相关错误
  INSUFFICIENT_CREDITS = 'INSUFFICIENT_CREDITS',
  
  // AI 相关错误
  AI_GENERATION_FAILED = 'AI_GENERATION_FAILED',
  
  // 数据相关错误
  DATA_NOT_FOUND = 'DATA_NOT_FOUND',
}

/**
 * 错误配置映射
 * 为每种错误类型定义HTTP状态码和默认消息
 */
export const errorConfig: Record<ErrorType, { status: number; defaultMessage: string }> = {
  // 通用错误
  [ErrorType.INTERNAL_SERVER_ERROR]: { 
    status: 500, 
    defaultMessage: 'Internal Server Error' 
  },
  [ErrorType.UNAUTHORIZED]: { 
    status: 401, 
    defaultMessage: 'Unauthorized Access' 
  },
  [ErrorType.FORBIDDEN]: { 
    status: 403, 
    defaultMessage: 'Access Forbidden' 
  },
  [ErrorType.NOT_FOUND]: { 
    status: 404, 
    defaultMessage: 'Resource Not Found' 
  },
  [ErrorType.BAD_REQUEST]: { 
    status: 400, 
    defaultMessage: 'Invalid Request Parameters' 
  },
  
  // 用户相关错误
  [ErrorType.USER_NOT_FOUND]: { 
    status: 404, 
    defaultMessage: 'User Not Found' 
  },
  
  // 积分相关错误
  [ErrorType.INSUFFICIENT_CREDITS]: { 
    status: 402, 
    defaultMessage: 'Insufficient Credits' 
  },
  
  // AI 相关错误
  [ErrorType.AI_GENERATION_FAILED]: { 
    status: 500, 
    defaultMessage: 'AI Generation Failed' 
  },
  
  // 数据相关错误
  [ErrorType.DATA_NOT_FOUND]: { 
    status: 404, 
    defaultMessage: 'Data Not Found' 
  },
};
