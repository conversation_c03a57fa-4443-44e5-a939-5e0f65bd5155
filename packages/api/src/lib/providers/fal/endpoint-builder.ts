export class FalEndpointBuilder {
  /**
   * 构建FAL端点
   * @param baseEndpoint 基础端点，如 "fal-ai/pixverse/v4.5"
   * @param featureCode 功能代码，如 "image-to-video"
   * @param modeCode 模式代码，如 "fast", "pro"
   * @returns 完整的FAL端点
   */
  static buildEndpoint(
    baseEndpoint: string,
    featureCode: string,
    modeCode?: string
  ): string {
    const parts = [baseEndpoint];
    
    // 添加feature（如 image-to-video）
    if (featureCode) {
      parts.push(featureCode);
    }
    
    // 添加mode（如 fast, pro）
    if (modeCode) {
      parts.push(modeCode);
    }
    
    return parts.join('/');
  }

  /**
   * 根据模型特殊处理某些端点构建规则
   */
  static buildModelSpecificEndpoint(
    baseEndpoint: string,
    modelCode: string,
    featureCode: string,
    modeCode?: string
  ): string {
    // 某些模型可能有特殊的端点构建规则
    if (modelCode.startsWith('kling-')) {
      // Kling模型可能不需要feature在端点中
      return modeCode ? `${baseEndpoint}/${modeCode}` : baseEndpoint;
    }
    
    if (modelCode.startsWith('pixverse-')) {
      // Pixverse使用标准的feature/mode结构
      return this.buildEndpoint(baseEndpoint, featureCode, modeCode);
    }
    
    // 默认构建方式
    return this.buildEndpoint(baseEndpoint, featureCode, modeCode);
  }
}