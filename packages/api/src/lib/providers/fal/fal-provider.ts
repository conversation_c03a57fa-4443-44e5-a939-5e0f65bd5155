import { fal } from "@fal-ai/client";
import type { VideoProvider, VideoGenerationParams, VideoGenerationResponse } from "../types";
import { FalEndpointBuilder } from "./endpoint-builder";

export class FalProvider implements VideoProvider {
  constructor(private baseEndpoint: string) {
    // 配置FAL客户端
    fal.config({
      credentials: process.env.FAL_KEY || process.env.FAL_API_KEY,
    });
  }
  
  async generateVideo(params: VideoGenerationParams): Promise<VideoGenerationResponse> {
    try {
      // 1. 构建完整端点
      const endpoint = FalEndpointBuilder.buildModelSpecificEndpoint(
        this.baseEndpoint,
        params.modelCode,
        params.featureCode,
        params.modeCode
      );
      
      console.log(`📡 FAL endpoint: ${endpoint}`);
      
      // 2. 转换参数为FAL格式
      const falParams = this.transformParams(params);
      
      console.log(`📤 FAL params:`, falParams);
      
      // 3. 调用FAL API
      const webhookUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/webhooks/fal`;
      
      console.log(`🌐 Using webhook URL: ${webhookUrl}`);
      
      const submitRequest = {
        input: falParams,
        webhookUrl: webhookUrl
      };
      
      console.log(`📤 Submitting to FAL with request:`, JSON.stringify(submitRequest, null, 2));
      
      const response = await fal.queue.submit(endpoint, submitRequest);
      
      console.log(`🔍 FAL response:`, JSON.stringify(response, null, 2));
      
      return {
        taskId: response.request_id,
        status: 'processing',
        estimatedTime: this.estimateTime(params),
        metadata: { 
          endpoint,
          provider: 'fal',
          model: params.modelCode,
          responseUrl: response.response_url,
          statusUrl: response.status_url
        }
      };
    } catch (error) {
      console.error('FAL API error:', error);
      throw new Error(`FAL generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  
  getName(): string {
    return 'FAL';
  }
  
  private transformParams(params: VideoGenerationParams): any {
    const falParams: any = {};
    
    // 基础参数映射
    if (params.prompt) falParams.prompt = params.prompt;
    if (params.image) falParams.image_url = params.image;
    
    // Negative Prompt - 总是传递，没有则为空字符串
    falParams.negative_prompt = params.negativePrompt || "";
    
    // Prompt Strength 映射为 guidance_scale 或 cfg_scale
    if (params.promptStrength !== undefined) {
      falParams.guidance_scale = params.promptStrength;
    }
    
    // 视频参数
    if (params.duration) {
      // 根据模型调整duration格式
      if (params.modelCode.startsWith('kling-')) {
        falParams.duration = params.duration; // Kling使用秒
      } else {
        falParams.num_frames = params.duration * 24; // 其他模型可能使用帧数
      }
    }
    
    if (params.aspectRatio) falParams.aspect_ratio = params.aspectRatio;
    if (params.resolution) falParams.resolution = params.resolution;
    if (params.style) falParams.style = params.style;
    if (params.motionRange) falParams.motion_range = params.motionRange;
    if (params.seed) falParams.seed = params.seed;
    
    // 模型特定参数
    if (params.modelCode.startsWith('pixverse-')) {
      // Pixverse特定参数
      if (params.modeCode) falParams.mode = params.modeCode;
    }
    
    if (params.modelCode.startsWith('kling-')) {
      // Kling特定参数
      if (params.modeCode) falParams.mode = params.modeCode;
      if (params.imageTail) falParams.image_tail = params.imageTail;
    }
    
    return falParams;
  }
  
  private estimateTime(params: VideoGenerationParams): number {
    // 基于模型和参数估算时间
    const baseTime: Record<string, number> = {
      'kling-v2.0': 300,
      'kling-v1.6': 240,
      'pixverse-v4.5': 120,
      'veo-2': 300,
      'pika-v2.2': 100,
    };
    
    const base = baseTime[params.modelCode] || 180;
    const durationMultiplier = params.duration > 5 ? 1.5 : 1;
    const modeMultiplier = params.modeCode === 'pro' ? 2 : 1;
    
    return Math.round(base * durationMultiplier * modeMultiplier);
  }
}