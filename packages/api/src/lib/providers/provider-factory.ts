import { modelApiMapping } from "@repo/config/mpp";
import { videoModelConfig } from "@repo/config/vmodel";
import type { VideoProvider, ProviderCode } from "./types";
import { FalProvider } from "./fal/fal-provider";
import { ReplicateProvider } from "./replicate/replicate-provider";

export class ProviderFactory {
  /**
   * 获取指定模型的供应商实例
   * @param modelCode 模型代码
   * @param providerCode 可选的供应商代码，如果不提供则使用模型默认供应商
   * @returns VideoProvider实例
   */
  static getProvider(
    modelCode: string,
    providerCode?: ProviderCode
  ): VideoProvider {
    // 1. 从模型配置获取默认provider
    const model = videoModelConfig.models.find(m => m.code === modelCode);
    if (!model) {
      throw new Error(`Model not found: ${modelCode}`);
    }
    
    // 2. 确定使用哪个provider
    const actualProvider = providerCode || model.apiProviderCode;
    if (!actualProvider) {
      throw new Error(`No provider specified for model: ${modelCode}`);
    }
    
    // 3. 获取模型映射
    const modelMapping = modelApiMapping.models[modelCode];
    if (!modelMapping) {
      throw new Error(`No mapping found for model: ${modelCode}`);
    }
    
    // 4. 获取provider端点
    const endpoint = modelMapping[actualProvider];
    if (!endpoint) {
      throw new Error(`No ${actualProvider} endpoint for model: ${modelCode}`);
    }
    
    // 5. 创建对应的provider实例
    switch (actualProvider) {
      case 'replicate':
        return new ReplicateProvider(endpoint);
      case 'fal':
        return new FalProvider(endpoint);
      default:
        throw new Error(`Unknown provider: ${actualProvider}`);
    }
  }
  
  /**
   * 获取模型支持的所有provider
   * @param modelCode 模型代码
   * @returns 支持的provider列表
   */
  static getSupportedProviders(modelCode: string): ProviderCode[] {
    const modelMapping = modelApiMapping.models[modelCode];
    if (!modelMapping) {
      return [];
    }
    
    return Object.keys(modelMapping) as ProviderCode[];
  }
  
  /**
   * 检查模型是否支持指定的provider
   * @param modelCode 模型代码
   * @param providerCode 供应商代码
   * @returns 是否支持
   */
  static isProviderSupported(modelCode: string, providerCode: ProviderCode): boolean {
    const supportedProviders = this.getSupportedProviders(modelCode);
    return supportedProviders.includes(providerCode);
  }
}