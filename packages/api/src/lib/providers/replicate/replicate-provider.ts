import Replicate from "replicate";
import type { VideoProvider, VideoGenerationParams, VideoGenerationResponse } from "../types";

export class ReplicateProvider implements VideoProvider {
  private replicate: Replicate;
  
  constructor(private modelEndpoint: string) {
    this.replicate = new Replicate({
      auth: process.env.REPLICATE_API_TOKEN,
    });
  }
  
  async generateVideo(params: VideoGenerationParams): Promise<VideoGenerationResponse> {
    try {
      console.log(`📡 Replicate model: ${this.modelEndpoint}`);
      console.log(`📥 Raw params received:`, JSON.stringify(params, null, 2));
      
      // 1. 转换参数为Replicate格式
      const replicateParams = this.transformParams(params);
      
      console.log(`📤 Replicate params:`, replicateParams);
      
      // 2. 添加 metadata 信息（如果提供了用户ID和任务ID）
      let inputWithMetadata = replicateParams;
      if (params.userId && params.taskId) {
        // 直接构造带metadata的input，不使用security token
        inputWithMetadata = {
          ...replicateParams,
          metadata: {
            user_id: params.userId,
            generation_id: params.taskId, // taskId 就是 generationId
            provider: 'replicate',
            feature_code: params.featureCode || 'image-to-video',
            organization_id: params.organizationId,
            model_code: params.modelCode
          }
        };
        console.log(`📦 Added metadata for user ${params.userId}, generation ${params.taskId}`);
      }
      
      // 3. 调用Replicate API
      //const webhookUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/webhooks/replicate`;
      const webhookUrl = 'https://test.connectionshinttoday.tips/api/webhooks/replicate';
      
      console.log(`🌐 Using webhook URL: ${webhookUrl}`);
      
      const predictionRequest = {
        model: this.modelEndpoint,
        input: inputWithMetadata,
        webhook: webhookUrl,
        webhook_events_filter: ["start", "completed"] as any
      };
      
      console.log(`📤 Creating prediction with request:`, JSON.stringify(predictionRequest, null, 2));
      
      const prediction = await this.replicate.predictions.create(predictionRequest);
      
      console.log(`🔍 Full prediction response:`, JSON.stringify(prediction, null, 2));
      
      return {
        taskId: prediction.id,
        status: 'processing',
        estimatedTime: this.estimateTime(params),
        metadata: { 
          endpoint: this.modelEndpoint,
          provider: 'replicate',
          model: params.modelCode
        }
      };
    } catch (error) {
      console.error('🚨 Replicate API error:', error);
      console.error('🚨 Error details:', JSON.stringify(error, null, 2));
      throw new Error(`Replicate generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  
  getName(): string {
    return 'Replicate';
  }
  
  private transformParams(params: VideoGenerationParams): any {
    const replicateParams: any = {};
    
    // 基础参数映射
    if (params.prompt) replicateParams.prompt = params.prompt;
    if (params.image) replicateParams.start_image = params.image;
    
    // Negative Prompt - 总是传递，没有则为空字符串
    replicateParams.negative_prompt = params.negativePrompt || "";
    
    // Prompt Strength 映射为 cfg_scale
    if (params.promptStrength !== undefined) {
      replicateParams.cfg_scale = params.promptStrength;
    }
    
    // 视频参数
    if (params.duration) replicateParams.duration = params.duration;
    if (params.aspectRatio) replicateParams.aspect_ratio = params.aspectRatio;
    if (params.resolution) replicateParams.resolution = params.resolution;
    if (params.seed) replicateParams.seed = params.seed;
    
    // 模型特定参数映射
    if (params.modelCode.startsWith('kling-')) {
      // Kling模型在Replicate上的参数映射
      if (params.modeCode) replicateParams.mode = params.modeCode;
      if (params.imageTail) replicateParams.image_tail = params.imageTail;
      if (params.style) replicateParams.style = params.style;
    }
    
    return replicateParams;
  }
  
  private estimateTime(params: VideoGenerationParams): number {
    // Replicate通常比FAL稍慢
    const baseTime: Record<string, number> = {
      'kling-v2.0': 400,
      'kling-v1.6': 300,
      'pixverse-v4.5': 180,
    };
    
    const base = baseTime[params.modelCode] || 240;
    const durationMultiplier = params.duration > 5 ? 1.8 : 1;
    const modeMultiplier = params.modeCode === 'pro' ? 2.5 : 1;
    
    return Math.round(base * durationMultiplier * modeMultiplier);
  }
}