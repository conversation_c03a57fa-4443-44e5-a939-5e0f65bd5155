/**
 * 测试脚本，验证ProviderFactory工作是否正常
 * 运行方式: node -r ts-node/register test-provider-factory.ts
 */

import { ProviderFactory } from './provider-factory';

async function testProviderFactory() {
  console.log('🧪 Testing ProviderFactory...\n');

  // 测试各种模型的provider获取
  const testCases = [
    { modelCode: 'kling-v2.0', expectedProvider: 'replicate' },
    { modelCode: 'kling-v1.6', expectedProvider: 'fal' },
    { modelCode: 'pixverse-v4.5', expectedProvider: 'fal' },
    { modelCode: 'veo-2', expectedProvider: 'fal' },
  ];

  for (const testCase of testCases) {
    try {
      console.log(`📋 Testing model: ${testCase.modelCode}`);
      
      // 获取默认provider
      const provider = ProviderFactory.getProvider(testCase.modelCode);
      console.log(`   ✅ Default provider: ${provider.getName()}`);
      
      // 获取支持的providers
      const supportedProviders = ProviderFactory.getSupportedProviders(testCase.modelCode);
      console.log(`   📊 Supported providers: ${supportedProviders.join(', ')}`);
      
      // 测试每个支持的provider
      for (const providerCode of supportedProviders) {
        const specificProvider = ProviderFactory.getProvider(testCase.modelCode, providerCode);
        console.log(`   🔗 ${providerCode} provider: ${specificProvider.getName()}`);
      }
      
      console.log('');
    } catch (error) {
      console.error(`   ❌ Error testing ${testCase.modelCode}:`, error);
      console.log('');
    }
  }

  // 测试端点构建逻辑
  console.log('📋 Testing endpoint building...');
  try {
    const falProvider = ProviderFactory.getProvider('pixverse-v4.5', 'fal');
    console.log('   ✅ FAL provider created for pixverse-v4.5');
    
    const replicateProvider = ProviderFactory.getProvider('kling-v2.0', 'replicate');
    console.log('   ✅ Replicate provider created for kling-v2.0');
    
  } catch (error) {
    console.error('   ❌ Error testing endpoint building:', error);
  }

  console.log('\n🎉 Provider factory test completed!');
}

// 如果直接运行此文件则执行测试
if (require.main === module) {
  testProviderFactory().catch(console.error);
}

export { testProviderFactory };