export interface VideoGenerationParams {
  modelCode: string;
  featureCode: string;
  modeCode?: string;
  prompt?: string;
  image?: string;
  imageTail?: string;
  negativePrompt?: string;
  promptStrength?: number;
  duration: number;
  aspectRatio: string;
  resolution?: string;
  style?: string;
  motionRange?: string;
  seed?: number;
  // Security parameters for webhook validation
  userId?: string;
  taskId?: string;
  organizationId?: string;
  [key: string]: any;
}

export interface VideoGenerationResponse {
  taskId: string;
  status: string;
  estimatedTime?: number;
  metadata?: Record<string, any>;
}

export interface VideoProvider {
  generateVideo(params: VideoGenerationParams): Promise<VideoGenerationResponse>;
  getName(): string;
}

export type ProviderCode = "replicate" | "fal";