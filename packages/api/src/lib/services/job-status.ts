import { JobsManager } from "@repo/jobs";
import { logger } from "@repo/logs";

export interface JobStatusStats {
  totalGenerations: number;
  completedGenerations: number;
  successfulGenerations: number;
  failedGenerations: number;
  processingGenerations: number;
  allCompleted: boolean;
  successRate: number;
}

export interface CreditRefundResult {
  refunded: boolean;
  amount: number;
  reason: string;
}

export class JobStatusService {
  /**
   * 获取 Job 的状态统计信息
   */
  static async getJobStatusStats(jobId: string, tx?: any): Promise<JobStatusStats> {
    const database = tx || JobsManager;
    
    const allGenerations = await database.generation.findMany({
      where: { jobId },
      select: { status: true }
    });

    const totalGenerations = allGenerations.length;
    const successfulGenerations = allGenerations.filter(g => g.status === "succeeded").length;
    const failedGenerations = allGenerations.filter(g => g.status === "failed").length;
    const processingGenerations = allGenerations.filter(g => 
      g.status === "processing" || g.status === "waiting"
    ).length;
    
    const completedGenerations = successfulGenerations + failedGenerations;
    const allCompleted = completedGenerations === totalGenerations;
    const successRate = totalGenerations > 0 ? successfulGenerations / totalGenerations : 0;

    return {
      totalGenerations,
      completedGenerations,
      successfulGenerations,
      failedGenerations,
      processingGenerations,
      allCompleted,
      successRate,
    };
  }

  /**
   * 检查并更新 Job 状态
   */
  static async checkAndUpdateJobStatus(
    jobId: string, 
    processingTime: number,
    tx?: any
  ): Promise<{ updated: boolean; finalStatus?: string; creditRefund?: CreditRefundResult }> {
    const stats = await this.getJobStatusStats(jobId, tx);
    
    if (!stats.allCompleted) {
      logger.info("[JobStatus] Job not yet completed", {
        jobId,
        completed: stats.completedGenerations,
        total: stats.totalGenerations,
      });
      return { updated: false };
    }

    // 确定最终状态
    const finalStatus = stats.successfulGenerations > 0 ? "succeeded" : "failed";
    
    // 获取 Job 信息
    const database = tx || JobsManager;
    const job = await database.job.findUnique({
      where: { id: jobId },
      select: { id: true, userId: true, credit: true, status: true }
    });

    if (!job) {
      throw new Error(`Job not found: ${jobId}`);
    }

    // 如果状态已经是最终状态，跳过更新
    if (job.status === finalStatus) {
      logger.info("[JobStatus] Job already in final status", { jobId, status: finalStatus });
      return { updated: false, finalStatus };
    }

    // 更新 Job 状态
    await database.job.update({
      where: { id: jobId },
      data: {
        status: finalStatus,
        timeCostSeconds: processingTime,
        updatedAt: new Date(),
      }
    });

    logger.info("[JobStatus] Job status updated", {
      jobId,
      status: finalStatus,
      successRate: `${stats.successfulGenerations}/${stats.totalGenerations}`,
    });

    // 处理积分返还
    const creditRefund = await this.handleCreditRefund(job, stats, tx);

    return {
      updated: true,
      finalStatus,
      creditRefund,
    };
  }

  /**
   * 处理积分返还逻辑
   */
  private static async handleCreditRefund(
    job: { id: string; userId: string; credit: number },
    stats: JobStatusStats,
    tx?: any
  ): Promise<CreditRefundResult> {
    const database = tx || JobsManager;
    
    // 不需要返还的情况
    if (job.credit <= 0 || stats.failedGenerations === 0) {
      return {
        refunded: false,
        amount: 0,
        reason: "No credit to refund or no failed generations",
      };
    }

    let refundAmount = 0;
    let reason = "";

    if (stats.successfulGenerations === 0) {
      // 全部失败，100% 返还
      refundAmount = job.credit;
      reason = "All generations failed";
    } else if (stats.failedGenerations > 0) {
      // 部分失败，按比例返还
      refundAmount = Math.floor(job.credit * (stats.failedGenerations / stats.totalGenerations));
      reason = `Partial failure: ${stats.failedGenerations}/${stats.totalGenerations} failed`;
    }

    if (refundAmount <= 0) {
      return {
        refunded: false,
        amount: 0,
        reason: "No refund needed",
      };
    }

    // 执行积分返还
    try {
      await database.creditUsage.update({
        where: { userId: job.userId },
        data: {
          balance: { increment: refundAmount },
          used: { decrement: refundAmount },
          updatedAt: new Date(),
        }
      });

      logger.info("[JobStatus] Credit refunded", {
        jobId: job.id,
        userId: job.userId,
        amount: refundAmount,
        reason,
      });

      return {
        refunded: true,
        amount: refundAmount,
        reason,
      };
    } catch (error) {
      logger.error("[JobStatus] Credit refund failed", {
        jobId: job.id,
        userId: job.userId,
        amount: refundAmount,
        error: error instanceof Error ? error.message : error,
      });
      
      return {
        refunded: false,
        amount: 0,
        reason: `Refund failed: ${error instanceof Error ? error.message : error}`,
      };
    }
  }

  /**
   * 计算 Job 的总体进度百分比
   */
  static async calculateJobProgress(jobId: string): Promise<number> {
    const stats = await this.getJobStatusStats(jobId);
    
    if (stats.totalGenerations === 0) {
      return 0;
    }
    
    if (stats.allCompleted) {
      return 100;
    }
    
    // 简化的进度计算：完成的 generation 占比
    return Math.round((stats.completedGenerations / stats.totalGenerations) * 100);
  }

  /**
   * 检查 Job 是否可以被标记为完成
   */
  static async canMarkJobAsCompleted(jobId: string): Promise<boolean> {
    const stats = await this.getJobStatusStats(jobId);
    return stats.allCompleted;
  }

  /**
   * 获取 Job 的详细状态报告
   */
  static async getJobStatusReport(jobId: string): Promise<{
    jobId: string;
    stats: JobStatusStats;
    progress: number;
    canComplete: boolean;
  }> {
    const stats = await this.getJobStatusStats(jobId);
    const progress = await this.calculateJobProgress(jobId);
    const canComplete = stats.allCompleted;

    return {
      jobId,
      stats,
      progress,
      canComplete,
    };
  }
}