import { logger } from "@repo/logs";

export interface RetryOptions {
  maxAttempts?: number;
  baseDelay?: number;
  maxDelay?: number;
  exponentialBase?: number;
  jitter?: boolean;
  shouldRetry?: (error: any, attempt: number) => boolean;
}

export interface RetryResult<T> {
  success: boolean;
  result?: T;
  error?: Error;
  attempts: number;
  totalTime: number;
}

export class RetryService {
  private static defaultOptions: Required<RetryOptions> = {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 30000,
    exponentialBase: 2,
    jitter: true,
    shouldRetry: (error: any, attempt: number) => {
      // 默认重试策略
      if (attempt >= 3) return false;
      
      // 网络错误重试
      if (error?.code === 'ECONNRESET' || 
          error?.code === 'ENOTFOUND' || 
          error?.code === 'ETIMEDOUT') {
        return true;
      }
      
      // HTTP 5xx 错误重试
      if (error?.status >= 500 && error?.status < 600) {
        return true;
      }
      
      // 超时错误重试
      if (error?.name === 'TimeoutError' || error?.message?.includes('timeout')) {
        return true;
      }
      
      return false;
    }
  };

  /**
   * 执行带重试的异步操作
   */
  static async execute<T>(
    operation: () => Promise<T>,
    options: RetryOptions = {},
    context?: string
  ): Promise<RetryResult<T>> {
    const config = { ...this.defaultOptions, ...options };
    const startTime = Date.now();
    let lastError: Error | null = null;
    let attempt = 0;

    while (attempt < config.maxAttempts) {
      attempt++;
      
      try {
        logger.info("[Retry] Attempting operation", {
          context,
          attempt,
          maxAttempts: config.maxAttempts,
        });

        const result = await operation();
        const totalTime = Date.now() - startTime;

        logger.info("[Retry] Operation succeeded", {
          context,
          attempt,
          totalTime,
        });

        return {
          success: true,
          result,
          attempts: attempt,
          totalTime,
        };
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        logger.warn("[Retry] Operation failed", {
          context,
          attempt,
          error: lastError.message,
          errorCause: (lastError as any).cause,
          errorStack: lastError.stack?.split('\n').slice(0, 3).join('\n'),
          willRetry: attempt < config.maxAttempts && config.shouldRetry(lastError, attempt),
        });

        // 检查是否应该重试
        if (attempt >= config.maxAttempts || !config.shouldRetry(lastError, attempt)) {
          break;
        }

        // 计算延迟时间
        const delay = this.calculateDelay(attempt, config);
        
        logger.info("[Retry] Waiting before retry", {
          context,
          attempt,
          delay,
        });

        await this.sleep(delay);
      }
    }

    const totalTime = Date.now() - startTime;
    
    logger.error("[Retry] Operation failed after all attempts", {
      context,
      attempts: attempt,
      totalTime,
      finalError: lastError?.message,
    });

    return {
      success: false,
      error: lastError || new Error("Unknown error"),
      attempts: attempt,
      totalTime,
    };
  }

  /**
   * 计算指数退避延迟
   */
  private static calculateDelay(attempt: number, config: Required<RetryOptions>): number {
    let delay = config.baseDelay * Math.pow(config.exponentialBase, attempt - 1);
    
    // 限制最大延迟
    delay = Math.min(delay, config.maxDelay);
    
    // 添加抖动以避免雷群效应
    if (config.jitter) {
      delay = delay * (0.5 + Math.random() * 0.5);
    }
    
    return Math.round(delay);
  }

  /**
   * 休眠指定毫秒
   */
  private static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 创建特定于视频下载的重试策略
   */
  static createVideoDownloadRetry(): RetryOptions {
    return {
      maxAttempts: 5,
      baseDelay: 3000,
      maxDelay: 30000,
      exponentialBase: 1.5,
      jitter: true,
      shouldRetry: (error: any, attempt: number) => {
        // 视频下载特定的重试逻辑
        if (attempt >= 5) return false;
        
        // 网络相关错误
        if (error?.code === 'ECONNRESET' || 
            error?.code === 'ENOTFOUND' || 
            error?.code === 'ETIMEDOUT' ||
            error?.name === 'TimeoutError' ||
            error?.name === 'AbortError') {
          return true;
        }
        
        // 通用 fetch 失败错误和底层网络错误
        if (error?.message === 'fetch failed' || 
            error?.message?.includes('fetch failed') ||
            error?.cause?.code === 'ECONNRESET' ||
            error?.cause?.code === 'ETIMEDOUT' ||
            error?.cause?.code === 'ENOTFOUND') {
          return true;
        }
        
        // HTTP 状态码相关
        const status = error?.status || error?.response?.status || error?.statusCode;
        if (status === 408 || // 请求超时
            status === 429 || // 速率限制
            status === 500 || // 服务器内部错误
            status === 502 || // 网关错误
            status === 503 || // 服务不可用
            status === 504) { // 网关超时
          return true;
        }
        
        // 404 不重试 - 文件不存在
        if (status === 404) {
          return false;
        }
        
        return false;
      }
    };
  }

  /**
   * 创建特定于文件上传的重试策略
   */
  static createFileUploadRetry(): RetryOptions {
    return {
      maxAttempts: 5,
      baseDelay: 1000,
      maxDelay: 10000,
      exponentialBase: 1.5,
      jitter: true,
      shouldRetry: (error: any, attempt: number) => {
        if (attempt >= 5) return false;
        
        // 上传特定错误
        if (error?.code === 'ECONNRESET' ||
            error?.code === 'EPIPE' ||
            error?.name === 'TimeoutError') {
          return true;
        }
        
        // S3/R2 特定错误
        if (error?.name === 'ServiceUnavailable' ||
            error?.name === 'SlowDown' ||
            error?.name === 'RequestTimeout') {
          return true;
        }
        
        return false;
      }
    };
  }

  /**
   * 创建特定于数据库操作的重试策略
   */
  static createDatabaseRetry(): RetryOptions {
    return {
      maxAttempts: 3,
      baseDelay: 500,
      maxDelay: 5000,
      exponentialBase: 2,
      jitter: true,
      shouldRetry: (error: any, attempt: number) => {
        if (attempt >= 3) return false;
        
        // 数据库连接错误
        if (error?.code === 'ECONNREFUSED' ||
            error?.code === 'P1001' || // Prisma connection error
            error?.code === 'P1008' || // Operations timed out
            error?.code === 'P1017') { // Server has closed the connection
          return true;
        }
        
        // 事务冲突
        if (error?.code === 'P2034') { // Transaction conflict
          return true;
        }
        
        return false;
      }
    };
  }
}

/**
 * 便捷的重试装饰器
 */
export function withRetry<T extends any[], R>(
  options: RetryOptions = {},
  context?: string
) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: TypedPropertyDescriptor<(...args: T) => Promise<R>>
  ) {
    const originalMethod = descriptor.value!;
    
    descriptor.value = async function (...args: T): Promise<R> {
      const result = await RetryService.execute(
        () => originalMethod.apply(this, args),
        options,
        context || `${target.constructor.name}.${propertyKey}`
      );
      
      if (!result.success) {
        throw result.error;
      }
      
      return result.result!;
    };
    
    return descriptor;
  };
}