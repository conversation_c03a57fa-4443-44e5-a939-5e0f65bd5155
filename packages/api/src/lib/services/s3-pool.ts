import { S3Client, HeadObjectCommand, PutObjectCommand } from "@aws-sdk/client-s3";
import { Upload } from "@aws-sdk/lib-storage";
import { Readable } from "stream";
import { logger } from "@repo/logs";

export interface S3PoolConfig {
  poolSize: number;
  maxRetries: number;
  requestTimeout: number;
  healthCheckInterval: number;
  connectionTimeout: number;
}

export interface S3PoolStats {
  totalClients: number;
  activeClients: number;
  healthyClients: number;
  failedClients: number;
  totalRequests: number;
  failedRequests: number;
  avgResponseTime: number;
  lastHealthCheck: Date;
}

export interface S3OperationResult<T> {
  result: T;
  clientIndex: number;
  duration: number;
  retryCount: number;
}

/**
 * S3客户端池管理器
 * 提供连接池、负载均衡、故障转移和性能监控
 */
export class S3ClientPool {
  private static instance: S3ClientPool;
  private clientPool: S3Client[] = [];
  private config: S3PoolConfig;
  private roundRobinIndex = 0;
  private healthCheckTimer?: NodeJS.Timeout;
  private stats: S3PoolStats;
  private bucketName: string;
  
  private constructor() {
    this.config = this.loadPoolConfig();
    this.bucketName = process.env.NEXT_PUBLIC_MEDIA_BUCKET_NAME || "fluxfly";
    this.stats = this.initializeStats();
    
    this.initializePool();
    this.startHealthCheck();
  }
  
  static getInstance(): S3ClientPool {
    if (!this.instance) {
      this.instance = new S3ClientPool();
    }
    return this.instance;
  }

  /**
   * 加载连接池配置
   */
  private loadPoolConfig(): S3PoolConfig {
    return {
      poolSize: parseInt(process.env.S3_POOL_SIZE || '4'),
      maxRetries: parseInt(process.env.S3_MAX_RETRIES || '3'),
      requestTimeout: parseInt(process.env.S3_REQUEST_TIMEOUT || '300000'), // 5分钟
      healthCheckInterval: parseInt(process.env.S3_HEALTH_CHECK_INTERVAL || '120000'), // 2分钟
      connectionTimeout: parseInt(process.env.S3_CONNECTION_TIMEOUT || '60000') // 1分钟
    };
  }

  /**
   * 初始化统计信息
   */
  private initializeStats(): S3PoolStats {
    return {
      totalClients: 0,
      activeClients: 0,
      healthyClients: 0,
      failedClients: 0,
      totalRequests: 0,
      failedRequests: 0,
      avgResponseTime: 0,
      lastHealthCheck: new Date()
    };
  }

  /**
   * 初始化客户端池
   */
  private initializePool() {
    const s3Endpoint = process.env.S3_ENDPOINT;
    const s3AccessKeyId = process.env.S3_ACCESS_KEY_ID;
    const s3SecretAccessKey = process.env.S3_SECRET_ACCESS_KEY;
    
    if (!s3Endpoint || !s3AccessKeyId || !s3SecretAccessKey) {
      throw new Error("Missing required S3 environment variables");
    }

    logger.info('[S3Pool] Initializing S3 client pool', {
      poolSize: this.config.poolSize,
      endpoint: s3Endpoint,
      bucketName: this.bucketName
    });

    for (let i = 0; i < this.config.poolSize; i++) {
      try {
        const client = this.createS3Client(i);
        this.clientPool.push(client);
        
        logger.debug('[S3Pool] S3 client created', { 
          clientIndex: i,
          endpoint: s3Endpoint
        });
        
      } catch (error) {
        logger.error('[S3Pool] Failed to create S3 client', {
          clientIndex: i,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    this.stats.totalClients = this.clientPool.length;
    this.stats.activeClients = this.clientPool.length;
    
    logger.info('[S3Pool] S3 client pool initialized', {
      totalClients: this.stats.totalClients,
      poolConfig: this.config
    });
  }

  /**
   * 创建单个S3客户端
   */
  private createS3Client(index: number): S3Client {
    return new S3Client({
      endpoint: process.env.S3_ENDPOINT!,
      region: process.env.S3_REGION || "auto",
      credentials: {
        accessKeyId: process.env.S3_ACCESS_KEY_ID!,
        secretAccessKey: process.env.S3_SECRET_ACCESS_KEY!,
      },
      forcePathStyle: true,
      // 每个客户端独立配置
      maxAttempts: this.config.maxRetries,
      requestTimeout: this.config.requestTimeout,
      retryMode: 'adaptive',
      // 为每个客户端添加标识
      customUserAgent: `VideoProcessor-S3Pool-Client-${index}`
    });
  }

  /**
   * 获取可用的S3客户端（轮询分配）
   */
  getClient(): S3Client {
    if (this.clientPool.length === 0) {
      throw new Error('No S3 clients available in pool');
    }

    // 轮询分配
    const client = this.clientPool[this.roundRobinIndex];
    this.roundRobinIndex = (this.roundRobinIndex + 1) % this.clientPool.length;
    
    return client;
  }

  /**
   * 获取最优客户端（基于健康状态）
   */
  getOptimalClient(): S3Client {
    // 简化版本，返回轮询客户端
    // 可以扩展为根据响应时间和错误率选择最优客户端
    return this.getClient();
  }

  /**
   * 执行S3操作（带重试和故障转移）
   */
  async executeOperation<T>(
    operation: (client: S3Client) => Promise<T>,
    operationName: string = 'unknown'
  ): Promise<S3OperationResult<T>> {
    const startTime = Date.now();
    let lastError: Error;
    let retryCount = 0;
    
    this.stats.totalRequests++;

    // 尝试所有客户端
    for (let attempt = 0; attempt < this.config.maxRetries; attempt++) {
      const clientIndex = this.roundRobinIndex;
      const client = this.getClient();
      
      try {
        logger.debug('[S3Pool] Executing S3 operation', {
          operationName,
          clientIndex,
          attempt: attempt + 1,
          maxRetries: this.config.maxRetries
        });

        const result = await operation(client);
        const duration = Date.now() - startTime;
        
        // 更新统计信息
        this.updateResponseTimeStats(duration);
        
        logger.debug('[S3Pool] S3 operation completed successfully', {
          operationName,
          clientIndex,
          duration: `${duration}ms`,
          retryCount
        });

        return {
          result,
          clientIndex,
          duration,
          retryCount
        };

      } catch (error) {
        lastError = error as Error;
        retryCount++;
        
        logger.warn('[S3Pool] S3 operation failed, will retry', {
          operationName,
          clientIndex,
          attempt: attempt + 1,
          error: lastError.message,
          willRetry: attempt < this.config.maxRetries - 1
        });

        // 等待一段时间再重试（指数退避）
        if (attempt < this.config.maxRetries - 1) {
          const delay = Math.min(1000 * Math.pow(2, attempt), 10000);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // 所有重试都失败
    const duration = Date.now() - startTime;
    this.stats.failedRequests++;
    
    logger.error('[S3Pool] S3 operation failed after all retries', {
      operationName,
      totalRetries: this.config.maxRetries,
      totalDuration: `${duration}ms`,
      finalError: lastError!.message
    });

    throw lastError!;
  }

  /**
   * 上传文件流
   */
  async uploadStream(
    stream: Readable,
    key: string,
    contentType: string,
    contentLength?: number,
    useMultipart: boolean = false
  ): Promise<S3OperationResult<{ size: number; contentType: string }>> {
    
    if (useMultipart) {
      return await this.executeOperation(async (client) => {
        const upload = new Upload({
          client,
          params: {
            Bucket: this.bucketName,
            Key: key,
            Body: stream,
            ContentType: contentType,
          },
          partSize: this.calculatePartSize(contentLength),
          queueSize: 3, // 控制并发数
        });

        await upload.done();
        
        // 获取文件元数据
        const headResult = await client.send(new HeadObjectCommand({
          Bucket: this.bucketName,
          Key: key,
        }));

        return {
          size: headResult.ContentLength || 0,
          contentType: headResult.ContentType || contentType
        };
      }, 'uploadStreamMultipart');
      
    } else {
      return await this.executeOperation(async (client) => {
        await client.send(new PutObjectCommand({
          Bucket: this.bucketName,
          Key: key,
          Body: stream,
          ContentType: contentType,
        }));

        // 获取文件元数据
        const headResult = await client.send(new HeadObjectCommand({
          Bucket: this.bucketName,
          Key: key,
        }));

        return {
          size: headResult.ContentLength || 0,
          contentType: headResult.ContentType || contentType
        };
      }, 'uploadStreamSimple');
    }
  }

  /**
   * 检查文件是否存在
   */
  async headObject(key: string): Promise<S3OperationResult<any>> {
    return await this.executeOperation(async (client) => {
      return await client.send(new HeadObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      }));
    }, 'headObject');
  }

  /**
   * 批量操作（利用多个客户端并行处理）
   */
  async executeBatchOperations<T>(
    operations: Array<{
      operation: (client: S3Client) => Promise<T>;
      operationName: string;
    }>,
    maxConcurrency: number = this.config.poolSize
  ): Promise<Array<S3OperationResult<T> | Error>> {
    
    logger.info('[S3Pool] Executing batch S3 operations', {
      operationCount: operations.length,
      maxConcurrency,
      poolSize: this.config.poolSize
    });

    const results: Array<S3OperationResult<T> | Error> = [];
    
    // 分批处理，控制并发数
    for (let i = 0; i < operations.length; i += maxConcurrency) {
      const batch = operations.slice(i, i + maxConcurrency);
      
      const batchResults = await Promise.allSettled(
        batch.map(({ operation, operationName }) => 
          this.executeOperation(operation, operationName)
        )
      );
      
      batchResults.forEach(result => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push(result.reason);
        }
      });
    }

    const successCount = results.filter(r => !(r instanceof Error)).length;
    
    logger.info('[S3Pool] Batch S3 operations completed', {
      total: operations.length,
      successful: successCount,
      failed: operations.length - successCount
    });

    return results;
  }

  /**
   * 健康检查
   */
  async performHealthCheck(): Promise<S3PoolStats> {
    const startTime = Date.now();
    let healthyCount = 0;
    let failedCount = 0;

    logger.debug('[S3Pool] Starting S3 client pool health check');

    const healthPromises = this.clientPool.map(async (client, index) => {
      try {
        // 简单的HEAD请求测试连接
        await client.send(new HeadObjectCommand({
          Bucket: this.bucketName,
          Key: 'health-check-non-existent-key'
        }));
      } catch (error) {
        // 404错误是正常的（文件不存在），其他错误表示连接问题
        const errorCode = (error as any)?.name;
        if (errorCode === 'NotFound' || errorCode === 'NoSuchKey') {
          healthyCount++;
          return { index, status: 'healthy' };
        } else {
          failedCount++;
          return { 
            index, 
            status: 'failed', 
            error: error instanceof Error ? error.message : String(error) 
          };
        }
      }
      
      healthyCount++;
      return { index, status: 'healthy' };
    });

    const results = await Promise.allSettled(healthPromises);
    
    // 更新统计信息
    this.stats.healthyClients = healthyCount;
    this.stats.failedClients = failedCount;
    this.stats.activeClients = healthyCount;
    this.stats.lastHealthCheck = new Date();

    const duration = Date.now() - startTime;
    
    logger.info('[S3Pool] S3 client pool health check completed', {
      ...this.stats,
      duration: `${duration}ms`,
      healthCheckResults: results.length
    });

    // 如果失败的客户端过多，记录警告
    if (failedCount > this.config.poolSize / 2) {
      logger.warn('[S3Pool] High number of failed S3 clients detected', {
        failedCount,
        totalCount: this.config.poolSize,
        failureRate: `${Math.round((failedCount / this.config.poolSize) * 100)}%`
      });
    }

    return { ...this.stats };
  }

  /**
   * 启动定期健康检查
   */
  private startHealthCheck() {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }

    this.healthCheckTimer = setInterval(async () => {
      try {
        await this.performHealthCheck();
      } catch (error) {
        logger.error('[S3Pool] Health check failed', {
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }, this.config.healthCheckInterval);

    logger.info('[S3Pool] S3 health check started', {
      interval: `${this.config.healthCheckInterval}ms`
    });
  }

  /**
   * 更新响应时间统计
   */
  private updateResponseTimeStats(duration: number) {
    // 简单的移动平均算法
    const weight = 0.1;
    this.stats.avgResponseTime = this.stats.avgResponseTime * (1 - weight) + duration * weight;
  }

  /**
   * 计算最优分片大小
   */
  private calculatePartSize(contentLength?: number): number {
    if (!contentLength) return 10 * 1024 * 1024; // 默认 10MB

    const MAX_PARTS = 10000;
    const MIN_PART_SIZE = 5 * 1024 * 1024;   // 5MB
    const MAX_PART_SIZE = 100 * 1024 * 1024; // 100MB
    
    let partSize = Math.ceil(contentLength / MAX_PARTS);
    partSize = Math.max(partSize, MIN_PART_SIZE);
    partSize = Math.min(partSize, MAX_PART_SIZE);
    
    return partSize;
  }

  /**
   * 获取统计信息
   */
  getStats(): S3PoolStats {
    return { ...this.stats };
  }

  /**
   * 关闭连接池
   */
  async shutdown(): Promise<void> {
    logger.info('[S3Pool] Shutting down S3 client pool');
    
    // 停止健康检查
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }

    // S3Client 不需要显式关闭连接
    this.clientPool = [];
    
    logger.info('[S3Pool] S3 client pool shutdown completed');
  }
}

/**
 * 便捷函数：获取S3连接池实例
 */
export function getS3Pool(): S3ClientPool {
  return S3ClientPool.getInstance();
}

/**
 * 便捷函数：执行S3操作
 */
export async function executeS3Operation<T>(
  operation: (client: S3Client) => Promise<T>,
  operationName?: string
): Promise<S3OperationResult<T>> {
  return S3ClientPool.getInstance().executeOperation(operation, operationName);
}