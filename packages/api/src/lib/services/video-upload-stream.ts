import { Readable, Transform, pipeline } from 'stream';
import { promisify } from 'util';
import { logger } from '@repo/logs';
import { request } from 'undici';
import { VideoUploadService, type UploadOptions, type VideoUploadResult } from './video-upload';
import type { FileInfo, UploadStrategy } from '../../../../apps/consumer/src/services/file-precheck';

const pipelineAsync = promisify(pipeline);

export interface StreamingUploadOptions extends UploadOptions {
  enableCompression?: boolean;
  chunkTimeout?: number;
  retryAttempts?: number;
}

export interface StreamChunk {
  data: Buffer;
  index: number;
  size: number;
  isLast: boolean;
}

/**
 * 流式上传服务 - 零拷贝实现
 * 消除临时文件，直接内存流处理
 */
export class StreamingVideoUploadService extends VideoUploadService {
  private static instance: StreamingVideoUploadService;
  
  static getInstance(): StreamingVideoUploadService {
    if (!this.instance) {
      this.instance = new StreamingVideoUploadService();
    }
    return this.instance;
  }

  /**
   * 零拷贝流式上传 - 主入口
   */
  async uploadViaZeroCopyStream(
    sourceUrl: string,
    fileInfo: FileInfo,
    strategy: UploadStrategy,
    options: StreamingUploadOptions
  ): Promise<VideoUploadResult> {
    const startTime = Date.now();
    
    logger.info('[StreamingUpload] Starting zero-copy streaming upload', {
      sourceUrl,
      fileSize: fileInfo.size,
      fileSizeMB: Math.round(fileInfo.size / (1024 * 1024)),
      strategy: strategy.method,
      userId: options.userId,
      generationId: options.generationId
    });

    try {
      // 根据策略选择上传方法
      switch (strategy.method) {
        case 'direct-stream':
          return await this.directStreamUpload(sourceUrl, fileInfo, options);
        
        case 'chunked-stream':
          return await this.chunkedStreamUpload(sourceUrl, fileInfo, strategy, options);
        
        case 'parallel-multipart':
          return await this.parallelMultipartUpload(sourceUrl, fileInfo, strategy, options);
        
        default:
          // 回退到原有的临时文件方式
          logger.warn('[StreamingUpload] Unknown strategy, falling back to temp file upload', {
            strategy: strategy.method
          });
          return await this.uploadViaTempFile(sourceUrl, options);
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('[StreamingUpload] Zero-copy streaming upload failed', {
        sourceUrl,
        strategy: strategy.method,
        error: error instanceof Error ? error.message : String(error),
        duration: `${duration}ms`
      });
      throw error;
    }
  }

  /**
   * 直接流式上传（小文件 <10MB）
   */
  private async directStreamUpload(
    sourceUrl: string,
    fileInfo: FileInfo,
    options: StreamingUploadOptions
  ): Promise<VideoUploadResult> {
    logger.info('[StreamingUpload] Using direct stream upload', {
      fileSize: fileInfo.size
    });

    // 1. 创建下载流
    const downloadStream = await this.createOptimizedDownloadStream(sourceUrl);
    
    // 2. 生成S3密钥
    const filename = options.filename || `${Date.now()}-${options.generationId}.mp4`;
    const s3Key = this.generateS3Key(options.userId, options.generationId, filename);
    
    // 3. 创建上传流处理管道
    const uploadResult = await this.streamToS3Direct(
      downloadStream,
      s3Key,
      fileInfo.contentType,
      fileInfo.size
    );
    
    // 4. 生成CDN URL
    const cdnUrl = this.generateCdnUrl(s3Key);

    logger.info('[StreamingUpload] Direct stream upload completed', {
      cdnUrl,
      uploadedSize: uploadResult.size,
      contentType: uploadResult.contentType
    });

    return {
      cdnUrl,
      s3Key,
      size: uploadResult.size,
      contentType: uploadResult.contentType
    };
  }

  /**
   * 分块流式上传（中等文件 10-100MB）
   */
  private async chunkedStreamUpload(
    sourceUrl: string,
    fileInfo: FileInfo,
    strategy: UploadStrategy,
    options: StreamingUploadOptions
  ): Promise<VideoUploadResult> {
    logger.info('[StreamingUpload] Using chunked stream upload', {
      fileSize: fileInfo.size,
      chunkSize: strategy.chunkSize,
      concurrency: strategy.concurrency
    });

    // 1. 生成S3密钥
    const filename = options.filename || `${Date.now()}-${options.generationId}.mp4`;
    const s3Key = this.generateS3Key(options.userId, options.generationId, filename);
    
    // 2. 创建分块下载流
    const chunkStreams = await this.createChunkedDownloadStreams(
      sourceUrl,
      fileInfo.size,
      strategy.chunkSize,
      strategy.concurrency
    );
    
    // 3. 并行上传分块
    const uploadResult = await this.streamChunksToS3(
      chunkStreams,
      s3Key,
      fileInfo.contentType,
      strategy.concurrency
    );
    
    // 4. 生成CDN URL
    const cdnUrl = this.generateCdnUrl(s3Key);

    logger.info('[StreamingUpload] Chunked stream upload completed', {
      cdnUrl,
      uploadedSize: uploadResult.size,
      chunkCount: chunkStreams.length
    });

    return {
      cdnUrl,
      s3Key,
      size: uploadResult.size,
      contentType: uploadResult.contentType
    };
  }

  /**
   * 并行分片上传（大文件 >100MB）
   */
  private async parallelMultipartUpload(
    sourceUrl: string,
    fileInfo: FileInfo,
    strategy: UploadStrategy,
    options: StreamingUploadOptions
  ): Promise<VideoUploadResult> {
    logger.info('[StreamingUpload] Using parallel multipart upload', {
      fileSize: fileInfo.size,
      chunkSize: strategy.chunkSize,
      concurrency: strategy.concurrency
    });

    // 1. 生成S3密钥
    const filename = options.filename || `${Date.now()}-${options.generationId}.mp4`;
    const s3Key = this.generateS3Key(options.userId, options.generationId, filename);
    
    // 2. 创建并行分片流
    const partStreams = await this.createParallelPartStreams(
      sourceUrl,
      fileInfo.size,
      strategy.chunkSize,
      strategy.concurrency
    );
    
    // 3. 执行并行分片上传
    const uploadResult = await this.streamPartsToS3Multipart(
      partStreams,
      s3Key,
      fileInfo.contentType,
      strategy.concurrency
    );
    
    // 4. 生成CDN URL
    const cdnUrl = this.generateCdnUrl(s3Key);

    logger.info('[StreamingUpload] Parallel multipart upload completed', {
      cdnUrl,
      uploadedSize: uploadResult.size,
      partCount: partStreams.length
    });

    return {
      cdnUrl,
      s3Key,
      size: uploadResult.size,
      contentType: uploadResult.contentType
    };
  }

  /**
   * 创建优化的下载流
   */
  private async createOptimizedDownloadStream(url: string): Promise<Readable> {
    logger.debug('[StreamingUpload] Creating optimized download stream', { url });

    const { statusCode, body } = await request(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'VideoProcessor/2.0-Streaming',
        'Accept': 'video/*,*/*',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
      headersTimeout: 30000,
      bodyTimeout: 0, // 不设置body超时，让流自然结束
    });

    if (statusCode < 200 || statusCode >= 400) {
      throw new Error(`HTTP ${statusCode}: Failed to create download stream`);
    }

    if (!body) {
      throw new Error('Response body is null');
    }

    return body as Readable;
  }

  /**
   * 创建分块下载流
   */
  private async createChunkedDownloadStreams(
    url: string,
    totalSize: number,
    chunkSize: number,
    concurrency: number
  ): Promise<Array<{ stream: Readable; start: number; end: number; index: number }>> {
    const chunks = [];
    const chunkCount = Math.ceil(totalSize / chunkSize);
    
    logger.debug('[StreamingUpload] Creating chunked download streams', {
      totalSize,
      chunkSize,
      chunkCount,
      concurrency
    });

    for (let i = 0; i < chunkCount; i++) {
      const start = i * chunkSize;
      const end = Math.min(start + chunkSize - 1, totalSize - 1);
      
      const { body } = await request(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'VideoProcessor/2.0-Chunked',
          'Range': `bytes=${start}-${end}`,
          'Cache-Control': 'no-cache',
        },
        headersTimeout: 30000,
        bodyTimeout: 0,
      });

      chunks.push({
        stream: body as Readable,
        start,
        end,
        index: i
      });
    }

    return chunks;
  }

  /**
   * 创建并行分片流
   */
  private async createParallelPartStreams(
    url: string,
    totalSize: number,
    partSize: number,
    concurrency: number
  ): Promise<Array<{ stream: Readable; partNumber: number; size: number }>> {
    const parts = [];
    const partCount = Math.ceil(totalSize / partSize);
    
    logger.debug('[StreamingUpload] Creating parallel part streams', {
      totalSize,
      partSize,
      partCount,
      concurrency
    });

    // 分批创建，控制并发
    for (let batch = 0; batch < Math.ceil(partCount / concurrency); batch++) {
      const batchStart = batch * concurrency;
      const batchEnd = Math.min(batchStart + concurrency, partCount);
      
      const batchPromises = [];
      for (let i = batchStart; i < batchEnd; i++) {
        const start = i * partSize;
        const end = Math.min(start + partSize - 1, totalSize - 1);
        const size = end - start + 1;
        
        batchPromises.push(
          request(url, {
            method: 'GET',
            headers: {
              'User-Agent': 'VideoProcessor/2.0-Parallel',
              'Range': `bytes=${start}-${end}`,
              'Cache-Control': 'no-cache',
            },
            headersTimeout: 30000,
            bodyTimeout: 0,
          }).then(({ body }) => ({
            stream: body as Readable,
            partNumber: i + 1,
            size
          }))
        );
      }
      
      const batchResults = await Promise.all(batchPromises);
      parts.push(...batchResults);
    }

    return parts;
  }

  /**
   * 直接流到S3
   */
  private async streamToS3Direct(
    stream: Readable,
    s3Key: string,
    contentType: string,
    contentLength?: number
  ): Promise<{ size: number; contentType: string }> {
    // 使用继承的上传方法
    return await this.uploadStreamToR2(stream, s3Key, contentType, contentLength);
  }

  /**
   * 分块流到S3
   */
  private async streamChunksToS3(
    chunks: Array<{ stream: Readable; start: number; end: number; index: number }>,
    s3Key: string,
    contentType: string,
    concurrency: number
  ): Promise<{ size: number; contentType: string }> {
    // 合并所有分块流
    const combinedStream = this.combineChunkStreams(chunks);
    
    // 计算总大小
    const totalSize = chunks.reduce((sum, chunk) => sum + (chunk.end - chunk.start + 1), 0);
    
    return await this.uploadStreamToR2(combinedStream, s3Key, contentType, totalSize);
  }

  /**
   * 并行分片到S3
   */
  private async streamPartsToS3Multipart(
    parts: Array<{ stream: Readable; partNumber: number; size: number }>,
    s3Key: string,
    contentType: string,
    concurrency: number
  ): Promise<{ size: number; contentType: string }> {
    // 合并所有分片流
    const combinedStream = this.combinePartStreams(parts);
    
    // 计算总大小
    const totalSize = parts.reduce((sum, part) => sum + part.size, 0);
    
    return await this.uploadStreamToR2(combinedStream, s3Key, contentType, totalSize);
  }

  /**
   * 合并分块流
   */
  private combineChunkStreams(
    chunks: Array<{ stream: Readable; start: number; end: number; index: number }>
  ): Readable {
    // 按索引排序
    chunks.sort((a, b) => a.index - b.index);
    
    let currentIndex = 0;
    
    return new Readable({
      read() {
        if (currentIndex < chunks.length) {
          const chunk = chunks[currentIndex];
          chunk.stream.on('data', (data) => this.push(data));
          chunk.stream.on('end', () => {
            currentIndex++;
            if (currentIndex >= chunks.length) {
              this.push(null); // 结束流
            }
          });
          chunk.stream.on('error', (error) => this.emit('error', error));
        }
      }
    });
  }

  /**
   * 合并分片流
   */
  private combinePartStreams(
    parts: Array<{ stream: Readable; partNumber: number; size: number }>
  ): Readable {
    // 按分片号排序
    parts.sort((a, b) => a.partNumber - b.partNumber);
    
    let currentIndex = 0;
    
    return new Readable({
      read() {
        if (currentIndex < parts.length) {
          const part = parts[currentIndex];
          part.stream.on('data', (data) => this.push(data));
          part.stream.on('end', () => {
            currentIndex++;
            if (currentIndex >= parts.length) {
              this.push(null); // 结束流
            }
          });
          part.stream.on('error', (error) => this.emit('error', error));
        }
      }
    });
  }

  /**
   * 获取流式上传统计信息
   */
  getStreamingStats(): {
    totalUploads: number;
    directStreamUploads: number;
    chunkedStreamUploads: number;
    parallelMultipartUploads: number;
    avgUploadTime: number;
  } {
    // 这里可以添加统计追踪逻辑
    return {
      totalUploads: 0,
      directStreamUploads: 0,
      chunkedStreamUploads: 0,
      parallelMultipartUploads: 0,
      avgUploadTime: 0
    };
  }
}