import {
  S3Client,
  HeadObjectCommand,
  PutObjectCommand,
} from "@aws-sdk/client-s3";
import { Upload } from "@aws-sdk/lib-storage";
import { Readable, pipeline } from "stream";
import { createReadStream, createWriteStream } from "fs";
import { unlink, stat } from "fs/promises";
import { tmpdir } from "os";
import { join } from "path";
import { promisify } from "util";
import { randomUUID } from "crypto";
import { request, Agent } from "undici";
import { logger } from "@repo/logs";
import { RetryService } from "./retry";

const pipelineAsync = promisify(pipeline);

export interface UploadOptions {
  userId: string;
  generationId: string;
  filename?: string;
  contentType?: string;
}

export interface VideoUploadResult {
  cdnUrl: string;
  s3Key: string;
  size?: number;
  contentType?: string;
}

export class VideoUploadService {
  private s3Client: S3Client;
  private bucketName: string;
  private cdnBaseUrl: string;
  private environment: string;
  private isEdgeRuntime: boolean;
  private undiciAgent: Agent;

  constructor() {
    const s3Endpoint = process.env.S3_ENDPOINT;
    if (!s3Endpoint) {
      throw new Error("Missing S3_ENDPOINT environment variable");
    }

    const s3AccessKeyId = process.env.S3_ACCESS_KEY_ID;
    if (!s3AccessKeyId) {
      throw new Error("Missing S3_ACCESS_KEY_ID environment variable");
    }

    const s3SecretAccessKey = process.env.S3_SECRET_ACCESS_KEY;
    if (!s3SecretAccessKey) {
      throw new Error("Missing S3_SECRET_ACCESS_KEY environment variable");
    }

    this.bucketName = process.env.NEXT_PUBLIC_MEDIA_BUCKET_NAME || "fluxfly";
    this.cdnBaseUrl = process.env.NEXT_PUBLIC_VIDEO_CDN_BASE_URL || "https://videocdn.fluxfly.ai";
    this.environment = process.env.NODE_ENV || "production";
    this.isEdgeRuntime = process.env.NEXT_RUNTIME === "edge";

    logger.info("[VideoUpload] Environment", {
      runtime: process.env.NEXT_RUNTIME || "nodejs",
      supportsFS: !this.isEdgeRuntime,
    });

    this.s3Client = new S3Client({
      endpoint: s3Endpoint,
      region: process.env.S3_REGION || "auto",
      credentials: {
        accessKeyId: s3AccessKeyId,
        secretAccessKey: s3SecretAccessKey,
      },
      forcePathStyle: true,
    });

    // 创建专用的 undici Agent，强制使用 IPv4 解决连接问题
    this.undiciAgent = new Agent({
      connect: {
        family: 4,
        timeout: 30000,
        keepAlive: true,
        keepAliveInitialDelay: 10000,
      },
    });
  }

  /**
   * 主要上传方法：智能选择策略
   */
  async uploadFromUrl(
    sourceUrl: string,
    options: UploadOptions
  ): Promise<VideoUploadResult> {
    logger.info("[VideoUpload] Starting upload", {
      sourceUrl,
      userId: options.userId,
      generationId: options.generationId,
      strategy: this.isEdgeRuntime ? "direct-stream" : "temp-file",
    });

    if (this.isEdgeRuntime) {
      // Edge Runtime: 不支持fs，使用直接流式处理
      return await this.uploadViaDirectStream(sourceUrl, options);
    } else {
      // Node.js Runtime: 使用临时文件（最稳定）
      return await this.uploadViaTempFile(sourceUrl, options);
    }
  }

  /**
   * 流式上传入口（Edge Runtime专用）
   */
  async uploadFromUrlStreaming(
    sourceUrl: string,
    options: UploadOptions
  ): Promise<VideoUploadResult> {
    return await this.uploadViaDirectStream(sourceUrl, options);
  }

  /**
   * 方法1：临时文件方式（推荐，最稳定）
   */
  private async uploadViaTempFile(
    sourceUrl: string,
    options: UploadOptions
  ): Promise<VideoUploadResult> {
    let tempFilePath: string | null = null;

    try {
      // 1. 下载到临时文件
      tempFilePath = await this.downloadToTempFile(sourceUrl);
      
      // 2. 生成S3密钥
      const filename = options.filename || `${Date.now()}-${randomUUID()}.mp4`;
      const s3Key = this.generateS3Key(options.userId, options.generationId, filename);
      
      // 3. 从临时文件上传到R2
      const uploadResult = await this.uploadFileToR2(tempFilePath, s3Key, options.contentType);
      
      // 4. 生成CDN URL
      const cdnUrl = this.generateCdnUrl(s3Key);

      logger.info("[VideoUpload] Upload completed via temp file", {
        cdnUrl,
        size: uploadResult.size,
      });

      return {
        cdnUrl,
        s3Key,
        size: uploadResult.size,
        contentType: uploadResult.contentType,
      };
    } finally {
      // 清理临时文件
      if (tempFilePath) {
        try {
          await unlink(tempFilePath);
        } catch (error) {
          logger.warn("[VideoUpload] Failed to delete temp file", { tempFilePath });
        }
      }
    }
  }

  /**
   * 方法2：直接流式处理（Edge Runtime专用）
   */
  private async uploadViaDirectStream(
    sourceUrl: string,
    options: UploadOptions
  ): Promise<VideoUploadResult> {
    const retryOptions = RetryService.createVideoDownloadRetry();
    
    const result = await RetryService.execute(
      async () => {
        // 1. 获取视频流
        const response = await this.fetchVideoStream(sourceUrl);
        
        // 2. 生成S3密钥
        const filename = options.filename || `${Date.now()}-${randomUUID()}.mp4`;
        const s3Key = this.generateS3Key(options.userId, options.generationId, filename);
        
        // 3. 直接流式上传
        const contentLength = response.headers.get('content-length');
        const uploadResult = await this.uploadStreamToR2(
          response.body as any, 
          s3Key, 
          options.contentType,
          contentLength ? parseInt(contentLength, 10) : undefined
        );
        
        // 4. 生成CDN URL
        const cdnUrl = this.generateCdnUrl(s3Key);

        return {
          cdnUrl,
          s3Key,
          size: uploadResult.size,
          contentType: uploadResult.contentType,
        };
      },
      retryOptions,
      `directStream-${sourceUrl}`
    );

    if (!result.success) {
      throw result.error || new Error("Direct stream upload failed");
    }

    return result.result!;
  }

  /**
   * 下载到临时文件（带重试）
   */
  private async downloadToTempFile(url: string): Promise<string> {
    const retryOptions = RetryService.createVideoDownloadRetry();
    
    const result = await RetryService.execute(
      async () => {
        const tempFilename = `video-${randomUUID()}.mp4`;
        const tempPath = join(tmpdir(), tempFilename);

        logger.info("[VideoUpload] Starting download to temp file", { url, tempPath });

        const response = await this.fetchVideoStream(url);
        const writeStream = createWriteStream(tempPath);
        
        await pipelineAsync(response.body as any, writeStream);

        logger.info("[VideoUpload] Download to temp file completed", { tempPath });
        return tempPath;
      },
      retryOptions,
      `downloadToTemp-${url}`
    );

    if (!result.success) {
      throw result.error || new Error("Download to temp file failed");
    }

    return result.result!;
  }

  /**
   * 获取视频流（使用 undici，强制 IPv4）
   */
  private async fetchVideoStream(url: string): Promise<Response> {
    logger.info("[VideoUpload] Fetching video stream with undici (IPv4)", { url });

    const { statusCode, headers, body } = await request(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'VideoProcessor/1.0',
        'Accept': 'video/*,*/*',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
      headersTimeout: 60000,
      bodyTimeout: 300000,
      dispatcher: this.undiciAgent,
    });

    if (statusCode < 200 || statusCode >= 400) {
      throw new Error(`HTTP ${statusCode}: Request failed`);
    }

    if (!body) {
      throw new Error("Response body is null");
    }

    logger.info("[VideoUpload] Video stream obtained", {
      status: statusCode,
      contentType: headers['content-type'],
      contentLength: headers['content-length'],
    });

    return {
      ok: statusCode >= 200 && statusCode < 300,
      status: statusCode,
      statusText: statusCode === 200 ? 'OK' : 'Error',
      headers: {
        get: (name: string) => headers[name.toLowerCase()] as string | null,
      },
      body: body as any,
    } as Response;
  }

  /**
   * 从文件上传到R2
   */
  private async uploadFileToR2(
    filePath: string,
    s3Key: string,
    contentType?: string
  ): Promise<{ size: number; contentType: string }> {
    logger.info("[VideoUpload] Uploading file to R2", { filePath, s3Key });

    // 获取文件大小
    const fileStats = await stat(filePath);
    const fileSize = fileStats.size;
    
    const fileStream = createReadStream(filePath);
    const detectedContentType = contentType || "video/mp4";

    return this.uploadStreamToR2(fileStream, s3Key, detectedContentType, fileSize);
  }

  /**
   * 直接流式上传到R2
   */
  private async uploadStreamToR2(
    stream: Readable,
    s3Key: string,
    contentType?: string,
    contentLength?: number
  ): Promise<{ size: number; contentType: string }> {
    logger.info("[VideoUpload] Uploading stream to R2", { s3Key, contentLength });

    const detectedContentType = contentType || "video/mp4";
    
    // 智能选择上传策略
    const uploadStrategy = this.selectUploadStrategy(contentLength);
    logger.info("[VideoUpload] Selected upload strategy", { strategy: uploadStrategy, contentLength });

    try {
      if (uploadStrategy === 'simple') {
        return await this.simpleUploadToR2(stream, s3Key, detectedContentType, contentLength);
      } else {
        return await this.multipartUploadToR2(stream, s3Key, detectedContentType, contentLength);
      }
    } catch (error) {
      logger.error("[VideoUpload] Upload failed", { error: error instanceof Error ? error.message : error, s3Key });
      throw error;
    }
  }

  /**
   * 选择上传策略
   */
  private selectUploadStrategy(contentLength?: number): 'simple' | 'multipart' {
    // 如果未知文件大小或小于 50MB，使用简单上传
    const MULTIPART_THRESHOLD = 50 * 1024 * 1024; // 50MB
    return (contentLength && contentLength >= MULTIPART_THRESHOLD) ? 'multipart' : 'simple';
  }

  /**
   * 简单上传（适用于小文件）
   */
  private async simpleUploadToR2(
    stream: Readable,
    s3Key: string,
    contentType: string,
    contentLength?: number
  ): Promise<{ size: number; contentType: string }> {
    logger.info("[VideoUpload] Using simple upload strategy", { s3Key, contentLength });

    const putCommandParams: any = {
      Bucket: this.bucketName,
      Key: s3Key,
      Body: stream,
      ContentType: contentType,
    };

    // 如果有内容长度，添加到命令参数中
    if (contentLength !== undefined) {
      putCommandParams.ContentLength = contentLength;
    }

    const putCommand = new PutObjectCommand(putCommandParams);

    // 添加超时控制
    const uploadPromise = this.s3Client.send(putCommand);
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Simple upload timeout after 3 minutes')), 3 * 60 * 1000);
    });

    await Promise.race([uploadPromise, timeoutPromise]);

    // 获取文件元数据
    const headCommand = new HeadObjectCommand({
      Bucket: this.bucketName,
      Key: s3Key,
    });
    const metadata = await this.s3Client.send(headCommand);

    logger.info("[VideoUpload] Simple upload to R2 completed", { s3Key, size: metadata.ContentLength });

    return {
      size: metadata.ContentLength || 0,
      contentType: metadata.ContentType || contentType,
    };
  }

  /**
   * 分片上传（适用于大文件）
   */
  private async multipartUploadToR2(
    stream: Readable,
    s3Key: string,
    contentType: string,
    contentLength?: number
  ): Promise<{ size: number; contentType: string }> {
    logger.info("[VideoUpload] Using multipart upload strategy", { s3Key, contentLength });

    // 根据文件大小动态调整分片大小
    const partSize = this.calculatePartSize(contentLength);
    
    const upload = new Upload({
      client: this.s3Client,
      params: {
        Bucket: this.bucketName,
        Key: s3Key,
        Body: stream,
        ContentType: contentType,
      },
      partSize,
      queueSize: 3, // 降低并发数，提高稳定性
    });

    // 添加超时控制
    const uploadPromise = upload.done();
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Multipart upload timeout after 10 minutes')), 10 * 60 * 1000);
    });

    logger.info("[VideoUpload] Starting multipart upload", { partSize, queueSize: 3 });
    await Promise.race([uploadPromise, timeoutPromise]);

    // 获取文件元数据
    const headCommand = new HeadObjectCommand({
      Bucket: this.bucketName,
      Key: s3Key,
    });
    const metadata = await this.s3Client.send(headCommand);

    logger.info("[VideoUpload] Multipart upload to R2 completed", { s3Key, size: metadata.ContentLength });

    return {
      size: metadata.ContentLength || 0,
      contentType: metadata.ContentType || contentType,
    };
  }

  /**
   * 根据文件大小计算最优分片大小
   */
  private calculatePartSize(contentLength?: number): number {
    if (!contentLength) return 10 * 1024 * 1024; // 默认 10MB

    // AWS S3 最多支持 10,000 个分片
    const MAX_PARTS = 10000;
    const MIN_PART_SIZE = 5 * 1024 * 1024;   // 5MB
    const MAX_PART_SIZE = 100 * 1024 * 1024; // 100MB
    
    let partSize = Math.ceil(contentLength / MAX_PARTS);
    partSize = Math.max(partSize, MIN_PART_SIZE);
    partSize = Math.min(partSize, MAX_PART_SIZE);
    
    logger.info("[VideoUpload] Calculated part size", { contentLength, partSize });
    return partSize;
  }

  /**
   * 生成S3键
   */
  private generateS3Key(userId: string, generationId: string, filename: string): string {
    return `web-cdn/fluxfly/${this.environment}/${userId}/${generationId}/ori/${filename}`;
  }

  /**
   * 生成CDN URL
   */
  generateCdnUrl(s3Key: string): string {
    const cleanKey = s3Key.startsWith("/") ? s3Key.slice(1) : s3Key;
    return `${this.cdnBaseUrl}/${cleanKey}`;
  }

  /**
   * 验证视频URL
   */
  async validateVideo(url: string): Promise<boolean> {
    try {
      const { statusCode, headers } = await request(url, {
        method: "HEAD",
        headers: {
          'User-Agent': 'VideoProcessor/1.0',
          'Connection': 'keep-alive',
        },
        headersTimeout: 30000,
        dispatcher: this.undiciAgent,
      });
      
      if (statusCode < 200 || statusCode >= 400) {
        return false;
      }

      const contentType = headers['content-type'] as string;
      return contentType?.startsWith("video/") ?? false;
    } catch (error) {
      logger.error("[VideoUpload] Video validation failed", { url, error });
      return false;
    }
  }
}