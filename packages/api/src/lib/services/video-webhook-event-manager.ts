import { createId } from "@paralleldrive/cuid2";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { logger } from "@repo/logs";
import { db } from "@repo/database";
import type { VideoWebhookEvent, Prisma } from "@repo/database/prisma/generated/client";
import { VideoWebhookEventProcessStatus } from "@repo/database/prisma/generated/client";

dayjs.extend(utc);

export interface CreateVideoWebhookEventData {
  eventId: string;
  provider: string;
  eventType: string;
  externalTaskId: string;
  generationId?: string;
  userId?: string;
  status: string;  // 新增：webhook原始状态
  rawPayload: any;
  headers: Record<string, string>;
  signature?: string;
}

export interface VideoWebhookEventStats {
  total: number;
  completed: number;
  failed: number;
  pending: number;
  processing: number;
  avgProcessingTime?: number;
  retryRate?: number;
}

/**
 * VideoWebhookEvent 管理器
 * 负责 webhook 事件的创建、查询、状态管理等操作
 */
export class VideoWebhookEventManager {
  
  /**
   * 创建新的 webhook 事件记录
   */
  async createEvent(data: CreateVideoWebhookEventData): Promise<VideoWebhookEvent> {
    try {
      logger.info('[VideoWebhookEventManager] Creating webhook event', {
        provider: data.provider,
        eventId: data.eventId,
        externalTaskId: data.externalTaskId
      });

      // 检查是否重复事件
      const existingEvent = await this.findEventByProviderEventIdAndStatus(
        data.provider, 
        data.eventId, 
        data.status
      );
      if (existingEvent) {
        logger.warn('[VideoWebhookEventManager] Duplicate webhook event skipped', {
          provider: data.provider,
          eventType: data.eventType,
          status: data.status,
          existingId: existingEvent.id
        });
        return existingEvent;
      }

      const now = dayjs().utc().toDate();
      const eventData: Prisma.VideoWebhookEventCreateInput = {
        id: createId(),
        eventId: data.eventId,
        provider: data.provider,
        eventType: data.eventType,
        externalTaskId: data.externalTaskId,
        generationId: data.generationId,
        userId: data.userId,
        rawPayload: data.rawPayload as Prisma.InputJsonValue,
        headers: data.headers as Prisma.InputJsonValue,
        signature: data.signature,
        status: data.status,
        processStatus: VideoWebhookEventProcessStatus.pending,
        retryCount: 0,
        maxRetries: 5,
        receivedAt: now,
        createdAt: now,
        updatedAt: now
      };

      const event = await db.videoWebhookEvent.create({
        data: eventData
      });

      logger.info('[VideoWebhookEventManager] Webhook event created successfully', {
        id: event.id,
        provider: event.provider,
        eventId: event.eventId,
        generationId: event.generationId
      });

      return event;
    } catch (error) {
      logger.error('[VideoWebhookEventManager] Failed to create webhook event', {
        error: error instanceof Error ? error.message : String(error),
        provider: data.provider,
        eventId: data.eventId
      });
      throw error;
    }
  }

  /**
   * 根据提供商、事件ID和状态查找事件
   */
  async findEventByProviderEventIdAndStatus(
    provider: string, 
    eventId: string, 
    status: string
  ): Promise<VideoWebhookEvent | null> {
    return await db.videoWebhookEvent.findUnique({
      where: {
        provider_eventId_status: {
          provider,
          eventId,
          status
        }
      }
    });
  }


  /**
   * 获取待处理的事件（供处理器调用）
   */
  async getPendingEvents(limit: number = 10): Promise<VideoWebhookEvent[]> {
    const now = dayjs().utc().toDate();
    
    return await db.videoWebhookEvent.findMany({
      where: {
        OR: [
          { processStatus: VideoWebhookEventProcessStatus.pending },
          {
            processStatus: VideoWebhookEventProcessStatus.failed,
            nextRetryAt: {
              lte: now
            }
          }
        ]
      },
      orderBy: [
        { receivedAt: 'asc' }
      ],
      take: limit
    });
  }

  /**
   * 根据外部任务ID列表获取待处理的事件（用于Job触发webhook处理）
   */
  async getPendingEventsByExternalTaskIds(externalTaskIds: string[], limit: number = 10): Promise<VideoWebhookEvent[]> {
    if (externalTaskIds.length === 0) {
      return [];
    }

    return await db.videoWebhookEvent.findMany({
      where: {
        processStatus: VideoWebhookEventProcessStatus.pending,
        externalTaskId: {
          in: externalTaskIds
        }
      },
      take: limit,
      orderBy: { receivedAt: 'asc' }
    });
  }

  /**
   * 标记事件为处理中
   */
  async markAsProcessing(eventId: string): Promise<void> {
    try {
      await db.videoWebhookEvent.update({
        where: { id: eventId },
        data: {
          processStatus: VideoWebhookEventProcessStatus.processing,
          updatedAt: dayjs().utc().toDate()
        }
      });
      
      logger.debug('[VideoWebhookEventManager] Event marked as processing', { eventId });
    } catch (error) {
      logger.error('[VideoWebhookEventManager] Failed to mark event as processing', {
        eventId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 标记事件处理成功
   */
  async markAsCompleted(eventId: string): Promise<void> {
    try {
      const now = dayjs().utc().toDate();
      await db.videoWebhookEvent.update({
        where: { id: eventId },
        data: {
          processStatus: VideoWebhookEventProcessStatus.completed,
          processedAt: now,
          updatedAt: now,
          lastError: null,
          errorDetails: null
        }
      });
      
      logger.info('[VideoWebhookEventManager] Event marked as completed', { eventId });
    } catch (error) {
      logger.error('[VideoWebhookEventManager] Failed to mark event as completed', {
        eventId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 标记事件处理失败
   */
  async markAsFailed(eventId: string, error: Error): Promise<void> {
    try {
      const event = await db.videoWebhookEvent.findUnique({
        where: { id: eventId },
        select: { retryCount: true, maxRetries: true }
      });

      if (!event) {
        throw new Error(`Event not found: ${eventId}`);
      }

      const newRetryCount = event.retryCount + 1;
      const shouldRetry = newRetryCount < event.maxRetries;
      const now = dayjs().utc().toDate();

      const updateData: Prisma.VideoWebhookEventUpdateInput = {
        retryCount: newRetryCount,
        lastError: error.message,
        errorDetails: {
          message: error.message,
          stack: error.stack,
          timestamp: now.toISOString()
        } as Prisma.InputJsonValue,
        updatedAt: now
      };

      if (shouldRetry) {
        // 计算下次重试时间（指数退避）
        updateData.nextRetryAt = this.calculateNextRetryTime(newRetryCount);
        updateData.processStatus = VideoWebhookEventProcessStatus.failed; // 保持 failed 状态等待重试
        
        logger.warn('[VideoWebhookEventManager] Event failed, will retry', {
          eventId,
          retryCount: newRetryCount,
          maxRetries: event.maxRetries,
          nextRetryAt: updateData.nextRetryAt,
          error: error.message
        });
      } else {
        updateData.processStatus = VideoWebhookEventProcessStatus.failed; // 永久失败
        
        logger.error('[VideoWebhookEventManager] Event permanently failed', {
          eventId,
          retryCount: newRetryCount,
          maxRetries: event.maxRetries,
          error: error.message
        });
      }

      await db.videoWebhookEvent.update({
        where: { id: eventId },
        data: updateData
      });

    } catch (updateError) {
      logger.error('[VideoWebhookEventManager] Failed to mark event as failed', {
        eventId,
        originalError: error.message,
        updateError: updateError instanceof Error ? updateError.message : String(updateError)
      });
      throw updateError;
    }
  }


  /**
   * 计算下次重试时间（指数退避 + 抖动）
   */
  private calculateNextRetryTime(retryCount: number): Date {
    // 指数退避：1s, 2s, 4s, 8s, 16s, 32s, 60s (最长1分钟)
    const baseDelay = Math.min(Math.pow(2, retryCount), 60);
    
    // 添加随机抖动，避免雷群效应
    const jitter = Math.random() * 0.3; // ±30%
    const delay = baseDelay * (1 + jitter);
    
    return dayjs().utc().add(delay, 'second').toDate();
  }

  /**
   * 获取处理统计
   */
  async getProcessingStats(timeRangeHours: number = 24): Promise<VideoWebhookEventStats> {
    const since = dayjs().utc().subtract(timeRangeHours, 'hour').toDate();
    
    const [totalCount, completedCount, failedCount, pendingCount, processingCount] = await Promise.all([
      this.countEvents(since),
      this.countEventsByProcessStatus(VideoWebhookEventProcessStatus.completed, since),
      this.countEventsByProcessStatus(VideoWebhookEventProcessStatus.failed),
      this.countEventsByProcessStatus(VideoWebhookEventProcessStatus.pending),
      this.countEventsByProcessStatus(VideoWebhookEventProcessStatus.processing)
    ]);

    const avgProcessingTime = await this.getAverageProcessingTime(since);
    const retryRate = totalCount > 0 ? await this.getRetryRate(since) : 0;

    return {
      total: totalCount,
      completed: completedCount,
      failed: failedCount,
      pending: pendingCount,
      processing: processingCount,
      avgProcessingTime,
      retryRate
    };
  }

  /**
   * 统计指定时间范围内的事件数量
   */
  private async countEvents(since?: Date): Promise<number> {
    return await db.videoWebhookEvent.count({
      where: since ? {
        receivedAt: { gte: since }
      } : undefined
    });
  }

  /**
   * 按处理状态统计事件数量
   */
  private async countEventsByProcessStatus(processStatus: VideoWebhookEventProcessStatus, since?: Date): Promise<number> {
    return await db.videoWebhookEvent.count({
      where: {
        processStatus,
        ...(since && { receivedAt: { gte: since } })
      }
    });
  }

  /**
   * 计算平均处理时间
   */
  private async getAverageProcessingTime(since: Date): Promise<number | undefined> {
    // 直接使用原生查询计算平均处理时间，因为 Prisma 不支持计算时间差
    try {
      const avgResult = await db.$queryRaw<Array<{ avg_processing_time: number }>>`
        SELECT AVG(EXTRACT(EPOCH FROM ("processedAt" - "receivedAt"))) as avg_processing_time
        FROM "video_webhook_event"
        WHERE "processStatus" = 'completed' 
        AND "receivedAt" >= ${since}
        AND "processedAt" IS NOT NULL
      `;
      
      return avgResult[0]?.avg_processing_time || undefined;
    } catch (error) {
      logger.warn('[VideoWebhookEventManager] Failed to calculate average processing time', { error });
      return undefined;
    }
  }

  /**
   * 计算重试率
   */
  private async getRetryRate(since: Date): Promise<number> {
    const [totalEvents, retriedEvents] = await Promise.all([
      this.countEvents(since),
      db.videoWebhookEvent.count({
        where: {
          receivedAt: { gte: since },
          retryCount: { gt: 0 }
        }
      })
    ]);

    return totalEvents > 0 ? retriedEvents / totalEvents : 0;
  }

  /**
   * 获取失败事件详情
   */
  async getFailedEvents(limit: number = 50): Promise<VideoWebhookEvent[]> {
    return await db.videoWebhookEvent.findMany({
      where: {
        processStatus: VideoWebhookEventProcessStatus.failed
      },
      orderBy: {
        receivedAt: 'desc'
      },
      take: limit
    });
  }

  /**
   * 清理旧事件（定期任务）
   */
  async cleanupOldEvents(olderThanDays: number = 30): Promise<number> {
    const cutoffDate = dayjs().utc().subtract(olderThanDays, 'day').toDate();
    
    try {
      const result = await db.videoWebhookEvent.deleteMany({
        where: {
          processStatus: VideoWebhookEventProcessStatus.completed,
          processedAt: {
            lt: cutoffDate
          }
        }
      });

      logger.info('[VideoWebhookEventManager] Cleaned up old events', {
        deletedCount: result.count,
        olderThanDays
      });

      return result.count;
    } catch (error) {
      logger.error('[VideoWebhookEventManager] Failed to cleanup old events', {
        error: error instanceof Error ? error.message : String(error),
        olderThanDays
      });
      throw error;
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'warning' | 'critical';
    pendingCount: number;
    failedCount: number;
    avgProcessingTime?: number;
    lastProcessedAt?: Date;
  }> {
    const stats = await this.getProcessingStats(1); // 最近1小时
    const lastProcessedEvent = await db.videoWebhookEvent.findFirst({
      where: {
        processStatus: VideoWebhookEventProcessStatus.completed
      },
      orderBy: {
        processedAt: 'desc'
      },
      select: {
        processedAt: true
      }
    });

    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    
    // 判断健康状态
    if (stats.pending > 100 || stats.failed > 50) {
      status = 'critical';
    } else if (stats.pending > 50 || stats.failed > 20) {
      status = 'warning';
    }

    return {
      status,
      pendingCount: stats.pending,
      failedCount: stats.failed,
      avgProcessingTime: stats.avgProcessingTime,
      lastProcessedAt: lastProcessedEvent?.processedAt || undefined
    };
  }
}