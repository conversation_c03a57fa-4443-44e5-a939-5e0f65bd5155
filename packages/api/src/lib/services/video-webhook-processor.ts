import { logger } from "@repo/logs";
import { type VideoWebhookEvent, type Generation } from "@repo/database";
import { VideoWebhookEventManager } from "./video-webhook-event-manager";
import { VideoUploadService } from "./video-upload";
import type { WebhookPayload } from "../routes/webhooks/shared/types";
import { extractMetadataFromInput, type WebhookMetadata } from "@repo/utils";
import { JobsManager } from "@repo/jobs";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";

dayjs.extend(utc);

export interface ProcessingContext {
  useStreamingUpload?: boolean;
  preCheckResult?: {
    isValid: boolean;
    size: number;
    contentType: string;
    supportsRangeRequests: boolean;
  };
}

/**
 * VideoWebhook 处理器
 * 负责异步处理存储在数据库中的 webhook 事件
 */
export class VideoWebhookProcessor {
  private eventManager: VideoWebhookEventManager;
  private uploadService: VideoUploadService;
  
  constructor() {
    this.eventManager = new VideoWebhookEventManager();
    this.uploadService = new VideoUploadService();
  }

  /**
   * 处理单个 webhook 事件
   */
  async processEvent(event: VideoWebhookEvent, context?: ProcessingContext): Promise<void> {
    const startTime = Date.now();
    
    try {
      logger.info('[VideoWebhookProcessor] Starting event processing', {
        eventId: event.id,
        provider: event.provider,
        externalTaskId: event.externalTaskId
      });

      // 1. 标记为处理中
      await this.eventManager.markAsProcessing(event.id);
      
      // 2. 解析载荷和提取metadata
      const payload = this.parseEventPayload(event);
      let metadata: WebhookMetadata | undefined;
      
      // 尝试从原始载荷中提取metadata
      if (event.rawPayload && typeof event.rawPayload === 'object') {
        const rawPayload = event.rawPayload as any;
        if (rawPayload.input) {
          const extractedMetadata = extractMetadataFromInput(rawPayload.input);
          metadata = extractedMetadata || undefined;
        }
      }
      
      // 3. 查找对应的生成记录 (使用metadata优化)
      const generation = await this.findGeneration(payload.externalTaskId, metadata);
      
      if (!generation) {
        throw new Error(`Generation not found for external task ID: ${payload.externalTaskId}`);
      }
      
      // 4. 根据原始webhook状态决定处理策略
      if (event.status === 'processing') {
        // processing状态：仅更新job和generation状态
        await this.updateGenerationStatus(generation, payload);
        logger.info('[VideoWebhookProcessor] Processing status webhook - updated job and generation only', {
          generationId: generation.id,
          externalStatus: event.status
        });
        
      } else if (event.status === 'succeeded') {
        // succeeded状态：先上传视频，成功后再更新状态和URL
        if (payload.resultUrl) {
          await this.handleVideoUploadAndUpdateStatus(generation, payload, context);
        } else {
          // 没有视频URL，只更新状态
          await this.updateGenerationStatus(generation, payload);
          logger.warn('[VideoWebhookProcessor] Succeeded status but no result URL', {
            generationId: generation.id,
            externalTaskId: event.externalTaskId
          });
        }
        logger.info('[VideoWebhookProcessor] Succeeded status webhook - completed full processing', {
          generationId: generation.id,
          externalStatus: event.status,
          hasResultUrl: !!payload.resultUrl
        });
        
      } else {
        // 其他状态（如failed）：仅更新状态
        await this.updateGenerationStatus(generation, payload);
        logger.info(`[VideoWebhookProcessor] ${event.status} status webhook - updated status only`, {
          generationId: generation.id,
          externalStatus: event.status
        });
      }
      
      // 6. 标记事件处理成功
      await this.eventManager.markAsCompleted(event.id);
      
      const processingTime = Date.now() - startTime;
      logger.info('[VideoWebhookProcessor] Event processed successfully', {
        eventId: event.id,
        generationId: generation.id,
        status: payload.status,
        processingTime: `${processingTime}ms`,
        hasMetadata: !!metadata,
        userId: metadata?.userId,
        provider: metadata?.provider
      });
      
    } catch (error) {
      const processingTime = Date.now() - startTime;
      logger.error('[VideoWebhookProcessor] Event processing failed', {
        eventId: event.id,
        error: error instanceof Error ? error.message : String(error),
        processingTime: `${processingTime}ms`
      });
      
      // 标记失败，会自动计算重试
      await this.eventManager.markAsFailed(event.id, error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }

  /**
   * 批量处理待处理事件
   */
  async processPendingEvents(): Promise<{
    processed: number;
    succeeded: number;
    failed: number;
  }> {
    const events = await this.eventManager.getPendingEvents(10);
    
    if (events.length === 0) {
      return { processed: 0, succeeded: 0, failed: 0 };
    }

    logger.info('[VideoWebhookProcessor] Processing pending events', { 
      eventCount: events.length 
    });

    // 并行处理多个事件（控制并发数）
    const results = await Promise.allSettled(
      events.map(event => this.processEvent(event))
    );

    const stats = {
      processed: results.length,
      succeeded: results.filter(r => r.status === 'fulfilled').length,
      failed: results.filter(r => r.status === 'rejected').length
    };

    logger.info('[VideoWebhookProcessor] Batch processing completed', stats);
    return stats;
  }

  /**
   * 解析事件载荷
   */
  private parseEventPayload(event: VideoWebhookEvent): WebhookPayload {
    try {
      const rawPayload = event.rawPayload as any;
      
      // 根据不同提供商解析载荷
      switch (event.provider) {
        case 'replicate':
          return this.parseReplicatePayload(rawPayload, event.externalTaskId);
        case 'fal':
          return this.parseFalPayload(rawPayload, event.externalTaskId);
        default:
          throw new Error(`Unsupported provider: ${event.provider}`);
      }
    } catch (error) {
      logger.error('[VideoWebhookProcessor] Failed to parse event payload', {
        eventId: event.id,
        provider: event.provider,
        error: error instanceof Error ? error.message : String(error)
      });
      throw new Error(`Failed to parse ${event.provider} payload: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 解析 Replicate 载荷
   */
  private parseReplicatePayload(rawPayload: any, externalTaskId: string): WebhookPayload {
    return {
      externalTaskId,
      status: this.mapReplicateStatus(rawPayload.status),
      resultUrl: this.extractReplicateResultUrl(rawPayload.output),
      errorMessage: rawPayload.error?.message || rawPayload.error,
      progress: this.calculateReplicateProgress(rawPayload.status)
    };
  }

  /**
   * 解析 Fal 载荷
   */
  private parseFalPayload(rawPayload: any, externalTaskId: string): WebhookPayload {
    return {
      externalTaskId,
      status: this.mapFalStatus(rawPayload.status),
      resultUrl: this.extractFalResultUrl(rawPayload),
      errorMessage: rawPayload.error?.message || rawPayload.error,
      progress: rawPayload.progress || this.calculateFalProgress(rawPayload.status)
    };
  }

  /**
   * 映射 Replicate 状态
   */
  private mapReplicateStatus(status: string): 'waiting' | 'processing' | 'succeeded' | 'failed' {
    const statusMap: Record<string, 'waiting' | 'processing' | 'succeeded' | 'failed'> = {
      'starting': 'waiting',
      'processing': 'processing',
      'succeeded': 'succeeded',
      'failed': 'failed',
      'canceled': 'failed'
    };
    
    return statusMap[status] || 'waiting';
  }

  /**
   * 映射 Fal 状态
   */
  private mapFalStatus(status: string): 'waiting' | 'processing' | 'succeeded' | 'failed' {
    const statusMap: Record<string, 'waiting' | 'processing' | 'succeeded' | 'failed'> = {
      'IN_QUEUE': 'waiting',
      'IN_PROGRESS': 'processing', 
      'COMPLETED': 'succeeded',
      'FAILED': 'failed'
    };
    
    return statusMap[status] || 'waiting';
  }

  /**
   * 从 Replicate output 中提取结果 URL
   */
  private extractReplicateResultUrl(output: any): string | undefined {
    if (!output) return undefined;
    
    if (Array.isArray(output) && output.length > 0) {
      return output[0];
    }
    
    if (typeof output === 'string') {
      return output;
    }
    
    if (typeof output === 'object' && output.video_url) {
      return output.video_url;
    }
    
    return undefined;
  }

  /**
   * 从 Fal 响应中提取结果 URL
   */
  private extractFalResultUrl(payload: any): string | undefined {
    return payload.video?.url || payload.result_url || payload.output?.url;
  }

  /**
   * 计算 Replicate 进度
   */
  private calculateReplicateProgress(status: string): number | undefined {
    switch (status) {
      case 'starting':
        return 0;
      case 'processing':
        return undefined;
      case 'succeeded':
      case 'failed':
      case 'canceled':
        return 100;
      default:
        return undefined;
    }
  }

  /**
   * 计算 Fal 进度
   */
  private calculateFalProgress(status: string): number | undefined {
    switch (status) {
      case 'IN_QUEUE':
        return 0;
      case 'IN_PROGRESS':
        return undefined;
      case 'COMPLETED':
      case 'FAILED':
        return 100;
      default:
        return undefined;
    }
  }

  /**
   * 查找生成记录 (优化版，支持从metadata直接获取)
   */
  private async findGeneration(externalTaskId: string, metadata?: WebhookMetadata): Promise<Generation | null> {
    try {
      // 如果有metadata且包含generationId，直接根据ID查找（更快）
      if (metadata?.generationId) {
        logger.debug('[VideoWebhookProcessor] Using generationId from metadata', {
          generationId: metadata.generationId,
          externalTaskId
        });
        
        const generation = await JobsManager.findGenerationWithUserAndJob(metadata.generationId);
        
        if (generation) {
          return generation;
        }
        
        logger.warn('[VideoWebhookProcessor] Generation not found by metadata ID, falling back to externalTaskId lookup', {
          generationId: metadata.generationId,
          externalTaskId
        });
      }
      
      // 回退到传统的externalTaskId查找
      return await JobsManager.findGenerationByExternalTaskIdWithUserAndJob(externalTaskId);
    } catch (error) {
      logger.error('[VideoWebhookProcessor] Failed to find generation', {
        externalTaskId,
        generationId: metadata?.generationId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw new Error(`Failed to find generation: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 更新生成状态 (使用JobsManager封装的方法)
   */
  private async updateGenerationStatus(generation: Generation, payload: WebhookPayload): Promise<void> {
    try {
      await JobsManager.updateJobStatusWithGeneration(
        generation.jobId,
        generation.id,
        payload.status,
        payload.errorMessage
      );

      logger.info('[VideoWebhookProcessor] Generation status updated', {
        generationId: generation.id,
        jobId: generation.jobId,
        oldStatus: generation.status,
        newStatus: payload.status
      });

    } catch (error) {
      logger.error('[VideoWebhookProcessor] Failed to update generation status', {
        generationId: generation.id,
        error: error instanceof Error ? error.message : String(error)
      });
      throw new Error(`Failed to update generation status: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 处理视频上传并更新状态（事务处理）
   */
  private async handleVideoUploadAndUpdateStatus(generation: Generation, payload: WebhookPayload, context?: ProcessingContext): Promise<void> {
    try {
      logger.info('[VideoWebhookProcessor] Starting video upload and status update', {
        generationId: generation.id,
        jobId: generation.jobId,
        userId: generation.userId,
        resultUrl: payload.resultUrl,
        status: payload.status
      });

      // 步骤1: 基础URL检查（快速失败）
      if (!payload.resultUrl || !payload.resultUrl.startsWith('http')) {
        throw new Error(`Invalid video URL format: ${payload.resultUrl}`);
      }

      // 步骤2: 直接上传视频（集成验证和上传）
      logger.info('[VideoWebhookProcessor] Step 1: Starting video upload', {
        generationId: generation.id,
        resultUrl: payload.resultUrl,
        useStreamingUpload: context?.useStreamingUpload || false
      });
      
      const uploadResult = context?.useStreamingUpload 
        ? await this.uploadService.uploadFromUrlStreaming(payload.resultUrl!, {
            userId: generation.userId,
            generationId: generation.id,
            filename: `${generation.id}.mp4`,
            contentType: 'video/mp4'
          })
        : await this.uploadService.uploadFromUrl(payload.resultUrl!, {
            userId: generation.userId,
            generationId: generation.id,
            filename: `${generation.id}.mp4`,
            contentType: 'video/mp4'
          });

      logger.info('[VideoWebhookProcessor] Video upload completed', {
        generationId: generation.id,
        cdnUrl: uploadResult.cdnUrl,
        size: uploadResult.size,
        contentType: uploadResult.contentType
      });

      // 步骤2: 在事务中同时更新Job和Generation
      logger.info('[VideoWebhookProcessor] Step 2: Updating database with transaction', {
        generationId: generation.id,
        jobId: generation.jobId,
        status: payload.status,
        cdnUrl: uploadResult.cdnUrl
      });
      
      await JobsManager.updateJobAndGenerationWithVideoUrl(
        generation.jobId,
        generation.id,
        payload.status,
        uploadResult.cdnUrl,
        uploadResult.cdnUrl,
        payload.errorMessage
      );

      logger.info('[VideoWebhookProcessor] ✅ Video upload and status update completed successfully', {
        generationId: generation.id,
        jobId: generation.jobId,
        cdnUrl: uploadResult.cdnUrl,
        size: uploadResult.size,
        status: payload.status,
        duration: Date.now() - Date.now() // 会在实际使用时计算
      });

    } catch (error) {
      logger.error('[VideoWebhookProcessor] Video upload or status update failed', {
        generationId: generation.id,
        resultUrl: payload.resultUrl,
        error: error instanceof Error ? error.message : String(error)
      });
      
      // 视频上传失败，更新状态为failed
      await this.updateGenerationStatus(generation, {
        ...payload,
        status: 'failed',
        errorMessage: `Video upload failed: ${error instanceof Error ? error.message : String(error)}`
      });
      
      throw error;
    }
  }
}