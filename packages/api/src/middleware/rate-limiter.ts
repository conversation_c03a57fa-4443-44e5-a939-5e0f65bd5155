import { RateLimiterRedis } from 'rate-limiter-flexible';
import { HTTPException } from 'hono/http-exception';
import { redis } from '@repo/redis';
import type { Context, Next } from 'hono';

// 将 Upstash Redis 适配为 rate-limiter-flexible 格式
const redisAdapter = {
  async get(key: string) {
    const result = await redis.get(key);
    return result;
  },
  
  async set(key: string, value: any, ttl?: number) {
    if (ttl) {
      await redis.setex(key, ttl, value);
    } else {
      await redis.set(key, value);
    }
  },
  
  async del(key: string) {
    await redis.del(key);
  },
  
  async eval(script: string, numKeys: number, ...args: any[]) {
    return await redis.eval(script, numKeys, ...args);
  }
};

// 创建限流器配置
const createLimiterConfig = (userPlan: string = 'free') => {
  const limits = {
    free: { 
      points: 3,           // 3次请求
      duration: 300,       // 5分钟内
      blockDuration: 300,  // 超限后阻塞5分钟
    },
    pro: { 
      points: 15,          // 15次请求
      duration: 300,       // 5分钟内
      blockDuration: 180,  // 超限后阻塞3分钟
    },
    enterprise: { 
      points: 60,          // 60次请求
      duration: 300,       // 5分钟内
      blockDuration: 60,   // 超限后阻塞1分钟
    },
  };
  
  return limits[userPlan] || limits.free;
};

// 视频生成限流中间件 - 基于IP的通用限流
export const videoGenerationRateLimit = async (c: Context, next: Next) => {
  // 完全不使用 c.get('user')，只基于IP
  const clientIp = c.req.header('x-forwarded-for') || 
                   c.req.header('x-real-ip') || 
                   c.req.header('cf-connecting-ip') ||
                   'unknown';
  
  const limiter = new RateLimiterRedis({
    storeClient: redisAdapter,
    keyPrefix: 'video_gen_ip',
    points: 1000,        // 每个IP 1000次请求（开发环境）
    duration: 60,        // 1分钟内
    blockDuration: 10,   // 超限阻塞10秒
  });
  
  try {
    const resRateLimiter = await limiter.consume(clientIp);
    
    c.header('X-RateLimit-Limit', '1000');
    c.header('X-RateLimit-Remaining', String(resRateLimiter.remainingPoints));
    c.header('X-RateLimit-Reset', String(new Date(Date.now() + resRateLimiter.msBeforeNext)));
    
    await next();
  } catch (rejRes) {
    const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
    
    c.header('Retry-After', String(secs));
    c.header('X-RateLimit-Limit', '1000');
    c.header('X-RateLimit-Remaining', '0');
    c.header('X-RateLimit-Reset', String(new Date(Date.now() + rejRes.msBeforeNext)));
    
    throw new HTTPException(429, {
      message: `Rate limit exceeded. Try again in ${secs} seconds.`,
    });
  }
};

// 通用API限流中间件
export const generalApiRateLimit = async (c: Context, next: Next) => {
  const user = c.get('user');
  const key = user?.id || c.req.header('x-forwarded-for') || 'anonymous';
  
  const limiter = new RateLimiterRedis({
    storeClient: redisAdapter,
    keyPrefix: 'api_general',
    points: 100,        // 100次请求
    duration: 60,       // 每分钟
    blockDuration: 60,  // 阻塞60秒
  });
  
  try {
    await limiter.consume(key);
    await next();
  } catch (rejRes) {
    const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
    
    c.header('Retry-After', String(secs));
    throw new HTTPException(429, {
      message: `API rate limit exceeded. Try again in ${secs} seconds.`,
    });
  }
};