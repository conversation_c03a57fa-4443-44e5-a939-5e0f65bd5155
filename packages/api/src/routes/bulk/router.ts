import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { authMiddleware } from "../../middleware/auth";
import { GenerationService } from "../generations/lib/generation-service";
import {
  BatchDeleteGenerationsRequestSchema,
  BatchDeleteGenerationResponseSchema,
  BatchFavoriteGenerationsRequestSchema,
  BatchFavoriteGenerationResponseSchema,
  BatchDownloadGenerationsRequestSchema,
  BatchDownloadGenerationResponseSchema
} from "./schemas";

export const bulkRouter = new Hono()
  .basePath("/bulk")

  // POST /api/bulk/generations/delete - 批量软删除generations
  .post(
    "/generations/delete",
    authMiddleware,
    describeRoute({
      tags: ["Bulk Operations"],
      summary: "Batch delete generations",
      description: "Soft delete multiple generations at once using transaction. Users can only delete their own generations unless they are admin. Maximum 50 generations per request. All generations will be deleted or none (atomic operation).",
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: BatchDeleteGenerationsRequestSchema,
          },
        },
      },
      responses: {
        200: {
          description: "All generations deleted successfully",
          content: {
            'application/json': {
              schema: BatchDeleteGenerationResponseSchema,
            },
          },
        },
        400: {
          description: "Bad request - Invalid parameters or some generations cannot be deleted",
          content: {
            'application/json': {
              schema: BatchDeleteGenerationResponseSchema,
            },
          },
        },
        401: {
          description: "Unauthorized - Authentication required",
          content: {
            'application/json': {
              schema: BatchDeleteGenerationResponseSchema,
            },
          },
        },
        403: {
          description: "Forbidden - Access denied for some generations",
          content: {
            'application/json': {
              schema: BatchDeleteGenerationResponseSchema,
            },
          },
        },
        404: {
          description: "Some generations not found",
          content: {
            'application/json': {
              schema: BatchDeleteGenerationResponseSchema,
            },
          },
        },
        409: {
          description: "Some generations already deleted",
          content: {
            'application/json': {
              schema: BatchDeleteGenerationResponseSchema,
            },
          },
        },
        500: {
          description: "Internal server error",
          content: {
            'application/json': {
              schema: BatchDeleteGenerationResponseSchema,
            },
          },
        },
      },
    }),
    validator("json", BatchDeleteGenerationsRequestSchema),
    async (c) => {
      const user = c.get("user");
      const { generationIds } = c.req.valid("json");

      console.log(`[BulkAPI] User ${user.id} requesting batch deletion of ${generationIds.length} generations`);

      const result = await GenerationService.batchDeleteGenerations(user, generationIds);
      
      if (result.deleted) {
        console.log(`[BulkAPI] Batch deletion completed by user ${user.id}: ${result.deleted.length}/${generationIds.length} deleted`);
        return c.json(result, 200);
      } else if (result.error) {
        console.warn(`[BulkAPI] Failed batch deletion by user ${user.id}:`, result.error);
        
        // 根据错误类型返回相应的状态码
        const errorMsg = result.error;
        if (errorMsg.includes('not found')) {
          return c.json(result, 404);
        } else if (errorMsg.includes('Access denied')) {
          return c.json(result, 403);
        } else if (errorMsg.includes('already deleted')) {
          return c.json(result, 409);
        } else if (errorMsg.includes('No generation IDs')) {
          return c.json(result, 400);
        } else if (errorMsg.includes('cannot be deleted')) {
          return c.json(result, 400);
        } else {
          return c.json(result, 500);
        }
      } else {
        return c.json({ error: 'Unknown error' }, 500);
      }
    }
  )

  // POST /api/bulk/generations/favorite - 批量设置favorite
  .post(
    "/generations/favorite",
    authMiddleware,
    describeRoute({
      tags: ["Bulk Operations"],
      summary: "Batch favorite generations",
      description: "Set favorite status for multiple generations at once. Users can only modify their own generations unless they are admin. Maximum 50 generations per request.",
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: BatchFavoriteGenerationsRequestSchema,
          },
        },
      },
      responses: {
        200: {
          description: "All generations updated successfully",
          content: {
            'application/json': {
              schema: BatchFavoriteGenerationResponseSchema,
            },
          },
        },
        400: {
          description: "Bad request - Invalid parameters",
          content: {
            'application/json': {
              schema: BatchFavoriteGenerationResponseSchema,
            },
          },
        },
        401: {
          description: "Unauthorized - Authentication required",
          content: {
            'application/json': {
              schema: BatchFavoriteGenerationResponseSchema,
            },
          },
        },
        403: {
          description: "Forbidden - Access denied for some generations",
          content: {
            'application/json': {
              schema: BatchFavoriteGenerationResponseSchema,
            },
          },
        },
        404: {
          description: "Some generations not found",
          content: {
            'application/json': {
              schema: BatchFavoriteGenerationResponseSchema,
            },
          },
        },
        500: {
          description: "Internal server error",
          content: {
            'application/json': {
              schema: BatchFavoriteGenerationResponseSchema,
            },
          },
        },
      },
    }),
    validator("json", BatchFavoriteGenerationsRequestSchema),
    async (c) => {
      const user = c.get("user");
      const { generationIds, favorite } = c.req.valid("json");

      console.log(`[BulkAPI] User ${user.id} requesting batch favorite update for ${generationIds.length} generations to ${favorite}`);

      const result = await GenerationService.batchFavoriteGenerations(user, generationIds, favorite);
      
      if (result.updated) {
        console.log(`[BulkAPI] Batch favorite update completed by user ${user.id}: ${result.updated.length}/${generationIds.length} updated`);
        return c.json(result, 200);
      } else if (result.error) {
        console.warn(`[BulkAPI] Failed batch favorite update by user ${user.id}:`, result.error);
        
        // 根据错误类型返回相应的状态码
        const errorMsg = result.error;
        if (errorMsg.includes('not found')) {
          return c.json(result, 404);
        } else if (errorMsg.includes('Access denied')) {
          return c.json(result, 403);
        } else if (errorMsg.includes('No generation IDs')) {
          return c.json(result, 400);
        } else {
          return c.json(result, 500);
        }
      } else {
        return c.json({ error: 'Unknown error' }, 500);
      }
    }
  )

  // POST /api/bulk/generations/download - 批量生成下载链接
  .post(
    "/generations/download",
    authMiddleware,
    describeRoute({
      tags: ["Bulk Operations"],
      summary: "Batch generate download URLs",
      description: "Generate signed download URLs for multiple generations at once. Users can only download their own generations unless they are admin. Maximum 50 generations per request. All URLs will be generated or none (atomic operation).",
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: BatchDownloadGenerationsRequestSchema,
          },
        },
      },
      responses: {
        200: {
          description: "All download URLs generated successfully",
          content: {
            'application/json': {
              schema: BatchDownloadGenerationResponseSchema,
            },
          },
        },
        400: {
          description: "Bad request - Invalid parameters or some files not ready",
          content: {
            'application/json': {
              schema: BatchDownloadGenerationResponseSchema,
            },
          },
        },
        401: {
          description: "Unauthorized - Authentication required",
          content: {
            'application/json': {
              schema: BatchDownloadGenerationResponseSchema,
            },
          },
        },
        403: {
          description: "Forbidden - Access denied for some generations",
          content: {
            'application/json': {
              schema: BatchDownloadGenerationResponseSchema,
            },
          },
        },
        404: {
          description: "Some generations not found",
          content: {
            'application/json': {
              schema: BatchDownloadGenerationResponseSchema,
            },
          },
        },
        422: {
          description: "Unprocessable Entity - Some files are not ready for download",
          content: {
            'application/json': {
              schema: BatchDownloadGenerationResponseSchema,
            },
          },
        },
        500: {
          description: "Internal server error",
          content: {
            'application/json': {
              schema: BatchDownloadGenerationResponseSchema,
            },
          },
        },
        503: {
          description: "Service unavailable - Storage service temporarily unavailable",
          content: {
            'application/json': {
              schema: BatchDownloadGenerationResponseSchema,
            },
          },
        },
      },
    }),
    validator("json", BatchDownloadGenerationsRequestSchema),
    async (c) => {
      const user = c.get("user");
      const { generationIds } = c.req.valid("json");

      console.log(`[BulkAPI] User ${user.id} requesting batch download for ${generationIds.length} generations`);

      const result = await GenerationService.batchGenerateDownloadUrls(user, generationIds);
      
      if (result.downloadUrls) {
        console.log(`[BulkAPI] Batch download URLs generated by user ${user.id}: ${result.downloadUrls.length}/${generationIds.length} URLs created`);
        return c.json(result, 200);
      } else if (result.error) {
        console.warn(`[BulkAPI] Failed batch download by user ${user.id}:`, result.error);
        
        // 根据错误类型返回相应的状态码
        const errorMsg = result.error;
        if (errorMsg.includes('not found')) {
          return c.json(result, 404);
        } else if (errorMsg.includes('Access denied')) {
          return c.json(result, 403);
        } else if (errorMsg.includes('not ready') || errorMsg.includes('processing')) {
          return c.json(result, 422);
        } else if (errorMsg.includes('No generation IDs')) {
          return c.json(result, 400);
        } else if (errorMsg.includes('Storage service') || errorMsg.includes('unavailable')) {
          return c.json(result, 503);
        } else {
          return c.json(result, 500);
        }
      } else {
        return c.json({ error: 'Unknown error' }, 500);
      }
    }
  );

export type BulkRouter = typeof bulkRouter;