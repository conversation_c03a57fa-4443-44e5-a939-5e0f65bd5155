import { z } from "zod";

// 批量删除请求 Schema
export const BatchDeleteGenerationsRequestSchema = z.object({
  generationIds: z.array(z.string()).min(1, "At least one generation ID is required").max(50, "Maximum 50 generations allowed per request"),
});

// 批量删除响应 Schema
export const BatchDeleteGenerationResponseSchema = z.union([
  // 成功响应
  z.object({
    deleted: z.array(z.string()).describe("Array of successfully deleted generation IDs"),
  }),
  // 失败响应
  z.object({
    error: z.string().describe("Error message describing why the batch operation failed"),
  }),
]);

// 批量favorite请求 Schema
export const BatchFavoriteGenerationsRequestSchema = z.object({
  generationIds: z.array(z.string()).min(1, "At least one generation ID is required").max(50, "Maximum 50 generations allowed per request"),
  favorite: z.boolean().describe("Whether to set favorite to true or false"),
});

// 批量favorite响应 Schema
export const BatchFavoriteGenerationResponseSchema = z.union([
  // 成功响应
  z.object({
    updated: z.array(z.string()).describe("Array of successfully updated generation IDs"),
  }),
  // 失败响应
  z.object({
    error: z.string().describe("Error message describing why the batch operation failed"),
  }),
]);

// 批量下载请求 Schema
export const BatchDownloadGenerationsRequestSchema = z.object({
  generationIds: z.array(z.string()).min(1, "At least one generation ID is required").max(50, "Maximum 50 generations allowed per request"),
});

// 批量下载响应 Schema
export const BatchDownloadGenerationResponseSchema = z.union([
  // 成功响应
  z.object({
    downloadUrls: z.array(z.string()).describe("Array of signed download URLs"),
  }),
  // 失败响应
  z.object({
    error: z.string().describe("Error message describing why the batch operation failed"),
  }),
]);

// 导出类型
export type BatchDeleteGenerationsRequest = z.infer<typeof BatchDeleteGenerationsRequestSchema>;
export type BatchDeleteGenerationResponse = z.infer<typeof BatchDeleteGenerationResponseSchema>;
export type BatchFavoriteGenerationsRequest = z.infer<typeof BatchFavoriteGenerationsRequestSchema>;
export type BatchFavoriteGenerationResponse = z.infer<typeof BatchFavoriteGenerationResponseSchema>;
export type BatchDownloadGenerationsRequest = z.infer<typeof BatchDownloadGenerationsRequestSchema>;
export type BatchDownloadGenerationResponse = z.infer<typeof BatchDownloadGenerationResponseSchema>;