import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver } from "hono-openapi/zod";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";
import { creditService } from "./lib/user-credits";

// 用户积分响应的 Zod 模式
const UserCreditsSchema = z.object({
	credits: z.number(),
	hasCredits: z.boolean(),
});

export const creditsRouter = new Hono()
	.basePath("/")

	// 获取用户积分
	.get(
		"/credits",
		authMiddleware,
		describeRoute({
			tags: ["Credits"],
			summary: "Get user credits",
			description: "Get the current user's credits for Connections Hint",
			responses: {
				200: {
					description: "User credits",
					content: {
						"application/json": {
							schema: resolver(UserCreditsSchema),
						},
					},
				},
				401: {
					description: "Unauthorized",
				},
			},
		}),
		async (c) => {
			const user = c.get("user");

			// 查询用户的信用点余额和上次奖励时间
			let userCredits = await creditService.findCreditsByUserId(
				user.id,
			);
			let { balance, hasCredits, lastAward } = userCredits;

			// 检查用户是否有积分记录
			if (balance === 0 && lastAward === null) {
				// 用户可能没有积分记录，尝试创建
				console.log(`用户 ${user.id} 没有积分记录，正在创建...`);
				userCredits = await creditService.createCredits(user.id);
				balance = userCredits.balance;
				hasCredits = userCredits.hasCredits;
				lastAward = userCredits.lastAward;
			}

			// 检查是否需要奖励积分
			const now = new Date();
			const daysSinceAward = Math.floor(
				(now.getTime() - (lastAward?.getTime() || 0)) /
					(1000 * 60 * 60 * 24),
			);

			// 每月赠送20个积分（假设30天为一个月）
			if (daysSinceAward >= 30) {
				// 更新用户积分，增加20个，同时更新最后奖励时间
				await creditService.updateCreditUsage(user.id, {
					balance: { increment: 20 },
					lastAward: now,
				});

				return c.json({
					credits: 20,
					hasCredits: true,
				});
			}

			return c.json({
				credits: balance,
				hasCredits: balance > 0,
			});
		},
	);
