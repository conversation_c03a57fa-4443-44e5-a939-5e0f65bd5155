import { db } from "@repo/database";
import { GenerationsManager, LikesRedisManager } from "@repo/jobs";
import type { Session } from "@repo/auth";
import type { 
  GetGenerationsRequest, 
  GetGenerationsResponse, 
  GetGenerationsErrorResponse,
  GetGenerationByIdApiResponse,
  PaginationInfo,
  UserSummary,
  GenerationWhereCondition,
  FavoriteApiResponse 
} from "../types";

export class GenerationService {
  /**
   * 获取生成内容列表
   */
  static async getGenerations(
    user: Session["user"], 
    params: GetGenerationsRequest
  ): Promise<GetGenerationsResponse | GetGenerationsErrorResponse> {
    try {
      // 1. 权限检查
      GenerationsManager.checkPermissions(user, params);
      
      // 2. 构建查询条件
      const whereCondition = GenerationsManager.buildWhereCondition(params, user);
      
      // 3. 执行查询（包含用户点赞状态）
      const { generations, total } = await GenerationsManager.getGenerations(
        whereCondition,
        { 
          page: params.page, 
          pageSize: params.pageSize 
        },
        user.id // 传递当前用户ID以获取点赞状态
      );

      // 4. 处理数据
      const uniqueJobs = GenerationsManager.extractUniqueJobs(generations);
      const uniqueUsers = GenerationsManager.extractUniqueUsers(generations);
      const cleanGenerations = GenerationsManager.cleanGenerationsData(generations);

      // 5. 构建分页信息
      const pagination = GenerationsManager.buildPaginationInfo(params.page, params.pageSize, total);

      return {
        success: true,
        data: {
          generations: cleanGenerations,
          jobs: uniqueJobs,
          users: uniqueUsers,
          pagination
        }
      };

    } catch (error) {
      console.error('[GenerationService] Error fetching generations:', error);
      
      if (error instanceof Error) {
        // 处理已知错误类型
        if (error.message === 'Access denied') {
          return {
            success: false,
            error: 'Access denied'
          };
        }
        
        if (error.message.includes('Invalid')) {
          return {
            success: false,
            error: error.message
          };
        }
      }
      
      // 未知错误
      return {
        success: false,
        error: 'Internal server error'
      };
    }
  }



  /**
   * 获取单个generation的详情（扩展功能）
   */
  static async getGenerationById(
    user: Session["user"], 
    generationId: string
  ): Promise<GetGenerationByIdApiResponse> {
    try {
      const generation = await GenerationsManager.getGenerationById(generationId, user.id);

      if (!generation) {
        return {
          success: false,
          error: 'Generation not found'
        };
      }

      // 权限检查
      if (!GenerationsManager.canUserAccessGeneration(user, generation)) {
        return {
          success: false,
          error: 'Access denied'
        };
      }

      const cleanGeneration = GenerationsManager.cleanGenerationsData([generation]);
      const jobs = GenerationsManager.extractUniqueJobs([generation]);
      const users = GenerationsManager.extractUniqueUsers([generation]);

      // RESTful风格的单个资源响应 - 直接返回资源数据
      return {
        generation: cleanGeneration[0],  // 单个对象，不是数组
        job: jobs[0] || null,           // 单个对象，不是数组  
        user: users[0] || null          // 单个对象，不是数组
      };

    } catch (error) {
      console.error('[GenerationService] Error fetching generation by id:', error);
      return {
        success: false,
        error: 'Internal server error'
      };
    }
  }

  /**
   * 切换generation点赞状态（统一的点赞/取消点赞接口）- Redis超快速版本
   * 响应时间从600ms优化到20-50ms
   */
  static async toggleLikeGeneration(
    user: Session["user"], 
    generationId: string
  ): Promise<{
    generationId: string;
    likeCount: number;
    isLiked: boolean;
  } | null> {
    const startTime = Date.now();
    console.log(`[GenerationService] 🚀 Starting Redis like toggle for ${generationId}`);
    
    try {
      // 使用Redis Lua脚本超快速切换方法 (单次网络往返)
      const redisStartTime = Date.now();
      const result = await LikesRedisManager.toggleLikeFast(generationId, user.id);
      const redisTime = Date.now() - redisStartTime;

      if (!result.success) {
        console.log(`[GenerationService] ❌ Redis toggle failed for ${generationId} (${redisTime}ms)`);
        return null;
      }

      const totalTime = Date.now() - startTime;
      console.log(`[GenerationService] ✅ Redis Lua toggle SUCCESS: ${generationId} -> ${result.isLiked ? 'liked' : 'unliked'} (Redis: ${redisTime}ms, Total: ${totalTime}ms)`);

      return {
        generationId: generationId,
        likeCount: result.likeCount,
        isLiked: result.isLiked
      };

    } catch (error) {
      const redisFailTime = Date.now() - startTime;
      console.error(`[GenerationService] ❌ Redis FAILED after ${redisFailTime}ms:`, error);
      
      // 降级到数据库方法
      console.log('[GenerationService] 🔄 Falling back to database method...');
      try {
        const fallbackResult = await GenerationsManager.toggleLikeGenerationFast(generationId, user.id);
        
        if (!fallbackResult) {
          return null;
        }

        console.log(`[GenerationService] Fallback success: User ${user.id} ${fallbackResult.isLiked ? 'liked' : 'unliked'} generation ${generationId}, new count: ${fallbackResult.starNum} (DB)`);

        return {
          generationId: fallbackResult.id,
          likeCount: fallbackResult.starNum,
          isLiked: fallbackResult.isLiked
        };
      } catch (fallbackError) {
        console.error('[GenerationService] Fallback also failed:', fallbackError);
        throw fallbackError;
      }
    }
  }


  /**
   * 带水印下载
   */
  static async downloadWithWatermark(
    user: Session["user"], 
    generationId: string
  ): Promise<{
    success: true;
    downloadUrl: string;
  } | {
    success: false;
    error: string;
  }> {
    return await GenerationsManager.downloadWithWatermark(user.id, generationId);
  }

  /**
   * 无水印下载
   */
  static async downloadWithoutWatermark(
    user: Session["user"], 
    generationId: string
  ): Promise<{
    success: true;
    downloadUrl: string;
  } | {
    success: false;
    error: string;
  }> {
    return await GenerationsManager.downloadWithoutWatermark(user.id, generationId);
  }

  /**
   * 软删除generation
   */
  static async deleteGeneration(
    user: Session["user"], 
    generationId: string
  ): Promise<{
    deleted?: string;
    failed?: Record<string, string>;
  }> {
    try {
      const isAdmin = user.role?.includes('admin') || false;
      
      const result = await GenerationsManager.softDeleteGeneration(
        generationId,
        user.id,
        isAdmin
      );

      if (result.deleted) {
        console.log(`[GenerationService] User ${user.id}${isAdmin ? ' (admin)' : ''} deleted generation ${generationId}`);
      } else {
        console.warn(`[GenerationService] Deletion failed for user ${user.id}:`, result.failed);
      }

      return result;

    } catch (error) {
      console.error('[GenerationService] Error deleting generation:', error);
      return {
        failed: {
          [generationId]: 'Internal server error'
        }
      };
    }
  }

  /**
   * 批量删除generations - 事务版本
   */
  static async batchDeleteGenerations(
    user: Session["user"], 
    generationIds: string[]
  ): Promise<{
    deleted?: string[];
    error?: string;
  }> {
    try {
      const isAdmin = user.role?.includes('admin') || false;
      
      const result = await GenerationsManager.batchSoftDeleteGenerations(
        generationIds,
        user.id,
        isAdmin
      );

      if (result.deleted) {
        console.log(`[GenerationService] User ${user.id}${isAdmin ? ' (admin)' : ''} batch deleted ${result.deleted.length} generations`);
      } else {
        console.warn(`[GenerationService] Batch deletion failed for user ${user.id}:`, result.error);
      }

      return result;

    } catch (error) {
      console.error('[GenerationService] Error batch deleting generations:', error);
      return {
        error: 'Internal server error'
      };
    }
  }


  /**
   * 切换Generation的收藏状态（Redis优化版）
   */
  static async toggleFavoriteGeneration(
    user: Session["user"], 
    generationId: string
  ): Promise<{ favorite: boolean } | null> {
    try {
      const result = await GenerationsManager.toggleFavoriteGeneration(generationId, user.id);

      if (!result) {
        return null;
      }

      console.log(`[GenerationService] User ${user.id} toggled favorite status for generation ${generationId}, new status: ${result.favorite}`);

      return {
        favorite: result.favorite,
      };

    } catch (error) {
      console.error('[GenerationService] Error toggling favorite status:', error);
      throw error; // 让路由层处理错误
    }
  }

  /**
   * 批量设置favorite状态
   */
  static async batchFavoriteGenerations(
    user: Session["user"], 
    generationIds: string[],
    favorite: boolean
  ): Promise<{
    updated?: string[];
    error?: string;
  }> {
    try {
      const isAdmin = user.role?.includes('admin') || false;
      
      const result = await GenerationsManager.batchUpdateFavoriteGenerations(
        generationIds,
        user.id,
        favorite,
        isAdmin
      );

      if (result.updated) {
        console.log(`[GenerationService] User ${user.id}${isAdmin ? ' (admin)' : ''} batch updated favorite status for ${result.updated.length} generations to ${favorite}`);
      } else {
        console.warn(`[GenerationService] Batch favorite update failed for user ${user.id}:`, result.error);
      }

      return result;

    } catch (error) {
      console.error('[GenerationService] Error batch updating favorite status:', error);
      return {
        error: 'Internal server error'
      };
    }
  }

  /**
   * 批量生成下载链接
   */
  static async batchGenerateDownloadUrls(
    user: Session["user"], 
    generationIds: string[]
  ): Promise<{
    downloadUrls?: string[];
    error?: string;
  }> {
    try {
      const isAdmin = user.role?.includes('admin') || false;
      
      console.log(`[GenerationService] User ${user.id}${isAdmin ? ' (admin)' : ''} requesting batch download for ${generationIds.length} generations`);

      // 使用 GenerationsManager 的批量下载方法
      const result = await GenerationsManager.batchGenerateDownloadUrls(
        generationIds,
        user.id,
        isAdmin
      );

      if (result.downloadUrls) {
        console.log(`[GenerationService] User ${user.id} batch download generated ${result.downloadUrls.length} URLs successfully`);
      } else {
        console.warn(`[GenerationService] Batch download failed for user ${user.id}:`, result.error);
      }

      return result;

    } catch (error) {
      console.error('[GenerationService] Error batch generating download URLs:', error);
      return {
        error: 'Internal server error'
      };
    }
  }
}