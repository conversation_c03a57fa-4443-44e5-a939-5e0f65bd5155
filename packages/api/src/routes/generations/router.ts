import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { authMiddleware } from "../../middleware/auth";
import { GenerationService } from "./lib/generation-service";
// import { GenerationsManager } from "@repo/jobs"; // 导致认证问题
import { 
  GetGenerationsRequestSchema, 
  GenerationIdParamSchema,
  RestfulFavoriteResponseSchema,
  RestfulErrorResponseSchema
} from "./schemas";

export const generationsRouter = new Hono()
  .basePath("/generations")
  
  // GET /api/generations - 获取生成内容列表
  .get(
    "/",
    authMiddleware, // 认证中间件
    describeRoute({
      tags: ["Generations"],
      summary: "Get generations list",
      description: "Get paginated list of generations with filtering options. Users can only see their own generations unless they are admin.",
      parameters: [
        {
          name: "userId",
          in: "query",
          description: "Filter by user ID (admin only for other users)",
          schema: { type: "string" },
          required: false,
        },
        {
          name: "generationType", 
          in: "query",
          description: "Filter by generation type (corresponds to Job.type)",
          schema: { 
            type: "string",
            enum: ["video", "image"]
          },
          required: false,
        },
        {
          name: "pageSize",
          in: "query", 
          description: "Number of items per page (1-100)",
          schema: { 
            type: "integer",
            minimum: 1,
            maximum: 100,
            default: 20
          },
          required: false,
        },
        {
          name: "page",
          in: "query",
          description: "Page number (starts from 1)",
          schema: { 
            type: "integer",
            minimum: 1,
            default: 1
          },
          required: false,
        },
        {
          name: "publishStatus",
          in: "query",
          description: "Filter by publish status",
          schema: { 
            type: "string",
            enum: ["reviewing", "published", "rejected"]
          },
          required: false,
        },
        {
          name: "mediaType",
          in: "query",
          description: "Filter by media type (corresponds to Generation.mediaType)",
          schema: { 
            type: "string",
            enum: ["video", "image"]
          },
          required: false,
        },
        {
          name: "favorite",
          in: "query",
          description: "Filter by favorite status",
          schema: { type: "boolean" },
          required: false,
        },
      ],
      responses: {
        200: {
          description: "Generations retrieved successfully",
        },
        400: {
          description: "Bad request - Invalid parameters",
        },
        401: {
          description: "Unauthorized",
        },
        403: {
          description: "Forbidden - Access denied",
        },
        429: {
          description: "Rate limit exceeded",
        },
        500: {
          description: "Internal server error",
        },
      },
    }),
    validator("query", GetGenerationsRequestSchema),
    async (c) => {
      const user = c.get("user");
      const params = c.req.valid("query");

      // 记录请求日志
      console.log(`[GenerationsAPI] User ${user.id} requesting generations:`, {
        params,
        userRole: user.role,
      });

      const result = await GenerationService.getGenerations(user, params);
      
      if (result.success) {
        console.log(`[GenerationsAPI] Retrieved ${result.data.generations.length} generations for user ${user.id}`);
        return c.json(result, 200);
      } else {
        console.warn(`[GenerationsAPI] Error for user ${user.id}:`, result.error);
        
        // 根据错误类型返回相应的状态码
        if (result.error === 'Access denied') {
          return c.json(result, 403);
        } else if (result.error.includes('Invalid')) {
          return c.json(result, 400);
        } else {
          return c.json(result, 500);
        }
      }
    }
  )

  // POST /api/generations/migrate-likes - 一键迁移点赞数据到Redis (管理员专用)
  // 暂时注释掉，避免误操作
  /*
  .post(
    "/migrate-likes",
    authMiddleware,
    describeRoute({
      tags: ["Generations"],
      summary: "Migrate likes data to Redis",
      description: "One-time migration of all like data from database to Redis for performance optimization. Admin only.",
      responses: {
        200: {
          description: "Migration completed successfully",
        },
        401: {
          description: "Unauthorized - Authentication required",
        },
        403: {
          description: "Forbidden - Admin access required",
        },
        500: {
          description: "Internal server error",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");

      // 只允许管理员执行迁移（放宽权限检查）
      if (!user.role?.includes('admin') && !user.email?.includes('admin')) {
        return c.json({ error: 'Admin access required for data migration' }, 403);
      }

      console.log(`[GenerationsAPI] Admin ${user.id} initiated likes data migration to Redis`);

      try {
        const { LikesRedisManager } = await import("@repo/jobs");
        const result = await LikesRedisManager.migrateLikesToRedis();
        
        if (result.success) {
          console.log(`[GenerationsAPI] Migration completed: ${result.migrated} generations migrated`);
          return c.json({
            success: true,
            message: `Successfully migrated ${result.migrated} generations to Redis`,
            migrated: result.migrated
          }, 200);
        } else {
          console.error('[GenerationsAPI] Migration failed');
          return c.json({ error: 'Migration failed' }, 500);
        }
        
      } catch (error) {
        console.error('[GenerationsAPI] Error during migration:', error);
        return c.json({ error: 'Internal server error during migration' }, 500);
      }
    }
  )
  */

  // GET /api/generations/:generationId - 获取单个generation详情（动态路由在后）
  .get(
    "/:generationId",
    authMiddleware,
    describeRoute({
      tags: ["Generations"],
      summary: "Get generation details",
      description: "Get details of a specific generation by ID",
      parameters: [
        {
          name: "generationId",
          in: "path",
          description: "Generation ID",
          schema: { type: "string" },
          required: true,
        },
      ],
      responses: {
        200: {
          description: "Generation retrieved successfully",
        },
        400: {
          description: "Bad request - Invalid parameters",
        },
        401: {
          description: "Unauthorized",
        },
        403: {
          description: "Forbidden - Access denied",
        },
        404: {
          description: "Generation not found",
        },
        500: {
          description: "Internal server error",
        },
      },
    }),
    validator("param", GenerationIdParamSchema),
    async (c) => {
      const user = c.get("user");
      const { generationId } = c.req.valid("param");

      console.log(`[GenerationsAPI] User ${user.id} requesting generation ${generationId}`);

      const result = await GenerationService.getGenerationById(user, generationId);
      
      // 检查是否是错误响应（包含success字段）
      if ('success' in result && !result.success) {
        console.warn(`[GenerationsAPI] Error fetching generation ${generationId} for user ${user.id}:`, result.error);
        
        // 根据错误类型返回相应的状态码
        if (result.error === 'Generation not found') {
          return c.json(result, 404);
        } else if (result.error === 'Access denied') {
          return c.json(result, 403);
        } else {
          return c.json(result, 500);
        }
      } else {
        // RESTful成功响应
        console.log(`[GenerationsAPI] Retrieved generation ${generationId} for user ${user.id}`);
        return c.json(result, 200);
      }
    }
  )


  // POST /api/generations/:generationId/likes/toggle - 切换点赞状态
  .post(
    "/:generationId/likes/toggle",
    authMiddleware,
    describeRoute({
      tags: ["Generations"],
      summary: "Toggle like status for a generation",
      description: "Toggle like status for a specific generation. If the user has already liked the generation, it will be unliked. If not liked, it will be liked. This is a unified endpoint that replaces separate like/unlike operations.",
      parameters: [
        {
          name: "generationId",
          in: "path",
          description: "Generation ID",
          schema: { type: "string" },
          required: true,
        },
      ],
      responses: {
        200: {
          description: "Like status toggled successfully",
        },
        400: {
          description: "Bad request - Invalid generation ID",
        },
        401: {
          description: "Unauthorized - Authentication required",
        },
        403: {
          description: "Forbidden - Access denied",
        },
        404: {
          description: "Generation not found",
        },
        500: {
          description: "Internal server error",
        },
      },
    }),
    validator("param", GenerationIdParamSchema),
    async (c) => {
      const user = c.get("user");
      const { generationId } = c.req.valid("param");

      const startTime = Date.now();
      console.log(`[GenerationsAPI] User ${user.id} toggling like for generation ${generationId}`);

      try {
        const result = await GenerationService.toggleLikeGeneration(user, generationId);
        
        if (!result) {
          return c.json({ error: 'Generation not found' }, 404);
        }

        const responseTime = Date.now() - startTime;
        console.log(`[GenerationsAPI] ⚡ Generation ${generationId} like toggled successfully in ${responseTime}ms, new state: ${result.isLiked ? 'liked' : 'unliked'}, count: ${result.likeCount}`);
        
        // RESTful 风格：直接返回资源数据
        return c.json({
          ...result,
          responseTime // 返回响应时间供前端展示
        }, 200);
        
      } catch (error) {
        console.warn(`[GenerationsAPI] Error toggling like for generation ${generationId}:`, error);
        
        if (error instanceof Error) {
          if (error.message.includes('Cannot like') || error.message.includes('Cannot unlike')) {
            return c.json({ error: error.message }, 400);
          }
        }
        
        return c.json({ error: 'Internal server error' }, 500);
      }
    }
  )


  // POST /api/generations/:generationId/favorite/toggle - 切换收藏状态（Redis优化）
  .post(
    "/:generationId/favorite/toggle",
    authMiddleware,
    describeRoute({
      tags: ["Generations"],
      summary: "Toggle generation favorite status (Redis optimized)",
      description: "Toggle the favorite status of a generation with Redis caching for high performance. If already favorited, it will be unfavorited. If not favorited, it will be favorited. Only works for generations created by the current user.",
      parameters: [
        {
          name: "generationId",
          in: "path",
          description: "Generation ID",
          schema: { type: "string" },
          required: true,
        },
      ],
      responses: {
        200: {
          description: "Favorite status toggled successfully",
          content: {
            'application/json': {
              schema: RestfulFavoriteResponseSchema,
            },
          },
        },
        400: {
          description: "Bad request - Invalid generation ID",
          content: {
            'application/json': {
              schema: RestfulErrorResponseSchema,
            },
          },
        },
        401: {
          description: "Unauthorized - Authentication required",
          content: {
            'application/json': {
              schema: RestfulErrorResponseSchema,
            },
          },
        },
        403: {
          description: "Forbidden - Can only toggle favorite status for your own generations",
          content: {
            'application/json': {
              schema: RestfulErrorResponseSchema,
            },
          },
        },
        404: {
          description: "Generation not found",
          content: {
            'application/json': {
              schema: RestfulErrorResponseSchema,
            },
          },
        },
        500: {
          description: "Internal server error",
          content: {
            'application/json': {
              schema: RestfulErrorResponseSchema,
            },
          },
        },
      },
    }),
    validator("param", GenerationIdParamSchema),
    async (c) => {
      const user = c.get("user");
      const { generationId } = c.req.valid("param");

      console.log(`[GenerationsAPI] 用户 ${user.id} 正在切换 generation ${generationId} 的收藏状态`);

      try {
        const result = await GenerationService.toggleFavoriteGeneration(user, generationId);
        
        if (!result) {
          return c.json({ error: 'Generation not found' }, 404);
        }

        console.log(`[GenerationsAPI] Generation ${generationId} 收藏状态切换成功，新状态: ${result.favorite}`);
        
        // RESTful风格：直接返回资源数据
        return c.json(result, 200);
        
      } catch (error) {
        console.warn(`[GenerationsAPI] 切换Generation ${generationId}收藏状态失败:`, error);
        
        if (error instanceof Error) {
          if (error.message.includes('only toggle favorite status for your own')) {
            return c.json({ error: error.message }, 403);
          }
        }
        
        return c.json({ error: 'Internal server error' }, 500);
      }
    }
  )

  // 统一下载端点：GET /:generationId/download?media=video&type=watermark
  .get(
    "/:generationId/download",
    authMiddleware,
    describeRoute({
      tags: ["Generations"],
      summary: "Download generation media",
      description: "Download generation media with specified type and format",
      parameters: [
        {
          name: "generationId",
          in: "path",
          description: "Generation ID",
          schema: { type: "string" },
          required: true,
        },
        {
          name: "media",
          in: "query",
          description: "Media type to download",
          schema: { 
            type: "string",
            enum: ["video", "image"],
            default: "video"
          },
          required: false,
        },
        {
          name: "type",
          in: "query", 
          description: "Download type",
          schema: {
            type: "string",
            enum: ["watermark", "no-watermark", "thumbnail", "high-res"],
            default: "watermark"
          },
          required: false,
        }
      ],
      responses: {
        200: {
          description: "Download URL returned successfully",
        },
        400: {
          description: "Bad request - Invalid parameters",
        },
        401: {
          description: "Unauthorized",
        },
        404: {
          description: "Generation not found",
        },
        500: {
          description: "Internal server error",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      const generationId = c.req.param("generationId");
      const media = c.req.query("media") || "video";
      const type = c.req.query("type") || "watermark";

      try {
        console.log(`[Download] User ${user.id} downloading ${media} (${type}) for generation ${generationId}`);

        // 目前只支持video，未来可扩展image
        if (media !== "video") {
          return c.json({ error: "Currently only video downloads are supported" }, 400);
        }

        let result;
        if (type === "watermark") {
          result = await GenerationService.downloadWithWatermark(user, generationId);
        } else if (type === "no-watermark") {
          result = await GenerationService.downloadWithoutWatermark(user, generationId);
        } else {
          return c.json({ error: "Invalid download type" }, 400);
        }
        
        if (result.success) {
          return c.json({ 
            downloadUrl: result.downloadUrl,
            media,
            type 
          }, 200);
        } else {
          return c.json({ error: result.error }, result.error === "Generation not found" ? 404 : 500);
        }

      } catch (error) {
        console.error(`[Download] Error in ${media} ${type} download:`, error);
        return c.json({ error: "Internal server error" }, 500);
      }
    }
  )


  // DELETE /api/generations/:generationId - 软删除单个generation（动态路由在后）
  .delete(
    "/:generationId",
    authMiddleware,
    describeRoute({
      tags: ["Generations"],
      summary: "Delete a generation",
      description: "Soft delete a specific generation. Users can only delete their own generations unless they are admin. The generation will be marked as deleted but not physically removed from the database.",
      parameters: [
        {
          name: "generationId",
          in: "path",
          description: "Generation ID to delete",
          schema: { type: "string" },
          required: true,
        },
      ],
      responses: {
        200: {
          description: "Generation deleted successfully",
        },
        400: {
          description: "Bad request - Generation cannot be deleted",
        },
        401: {
          description: "Unauthorized - Authentication required",
        },
        403: {
          description: "Forbidden - Access denied (not the owner or admin)",
        },
        404: {
          description: "Generation not found",
        },
        409: {
          description: "Conflict - Generation already deleted",
        },
        500: {
          description: "Internal server error",
        },
      },
    }),
    validator("param", GenerationIdParamSchema),
    async (c) => {
      const user = c.get("user");
      const { generationId } = c.req.valid("param");

      console.log(`[GenerationsAPI] User ${user.id} requesting deletion of generation ${generationId}`);

      const result = await GenerationService.deleteGeneration(user, generationId);
      
      if (result.deleted) {
        console.log(`[GenerationsAPI] Generation ${generationId} deleted successfully by user ${user.id}`);
        return c.json(result, 200);
      } else if (result.failed) {
        console.warn(`[GenerationsAPI] Failed to delete generation ${generationId} by user ${user.id}:`, result.failed);
        
        // 根据错误类型返回相应的状态码
        const errorMessage = Object.values(result.failed)[0];
        if (errorMessage === 'Generation not found') {
          return c.json(result, 404);
        } else if (errorMessage === 'Access denied') {
          return c.json(result, 403);
        } else if (errorMessage === 'Generation already deleted') {
          return c.json(result, 409);
        } else if (errorMessage === 'Generation cannot be deleted') {
          return c.json(result, 400);
        } else {
          return c.json(result, 500);
        }
      } else {
        return c.json({ failed: { [generationId]: 'Unknown error' } }, 500);
      }
    }
  );

export type GenerationsRouter = typeof generationsRouter;