import { z } from "zod";

// 基础请求参数验证schema
export const GetGenerationsRequestSchema = z.object({
  userId: z.string().cuid2().optional(),
  generationType: z.enum(['video', 'image']).optional(),
  pageSize: z.coerce.number().min(1).max(100).default(20),
  page: z.coerce.number().min(1).default(1),
  publishStatus: z.enum(['reviewing', 'published', 'rejected']).optional(),
  mediaType: z.enum(['video', 'image']).optional(),
  favorite: z.coerce.boolean().optional(),
});

// 点赞相关的路径参数验证schema
export const GenerationIdParamSchema = z.object({
  generationId: z.string().cuid2(),
});


// 切换点赞响应schema（RESTful风格）
export const ToggleLikeResponseSchema = z.object({
  generationId: z.string(),
  likeCount: z.number().min(0),
  isLiked: z.boolean(),
});

// 错误响应schema
export const ErrorResponseSchema = z.object({
  error: z.string(),
});

// 用户信息schema
export const UserSummarySchema = z.object({
  id: z.string(),
  name: z.string(),
  image: z.string().nullable(),
});

// 分页信息schema
export const PaginationInfoSchema = z.object({
  page: z.number(),
  pageSize: z.number(),
  total: z.number(),
  totalPages: z.number(),
  hasNext: z.boolean(),
  hasPrev: z.boolean(),
});

// Generation完整数据schema - 基于数据库模型
export const GenerationDataSchema = z.object({
  id: z.string(),
  userId: z.string(),
  jobId: z.string(),
  mediaId: z.string(),
  externalTaskId: z.string().nullable(),
  cover: z.string().nullable(),
  thumbnail: z.string().nullable(),
  videoUrl: z.string().nullable(),
  starNum: z.number(),
  shareNum: z.number(),
  playNum: z.number(),
  downloadNum: z.number(),
  videoRatio: z.string().nullable(),
  duration: z.number().nullable(),
  klVideoId: z.string().nullable(),
  publishStatus: z.enum(['reviewing', 'published', 'rejected']).nullable(),
  publishDate: z.string().nullable(), // ISO string
  protectionMode: z.boolean(),
  isLike: z.boolean(),
  favorite: z.boolean(),
  score: z.number().nullable(),
  sort: z.number(),
  mediaUrl: z.string().nullable(),
  videoUrlNoWatermark: z.string().nullable(),
  mediaType: z.enum(['video', 'image']),
  status: z.enum(['waiting', 'processing', 'succeeded', 'failed']),
  deleted: z.boolean(),
  canRead: z.boolean(),
  canCopyPrompt: z.boolean(),
  canPublish: z.boolean(),
  canProtectCopy: z.boolean(),
  canDelete: z.boolean(),
  canOwn: z.boolean(),
  canCreateSimilar: z.boolean(),
  createdAt: z.string(), // ISO string
  updatedAt: z.string(), // ISO string
});

// Job完整数据schema - 基于数据库模型
export const JobDataSchema = z.object({
  id: z.string(),
  userId: z.string(),
  featureCode: z.string(),
  type: z.enum(['video', 'image']),
  credit: z.number(),
  apiProviderCost: z.number().nullable(),
  status: z.enum(['waiting', 'processing', 'succeeded', 'failed']),
  numOutputs: z.number(),
  timeCostSeconds: z.number().nullable(),
  modelCode: z.string(),
  apiProviderCode: z.string(),
  prompt: z.string().nullable(),
  image: z.string().nullable(),
  imageTail: z.string().nullable(),
  negativePrompt: z.string().nullable(),
  promptStrength: z.number().nullable(),
  duration: z.number(),
  modeCode: z.string().nullable(),
  resolution: z.string().nullable(),
  aspectRatio: z.string(),
  style: z.string().nullable(),
  motionRange: z.string().nullable(),
  seed: z.number().nullable(),
  cameraType: z.string().nullable(),
  cameraConfig: z.string().nullable(),
  cameraFixed: z.boolean().nullable(),
  video: z.string().nullable(),
  generationTemplate: z.string().nullable(),
  templateId: z.string().nullable(),
  templateImage: z.string().nullable(),
  processType: z.enum(['text', 'image', 'video']),
  published: z.boolean(),
  protectionMode: z.boolean(),
  enableMagicPrompt: z.boolean(),
  enableTranslatePrompt: z.boolean(),
  enableTranslateNegativePrompt: z.boolean(),
  externalTaskId: z.string().nullable(),
  errorMessage: z.string().nullable(),
  createdAt: z.string(), // ISO string
  updatedAt: z.string(), // ISO string
});

// 响应数据schema
export const GetGenerationsResponseDataSchema = z.object({
  generations: z.array(GenerationDataSchema),
  jobs: z.array(JobDataSchema),
  users: z.array(UserSummarySchema),
  pagination: PaginationInfoSchema,
});

// 成功响应schema
export const GetGenerationsResponseSchema = z.object({
  success: z.literal(true),
  data: GetGenerationsResponseDataSchema,
});

// 错误响应schema
export const GetGenerationsErrorResponseSchema = z.object({
  success: z.literal(false),
  error: z.string(),
  details: z.object({
    field: z.string(),
    message: z.string(),
  }).optional(),
});

// OpenAPI 响应定义
export const OpenAPIResponses = {
  200: {
    description: "Generations retrieved successfully",
    content: {
      'application/json': {
        schema: GetGenerationsResponseSchema,
      },
    },
  },
  400: {
    description: "Bad request - Invalid parameters",
    content: {
      'application/json': {
        schema: GetGenerationsErrorResponseSchema,
      },
    },
  },
  401: {
    description: "Unauthorized - Authentication required",
    content: {
      'application/json': {
        schema: GetGenerationsErrorResponseSchema,
      },
    },
  },
  403: {
    description: "Forbidden - Access denied",
    content: {
      'application/json': {
        schema: GetGenerationsErrorResponseSchema,
      },
    },
  },
  429: {
    description: "Rate limit exceeded",
    content: {
      'application/json': {
        schema: GetGenerationsErrorResponseSchema,
      },
    },
  },
  500: {
    description: "Internal server error",
    content: {
      'application/json': {
        schema: GetGenerationsErrorResponseSchema,
      },
    },
  },
} as const;

// =====================================
// 删除相关Schema定义
// =====================================

// 删除响应Schema（更直观的格式）
export const DeleteGenerationResponseSchema = z.object({
  deleted: z.string().optional().describe("Successfully deleted generation ID"),
  failed: z.record(z.string(), z.string()).optional().describe("Failed deletions with reasons")
}).refine(data => data.deleted || data.failed, {
  message: "Response must contain either deleted or failed"
});

// 批量删除请求Body Schema
export const BatchDeleteGenerationsRequestSchema = z.object({
  generationIds: z.array(z.string().min(1)).min(1).max(50).describe("Array of generation IDs to delete (max 50)"),
});

// 批量删除响应Schema
export const BatchDeleteGenerationResponseSchema = z.union([
  z.object({
    deleted: z.array(z.string()).describe("Successfully deleted generation IDs")
  }),
  z.object({
    error: z.string().describe("Error message")
  })
]);

// =====================================
// 收藏相关Schema定义
// =====================================

// 收藏响应数据schema（RPC风格）
export const FavoriteResponseDataSchema = z.object({
  favorite: z.boolean().describe("Favorite status of the generation"),
});

// 收藏成功响应schema（RPC风格）
export const FavoriteSuccessResponseSchema = z.object({
  success: z.literal(true),
  data: FavoriteResponseDataSchema,
});

// 收藏错误响应schema（RPC风格）
export const FavoriteErrorResponseSchema = z.object({
  success: z.literal(false),
  error: z.string().describe("Error message"),
});

// RESTful风格的收藏响应schema
export const RestfulFavoriteResponseSchema = z.object({
  favorite: z.boolean().describe("Favorite status of the generation"),
});

// RESTful风格的错误响应schema
export const RestfulErrorResponseSchema = z.object({
  error: z.string().describe("Error message"),
});