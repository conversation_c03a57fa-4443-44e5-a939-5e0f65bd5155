import type { z } from "zod";
import type { Generation, Job, User } from "@repo/database";
import type { 
  GetGenerationsRequestSchema,
  GenerationIdParamSchema
} from "./schemas";

// TypeScript 类型推导
export type GetGenerationsRequest = z.infer<typeof GetGenerationsRequestSchema>;
export type GenerationIdParam = z.infer<typeof GenerationIdParamSchema>;

// 切换点赞响应类型（RESTful风格）
export interface ToggleLikeResponse {
  generationId: string;
  likeCount: number;
  isLiked: boolean;
}

// 分页信息类型
export interface PaginationInfo {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 用户信息类型（简化版）
export interface UserSummary {
  id: string;
  name: string | null;
  image: string | null;
}

// API响应数据类型
export interface GetGenerationsResponseData {
  generations: Generation[];
  jobs: Job[];
  users: UserSummary[];
  pagination: PaginationInfo;
}

// API响应类型
export interface GetGenerationsResponse {
  success: true;
  data: GetGenerationsResponseData;
}

// 错误响应类型
export interface GetGenerationsErrorResponse {
  success: false;
  error: string;
  details?: {
    field: string;
    message: string;
  };
}

// 联合响应类型
export type GetGenerationsApiResponse = GetGenerationsResponse | GetGenerationsErrorResponse;

// RESTful单个资源响应类型（不包装在success/data中）
export interface GetGenerationByIdResponse {
  generation: Generation;
  job: Job | null;
  user: UserSummary | null;
}

// 单资源联合响应类型
export type GetGenerationByIdApiResponse = GetGenerationByIdResponse | GetGenerationsErrorResponse;

// 数据库查询的where条件类型
export interface GenerationWhereCondition {
  deleted: false;
  userId?: string;
  mediaType?: 'video' | 'image';
  publishStatus?: 'reviewing' | 'published' | 'rejected';
  favorite?: boolean;
  job?: {
    type?: 'video' | 'image';
  };
}

// 查询选项类型
export interface QueryOptions {
  page: number;
  pageSize: number;
  orderBy?: {
    createdAt: 'desc' | 'asc';
  };
}

// 删除响应类型（更直观的格式）
export interface DeleteGenerationResponse {
  deleted?: string;
  failed?: Record<string, string>;
}

// 批量删除响应类型
export interface BatchDeleteGenerationResponse {
  deleted?: string[];
  error?: string;
}

// 收藏响应数据类型
export interface FavoriteResponseData {
  favorite: boolean;
}

// 收藏成功响应类型
export interface FavoriteSuccessResponse {
  success: true;
  data: FavoriteResponseData;
}

// 收藏错误响应类型
export interface FavoriteErrorResponse {
  success: false;
  error: string;
}

// 收藏API联合响应类型
export type FavoriteApiResponse = FavoriteSuccessResponse | FavoriteErrorResponse;

// RESTful风格的收藏响应类型
export interface RestfulFavoriteResponse {
  favorite: boolean;
}

// RESTful风格的错误响应类型
export interface RestfulErrorResponse {
  error: string;
}