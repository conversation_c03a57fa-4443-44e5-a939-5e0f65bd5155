import { z } from "zod";

// AspectRatio 常量和类型定义
export const ASPECT_RATIO = {
  ASPECT_16_9: "16:9",
  ASPECT_9_16: "9:16", 
  ASPECT_1_1: "1:1",
  ASPECT_4_3: "4:3",
  ASPECT_3_4: "3:4",
  ASPECT_21_9: "21:9",
} as const;

// TypeScript 枚举常量
export enum AspectRatioEnum {
  ASPECT_16_9 = "16:9",
  ASPECT_9_16 = "9:16",
  ASPECT_1_1 = "1:1", 
  ASPECT_4_3 = "4:3",
  ASPECT_3_4 = "3:4",
  ASPECT_21_9 = "21:9",
}

// 所有可用的宽高比值数组
export const ASPECT_RATIO_VALUES = Object.values(ASPECT_RATIO);

// Zod 验证 schema
export const AspectRatioSchema = z.enum(ASPECT_RATIO_VALUES as [string, ...string[]]);

// StyleType enum values
export const STYLE_TYPE = {
  AUTO: "auto",
  ANIME: "anime",
  THREE_D_ANIMATION: "3d_animation",
  COMIC: "comic",
  CLAY: "clay",
  CYBERPUNK: "cyberpunk",
} as const;

export const StyleTypeSchema = z.enum([
  STYLE_TYPE.AUTO,
  STYLE_TYPE.ANIME,
  STYLE_TYPE.THREE_D_ANIMATION,
  STYLE_TYPE.COMIC,
  STYLE_TYPE.CLAY,
  STYLE_TYPE.CYBERPUNK,
]);

// MotionRangeType enum values
export const MOTION_RANGE_TYPE = {
  AUTO: "auto",
  SMALL: "small",
  MEDIUM: "medium",
  LARGE: "large",
} as const;

export const MotionRangeTypeSchema = z.enum([
  MOTION_RANGE_TYPE.AUTO,
  MOTION_RANGE_TYPE.SMALL,
  MOTION_RANGE_TYPE.MEDIUM,
  MOTION_RANGE_TYPE.LARGE,
]);

// ProcessType enum values
export const PROCESS_TYPE = {
  TEXT: "text",
  IMAGE: "image",
  VIDEO: "video",
} as const;

export const ProcessTypeSchema = z.enum([
  PROCESS_TYPE.TEXT,
  PROCESS_TYPE.IMAGE,
  PROCESS_TYPE.VIDEO,
]);

// Export types
export type AspectRatio = z.infer<typeof AspectRatioSchema>;
export type StyleType = z.infer<typeof StyleTypeSchema>;
export type MotionRangeType = z.infer<typeof MotionRangeTypeSchema>;
export type ProcessType = z.infer<typeof ProcessTypeSchema>;