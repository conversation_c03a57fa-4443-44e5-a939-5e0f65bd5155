import { createId } from "@paralleldrive/cuid2";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
// HTTPException removed - using JobResponseBuilder instead
import type { CreateJobRequest } from "../types";
import { videoModelConfig } from "@repo/config/vmodel";
import { 
  calculateCredits, 
  CreditCalculationError,
  type CreditCalculationInput 
} from "@repo/utils";
import { generateCorrelationId } from "../../../lib/providers/utils";
import { JobsManager, JobDataMapper, JobResponseBuilder, ApiResponseBuilder } from "@repo/jobs";
import type { 
  CreateGenerationData, 
  BaseApiResponse, 
  CreateJobResponseData, 
  JobDetailsResponseData 
} from "@repo/jobs";
import { ProviderFactory, type VideoGenerationParams } from "../../../lib/providers";

dayjs.extend(utc);

export class JobService {

  /**
   * 创建新的任务
   */
  static async createJob(userId: string, request: CreateJobRequest): Promise<BaseApiResponse<CreateJobResponseData>> {
    
    // 用户已经通过中间件认证，无需再次验证

    // 1. 验证功能代码存在（硬编码）
    const supportedFeatures = ["image-to-video", "text-to-video"];
    if (!supportedFeatures.includes(request.featureCode)) {
      return JobResponseBuilder.invalidParameters(`Feature code '${request.featureCode}' is not supported`);
    }

    // 2. 验证模型存在（使用配置文件）
    const model = videoModelConfig.models.find(m => m.code === request.modelParam.modelCode);
    
    if (!model || !model.isActive) {
      return JobResponseBuilder.invalidParameters(`Model '${request.modelParam.modelCode}' is invalid or inactive`);
    }
    
    // 验证模型是否支持当前feature
    if (!model.supportedFeatures.includes(request.featureCode as any)) {
      return JobResponseBuilder.invalidParameters(`Model '${request.modelParam.modelCode}' does not support feature '${request.featureCode}'`);
    }

    // 3. 计算积分消耗（使用共享工具函数）

    // 4. 计算总积分消耗
    let singleCredit: number;
    let totalCredit: number;
    
    try {
      const calculationInput: CreditCalculationInput = {
        modelConfig: model,
        params: {
          duration: request.modelParam.duration,
          modeCode: request.modelParam.modeCode,
          resolution: request.modelParam.resolution,
          style: request.modelParam.style,
        }
      };
      
      const result = calculateCredits(calculationInput);
      singleCredit = result.totalCredits;
      totalCredit = singleCredit * request.numOutputs;
    } catch (error) {
      if (error instanceof CreditCalculationError) {
        return JobResponseBuilder.invalidParameters(error.message);
      }
      return JobResponseBuilder.invalidParameters("Failed to calculate credits");
    }

    // 5. 检查用户积分（暂时注释掉）
    // const creditUsage = await db.creditUsage.findFirst({
    //   where: { userId },
    //   select: { balance: true }
    // });

    // if (!creditUsage || creditUsage.balance < totalCredit) {
    //   throw new HTTPException(402, { message: "Insufficient credits" });
    // }

    // 6. 创建事务执行所有操作
    const jobId = createId();
    
    // 验证和标准化请求数据
    JobDataMapper.validateJobRequest(request);
    const normalizedRequest = JobDataMapper.normalizeJobRequest(request);
    
    // 使用mapper创建数据
    const { jobData } = JobDataMapper.createJobWithParamsData(
      jobId,
      userId,
      normalizedRequest,
      model,
      totalCredit
    );

    // 获取用户信息
    const { db } = await import("@repo/database");
    const user = await db.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        username: true,
        image: true,
      },
    });

    if (!user) {
      return ApiResponseBuilder.error("User not found", "The user could not be found");
    }

    // 6. 事务中同时创建Job和Generation记录
    const result = await db.$transaction(async (tx) => {
      const now = dayjs.utc().toDate();
      
      // 创建Job
      const job = await tx.job.create({
        data: {
          ...jobData,
          createdAt: now,
          updatedAt: now,
        },
      });
      
      // 批量创建Generations
      const generationsData = Array.from({ length: request.numOutputs }, () => ({
        id: createId(),
        userId: userId,
        jobId: job.id,
        mediaId: createId(),
        externalTaskId: undefined, // 初始为空，异步调用后更新
        mediaType: jobData.type,
        status: "waiting" as const, // 初始状态为waiting
        createdAt: now,
        updatedAt: now,
      }));
      
      await tx.generation.createMany({ data: generationsData });
      
      // 查询返回完整的generation数据
      const generations = await tx.generation.findMany({
        where: { jobId: job.id },
        orderBy: { createdAt: 'asc' }
      });
      
      return { job, generations };
    });

    // 8. 创建进度对象
    const progress = {
      completed: 0,
      total: request.numOutputs,
    };

    // 9. 异步调用API提供商创建任务
    this.generateVideosAsync(result.job.id, jobData, result.generations).catch(error => {
      console.error(`Failed to process job ${result.job.id}:`, error);
      // 更新任务状态为失败
      JobsManager.updateJob(result.job.id, {
        status: "failed",
        errorMessage: error instanceof Error ? error.message : String(error),
      }).catch(err => console.error('Failed to update job status:', err));
    });

    return JobResponseBuilder.fromCreateResult(result.job, user, result.generations, progress);
  }

  /**
   * 获取任务详情
   */
  static async getJobDetails(jobId: string, userId?: string): Promise<BaseApiResponse<JobDetailsResponseData>> {
    const job = await JobsManager.getJobDetails(jobId);

    if (!job) {
      return JobResponseBuilder.jobNotFound(jobId);
    }

    // 如果提供了userId，验证用户权限
    if (userId && job.userId !== userId) {
      return JobResponseBuilder.accessDenied();
    }

    // 获取用户信息
    const { db } = await import("@repo/database");
    const user = await db.user.findUnique({
      where: { id: job.userId },
      select: {
        id: true,
        name: true,
        username: true,
        image: true,
      },
    });

    if (!user) {
      return ApiResponseBuilder.error("User not found", "The job owner could not be found");
    }

    // 计算进度
    const progress = await JobsManager.calculateJobProgress(jobId);

    // 传入用户信息而不是null
    return JobResponseBuilder.fromJobEntity(job, user, progress);
  }


  /**
   * 计算预估时间 - 基于模型配置中的generationTimeSeconds
   */
  private static calculateEstimatedTime(modelCode: string, numOutputs: number): number {
    // 从模型配置中获取生成时间
    const model = videoModelConfig.models.find(m => m.code === modelCode);
    const baseTime = model?.generationTimeSeconds || 120; // 默认120秒
    // 并发任务，总时间等于单个任务时间
    return baseTime;
  }

  /**
   * 映射提供商状态到内部状态
   */
  private static mapProviderStatusToInternal(providerStatus: string, providerName: string): 'waiting' | 'processing' | 'succeeded' | 'failed' {
    const normalizedProvider = providerName.toLowerCase();
    
    switch (normalizedProvider) {
      case 'replicate':
        switch (providerStatus) {
          case 'starting':
            return 'waiting';
          case 'processing':
            return 'processing';
          case 'succeeded':
            return 'succeeded';
          case 'failed':
          case 'canceled':
            return 'failed';
          default:
            return 'processing'; // 默认为处理中
        }
      case 'fal':
        switch (providerStatus) {
          case 'IN_QUEUE':
            return 'waiting';
          case 'IN_PROGRESS':
            return 'processing';
          case 'COMPLETED':
            return 'succeeded';
          case 'FAILED':
            return 'failed';
          default:
            return 'processing';
        }
      default:
        // 对于未知提供商，使用通用映射
        const lowerStatus = providerStatus.toLowerCase();
        if (lowerStatus.includes('success') || lowerStatus.includes('complete')) {
          return 'succeeded';
        } else if (lowerStatus.includes('fail') || lowerStatus.includes('error')) {
          return 'failed';
        } else if (lowerStatus.includes('process') || lowerStatus.includes('running')) {
          return 'processing';
        } else {
          return 'processing'; // 默认为处理中
        }
    }
  }

  /**
   * 异步生成视频
   */
  private static async generateVideosAsync(jobId: string, jobData: any, generations: any[]) {
    const startTime = Date.now();
    
    try {
      console.log(`🚀 Processing job outputs: ${jobId}, numOutputs: ${jobData.numOutputs}`);

      // 1. 使用传入的Generation记录，避免重复查询
      if (generations.length === 0) {
        throw new Error(`No generations found for job ${jobId}`);
      }

      // 2. 更新任务状态为处理中
      await JobsManager.updateJob(jobId, {
        status: "processing",
      });

      // 3. 为每个generation调用API提供商
      const promises = [];
      for (let i = 0; i < generations.length; i++) {
        promises.push(this.generateVideo(jobData, generations[i], i));
      }

      // 4. 等待所有API调用完成
      const results = await Promise.allSettled(promises);
      
      // 5. 检查是否有失败的
      const failedCount = results.filter(r => r.status === 'rejected').length;
      if (failedCount === results.length) {
        // 全部失败
        throw new Error('All API calls failed');
      }

      console.log(`✅ Job ${jobId} processed: ${results.length - failedCount}/${results.length} succeeded`);

    } catch (error) {
      console.error(`❌ Error processing job outputs ${jobId}:`, error);
      
      // 计算处理时间
      const processingTime = Math.round((Date.now() - startTime) / 1000);
      
      // 更新任务状态为失败
      await JobsManager.updateJob(jobId, {
        status: "failed",
        errorMessage: error instanceof Error ? error.message : String(error),
        timeCostSeconds: processingTime,
      });

      // 返还积分
      await refundCredits(jobId);
    }
  }

  /**
   * 为已存在的Generation记录调用API提供商
   */
  private static async generateVideo(
    jobData: any, 
    generation: any,
    outputIndex: number
  ) {
    try {
      // 生成相关ID用于追踪
      const correlationId = generateCorrelationId(jobData.id, outputIndex);
      
      console.log(`📡 Generating video for generation ${generation.id} [${correlationId}]`);

      // 1. 获取provider实例
      const provider = ProviderFactory.getProvider(
        jobData.modelCode,
        jobData.apiProviderCode
      );
      
      console.log(`📡 Using ${provider.getName()} for model ${jobData.modelCode} [${correlationId}]`);
      
      // 2. 准备参数
      const params: VideoGenerationParams = {
        modelCode: jobData.modelCode,
        featureCode: jobData.featureCode,
        modeCode: jobData.modeCode,
        prompt: jobData.prompt || '',
        image: jobData.image,
        imageTail: jobData.imageTail,
        negativePrompt: jobData.negativePrompt,
        promptStrength: jobData.promptStrength,
        duration: jobData.duration,
        aspectRatio: jobData.aspectRatio,
        resolution: jobData.resolution,
        style: jobData.style,
        motionRange: jobData.motionRange,
        seed: jobData.seed ? jobData.seed + outputIndex : undefined,
        // 安全参数用于 webhook 验证
        userId: jobData.userId,
        taskId: generation.id, // 使用已存在的generation ID
      };
      
      // 3. 调用provider生成视频
      const response = await provider.generateVideo(params);
      
      console.log(`✅ ${provider.getName()} response for generation ${generation.id} [${correlationId}]:`, {
        externalTaskId: response.taskId,
        status: response.status,
        estimatedTime: response.estimatedTime,
      });
      
      // 4. 更新Generation记录
      const updatedGeneration = await JobsManager.updateGeneration(generation.id, {
        externalTaskId: response.taskId,
        status: this.mapProviderStatusToInternal(response.status, provider.getName()) as any,
      });
      
      console.log(`✅ Updated generation ${generation.id} with externalTaskId: ${response.taskId} [${correlationId}]`);
      
      return updatedGeneration;

    } catch (error) {
      console.error(`❌ Error processing generation ${generation.id}:`, error);
      
      // 更新generation状态为失败
      await JobsManager.updateGeneration(generation.id, {
        status: "failed",
      }).catch(err => console.error('Failed to update generation status:', err));
      
      throw error;
    }
  }
}

/**
 * 返还积分
 */
async function refundCredits(jobId: string) {
  try {
    const job = await JobsManager.findJobById(jobId);

    if (job && job.credit > 0) {
      await JobsManager.handleCreditOperation({
        userId: job.userId,
        amount: job.credit,
        operation: "increment",
        reason: `Refund for failed job ${jobId}`,
      });
      
      console.log(`💰 Refunded ${job.credit} credits for job ${jobId}`);
    }
  } catch (error) {
    console.error(`Failed to refund credits for job ${jobId}:`, error);
  }
}