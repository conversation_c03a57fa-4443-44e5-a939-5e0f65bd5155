import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { authMiddleware } from "../../middleware/auth";
import { videoGenerationRateLimit } from "../../middleware/rate-limiter";
import { JobService } from "./lib/job-service";
import { getResponseStatusCode } from "@repo/jobs";
import { CreateJobRequestSchema } from "./types";

export const jobsRouter = new Hono()
  .basePath("/jobs")
  
  // POST /api/jobs - 创建视频生成任务
  .post(
    "/",
    authMiddleware, // 认证中间件
    // videoGenerationRateLimit, // 🔥 临时禁用限流中间件用于测试
    describeRoute({
      tags: ["Jobs"],
      summary: "Create video generation job",
      description: "Create a new video or image generation job",
      responses: {
        200: {
          description: "Job created successfully",
        },
        400: {
          description: "Bad request - Invalid parameters",
        },
        402: {
          description: "Payment required - Insufficient credits",
        },
        404: {
          description: "Not found - Invalid feature or model",
        },
        429: {
          description: "Rate limit exceeded",
        },
      },
    }),
    validator("json", CreateJobRequestSchema),
    async (c) => {
      const user = c.get("user");
      const request = c.req.valid("json");

      // 🔍 打印请求信息
      console.log("========== CREATE JOB REQUEST ==========");
      console.log("User ID:", user.id);
      console.log("User:", JSON.stringify(user, null, 2));
      console.log("Request Body:", JSON.stringify(request, null, 2));
      console.log("======================================");

      const result = await JobService.createJob(user.id, request);
      
      // 🔍 打印 JobService 返回的结果
      console.log("========== JOB SERVICE RESULT ==========");
      console.log("Result:", JSON.stringify(result, null, 2));
      console.log("========================================");
      
      // 使用新的响应基础设施
      const statusCode = getResponseStatusCode(result);
      
      // 🔍 打印响应信息
      console.log("========== CREATE JOB RESPONSE ==========");
      console.log("Response:", JSON.stringify(result, null, 2));
      console.log("Status Code:", statusCode);
      console.log("=========================================");
      
      return c.json(result, statusCode);
    }
  )

  // GET /api/jobs/details/:id - 获取任务详情
  .get(
    "/details/:id",
    authMiddleware, // 认证中间件
    describeRoute({
      tags: ["Jobs"],
      summary: "Get job details",
      description: "Get details of a specific job including progress and results",
      responses: {
        200: {
          description: "Job details retrieved successfully",
        },
        404: {
          description: "Job not found",
        },
      },
    }),
    async (c) => {
      const user = c.get("user");
      const jobId = c.req.param("id");

      const result = await JobService.getJobDetails(jobId, user.id);
      
      const statusCode = getResponseStatusCode(result);
      return c.json(result, statusCode);
    }
  )

  // VideoClient测试端点
  .get(
    "/test-providers",
    describeRoute({
      tags: ["Jobs"],
      summary: "Test Provider Factory",
      description: "Test the new Provider Factory system",
    }),
    async (c) => {
      try {
        const { ProviderFactory } = await import("../../lib/providers");
        const { videoModelConfig } = await import("@repo/config/vmodel");
        
        // 获取支持的模型
        const supportedModels = videoModelConfig.models
          .filter(m => m.isActive)
          .map(m => m.code);
        
        // 测试模型和供应商映射
        const providerTests = [];
        for (const modelCode of supportedModels.slice(0, 8)) { // 测试前8个
          try {
            const supportedProviders = ProviderFactory.getSupportedProviders(modelCode);
            const defaultProvider = ProviderFactory.getProvider(modelCode);
            
            providerTests.push({
              modelCode,
              supported: true,
              supportedProviders,
              defaultProvider: defaultProvider.getName(),
            });
          } catch (error) {
            providerTests.push({
              modelCode,
              supported: false,
              error: error instanceof Error ? error.message : String(error),
            });
          }
        }
        
        return c.json({
          success: true,
          data: {
            message: "Provider Factory working correctly",
            supportedModels,
            supportedModelsCount: supportedModels.length,
            providerTests,
            environment: {
              hasFalApiKey: !!process.env.FAL_API_KEY,
              hasReplicateApiToken: !!process.env.REPLICATE_API_TOKEN,
            }
          }
        });
      } catch (error) {
        console.error("Error testing Provider Factory:", error);
        return c.json({
          success: false,
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
        }, 500);
      }
    }
  );