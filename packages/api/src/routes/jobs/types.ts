import { z } from "zod";
import { 
  JobStatusSchema,
  JobTypeSchema 
} from "@repo/database";
import { 
  AspectRatioSchema, 
  StyleTypeSchema, 
  MotionRangeTypeSchema, 
  ProcessTypeSchema 
} from "./constants";

// 模型参数 Schem
export const ModelParamSchema = z.object({
  modelCode: z.string(),
  prompt: z.string().optional(),
  image: z.string().optional(),
  imageTail: z.string().optional(),
  negativePrompt: z.string().optional(),
  promptStrength: z.number().min(0).max(1).optional(),
  duration: z.number().min(1).max(30),
  modeCode: z.string().optional(),
  resolution: z.string().optional(),
  aspectRatio: AspectRatioSchema,
  style: StyleTypeSchema.optional(),
  motionRange: MotionRangeTypeSchema.optional(),
  seed: z.number().optional(),
  cameraType: z.string().optional(),
  cameraConfig: z.string().optional(),
  cameraFixed: z.boolean().optional(),
  video: z.string().optional(),
  processType: ProcessTypeSchema,
});

// 图生视频专用的ModelParam Schema
export const ImageToVideoModelParamSchema = z.object({
  modelCode: z.string(),
  prompt: z.string().optional(),
  image: z.string(), // 图生视频必需图片URL
  imageTail: z.string().optional(),
  negativePrompt: z.string().optional(),
  promptStrength: z.number().min(0).max(1).optional(),
  duration: z.number().min(1).max(30), // 视频长度必需
  modeCode: z.string().optional(),
  resolution: z.string().optional(),
  aspectRatio: AspectRatioSchema, // 屏幕比例必需，但会有默认值
  style: StyleTypeSchema.optional(),
  motionRange: MotionRangeTypeSchema.optional(),
  seed: z.number().optional(),
  processType: ProcessTypeSchema,
});

// 业务选项 Schema
export const OptionsSchema = z.object({
  enableMagicPrompt: z.boolean().optional(),
  enableTranslatePrompt: z.boolean().optional(),
  enableTranslateNegativePrompt: z.boolean().optional(),
  protectionMode: z.boolean().optional(),
  published: z.boolean().optional(),
}).optional();

// 创建任务请求 Schema
export const CreateJobRequestSchema = z.object({
  featureCode: z.string(),
  type: JobTypeSchema,
  numOutputs: z.number().min(1).max(4),
  modelParam: ModelParamSchema,
  options: OptionsSchema,
});

// 创建任务响应 Schema
export const CreateJobResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    jobId: z.string(),
    status: JobStatusSchema,
    credit: z.number(),
    estimatedTime: z.number(),
    numOutputs: z.number(),
  }).optional(),
  error: z.string().optional(),
});

// 视频任务参数 Schema - DEPRECATED: 已合并到Job表
// export const VideoJobParamSchema = z.object({
//   // 模型参数
//   modelCode: z.string(),
//   prompt: z.string().optional(),
//   image: z.string().optional(),
//   imageTail: z.string().optional(),
//   negativePrompt: z.string().optional(),
//   strength: z.number().optional(),
//   duration: z.number(),
//   modeCode: z.string(),
//   resolution: z.string().optional(),
//   aspectRatio: AspectRatioSchema,
//   style: StyleTypeSchema,
//   motionRange: MotionRangeTypeSchema,
//   seed: z.number().optional(),
//   cameraType: z.string().optional(),
//   cameraConfig: z.string().optional(),
//   cameraFixed: z.boolean().optional(),
//   video: z.string().optional(),
//   processType: ProcessTypeSchema,
//   
//   // 业务选项
//   enableMagicPrompt: z.boolean().optional(),
//   enableTranslatePrompt: z.boolean().optional(),
//   enableTranslateNegativePrompt: z.boolean().optional(),
//   protectionMode: z.boolean().optional(),
//   published: z.boolean().optional(),
//   
//   // 数据库相关字段
//   apiProviderCode: z.string(),
//   createdAt: z.string(),
//   updatedAt: z.string(),
// });

// 图片任务参数 Schema
export const ImageJobParamSchema = z.object({
  // 模型参数
  modelCode: z.string(),
  prompt: z.string().optional(),
  negativePrompt: z.string().optional(),
  style: z.string().optional(),
  resolution: z.string().optional(),
  aspectRatio: AspectRatioSchema,
  seed: z.number().optional(),
  
  // 业务选项
  enableMagicPrompt: z.boolean().optional(),
  enableTranslatePrompt: z.boolean().optional(),
  enableTranslateNegativePrompt: z.boolean().optional(),
  protectionMode: z.boolean().optional(),
  published: z.boolean().optional(),
  
  // 数据库相关字段
  apiProviderCode: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

// 联合任务参数 Schema - DEPRECATED: VideoJobParam已合并到Job
// export const JobParamSchema = z.union([VideoJobParamSchema, ImageJobParamSchema]);
export const JobParamSchema = ImageJobParamSchema; // 暂时只保留Image部分

// 生成进度 Schema
export const ProgressSchema = z.object({
  completed: z.number(),
  total: z.number(),
  percentage: z.number(),
});

// 生成结果 Schema
export const GenerationSchema = z.object({
  id: z.string(),
  mediaId: z.string(),
  cover: z.string().optional(),
  thumbnail: z.string().optional(),
  videoUrl: z.string().optional(),
  mediaUrl: z.string().optional(),
  status: JobStatusSchema,
  mediaType: z.enum(["video", "image"]),
  duration: z.number().optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

// 获取任务详情响应 Schema
export const GetJobResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    job: z.object({
      id: z.string(),
      userId: z.string(),
      featureCode: z.string(),
      type: JobTypeSchema,
      numOutputs: z.number(),
      status: JobStatusSchema,
      credit: z.number(),
      apiProviderCost: z.number().optional(),
      timeCostSeconds: z.number().optional(),
      createdAt: z.string(),
      updatedAt: z.string(),
    }),
    // params: JobParamSchema, // 参数现在直接在job中
    generations: z.array(GenerationSchema),
    progress: ProgressSchema,
  }).optional(),
  error: z.string().optional(),
});

// TypeScript 类型定义
export type CreateJobRequest = z.infer<typeof CreateJobRequestSchema>;
export type CreateJobResponse = z.infer<typeof CreateJobResponseSchema>;
// export type VideoJobParam = z.infer<typeof VideoJobParamSchema>; // DEPRECATED
export type ImageJobParam = z.infer<typeof ImageJobParamSchema>;
// export type JobParam = z.infer<typeof JobParamSchema>; // DEPRECATED
export type GetJobResponse = z.infer<typeof GetJobResponseSchema>;
export type Progress = z.infer<typeof ProgressSchema>;
export type Generation = z.infer<typeof GenerationSchema>;

// 类型守卫 - DEPRECATED: VideoJobParam已合并到Job
// export function isVideoJobParam(
//   param: VideoJobParam | ImageJobParam,
//   job: { type: "video" | "image" }
// ): param is VideoJobParam {
//   return job.type === "video";
// }

// export function isImageJobParam(
//   param: VideoJobParam | ImageJobParam,
//   job: { type: "video" | "image" }
// ): param is ImageJobParam {
//   return job.type === "image";
// }