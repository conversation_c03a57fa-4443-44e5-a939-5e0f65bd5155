import Replicate from "replicate";
import type { PromptGenerationRequest, PromptGenerationResponse } from "./types";

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

export async function generatePrompts(
  request: PromptGenerationRequest
): Promise<PromptGenerationResponse> {
  const { idea } = request;
  const count = 3; // 固定生成3条
  const language = "en"; // 固定使用英语
  const startTime = Date.now();

  try {
    console.log(`[Prompts API] 开始生成提示词: ${JSON.stringify(request)}`);

    // 构建系统提示词
    const systemPrompt = buildSystemPrompt(count, language);
    const userPrompt = `Original idea: ${idea}\n\nPlease generate ${count} enhanced video generation prompts based on this idea.`;

    // 检查API Token
    if (!process.env.REPLICATE_API_TOKEN) {
      throw new Error("REPLICATE_API_TOKEN is not configured");
    }

    // 调用Replicate GPT-4o
    const output = await replicate.run("openai/gpt-4o", {
      input: {
        prompt: userPrompt,
        system_prompt: systemPrompt,
        max_completion_tokens: 1000,
        temperature: 0.8,
        top_p: 1,
        presence_penalty: 0,
        frequency_penalty: 0,
      }
    });

    // 解析输出
    const generatedText = parseReplicateOutput(output);
    
    const prompts = parsePromptsFromText(generatedText, count);

    const duration = Date.now() - startTime;
    console.log(`[Prompts API] 生成完成，耗时: ${duration}ms`);

    return {
      prompts,
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[Prompts API] 生成失败，耗时: ${duration}ms`, error);
    
    // 根据错误类型返回不同的错误信息
    if (error instanceof Error) {
      if (error.message.includes("rate limit")) {
        throw new Error("API调用频率过高，请稍后重试");
      } else if (error.message.includes("auth")) {
        throw new Error("API认证失败，请联系管理员");
      }
    }
    
    throw error;
  }
}

function buildSystemPrompt(count: number, language: string): string {
  return `You are a professional video generation prompt optimization expert. Based on the user's idea, generate ${count} high-quality video generation prompts. Requirements:
1. Each prompt should describe visual scenes in detail
2. Include details about actions, environment, lighting, atmosphere, etc.
3. Suitable for AI video generation models
4. Output in English
5. Each prompt should have some variation while maintaining the core theme
Please return only the prompt list, one per line, without any other content.`;
}

function parseReplicateOutput(output: any): string {
  if (Array.isArray(output)) {
    return output.join("");
  } else if (typeof output === "string") {
    return output;
  } else {
    throw new Error("Replicate输出格式异常");
  }
}

function parsePromptsFromText(text: string, count: number): string[] {
  const prompts = text
    .split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0 && !line.match(/^\d+\./)) // 过滤数字编号
    .slice(0, count);

  // 如果生成数量不足，进行补充
  while (prompts.length < count) {
    prompts.push(`Enhanced cinematic prompt variation ${prompts.length + 1}`);
  }

  return prompts;
}