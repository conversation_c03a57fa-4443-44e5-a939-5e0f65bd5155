import { Hono } from "hono";
import { HTTPException } from "hono/http-exception";
import { zValidator } from "@hono/zod-validator";
import { promptGenerationSchema } from "./schemas";
import { generatePrompts } from "./handlers";

const promptsRouter = new Hono();

promptsRouter.post(
  "/prompts",
  zValidator("json", promptGenerationSchema),
  async (c) => {
    try {
      const validatedData = c.req.valid("json");
      const result = await generatePrompts(validatedData);
      return c.json(result);
    } catch (error) {
      console.error("Prompts API错误:", error);
      
      const message = error instanceof Error ? error.message : "Failed to generate prompts";
      throw new HTTPException(500, { message });
    }
  }
);

export { promptsRouter };