import { config } from "@repo/config";
import { getSignedUploadUrl, generateFileName, generateFilePath, validateFileType, getContentType } from "@repo/storage";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { HTTPException } from "hono/http-exception";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";

// Validation schema for upload sign request
const uploadSignSchema = z.object({
	filename: z.string().min(1),
	type: z.enum(["image", "video"]),
});

export const uploadRouter = new Hono()
	.basePath("/upload")
	.post(
		"/sign",
		authMiddleware,
		validator("json", uploadSignSchema),
		describeRoute({
			tags: ["Upload"],
			summary: "Get a signed upload URL",
			description: "Get a signed upload URL for uploading images or videos to R2 storage",
			responses: {
				200: {
					description: "Returns signed upload URL and access URL",
					content: {
						"application/json": {
							schema: resolver(
								z.object({
									sign: z.string(),
									expired: z.number(),
									accessURL: z.string(),
								})
							),
						},
					},
				},
				400: {
					description: "Bad request - invalid file type or other validation error",
				},
				401: {
					description: "Unauthorized - user not authenticated",
				},
			},
		}),
		async (c) => {
			try {
				const { filename, type } = c.req.valid("json");
				const user = c.get("user");
				
				if (!user?.id) {
					throw new HTTPException(401, { message: "User not authenticated" });
				}

				// Validate file type
				validateFileType(filename, type);

				// Generate unique filename
				const newFilename = generateFileName(filename);
				
				// Generate file path
				const fileType = type === "image" ? "image" : "video";
				const filePath = generateFilePath(
					user.id,
					fileType,
					newFilename,
					config.storage.paths.environment
				);

				// Get content type
				const contentType = getContentType(filename);

				// Generate signed upload URL
				const signedUrl = await getSignedUploadUrl(filePath, {
					bucket: config.storage.bucketNames.media,
					expiresIn: 600, // 10 minutes
					contentType,
				});

				// Generate access URL
				const accessURL = `${config.storage.videocdn.baseUrl}/${filePath}`;

				return c.json({
					sign: signedUrl,
					expired: Date.now() + 600000, // 10 minutes from now
					accessURL,
				});
			} catch (error) {
				if (error instanceof HTTPException) {
					throw error;
				}
				
				// Handle validation errors
				if (error instanceof Error) {
					throw new HTTPException(400, { message: error.message });
				}
				
				throw new HTTPException(500, { message: "Internal server error" });
			}
		}
	); 