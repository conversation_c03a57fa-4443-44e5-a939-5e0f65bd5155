# Webhook 架构最佳实践

## 📁 目录结构

```
webhooks/
├── README.md                    # 文档说明
├── router.ts                    # 路由定义（仅负责路由逻辑）
├── schemas/                     # Schema 定义层
│   ├── index.ts                # 统一导出和基础 schema
│   ├── replicate.schema.ts     # Replicate 专用 schema
│   ├── fal.schema.ts          # Fal 专用 schema
│   └── openapi.ts             # OpenAPI 格式转换
├── handlers/                   # 业务逻辑处理器
│   ├── index.ts               # 处理器导出
│   ├── replicate.ts           # Replicate 处理器
│   └── fal.ts                 # Fal 处理器
├── shared/                    # 共享组件
│   ├── types.ts              # 通用类型定义
│   ├── processor.ts          # 通用业务逻辑
│   └── validator.ts          # 验证器
└── types/                     # TypeScript 类型
    ├── index.ts              # 类型导出
    ├── requests.ts           # 请求类型
    └── responses.ts          # 响应类型
```

## 🏗️ 架构原则

### 1. 分层架构（Layered Architecture）
- **Router Layer**: 只负责路由定义和中间件
- **Schema Layer**: 数据验证和类型定义
- **Handler Layer**: 业务逻辑处理
- **Service Layer**: 通用服务（processor, validator）

### 2. 单一职责原则（SRP）
- 每个文件只负责一个关注点
- Schema 文件只定义数据结构
- Handler 文件只处理业务逻辑
- Router 文件只定义路由

### 3. 开闭原则（OCP）
- 对扩展开放：新增提供商只需添加新的 schema 和 handler
- 对修改关闭：不影响现有代码

### 4. 依赖倒置原则（DIP）
- 高层模块（router）不依赖低层模块（具体 handler）
- 都依赖抽象（BaseWebhookHandler）

## 📋 最佳实践对比

### ❌ 不好的做法
```typescript
// 所有逻辑都在 router 中
export const webhooksRouter = new Hono()
  .post("/replicate", async (c) => {
    // 内联 schema 定义
    const schema = {
      type: "object",
      properties: { /* 大量定义 */ }
    };
    
    // 内联业务逻辑
    const payload = await c.req.json();
    const generation = await db.generation.findFirst(/* ... */);
    // 50+ 行业务逻辑...
    
    return c.json(result);
  });
```

**问题**：
- 违反单一职责原则
- 难以测试
- 难以重用
- 文件过长
- 耦合度高

### ✅ 好的做法
```typescript
// router.ts - 只负责路由定义
import { replicateSchema, falSchema } from './schemas';
import { createReplicateHandler, createFalHandler } from './handlers';

export const webhooksRouter = new Hono()
  .basePath("/webhooks")
  .post("/replicate", 
    describeRoute({
      requestBody: replicateSchema.request,
      responses: replicateSchema.responses,
    }),
    createReplicateHandler()
  );
```

**优势**：
- 清晰的分层
- 易于测试
- 高度可重用
- 便于维护
- 低耦合

## 🚀 进阶优化

### 1. Schema 版本控制
```typescript
// schemas/v1/replicate.schema.ts
// schemas/v2/replicate.schema.ts
```

### 2. 自动类型生成
```typescript
// 从 OpenAPI schema 生成 TypeScript 类型
npm run generate:types
```

### 3. Schema 测试
```typescript
// schemas/__tests__/replicate.test.ts
describe('ReplicateWebhookSchema', () => {
  it('should validate valid payload', () => {
    // 测试用例
  });
});
```

### 4. 中间件复用
```typescript
// middleware/webhook-validator.ts
export const createWebhookValidator = (schema: ZodSchema) => {
  return async (c: Context, next: Next) => {
    const result = schema.safeParse(await c.req.json());
    if (!result.success) {
      return c.json({ error: 'Invalid payload' }, 400);
    }
    c.set('payload', result.data);
    await next();
  };
};
```

## 📊 性能优化

1. **Schema 缓存**: 预编译 schema 避免重复解析
2. **惰性加载**: 按需加载大型 schema
3. **Schema 组合**: 复用通用 schema 部分
4. **类型推导**: 利用 TypeScript 的类型推导减少重复定义

## 🔧 工具推荐

- **zod**: Schema 验证和类型推导
- **zod-to-openapi**: Zod schema 转 OpenAPI
- **json-schema-to-typescript**: JSON Schema 转 TypeScript
- **openapi-typescript**: OpenAPI 规范转 TypeScript