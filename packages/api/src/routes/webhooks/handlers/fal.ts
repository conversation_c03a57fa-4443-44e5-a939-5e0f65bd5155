import { BaseWebhookHandler, type WebhookPayload, type WebhookRequest } from "../shared/types";
import { WebhookValidator } from "../shared/validator";

/**
 * Fal Webhook Handler
 * 
 * Fal webhook format:
 * {
 *   "request_id": "f47ac10b-58cc-4372-a567-0e02b2c3d479",
 *   "status": "COMPLETED",
 *   "output": {
 *     "video": {
 *       "url": "https://v2.fal.media/files/zebra/...",
 *       "content_type": "video/mp4",
 *       "file_name": "video.mp4",
 *       "file_size": 2097152
 *     }
 *   },
 *   "logs": [],
 *   "metrics": {
 *     "inference_time": 12.5
 *   }
 * }
 */
export class FalWebhookHandler extends BaseWebhookHandler {
  private validator?: WebhookValidator;

  constructor(options: {
    secret?: string;
    allowedIPs?: string[];
    bearerToken?: string;
  } = {}) {
    super({
      validateSignature: !!(options.secret || options.bearerToken),
    });

    if (options.secret || options.allowedIPs || options.bearerToken) {
      this.validator = new WebhookValidator({
        secret: options.secret,
        allowedIPs: options.allowedIPs,
        bearerToken: options.bearerToken
      });
    }
  }

  async validateSignature(req: WebhookRequest): Promise<boolean> {
    if (!this.validator) {
      return true; // 如果没有配置验证器，则跳过验证
    }

    try {
      const payload = JSON.stringify(req.body);
      return await this.validator.validate(req, payload);
    } catch (error) {
      console.error('[Fal] Signature validation error:', error);
      return false;
    }
  }

  parsePayload(body: any): WebhookPayload {
    const externalTaskId = body.request_id;
    
    if (!externalTaskId) {
      throw new Error('Missing request_id in Fal webhook payload');
    }

    return {
      externalTaskId,
      status: this.mapFalStatus(body.status),
      resultUrl: this.extractResultUrl(body.output),
      errorMessage: this.extractErrorMessage(body),
      progress: this.calculateProgress(body.status)
    };
  }

  getProviderName(): string {
    return 'fal';
  }

  /**
   * 映射 Fal 状态到统一格式
   */
  private mapFalStatus(status: string): 'waiting' | 'processing' | 'succeeded' | 'failed' {
    const statusMap: Record<string, 'waiting' | 'processing' | 'succeeded' | 'failed'> = {
      'IN_QUEUE': 'waiting',
      'IN_PROGRESS': 'processing',
      'COMPLETED': 'succeeded',
      'FAILED': 'failed'
    };
    
    return statusMap[status] || 'waiting';
  }

  /**
   * 从 output 中提取视频 URL
   */
  private extractResultUrl(output: any): string | undefined {
    if (!output) return undefined;
    
    // Fal 视频输出格式
    if (output.video?.url) {
      return output.video.url;
    }
    
    // 兼容其他可能的格式
    if (output.video_url) {
      return output.video_url;
    }
    
    if (output.url) {
      return output.url;
    }
    
    // 有时候是数组格式
    if (Array.isArray(output) && output.length > 0) {
      const first = output[0];
      if (typeof first === 'string') {
        return first;
      }
      if (first.url) {
        return first.url;
      }
    }
    
    return undefined;
  }

  /**
   * 提取错误信息
   */
  private extractErrorMessage(body: any): string | undefined {
    // 从 logs 中查找错误
    if (body.logs && Array.isArray(body.logs)) {
      const errorLogs = body.logs.filter((log: any) => 
        log.level === 'ERROR' || log.message?.includes('error')
      );
      if (errorLogs.length > 0) {
        return errorLogs.map((log: any) => log.message).join('; ');
      }
    }
    
    // 从 error 字段获取
    if (body.error) {
      if (typeof body.error === 'string') {
        return body.error;
      }
      if (body.error.message) {
        return body.error.message;
      }
    }
    
    // 如果状态是失败但没有错误信息
    if (body.status === 'FAILED') {
      return 'Video generation failed';
    }
    
    return undefined;
  }

  /**
   * 根据状态计算进度
   */
  private calculateProgress(status: string): number | undefined {
    switch (status) {
      case 'IN_QUEUE':
        return 0;
      case 'IN_PROGRESS':
        return undefined; // Fal 通常不提供详细进度
      case 'COMPLETED':
      case 'FAILED':
        return 100;
      default:
        return undefined;
    }
  }
}

/**
 * 创建 Fal webhook 处理器实例
 */
export function createFalHandler() {
  return new FalWebhookHandler({
    secret: process.env.FAL_WEBHOOK_SECRET,
    bearerToken: process.env.FAL_WEBHOOK_TOKEN,
    // Fal 的 IP 地址范围（如果需要的话）
    allowedIPs: process.env.FAL_ALLOWED_IPS?.split(',').map(ip => ip.trim())
  });
}