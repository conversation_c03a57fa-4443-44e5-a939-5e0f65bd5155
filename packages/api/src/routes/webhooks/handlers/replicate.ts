import { BaseWebhookHandler, type WebhookPayload, type WebhookRequest } from "../shared/types";
import crypto from "crypto";
import { extractMetadataFromInput, type WebhookMetadata } from "@repo/utils";

/**
 * Replicate Webhook Handler
 * 
 * Replicate webhook format:
 * {
 *   "id": "p5cxlsr64scp6fm....",
 *   "status": "succeeded",
 *   "output": ["https://replicate.delivery/pbxt/..."],
 *   "error": null,
 *   "created_at": "2023-09-08T16:19:07.216219+00:00",
 *   "started_at": "2023-09-08T16:19:07.435811+00:00",
 *   "completed_at": "2023-09-08T16:19:34.467617+00:00"
 * }
 */
export class ReplicateWebhookHandler extends BaseWebhookHandler {
  constructor() {
    super({
      validateSignature: true
    });
  }

  async validateSignature(req: WebhookRequest): Promise<boolean> {
    const webhookSigningKey = process.env.REPLICATE_WEBHOOK_SIGNING_KEY;
    
    if (!webhookSigningKey) {
      console.warn('[Replicate] REPLICATE_WEBHOOK_SIGNING_KEY not configured, skipping signature validation');
      return true; // 开发模式下允许跳过验证
    }

    try {
      // 获取必需的头部
      const webhookId = req.headers['webhook-id'];
      const webhookTimestamp = req.headers['webhook-timestamp'];
      const webhookSignatures = req.headers['webhook-signature'];

      if (!webhookId || !webhookTimestamp || !webhookSignatures) {
        console.error('[Replicate] Missing required webhook headers', {
          'webhook-id': !!webhookId,
          'webhook-timestamp': !!webhookTimestamp,
          'webhook-signature': !!webhookSignatures
        });
        return false;
      }

      // 验证时间戳（防止重放攻击，5分钟内有效）
      const timestamp = parseInt(webhookTimestamp as string, 10);
      const now = Math.floor(Date.now() / 1000);
      const diff = Math.abs(now - timestamp);
      const MAX_DIFF_IN_SECONDS = 5 * 60; // 5分钟

      if (diff > MAX_DIFF_IN_SECONDS) {
        console.error(`[Replicate] Webhook timestamp is too old: ${diff} seconds`);
        return false;
      }

      // 构造签名内容
      const body = typeof req.body === 'string' ? req.body : JSON.stringify(req.body);
      const signedContent = `${webhookId}.${webhookTimestamp}.${body}`;

      // 处理密钥（从 'whsec_xxxx' 格式中提取 base64 编码的密钥）
      let secretKey: Buffer;
      if (webhookSigningKey.startsWith('whsec_')) {
        const base64Key = webhookSigningKey.split('_')[1];
        secretKey = Buffer.from(base64Key, 'base64');
      } else {
        // 如果不是 whsec_ 格式，直接使用
        secretKey = Buffer.from(webhookSigningKey, 'utf8');
      }

      // 计算 HMAC-SHA256 签名
      const computedSignature = crypto
        .createHmac('sha256', secretKey)
        .update(signedContent)
        .digest('base64');

      // 解析 webhook signatures（可能包含多个签名）
      const expectedSignatures = (webhookSignatures as string)
        .split(' ')
        .map(sig => {
          // 每个签名可能是 'v1,signature' 格式
          const parts = sig.split(',');
          return parts.length > 1 ? parts[1] : sig;
        });

      // 使用时间安全的比较
      const isValid = expectedSignatures.some(expectedSig => {
        try {
          return crypto.timingSafeEqual(
            Buffer.from(expectedSig, 'base64'),
            Buffer.from(computedSignature, 'base64')
          );
        } catch (error) {
          // 如果 buffer 长度不同，timingSafeEqual 会抛出错误
          return false;
        }
      });

      if (!isValid) {
        console.error('[Replicate] Invalid webhook signature');
        return false;
      }

      console.log('✅ [Replicate] Official webhook signature validated successfully');
      return true;
    } catch (error) {
      console.error('[Replicate] Webhook signature validation error:', error);
      return false;
    }
  }

  parsePayload(body: any): WebhookPayload & { metadata?: WebhookMetadata } {
    // 如果 body 是字符串，解析为 JSON（用于签名验证后的处理）
    const parsedBody = typeof body === 'string' ? JSON.parse(body) : body;
    
    const externalTaskId = parsedBody.id;
    
    if (!externalTaskId) {
      throw new Error('Missing external task ID in Replicate webhook payload');
    }

    // 提取 metadata 信息
    let metadata: WebhookMetadata | undefined;
    if (parsedBody.input) {
      const extractedMetadata = extractMetadataFromInput(parsedBody.input);
      metadata = extractedMetadata || undefined;
      
      if (metadata) {
        console.log('📦 [Replicate] Extracted metadata:', {
          userId: metadata.userId,
          generationId: metadata.generationId,
          provider: metadata.provider,
          featureCode: metadata.featureCode
        });
      }
    }

    return {
      externalTaskId,
      status: this.mapReplicateStatus(parsedBody.status),
      resultUrl: this.extractResultUrl(parsedBody.output),
      errorMessage: parsedBody.error?.message || parsedBody.error,
      progress: this.calculateProgress(parsedBody.status),
      metadata // 包含提取的metadata信息
    };
  }

  getProviderName(): string {
    return 'replicate';
  }

  /**
   * 映射 Replicate 状态到统一格式
   */
  private mapReplicateStatus(status: string): 'waiting' | 'processing' | 'succeeded' | 'failed' {
    const statusMap: Record<string, 'waiting' | 'processing' | 'succeeded' | 'failed'> = {
      'starting': 'waiting',
      'processing': 'processing',
      'succeeded': 'succeeded',
      'failed': 'failed',
      'canceled': 'failed'
    };
    
    return statusMap[status] || 'waiting';
  }

  /**
   * 从 output 数组中提取视频 URL
   */
  private extractResultUrl(output: any): string | undefined {
    if (!output) return undefined;
    
    // Replicate 通常返回数组格式的 output
    if (Array.isArray(output) && output.length > 0) {
      return output[0];
    }
    
    // 有时可能是字符串
    if (typeof output === 'string') {
      return output;
    }
    
    // 有时可能是对象
    if (typeof output === 'object' && output.video_url) {
      return output.video_url;
    }
    
    return undefined;
  }

  /**
   * 根据状态计算进度
   */
  private calculateProgress(status: string): number | undefined {
    switch (status) {
      case 'starting':
        return 0;
      case 'processing':
        return undefined; // 不知道具体进度
      case 'succeeded':
      case 'failed':
      case 'canceled':
        return 100;
      default:
        return undefined;
    }
  }
}

/**
 * 创建 Replicate webhook 处理器实例
 */
export function createReplicateHandler() {
  return new ReplicateWebhookHandler();
}