import { standardWebhookResponses } from "./responses";
import { openAPISchemas } from "../schemas";
import type { WebhookDescriptorConfig, WebhookOpenAPIDescriptor, WebhookProvider } from "./types";

/**
 * 字符串首字母大写工具函数
 */
function capitalizeFirst(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * 通用 Webhook OpenAPI 描述生成器
 * 
 * @param config - 生成器配置
 * @returns OpenAPI 描述对象
 * 
 * @example
 * ```typescript
 * const desc = createWebhookDescriptor({
 *   provider: 'replicate',
 *   requestSchema: openAPISchemas.replicateWebhookBody
 * });
 * ```
 */
export function createWebhookDescriptor(config: WebhookDescriptorConfig): WebhookOpenAPIDescriptor {
  const {
    provider,
    requestSchema,
    customResponses = {},
    customDescription,
    additionalTags = []
  } = config;

  const providerName = capitalizeFirst(provider);
  
  return {
    summary: `${providerName} Webhook Handler`,
    description: customDescription || `处理来自 ${providerName} API 的视频生成完成通知`,
    tags: ["Webhooks", ...additionalTags],
    requestBody: {
      content: {
        "application/json": {
          schema: requestSchema
        }
      }
    },
    responses: {
      ...standardWebhookResponses,
      ...customResponses
    }
  };
}

/**
 * 预配置的 Webhook 描述生成器
 * 为常用提供商提供便捷的描述生成
 */
export const webhookDescriptors = {
  /**
   * Replicate webhook 描述
   */
  replicate: (): WebhookOpenAPIDescriptor => createWebhookDescriptor({
    provider: 'replicate',
    requestSchema: openAPISchemas.replicateWebhookBody,
    additionalTags: ['AI', 'Video Generation']
  }),

  /**
   * Fal webhook 描述
   */
  fal: (): WebhookOpenAPIDescriptor => createWebhookDescriptor({
    provider: 'fal',
    requestSchema: openAPISchemas.falWebhookBody,
    additionalTags: ['AI', 'Video Generation']
  }),

};

/**
 * 动态创建新提供商的 webhook 描述
 * 
 * @param provider - 提供商名称
 * @param requestSchema - 请求体 schema
 * @returns webhook 描述生成函数
 * 
 * @example
 * ```typescript
 * const runwayDescriptor = createProviderDescriptor('runway', runwaySchema);
 * ```
 */
export function createProviderDescriptor(
  provider: WebhookProvider | string,
  requestSchema: any
) {
  return (): WebhookOpenAPIDescriptor => createWebhookDescriptor({
    provider,
    requestSchema,
    additionalTags: ['AI', 'Video Generation']
  });
}