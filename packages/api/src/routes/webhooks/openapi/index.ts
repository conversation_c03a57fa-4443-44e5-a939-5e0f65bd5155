/**
 * Webhook OpenAPI 模块统一导出
 * 
 * 提供所有 OpenAPI 相关的工具函数、类型定义和预配置
 */

// 先导入以便后续使用
import { webhookDescriptors as descriptors } from "./descriptors";

// 核心工具函数
export {
  createWebhookDescriptor,
  webhookDescriptors,
  createProviderDescriptor
} from "./descriptors";

// 响应定义
export {
  webhookResponses,
  standardWebhookResponses,
  publicWebhookResponses
} from "./responses";

// 类型定义
export type {
  WebhookDescriptorConfig,
  WebhookProvider,
  WebhookOpenAPIDescriptor
} from "./types";

// 便捷的重导出
export const replicate = descriptors.replicate;
export const fal = descriptors.fal;