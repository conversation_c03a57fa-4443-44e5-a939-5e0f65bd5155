import { openAPISchemas } from "../schemas";

/**
 * Webhook 端点的标准响应定义
 * 遵循 OpenAPI 3.0 规范
 */
export const webhookResponses = {
  /** 成功响应 */
  success: {
    description: "Webhook 处理成功",
    content: {
      "application/json": {
        schema: openAPISchemas.webhookSuccessResponse
      }
    }
  },

  /** 客户端错误响应 */
  badRequest: {
    description: "请求参数错误",
    content: {
      "application/json": {
        schema: openAPISchemas.webhookErrorResponse
      }
    }
  },

  /** 资源未找到响应 */
  notFound: {
    description: "任务未找到",
    content: {
      "application/json": {
        schema: openAPISchemas.webhookErrorResponse
      }
    }
  },

  /** 服务器错误响应 */
  serverError: {
    description: "服务器内部错误",
    content: {
      "application/json": {
        schema: openAPISchemas.webhookErrorResponse
      }
    }
  },

  /** 认证失败响应 */
  unauthorized: {
    description: "签名验证失败",
    content: {
      "application/json": {
        schema: openAPISchemas.webhookErrorResponse
      }
    }
  }
};

/**
 * 标准 Webhook 响应集合
 * 适用于大部分 webhook 端点
 */
export const standardWebhookResponses = {
  200: webhookResponses.success,
  400: webhookResponses.badRequest,
  401: webhookResponses.unauthorized,
  404: webhookResponses.notFound,
  500: webhookResponses.serverError
};

/**
 * 公开 Webhook 响应集合（无需认证）
 */
export const publicWebhookResponses = {
  200: webhookResponses.success,
  400: webhookResponses.badRequest,
  404: webhookResponses.notFound,
  500: webhookResponses.serverError
};