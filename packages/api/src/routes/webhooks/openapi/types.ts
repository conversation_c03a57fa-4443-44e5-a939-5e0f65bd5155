/**
 * Webhook OpenAPI 相关类型定义
 */

/**
 * Webhook 描述生成器配置
 */
export interface WebhookDescriptorConfig {
  /** 提供商名称 */
  provider: string;
  /** 请求体 schema */
  requestSchema: any;
  /** 自定义响应（可选） */
  customResponses?: Record<string, any>;
  /** 自定义描述（可选） */
  customDescription?: string;
  /** 额外的标签（可选） */
  additionalTags?: string[];
}

/**
 * 预定义的 Webhook 提供商类型
 */
export type WebhookProvider = 'replicate' | 'fal' | 'runway' | 'stable-diffusion';

/**
 * Webhook 描述生成器返回类型
 */
export interface WebhookOpenAPIDescriptor {
  summary: string;
  description: string;
  tags: string[];
  requestBody: {
    content: {
      "application/json": {
        schema: any;
      };
    };
  };
  responses: Record<string, any>;
}