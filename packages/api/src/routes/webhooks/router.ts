import { webhookHandler as paymentsWebhookHandler } from "@repo/payments";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
// import { processWebhookPayload } from "./shared/processor"; // 不再需要同步处理
import { createReplicateHandler } from "./handlers/replicate";
import { createFalHandler } from "./handlers/fal";
import { webhookDescriptors } from "./openapi";
import { JobsManager } from "@repo/jobs";
import type { WebhookRequest, BaseWebhookHandler } from "./shared/types";
import type { Context } from "hono";
import { VideoWebhookEventManager } from "../../lib/services/video-webhook-event-manager";
// 移除内置处理器服务 - 现在由Consumer处理
import { logger } from "@repo/logs";
import { extractMetadataFromInput } from "@repo/utils";

// 创建 webhook 处理器实例
const replicateHandler = createReplicateHandler();
const falHandler = createFalHandler();

/**
 * 通用 webhook 端点处理器 - 数据库缓冲模式
 * 立即响应，异步处理
 */
async function handleProviderWebhook(
  c: Context,
  provider: string,
  handler: BaseWebhookHandler
): Promise<Response> {
  const startTime = Date.now();
  
  try {
    // 对于 Replicate，我们需要原始的请求体进行签名验证
    let rawBody: string | undefined;
    let body: any;
    
    if (provider === 'replicate' && process.env.REPLICATE_WEBHOOK_SIGNING_KEY) {
      // 获取原始请求体文本
      rawBody = await c.req.text();
      // 手动解析 JSON
      body = JSON.parse(rawBody);
    } else {
      // 其他提供商使用标准的 JSON 解析
      body = await c.req.json();
    }
    
    logger.info(`[${provider}] Received webhook`, { 
      eventId: extractEventId(body, provider),
      externalTaskId: extractExternalTaskId(body, provider)
    });
    
    // 构造请求对象
    const headers: Record<string, string> = {};
    c.req.raw.headers.forEach((value, key) => {
      headers[key.toLowerCase()] = value;
    });
    
    // 打印完整的原始报文到日志
    console.log(`\n==================== ${provider.toUpperCase()} WEBHOOK RAW PAYLOAD ====================`);
    console.log(`Timestamp: ${new Date().toISOString()}`);
    console.log(`URL: ${c.req.url}`);
    console.log(`Method: ${c.req.method}`);
    console.log(`Headers:`, JSON.stringify(headers, null, 2));
    console.log(`Body:`, JSON.stringify(body, null, 2));
    console.log(`Raw Body Length:`, rawBody ? rawBody.length : 'N/A');
    console.log(`====================================================================================\n`);
    
    const webhookRequest: WebhookRequest = {
      headers,
      body: rawBody || body, // 如果有原始文本，优先使用原始文本
      url: c.req.url
    };
    
    // 验证签名
    const isValidSignature = await handler.validateSignature(webhookRequest);
    if (!isValidSignature) {
      logger.warn(`[${provider}] Invalid webhook signature`);
      return c.json({ error: "Invalid signature" }, 401);
    }
    
    // 从metadata中提取generationId和userId（如果有的话）
    let metadata: any = null;
    if (body.input) {
      metadata = extractMetadataFromInput(body.input);
    }
    
    // 存储事件到数据库（异步处理）
    const eventManager = new VideoWebhookEventManager();
    const event = await eventManager.createEvent({
      eventId: extractEventId(body, provider),
      provider,
      eventType: extractEventType(body, provider),
      externalTaskId: extractExternalTaskId(body, provider),
      generationId: metadata?.generationId,  // 从metadata中提取
      userId: metadata?.userId,              // 从metadata中提取
      status: extractStatus(body, provider), // 新增：原始状态
      rawPayload: body,
      headers,
      signature: extractSignature(headers, provider)
    });
    
    // 🆕 推送到队列进行异步处理
    try {
      const { VideoQueue } = await import('@repo/queue');
      const videoQueue = new VideoQueue();
      const jobId = await videoQueue.addVideoProcessing(event);
      
      logger.info(`[${provider}] Event queued for processing`, { 
        eventId: event.id, 
        jobId 
      });
    } catch (queueError) {
      logger.error(`[${provider}] Failed to queue event`, { 
        eventId: event.id, 
        error: queueError instanceof Error ? queueError.message : String(queueError) 
      });
      // 注意: 队列推送失败不应该影响 webhook 响应
    }
    
    // 立即返回成功响应
    const responseTime = Date.now() - startTime;
    logger.info(`[${provider}] Webhook received and queued in ${responseTime}ms`);
    
    return c.json({ 
      success: true, 
      message: "Webhook received and queued for processing",
      processingTime: responseTime 
    });
    
  } catch (error) {
    const responseTime = Date.now() - startTime;
    logger.error(`[${provider}] Webhook processing error:`, {
      error: error instanceof Error ? error.message : String(error),
      processingTime: responseTime
    });
    return c.json({ 
      error: "Internal server error",
      details: error instanceof Error ? error.message : String(error) 
    }, 500);
  }
}

/**
 * 提取事件ID（用于幂等性）
 */
function extractEventId(body: any, provider: string): string {
  switch (provider) {
    case 'replicate':
      return body.id || `${provider}-${Date.now()}-${Math.random()}`;
    case 'fal':
      return body.request_id || body.id || `${provider}-${Date.now()}-${Math.random()}`;
    default:
      return `${provider}-${Date.now()}-${Math.random()}`;
  }
}

/**
 * 提取事件类型
 */
function extractEventType(body: any, provider: string): string {
  switch (provider) {
    case 'replicate':
      if (body.status === 'succeeded') return 'generation.completed';
      if (body.status === 'failed') return 'generation.failed';
      return 'generation.updated';
    case 'fal':
      if (body.status === 'COMPLETED') return 'generation.completed';
      if (body.status === 'FAILED') return 'generation.failed';
      return 'generation.updated';
    default:
      return 'webhook.received';
  }
}

/**
 * 提取外部任务ID
 */
function extractExternalTaskId(body: any, provider: string): string {
  switch (provider) {
    case 'replicate':
      return body.id;
    case 'fal':
      return body.request_id || body.id;
    default:
      return body.id || body.task_id || 'unknown';
  }
}

/**
 * 提取签名信息
 */
function extractSignature(headers: Record<string, string>, provider: string): string | undefined {
  switch (provider) {
    case 'replicate':
      return headers['webhook-signature'];
    case 'fal':
      return headers['x-signature'] || headers['signature'];
    default:
      return headers['x-signature'] || headers['signature'] || headers['authorization'];
  }
}

/**
 * 提取原始webhook状态
 */
function extractStatus(body: any, provider: string): string {
  switch (provider) {
    case 'replicate':
      return body.status || 'unknown';  // "starting", "processing", "succeeded", "failed", "canceled"
    case 'fal':
      return body.status || 'unknown';  // "IN_QUEUE", "IN_PROGRESS", "COMPLETED", "FAILED"
    default:
      return body.status || 'unknown';
  }
}


export const webhooksRouter = new Hono()
  .basePath("/webhooks")
  
  	.post(
		"/payments",
		describeRoute({
			tags: ["Webhooks"],
			summary: "Handle payments webhook",
		}),
		(c) => {
			return paymentsWebhookHandler(c.req.raw);
		},
	)
  // Replicate 专用端点
  .post("/replicate", 
    describeRoute(webhookDescriptors.replicate()),
    (c) => handleProviderWebhook(c, "replicate", replicateHandler)
  )
  
  // Fal 专用端点
  .post("/fal",
    describeRoute(webhookDescriptors.fal()),
    (c) => handleProviderWebhook(c, "fal", falHandler)
  )
  
  
  // 健康检查端点 - 重定向到Consumer服务
  .get("/health", async (c) => {
    return c.json({
      status: "info",
      timestamp: new Date().toISOString(),
      service: "webhooks",
      message: "Webhook processing handled by Consumer service",
      consumerHealthCheck: "curl http://localhost:3001/health"
    });
  })
  
  // 获取处理统计 - 重定向到Consumer服务
  .get("/stats", async (c) => {
    return c.json({
      message: "Processing stats available from Consumer service",
      consumerStatsEndpoint: "curl http://localhost:3001/stats",
      note: "All webhook processing now handled by dedicated Consumer service"
    });
  })
  
  // 手动触发处理 - 重定向到Consumer服务
  .post("/process", async (c) => {
    return c.json({
      message: "Manual processing not needed - Consumer handles all webhook processing automatically",
      info: "Consumer service processes queued tasks in real-time",
      consumerStatus: "Check Consumer health at http://localhost:3001/health"
    });
  })
  
  // 获取失败事件
  .get("/failed", async (c) => {
    try {
      const limitParam = c.req.query("limit");
      const limit = limitParam ? parseInt(limitParam) : 50;
      
      const eventManager = new VideoWebhookEventManager();
      const failedEvents = await eventManager.getFailedEvents(limit);
      
      return c.json({
        total: failedEvents.length,
        events: failedEvents.map(event => ({
          id: event.id,
          provider: event.provider,
          eventId: event.eventId,
          externalTaskId: event.externalTaskId,
          retryCount: event.retryCount,
          lastError: event.lastError,
          receivedAt: event.receivedAt,
          nextRetryAt: event.nextRetryAt
        }))
      });
    } catch (error) {
      return c.json({
        error: error instanceof Error ? error.message : String(error)
      }, 500);
    }
  })
  
  // 测试端点 - 模拟webhook回调
  .post("/test-complete", async (c) => {
    const { jobId, status = "completed", resultUrl } = await c.req.json();
    
    if (!jobId) {
      return c.json({ error: "Missing jobId" }, 400);
    }

    // 查找任务
    const job = await JobsManager.findJobById(jobId);

    if (!job) {
      return c.json({ error: "Job not found" }, 404);
    }

    // 模拟webhook回调
    const mockPayload = {
      task_id: job.externalTaskId,
      status: status,
      result_url: resultUrl || "https://example.com/test-video.mp4",
      progress: 100
    };

    // 调用自己的webhook处理
    const response = await fetch(`${c.req.url.replace('/test-complete', '/video')}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(mockPayload)
    });

    const result = await response.json();
    
    return c.json({
      success: true,
      message: "Test webhook sent",
      mockPayload,
      result
    });
  });

