/**
 * Webhook 相关的 Schema 定义
 * 
 * 遵循以下原则：
 * 1. Schema 与业务逻辑分离
 * 2. 可重用和可组合
 * 3. 支持 TypeScript 类型推导
 * 4. 便于版本管理
 */

import { z } from "zod";

// ===== 基础类型定义 =====

/**
 * 视频生成状态枚举
 */
export const VideoGenerationStatusSchema = z.enum([
  'waiting',
  'processing', 
  'succeed',
  'failed'
]);

export type VideoGenerationStatus = z.infer<typeof VideoGenerationStatusSchema>;

// ===== 请求 Schema =====

/**
 * Replicate Webhook 请求体
 */
export const ReplicateWebhookBodySchema = z.object({
  id: z.string().describe("Replicate 预测 ID"),
  status: z.enum(["starting", "processing", "succeeded", "failed", "canceled"])
    .describe("任务状态"),
  output: z.array(z.string()).nullable()
    .describe("生成的视频 URL 数组"),
  error: z.string().nullable()
    .describe("错误信息"),
  created_at: z.string().datetime().optional(),
  started_at: z.string().datetime().optional(),
  completed_at: z.string().datetime().optional(),
});

export type ReplicateWebhookBody = z.infer<typeof ReplicateWebhookBodySchema>;

/**
 * Fal Webhook 请求体
 */
export const FalWebhookBodySchema = z.object({
  request_id: z.string().describe("Fal 请求 ID"),
  status: z.enum(["IN_QUEUE", "IN_PROGRESS", "COMPLETED", "FAILED"])
    .describe("任务状态"),
  output: z.object({
    video: z.object({
      url: z.string().url().describe("视频 URL"),
      content_type: z.string().optional(),
      file_name: z.string().optional(),
      file_size: z.number().optional(),
    }).optional(),
  }).nullable().describe("生成结果"),
  error: z.object({
    message: z.string(),
    code: z.string().optional(),
  }).nullable().optional(),
  logs: z.array(z.object({
    level: z.string(),
    message: z.string(),
    timestamp: z.string().optional(),
  })).optional().describe("执行日志"),
  metrics: z.object({
    inference_time: z.number().optional(),
  }).optional().describe("性能指标"),
});

export type FalWebhookBody = z.infer<typeof FalWebhookBodySchema>;

/**
 * 通用 Webhook 请求体（向后兼容）
 */
export const GenericWebhookBodySchema = z.object({
  // 支持多种 ID 字段名
  task_id: z.string().optional(),
  request_id: z.string().optional(),
  id: z.string().optional(),
  
  // 通用状态
  status: z.string(),
  
  // 结果 URL（支持多种格式）
  result_url: z.string().optional(),
  output: z.union([
    z.string(),
    z.array(z.string()),
    z.object({
      video_url: z.string().optional(),
      url: z.string().optional(),
    })
  ]).optional(),
  
  // 错误信息
  error_message: z.string().optional(),
  error: z.union([
    z.string(),
    z.object({
      message: z.string(),
      code: z.string().optional(),
    })
  ]).optional(),
  
  // 进度
  progress: z.number().min(0).max(100).optional(),
}).refine(
  (data) => data.task_id || data.request_id || data.id,
  { message: "至少需要提供一个 ID 字段" }
);

export type GenericWebhookBody = z.infer<typeof GenericWebhookBodySchema>;

// ===== 响应 Schema =====

/**
 * Webhook 成功响应
 */
export const WebhookSuccessResponseSchema = z.object({
  success: z.literal(true),
  message: z.string(),
  status: VideoGenerationStatusSchema,
  jobId: z.string().optional(),
  generationId: z.string().optional(),
});

export type WebhookSuccessResponse = z.infer<typeof WebhookSuccessResponseSchema>;

/**
 * Webhook 错误响应
 */
export const WebhookErrorResponseSchema = z.object({
  success: z.literal(false).optional(),
  error: z.string(),
  details: z.string().optional(),
  code: z.string().optional(),
});

export type WebhookErrorResponse = z.infer<typeof WebhookErrorResponseSchema>;

/**
 * 统一的 Webhook 响应
 */
export const WebhookResponseSchema = z.discriminatedUnion('success', [
  WebhookSuccessResponseSchema,
  WebhookErrorResponseSchema.extend({ success: z.literal(false) }),
]);

export type WebhookResponse = z.infer<typeof WebhookResponseSchema>;

// ===== OpenAPI Schema 生成器 =====

/**
 * 将 Zod schema 转换为 OpenAPI schema
 * 用于 describeRoute
 */
export function zodToOpenAPISchema(schema: z.ZodType<any>): any {
  // 这里可以使用 zod-to-openapi 或手动转换
  // 为了简单起见，这里返回基础格式
  if (schema instanceof z.ZodObject) {
    const shape = schema.shape;
    const properties: any = {};
    const required: string[] = [];
    
    for (const [key, value] of Object.entries(shape)) {
      properties[key] = zodToOpenAPISchema(value as z.ZodType<any>);
      if (!value.isOptional()) {
        required.push(key);
      }
    }
    
    return {
      type: "object",
      properties,
      required: required.length > 0 ? required : undefined,
    };
  }
  
  if (schema instanceof z.ZodString) {
    return { type: "string" };
  }
  
  if (schema instanceof z.ZodNumber) {
    return { type: "number" };
  }
  
  if (schema instanceof z.ZodBoolean) {
    return { type: "boolean" };
  }
  
  if (schema instanceof z.ZodArray) {
    return {
      type: "array",
      items: zodToOpenAPISchema(schema.element),
    };
  }
  
  if (schema instanceof z.ZodEnum) {
    return {
      type: "string",
      enum: schema.options,
    };
  }
  
  // 默认返回
  return { type: "object" };
}

// ===== 导出 OpenAPI 格式的 Schema =====

export const openAPISchemas = {
  webhookSuccessResponse: {
    type: "object",
    properties: {
      success: { type: "boolean", example: true },
      message: { type: "string", example: "Webhook processed successfully" },
      status: { 
        type: "string", 
        enum: ["waiting", "processing", "succeed", "failed"],
        example: "succeed"
      },
      jobId: { type: "string", example: "job_123" },
      generationId: { type: "string", example: "gen_456" }
    },
    required: ["success", "message", "status"]
  },
  
  webhookErrorResponse: {
    type: "object",
    properties: {
      error: { type: "string", example: "Invalid webhook payload" },
      details: { type: "string", example: "Missing required field: task_id" },
      code: { type: "string", example: "INVALID_PAYLOAD" }
    },
    required: ["error"]
  },
  
  replicateWebhookBody: {
    type: "object",
    properties: {
      id: { type: "string", description: "Replicate 预测 ID" },
      status: { 
        type: "string", 
        enum: ["starting", "processing", "succeeded", "failed", "canceled"],
        description: "任务状态" 
      },
      output: { 
        type: "array",
        items: { type: "string" },
        nullable: true,
        description: "生成的视频 URL 数组" 
      },
      error: { 
        type: "string", 
        nullable: true,
        description: "错误信息" 
      },
      created_at: { type: "string", format: "date-time" },
      started_at: { type: "string", format: "date-time" },
      completed_at: { type: "string", format: "date-time" }
    },
    required: ["id", "status"]
  },
  
  falWebhookBody: {
    type: "object",
    properties: {
      request_id: { type: "string", description: "Fal 请求 ID" },
      status: { 
        type: "string", 
        enum: ["IN_QUEUE", "IN_PROGRESS", "COMPLETED", "FAILED"],
        description: "任务状态" 
      },
      output: { 
        type: "object",
        nullable: true,
        properties: {
          video: {
            type: "object",
            properties: {
              url: { type: "string", format: "uri", description: "视频 URL" },
              content_type: { type: "string" },
              file_name: { type: "string" },
              file_size: { type: "number" }
            }
          }
        },
        description: "生成结果" 
      },
      logs: { 
        type: "array",
        items: { 
          type: "object",
          properties: {
            level: { type: "string" },
            message: { type: "string" },
            timestamp: { type: "string" }
          }
        },
        description: "执行日志" 
      },
      metrics: { 
        type: "object",
        properties: {
          inference_time: { type: "number" }
        },
        description: "性能指标" 
      }
    },
    required: ["request_id", "status"]
  }
} as const;