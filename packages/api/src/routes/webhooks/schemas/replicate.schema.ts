/**
 * Replicate Webhook Schema
 * 
 * 单独文件管理每个提供商的 schema
 * 便于维护和版本控制
 */

import { z } from "zod";

// Replicate 状态映射
export const REPLICATE_STATUS = {
  STARTING: 'starting',
  PROCESSING: 'processing',
  SUCCEEDED: 'succeeded',
  FAILED: 'failed',
  CANCELED: 'canceled',
} as const;

export const ReplicateStatusSchema = z.enum([
  REPLICATE_STATUS.STARTING,
  REPLICATE_STATUS.PROCESSING,
  REPLICATE_STATUS.SUCCEEDED,
  REPLICATE_STATUS.FAILED,
  REPLICATE_STATUS.CANCELED,
]);

// Replicate Webhook 请求体
export const ReplicateWebhookSchema = z.object({
  id: z.string().describe("Replicate prediction ID"),
  
  status: ReplicateStatusSchema.describe("Prediction status"),
  
  input: z.record(z.any())
    .optional()
    .describe("Original input parameters passed to the prediction"),
  
  output: z.array(z.string())
    .nullable()
    .describe("Array of output URLs"),
    
  error: z.string()
    .nullable()
    .describe("Error message if failed"),
    
  logs: z.string()
    .nullable()
    .optional()
    .describe("Execution logs"),
    
  metrics: z.object({
    predict_time: z.number().optional(),
    total_time: z.number().optional(),
  }).optional().describe("Performance metrics"),
  
  // 时间戳
  created_at: z.string().datetime().optional(),
  started_at: z.string().datetime().optional(),
  completed_at: z.string().datetime().optional(),
  
  // Webhook 元数据
  webhook: z.string().url().optional(),
  webhook_events_filter: z.array(z.string()).optional(),
});

// 导出类型
export type ReplicateWebhook = z.infer<typeof ReplicateWebhookSchema>;
export type ReplicateStatus = z.infer<typeof ReplicateStatusSchema>;

// 状态检查辅助函数
export const isReplicateSuccess = (status: string): boolean => {
  return status === REPLICATE_STATUS.SUCCEEDED;
};

export const isReplicateFailed = (status: string): boolean => {
  return status === REPLICATE_STATUS.FAILED || 
         status === REPLICATE_STATUS.CANCELED;
};

export const isReplicateProcessing = (status: string): boolean => {
  return status === REPLICATE_STATUS.PROCESSING || 
         status === REPLICATE_STATUS.STARTING;
};