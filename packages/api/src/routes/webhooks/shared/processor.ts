import { redisUtils } from "@repo/redis";
import type { WebhookPayload } from "./types";
import { JobsManager } from "@repo/jobs";
import { VideoUploadService, JobStatusService, RetryService } from "../../../lib/services";
import { logger } from "@repo/logs";

/**
 * 处理 webhook 载荷的核心逻辑
 */
export async function processWebhookPayload(payload: WebhookPayload, provider: string) {
  const startTime = Date.now();
  
  console.log(`📥 [${provider}] Processing webhook for externalTaskId ${payload.externalTaskId}, status: ${payload.status}`);

  // 查找对应的Generation记录
  const generation = await JobsManager.findGenerationByExternalTaskId(
    payload.externalTaskId,
    true // includeJob
  );

  if (!generation) {
    throw new Error(`Generation not found for externalTaskId: ${payload.externalTaskId}`);
  }

  const job = generation.job;
  const processingTime = Math.round((Date.now() - generation.createdAt.getTime()) / 1000);

  // 根据状态处理
  switch (payload.status) {
    case "succeeded":
      await handleGenerationCompletion(generation, job, payload, processingTime);
      break;
    case "failed":
      await handleGenerationFailure(generation, job, payload, processingTime);
      break;
    case "processing":
      await updateGenerationProgress(generation.id, payload);
      break;
    case "waiting":
      // 等待状态不需要特殊处理
      break;
  }

  // 清除缓存
  await redisUtils.clearJobCache(job.id);

  console.log(`✅ [${provider}] Webhook processed for job ${job.id} in ${Date.now() - startTime}ms`);
  
  return {
    success: true,
    message: `Job ${job.id} updated successfully`,
    status: payload.status,
    jobId: job.id,
    generationId: generation.id
  };
}

/**
 * 处理Generation完成
 */
async function handleGenerationCompletion(generation: any, job: any, payload: WebhookPayload, processingTime: number) {
  // 初始化视频上传服务
  const videoUploadService = new VideoUploadService();
  let cdnUrl: string | undefined;
  
  // 如果有视频结果，尝试上传到 CDN
  if (payload.resultUrl) {
    try {
      logger.info("[Webhook] Uploading video to CDN", {
        generationId: generation.id,
        sourceUrl: payload.resultUrl,
      });
      
      // 优先使用流式上传（节省磁盘空间），失败则回退到临时文件模式
      let uploadResult;
      let uploadMethod = 'streaming';
      
      try {
        // 首先尝试流式上传
        uploadResult = await videoUploadService.uploadFromUrlStreaming(
          payload.resultUrl,
          {
            userId: generation.userId,
            generationId: generation.id,
          }
        );
        logger.info("[Webhook] Streaming upload succeeded", { generationId: generation.id });
      } catch (streamingError) {
        logger.warn("[Webhook] Streaming upload failed, falling back to temp file mode", {
          generationId: generation.id,
          error: streamingError instanceof Error ? streamingError.message : streamingError,
        });
        
        // 回退到临时文件模式（已验证可工作）
        uploadResult = await videoUploadService.uploadFromUrl(
          payload.resultUrl,
          {
            userId: generation.userId,
            generationId: generation.id,
          }
        );
        uploadMethod = 'temp-file-fallback';
        logger.info("[Webhook] Temp file upload succeeded as fallback", { generationId: generation.id });
      }
      
      cdnUrl = uploadResult.cdnUrl;
      logger.info("[Webhook] Video uploaded to CDN", {
        generationId: generation.id,
        cdnUrl,
        size: uploadResult.size,
        method: uploadMethod,
      });
    } catch (error) {
      logger.error("[Webhook] Failed to upload video to CDN", {
        generationId: generation.id,
        error: error instanceof Error ? error.message : error,
      });
      // 如果上传失败，仍然使用原始 URL
      cdnUrl = payload.resultUrl;
    }
  }
  
  await JobsManager.transaction(async (tx) => {
    // 更新generation记录
    await tx.generation.update({
      where: { id: generation.id },
      data: {
        status: "succeeded",
        videoUrl: cdnUrl || payload.resultUrl,
        mediaUrl: cdnUrl || payload.resultUrl,
        updatedAt: new Date()
      }
    });

    // 检查并更新 Job 状态
    const statusResult = await JobStatusService.checkAndUpdateJobStatus(
      job.id,
      processingTime,
      tx
    );

    if (statusResult.updated) {
      logger.info("[Webhook] Job status updated", {
        jobId: job.id,
        finalStatus: statusResult.finalStatus,
        creditRefund: statusResult.creditRefund,
      });
    }
  });

  console.log(`✅ Generation ${generation.id} completed successfully in ${processingTime}s`);
}

/**
 * 处理Generation失败
 */
async function handleGenerationFailure(generation: any, job: any, payload: WebhookPayload, processingTime: number) {
  await JobsManager.transaction(async (tx) => {
    // 更新generation记录
    await tx.generation.update({
      where: { id: generation.id },
      data: {
        status: "failed",
        updatedAt: new Date()
      }
    });

    // 检查并更新 Job 状态
    const statusResult = await JobStatusService.checkAndUpdateJobStatus(
      job.id,
      processingTime,
      tx
    );

    if (statusResult.updated) {
      // 更新 Job 的错误消息（如果是失败状态）
      if (statusResult.finalStatus === "failed") {
        await tx.job.update({
          where: { id: job.id },
          data: {
            errorMessage: payload.errorMessage,
            updatedAt: new Date(),
          }
        });
      }

      logger.info("[Webhook] Job status updated after failure", {
        jobId: job.id,
        finalStatus: statusResult.finalStatus,
        creditRefund: statusResult.creditRefund,
      });
    }
  });

  console.log(`❌ Generation ${generation.id} failed: ${payload.errorMessage}`);
}

/**
 * 更新Generation进度
 */
async function updateGenerationProgress(generationId: string, payload: WebhookPayload) {
  const retryOptions = RetryService.createDatabaseRetry();
  
  const result = await RetryService.execute(
    async () => {
      await JobsManager.updateGeneration(generationId, {
        status: "processing",
      });
    },
    retryOptions,
    `updateGenerationProgress-${generationId}`
  );

  if (!result.success) {
    logger.error("[Webhook] Failed to update generation progress", {
      generationId,
      error: result.error?.message,
    });
    throw result.error || new Error("Failed to update generation progress");
  }

  console.log(`🔄 Generation ${generationId} progress updated: ${payload.progress || 'N/A'}%`);
}