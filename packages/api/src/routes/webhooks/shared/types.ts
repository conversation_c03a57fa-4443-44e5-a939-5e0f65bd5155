export interface WebhookPayload {
  externalTaskId: string;
  status: GenerationStatus;
  resultUrl?: string;
  errorMessage?: string;
  progress?: number;
}

export type GenerationStatus = 'waiting' | 'processing' | 'succeeded' | 'failed';

export interface WebhookRequest {
  headers: Record<string, string>;
  body: any;
  url: string;
}

export interface WebhookHandlerOptions {
  validateSignature?: boolean;
  secretKey?: string;
  allowedIPs?: string[];
}

export abstract class BaseWebhookHandler {
  protected options: WebhookHandlerOptions;

  constructor(options: WebhookHandlerOptions = {}) {
    this.options = {
      validateSignature: false,
      ...options
    };
  }

  /**
   * 验证 webhook 签名
   */
  abstract validateSignature(req: WebhookRequest): Promise<boolean>;

  /**
   * 解析 webhook 载荷
   */
  abstract parsePayload(body: any): WebhookPayload;

  /**
   * 获取提供商名称
   */
  abstract getProviderName(): string;

  /**
   * 处理 webhook 请求
   */
  async handle(req: WebhookRequest): Promise<WebhookPayload> {
    // 验证签名（如果启用）
    if (this.options.validateSignature) {
      const isValid = await this.validateSignature(req);
      if (!isValid) {
        throw new Error('Invalid webhook signature');
      }
    }

    // 解析载荷
    return this.parsePayload(req.body);
  }
}