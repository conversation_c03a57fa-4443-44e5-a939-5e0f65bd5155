import crypto from "crypto";
import type { WebhookRequest } from "./types";

/**
 * 验证 HMAC 签名
 */
export function validateHMACSignature(
  payload: string,
  signature: string,
  secret: string,
  algorithm = 'sha256'
): boolean {
  try {
    const expectedSignature = crypto
      .createHmac(algorithm, secret)
      .update(payload, 'utf8')
      .digest('hex');
    
    // 安全的字符串比较
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  } catch (error) {
    console.error('HMAC validation error:', error);
    return false;
  }
}

/**
 * 验证 SHA256 签名（GitHub 风格）
 */
export function validateSHA256Signature(
  payload: string,
  signature: string,
  secret: string
): boolean {
  try {
    // 移除 'sha256=' 前缀
    const cleanSignature = signature.replace(/^sha256=/, '');
    return validateHMACSignature(payload, cleanSignature, secret, 'sha256');
  } catch (error) {
    console.error('SHA256 validation error:', error);
    return false;
  }
}

/**
 * 验证 IP 地址白名单
 */
export function validateIPWhitelist(req: WebhookRequest, allowedIPs: string[]): boolean {
  try {
    // 从各种可能的头部获取 IP
    const clientIP = 
      req.headers['x-forwarded-for']?.split(',')[0]?.trim() ||
      req.headers['x-real-ip'] ||
      req.headers['cf-connecting-ip'] ||
      req.headers['x-client-ip'] ||
      req.headers['remote-addr'];

    if (!clientIP) {
      console.warn('No client IP found in request headers');
      return false;
    }

    const isAllowed = allowedIPs.includes(clientIP);
    
    if (!isAllowed) {
      console.warn(`IP ${clientIP} not in whitelist: ${allowedIPs.join(', ')}`);
    }

    return isAllowed;
  } catch (error) {
    console.error('IP validation error:', error);
    return false;
  }
}

/**
 * 验证 Bearer Token
 */
export function validateBearerToken(req: WebhookRequest, expectedToken: string): boolean {
  try {
    const authHeader = req.headers.authorization || req.headers.Authorization;
    
    if (!authHeader) {
      return false;
    }

    const token = authHeader.replace(/^Bearer\s+/, '');
    return crypto.timingSafeEqual(
      Buffer.from(token),
      Buffer.from(expectedToken)
    );
  } catch (error) {
    console.error('Bearer token validation error:', error);
    return false;
  }
}

/**
 * 通用的请求验证器
 */
export class WebhookValidator {
  private secret?: string;
  private allowedIPs?: string[];
  private bearerToken?: string;

  constructor(options: {
    secret?: string;
    allowedIPs?: string[];
    bearerToken?: string;
  } = {}) {
    this.secret = options.secret;
    this.allowedIPs = options.allowedIPs;
    this.bearerToken = options.bearerToken;
  }

  /**
   * 验证请求
   */
  async validate(req: WebhookRequest, payload: string): Promise<boolean> {
    // IP 白名单验证
    if (this.allowedIPs && this.allowedIPs.length > 0) {
      if (!validateIPWhitelist(req, this.allowedIPs)) {
        return false;
      }
    }

    // Bearer Token 验证
    if (this.bearerToken) {
      if (!validateBearerToken(req, this.bearerToken)) {
        return false;
      }
    }

    // HMAC 签名验证
    if (this.secret) {
      const signature = req.headers['x-signature'] || req.headers['X-Signature'];
      if (!signature) {
        return false;
      }

      if (!validateSHA256Signature(payload, signature, this.secret)) {
        return false;
      }
    }

    return true;
  }
}