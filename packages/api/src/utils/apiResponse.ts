import type { Context } from "hono";
import type { ContentfulStatusCode } from "hono/utils/http-status";

/**
 * API 响应工具
 * 提供统一的成功响应处理函数
 */
export const ApiResponse = {
  /**
   * 返回标准成功响应
   * @param c Hono 上下文
   * @param data 响应数据
   * @param status HTTP 状态码，默认为 200
   * @returns JSON 响应
   */
  success: <T>(c: Context, data: T, status = 200 as ContentfulStatusCode) => {
    // @ts-ignore - 忽略类型检查，因为项目中其他地方也是这样使用的
    return c.json(data, status);
  },

  /**
   * 返回带有剩余积分信息的成功响应
   * @param c Hono 上下文
   * @param data 响应数据
   * @param creditsRemaining 剩余积分
   * @param status HTTP 状态码，默认为 200
   * @returns JSON 响应
   */
  withCreditsResponse: <T>(
    c: Context,
    data: T,
    creditsRemaining: number,
    status = 200 as ContentfulStatusCode
  ) => {
    // @ts-ignore - 忽略类型检查，因为项目中其他地方也是这样使用的
    return c.json(
      {
        ...data,
        creditsRemaining,
      },
      status
    );
  },

  /**
   * 返回资源创建成功响应
   * @param c Hono 上下文
   * @param data 响应数据
   * @returns JSON 响应
   */
  created: <T>(c: Context, data: T) => {
    // @ts-ignore - 忽略类型检查，因为项目中其他地方也是这样使用的
    return c.json(data, 201 as ContentfulStatusCode);
  },

  /**
   * 返回无内容成功响应
   * @param c Hono 上下文
   * @returns 空响应
   */
  noContent: (c: Context) => {
    return c.body(null, 204);
  },
};
