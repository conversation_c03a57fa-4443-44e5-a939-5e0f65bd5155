import { logger } from "@repo/logs";
import type { Context } from "hono";
import { ErrorType, errorConfig } from "../constants/errors";

/**
 * 创建统一的错误响应
 * @param c Hono Context 对象
 * @param errorType 错误类型
 * @param data 附加数据
 * @returns JSON 响应
 */
function createErrorResponse(
	c: Context,
	errorType: ErrorType,
	data?: Record<string, any>,
) {
	const { status, defaultMessage } = errorConfig[errorType];

	// 记录错误日志
	logger.error(`API Error: ${errorType}`, { errorType, status, data });

	// 构建错误响应对象
	const responseBody = {
		code: errorType,
		message: defaultMessage,
		...(data || {}),
	};

	// 使用与项目中其他地方一致的方式返回 JSON 响应
	// @ts-ignore - 忽略类型检查，因为项目中其他地方也是这样使用的
	return c.json(responseBody, status);
}

/**
 * 积分不足错误
 * @param c Hono Context 对象
 * @param balance 当前积分余额
 * @returns JSON 响应
 */
function insufficientCreditsError(c: Context, balance: number) {
	return createErrorResponse(c, ErrorType.INSUFFICIENT_CREDITS, {
		creditsRemaining: balance,
	});
}

/**
 * 用户不存在错误
 * @param c Hono Context 对象
 * @returns JSON 响应
 */
function userNotFoundError(c: Context) {
	return createErrorResponse(c, ErrorType.USER_NOT_FOUND);
}

/**
 * AI 生成失败错误
 * @param c Hono Context 对象
 * @param details 错误详情
 * @returns JSON 响应
 */
function aiGenerationFailedError(c: Context, details?: string) {
	return createErrorResponse(
		c,
		ErrorType.AI_GENERATION_FAILED,
		details ? { details } : undefined,
	);
}

/**
 * 数据不存在错误
 * @param c Hono Context 对象
 * @param entityType 实体类型
 * @returns JSON 响应
 */
function dataNotFoundError(c: Context, entityType?: string) {
	return createErrorResponse(
		c,
		ErrorType.DATA_NOT_FOUND,
		entityType ? { entityType } : undefined,
	);
}

/**
 * 未授权错误
 * @param c Hono Context 对象
 * @returns JSON 响应
 */
function unauthorizedError(c: Context) {
	return createErrorResponse(c, ErrorType.UNAUTHORIZED);
}

/**
 * 请求参数错误
 * @param c Hono Context 对象
 * @param details 错误详情
 * @returns JSON 响应
 */
function badRequestError(c: Context, details?: string | Record<string, any>) {
	return createErrorResponse(
		c,
		ErrorType.BAD_REQUEST,
		details ? { details } : undefined,
	);
}

/**
 * 错误处理工具集合
 * 提供统一的错误响应处理函数
 */
export const errorHandler = {
	// 核心方法
	createErrorResponse,

	// 特定错误处理
	insufficientCreditsError,
	userNotFoundError,
	aiGenerationFailedError,
	dataNotFoundError,
	unauthorizedError,
	badRequestError,
};

// 为了向后兼容，继续导出单独的函数
export {
	createErrorResponse,
	insufficientCreditsError,
	userNotFoundError,
	aiGenerationFailedError,
	dataNotFoundError,
	unauthorizedError,
	badRequestError,
};
