/**
 * 测试 Webhook 优化改造后的功能
 * 
 * 使用方法：
 * 1. 确保开发服务器已启动：pnpm dev
 * 2. 运行测试：npx tsx packages/api/test-webhook-optimization.ts
 */

import { logger } from "@repo/logs";

const API_BASE_URL = process.env.API_URL || "http://localhost:3001/api";

// 测试数据
const testCases = [
  {
    name: "Replicate Processing Status",
    provider: "replicate",
    payload: {
      id: "test-replicate-processing-001",
      status: "processing",
      model: "stability-ai/stable-video-diffusion",
      created_at: new Date().toISOString(),
      started_at: new Date().toISOString(),
      input: {
        prompt: "Test video",
        metadata: {
          generationId: "gen-001",
          userId: "user-001",
          provider: "replicate"
        }
      }
    }
  },
  {
    name: "Replicate Succeeded Status",
    provider: "replicate",
    payload: {
      id: "test-replicate-succeeded-001",
      status: "succeeded",
      model: "stability-ai/stable-video-diffusion",
      created_at: new Date().toISOString(),
      started_at: new Date().toISOString(),
      completed_at: new Date().toISOString(),
      output: ["https://example.com/test-video.mp4"],
      input: {
        prompt: "Test video",
        metadata: {
          generationId: "gen-002",
          userId: "user-001",
          provider: "replicate"
        }
      }
    }
  },
  {
    name: "Fal Processing Status",
    provider: "fal",
    payload: {
      request_id: "test-fal-processing-001",
      status: "IN_PROGRESS",
      logs: [],
      metrics: {
        inference_time: 5.2
      }
    }
  },
  {
    name: "Fal Completed Status",
    provider: "fal",
    payload: {
      request_id: "test-fal-completed-001",
      status: "COMPLETED",
      output: {
        video: {
          url: "https://example.com/fal-video.mp4",
          content_type: "video/mp4",
          file_name: "video.mp4",
          file_size: 2097152
        }
      },
      logs: [],
      metrics: {
        inference_time: 12.5
      }
    }
  },
  {
    name: "Duplicate Event Test - Same provider, eventType, status",
    provider: "replicate",
    payload: {
      id: "test-replicate-duplicate-001",
      status: "processing",
      model: "stability-ai/stable-video-diffusion",
      created_at: new Date().toISOString(),
      started_at: new Date().toISOString()
    }
  }
];

async function sendWebhook(provider: string, payload: any): Promise<Response> {
  const url = `${API_BASE_URL}/webhooks/${provider}`;
  
  logger.info(`Sending webhook to ${url}`, {
    provider,
    status: payload.status
  });

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(payload)
  });

  const result = await response.json();
  
  logger.info(`Webhook response:`, {
    status: response.status,
    result
  });

  return response;
}

async function runTests() {
  logger.info("Starting webhook optimization tests...");

  for (const testCase of testCases) {
    logger.info(`\n========== Running test: ${testCase.name} ==========`);
    
    try {
      const response = await sendWebhook(testCase.provider, testCase.payload);
      
      if (response.ok) {
        logger.info(`✅ Test passed: ${testCase.name}`);
      } else {
        logger.error(`❌ Test failed: ${testCase.name}`, {
          status: response.status,
          statusText: response.statusText
        });
      }
      
      // 等待一下让处理完成
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      logger.error(`❌ Test error: ${testCase.name}`, {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  // 发送重复事件测试
  logger.info("\n========== Testing duplicate detection ==========");
  
  // 发送两次相同的webhook
  const duplicatePayload = {
    id: "test-duplicate-detection-001",
    status: "processing",
    model: "stability-ai/stable-video-diffusion",
    created_at: new Date().toISOString()
  };

  logger.info("Sending first webhook...");
  await sendWebhook("replicate", duplicatePayload);
  
  await new Promise(resolve => setTimeout(resolve, 500));
  
  logger.info("Sending duplicate webhook (should be skipped)...");
  await sendWebhook("replicate", duplicatePayload);

  // 检查处理统计
  logger.info("\n========== Checking processing stats ==========");
  
  try {
    const statsResponse = await fetch(`${API_BASE_URL}/webhooks/stats?hours=1`);
    const stats = await statsResponse.json();
    
    logger.info("Processing stats:", stats);
  } catch (error) {
    logger.error("Failed to get stats:", error);
  }

  // 检查健康状态
  logger.info("\n========== Checking health status ==========");
  
  try {
    const healthResponse = await fetch(`${API_BASE_URL}/webhooks/health`);
    const health = await healthResponse.json();
    
    logger.info("Health status:", health);
  } catch (error) {
    logger.error("Failed to get health status:", error);
  }

  logger.info("\n========== All tests completed ==========");
}

// 运行测试
runTests().catch(error => {
  logger.error("Test execution failed:", error);
  process.exit(1);
});