/**
 * 测试 Replicate webhook 视频处理流程
 * 
 * 使用方法：
 * 1. 确保环境变量已配置（参考 .env.local.example）
 * 2. 启动开发服务器：npm run dev
 * 3. 运行测试：npx tsx test-webhook-replicate.ts
 * 
 * 详细文档：/doc/replicate-webhook-testing-guide.md
 */

import { generateWebhookToken } from "@repo/utils";

// 模拟的 webhook 载荷
const createTestWebhookPayload = (status: 'starting' | 'processing' | 'succeeded' | 'failed', externalTaskId: string) => {
  const basePayload = {
    id: externalTaskId,
    model: "stability-ai/stable-video-diffusion-img2vid-xt",
    status,
    created_at: "2023-09-08T16:19:07.216219+00:00",
    started_at: "2023-09-08T16:19:07.435811+00:00",
  };

  if (status === 'succeeded') {
    return {
      ...basePayload,
      output: ["https://replicate.delivery/pbxt/abc123/output.mp4"],
      completed_at: "2023-09-08T16:19:34.467617+00:00",
      input: {
        prompt: "A castle in the sky",
        // 安全 token 参数（会被 webhook 回传）
        webhook_user_id: "test-user-123",
        webhook_task_id: "test-generation-456", 
        webhook_timestamp: Math.floor(Date.now() / 1000),
        webhook_token: "mock-token-for-testing"
      }
    };
  }

  if (status === 'failed') {
    return {
      ...basePayload,
      error: "Model run failed",
      completed_at: "2023-09-08T16:19:34.467617+00:00",
      input: {
        prompt: "A castle in the sky",
        webhook_user_id: "test-user-123",
        webhook_task_id: "test-generation-456",
        webhook_timestamp: Math.floor(Date.now() / 1000),
        webhook_token: "mock-token-for-testing"
      }
    };
  }

  return basePayload;
};

// 生成真实的安全 token（用于测试）
const generateTestSecurityToken = () => {
  const secretKey = process.env.REPLICATE_WEBHOOK_SECRET_KEY || "test-secret-key";
  const userId = "test-user-123";
  const taskId = "test-generation-456";
  
  return generateWebhookToken({
    userId,
    taskId,
    secretKey
  });
};

// 测试用例
const testCases = [
  {
    name: "视频生成成功",
    status: "succeeded" as const,
    externalTaskId: "test-prediction-success",
    description: "测试成功状态的视频上传和状态更新"
  },
  {
    name: "视频生成失败", 
    status: "failed" as const,
    externalTaskId: "test-prediction-failed",
    description: "测试失败状态的错误处理"
  },
  {
    name: "视频生成进行中",
    status: "processing" as const,
    externalTaskId: "test-prediction-processing", 
    description: "测试进度更新"
  }
];

// 发送测试 webhook
async function sendTestWebhook(testCase: typeof testCases[0]) {
  const webhookUrl = process.env.WEBHOOK_TEST_URL || "http://localhost:3001/api/webhooks/replicate";
  
  // 生成真实的安全 token
  const { token, timestamp } = generateTestSecurityToken();
  
  const payload = createTestWebhookPayload(testCase.status, testCase.externalTaskId);
  
  // 如果有 input，更新安全 token
  if (payload.input) {
    payload.input.webhook_timestamp = timestamp;
    payload.input.webhook_token = token;
  }

  console.log(`🧪 测试用例: ${testCase.name}`);
  console.log(`📝 描述: ${testCase.description}`);
  console.log(`📤 Payload:`, JSON.stringify(payload, null, 2));

  try {
    const response = await fetch(webhookUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        // 模拟 Replicate 的签名头（如果需要）
        "Replicate-Signature": "mock-signature"
      },
      body: JSON.stringify(payload)
    });

    const responseText = await response.text();
    
    if (response.ok) {
      console.log(`✅ 测试成功: ${response.status}`);
      console.log(`📥 响应:`, responseText);
    } else {
      console.log(`❌ 测试失败: ${response.status}`);
      console.log(`📥 错误响应:`, responseText);
    }
  } catch (error) {
    console.log(`💥 请求失败:`, error);
  }
  
  console.log("─".repeat(50));
}

// 运行所有测试
async function runAllTests() {
  console.log("🚀 开始 Replicate Webhook 测试流程");
  console.log("=".repeat(50));
  
  for (const testCase of testCases) {
    await sendTestWebhook(testCase);
    // 等待 1 秒再发送下一个请求
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log("✨ 测试完成");
}

// 环境检查
function checkEnvironment() {
  const requiredEnvVars = [
    'REPLICATE_WEBHOOK_SECRET_KEY',
    'DATABASE_URL',
    'S3_ENDPOINT',
    'S3_ACCESS_KEY_ID',
    'S3_SECRET_ACCESS_KEY'
  ];
  
  const missing = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    console.log("❌ 缺少环境变量:");
    missing.forEach(varName => console.log(`   - ${varName}`));
    console.log("\n请检查 .env.local 文件配置");
    return false;
  }
  
  console.log("✅ 环境变量检查通过");
  return true;
}

// 主函数
async function main() {
  console.log("🔧 Replicate Webhook 测试工具");
  console.log("用于测试视频上传和状态更新流程\n");
  
  if (!checkEnvironment()) {
    process.exit(1);
  }
  
  // 显示配置信息
  console.log("📋 测试配置:");
  console.log(`   Webhook URL: ${process.env.WEBHOOK_TEST_URL || "http://localhost:3001/api/webhooks/replicate"}`);
  console.log(`   Secret Key: ${process.env.REPLICATE_WEBHOOK_SECRET_KEY ? "已配置" : "未配置"}`);
  console.log(`   S3 Endpoint: ${process.env.S3_ENDPOINT || "未配置"}`);
  console.log("");
  
  await runAllTests();
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

export { testCases, sendTestWebhook, createTestWebhookPayload };