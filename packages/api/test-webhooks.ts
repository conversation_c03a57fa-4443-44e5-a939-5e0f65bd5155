/**
 * 测试新的 webhook 端点
 */

// 测试 Replicate webhook
const replicateWebhookPayload = {
  id: "test-prediction-id",
  status: "succeeded",
  output: ["https://example.com/test-video.mp4"],
  error: null,
  created_at: new Date().toISOString(),
  started_at: new Date().toISOString(),
  completed_at: new Date().toISOString()
};

// 测试 Fal webhook
const falWebhookPayload = {
  request_id: "test-request-id",
  status: "COMPLETED",
  output: {
    video: {
      url: "https://example.com/test-video.mp4",
      content_type: "video/mp4",
      file_name: "video.mp4",
      file_size: 2097152
    }
  },
  logs: [],
  metrics: {
    inference_time: 12.5
  }
};

console.log("✅ 新的 webhook 端点已准备就绪：");
console.log("- POST /api/webhooks/replicate - Replicate 专用端点");
console.log("- POST /api/webhooks/fal - Fal 专用端点");
console.log("- POST /api/webhooks/video - 统一端点（向后兼容）");
console.log("- POST /api/webhooks/payments - 支付 webhook");
console.log("\n📚 查看 API 文档：http://localhost:3001/api/docs");
console.log("\n🔐 环境变量配置：");
console.log("- REPLICATE_WEBHOOK_SECRET - Replicate 签名验证密钥");
console.log("- FAL_WEBHOOK_SECRET / FAL_WEBHOOK_TOKEN - Fal 验证凭据");
console.log("- REPLICATE_ALLOWED_IPS / FAL_ALLOWED_IPS - IP 白名单（可选）");

export { replicateWebhookPayload, falWebhookPayload };