export * from "./memory";
// 预留 Redis 提供者导出
// export * from "./redis";

import { CacheConfig, CacheProvider } from "../types";
import { MemoryCacheProvider } from "./memory";

/**
 * 创建缓存提供者实例
 * @param config 缓存配置
 * @returns 缓存提供者实例
 */
export function createCacheProvider(config: CacheConfig): CacheProvider {
  switch (config.provider) {
    case "memory":
      return new MemoryCacheProvider(config.defaultTtl);
    case "redis":
      // 预留 Redis 提供者实现
      throw new Error("Redis provider not implemented yet");
    default:
      return new MemoryCacheProvider(config.defaultTtl);
  }
}
