import { logger } from "@repo/logs";
import { CacheEntry, CacheProvider } from "../../types";

/**
 * 内存缓存项
 */
interface MemoryCacheItem<T> {
  data: T;
  timestamp: number;
  expiresAt: number | null;
}

/**
 * 内存缓存提供者实现
 * 使用 Map 存储缓存数据，适用于单实例部署
 */
export class MemoryCacheProvider implements CacheProvider {
  private cache: Map<string, MemoryCacheItem<any>>;
  private defaultTtl: number | null;
  
  /**
   * 创建内存缓存提供者
   * @param defaultTtl 默认缓存过期时间（秒），null 表示永不过期
   */
  constructor(defaultTtl: number | null = null) {
    this.cache = new Map();
    this.defaultTtl = defaultTtl;
    logger.debug("Memory cache provider initialized", { defaultTtl });
  }

  /**
   * 从缓存中获取数据
   * @param key 缓存键
   * @returns 缓存条目或 null（如果不存在或已过期）
   */
  async get<T>(key: string): Promise<CacheEntry<T> | null> {
    try {
      const item = this.cache.get(key);
      
      // 如果缓存不存在
      if (!item) {
        return null;
      }
      
      // 检查是否过期
      if (item.expiresAt !== null && Date.now() > item.expiresAt) {
        // 自动删除过期项
        this.cache.delete(key);
        return null;
      }
      
      return {
        data: item.data,
        timestamp: item.timestamp
      };
    } catch (error) {
      logger.error("Memory cache get error:", error);
      return null;
    }
  }

  /**
   * 将数据存入缓存
   * @param key 缓存键
   * @param data 要缓存的数据
   * @param ttl 缓存过期时间（秒），如果不提供则使用默认值
   */
  async set<T>(key: string, data: T, ttl?: number): Promise<void> {
    try {
      const timestamp = Date.now();
      const expiresAt = ttl !== undefined 
        ? timestamp + ttl * 1000 
        : this.defaultTtl !== null 
          ? timestamp + this.defaultTtl * 1000 
          : null;
      
      this.cache.set(key, {
        data,
        timestamp,
        expiresAt
      });
      
      logger.debug("Memory cache set", { key, ttl: ttl ?? this.defaultTtl });
    } catch (error) {
      logger.error("Memory cache set error:", error);
    }
  }

  /**
   * 删除缓存中的数据
   * @param key 缓存键
   */
  async delete(key: string): Promise<void> {
    try {
      const deleted = this.cache.delete(key);
      logger.debug("Memory cache delete", { key, success: deleted });
    } catch (error) {
      logger.error("Memory cache delete error:", error);
    }
  }

  /**
   * 清除所有缓存
   */
  async clear(): Promise<void> {
    try {
      this.cache.clear();
      logger.debug("Memory cache cleared");
    } catch (error) {
      logger.error("Memory cache clear error:", error);
    }
  }

  /**
   * 检查缓存是否存在
   * @param key 缓存键
   */
  async has(key: string): Promise<boolean> {
    try {
      const item = this.cache.get(key);
      
      // 如果缓存不存在
      if (!item) {
        return false;
      }
      
      // 检查是否过期
      if (item.expiresAt !== null && Date.now() > item.expiresAt) {
        // 自动删除过期项
        this.cache.delete(key);
        return false;
      }
      
      return true;
    } catch (error) {
      logger.error("Memory cache has error:", error);
      return false;
    }
  }
}
