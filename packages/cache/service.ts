import { logger } from "@repo/logs";
import { CacheConfig, CacheEntry, CacheProvider, CacheResult } from "./types";
import { createCacheProvider } from "./provider";

/**
 * Cache service class
 * Provides high-level cache operations and business logic encapsulation
 */
export class CacheService {
  private provider: CacheProvider;
  private config: CacheConfig;
  
  // Cache statistics
  private hits = 0;
  private misses = 0;

  /**
   * Creates a cache service instance
   * @param config Cache configuration
   */
  constructor(config: CacheConfig) {
    this.config = config;
    this.provider = createCacheProvider(config);
    logger.debug("Cache service initialized", { provider: config.provider });
  }

  /**
   * Gets the cache provider
   */
  getProvider(): CacheProvider {
    return this.provider;
  }

  /**
   * Gets the cache statistics
   */
  getStats() {
    const total = this.hits + this.misses;
    const hitRate = total > 0 ? (this.hits / total) * 100 : 0;
    
    return {
      hits: this.hits,
      misses: this.misses,
      total,
      hitRate: Math.round(hitRate * 100) / 100, // Keep two decimal places
    };
  }

  /**
   * Wraps data fetching with caching
   * @param key Cache key
   * @param fetchFn Data fetching function
   * @param options Cache options
   * @returns Data and cache information
   */
  async withCache<T>(
    key: string,
    fetchFn: () => Promise<T>,
    options: {
      ttl?: number;
      forceRefresh?: boolean;
      staleTtl?: number;
    } = {}
  ): Promise<CacheResult<T>> {
    const { forceRefresh = false, ttl, staleTtl } = options;
    
    // If not forcing a refresh, try to get from cache
    if (!forceRefresh) {
      const cached = await this.provider.get<T>(key);
      
      if (cached) {
        this.hits++;
        logger.debug("Cache hit", { key });
        
        // If staleTtl is set, check if a background refresh is needed
        if (staleTtl && Date.now() - cached.timestamp > staleTtl * 1000) {
          logger.debug("Cache stale but still usable, refreshing in background", { key });
          this.refreshInBackground(key, fetchFn, ttl);
        }
        
        return {
          data: cached.data,
          cached: true,
          cacheTime: cached.timestamp,
        };
      }
    }
    
    // Cache miss or force refresh, get new data
    this.misses++;
    logger.debug("Cache miss or force refresh", { key, forceRefresh });
    
    try {
      const data = await fetchFn();
      
      // Update cache
      await this.provider.set(key, data, ttl);
      
      const timestamp = Date.now();
      return {
        data,
        cached: false,
        cacheTime: timestamp,
      };
    } catch (error) {
      logger.error("Data fetch failed, trying to return stale cache", { key, error });
      
      // Try to get stale cache as a fallback
      const staleCache = await this.provider.get<T>(key);
      if (staleCache) {
        logger.debug("Returning stale cache as fallback", { key });
        return {
          data: staleCache.data,
          cached: true,
          cacheTime: staleCache.timestamp,
        };
      }
      
      // No cache available, throw the original error
      throw error;
    }
  }

  /**
   * Refreshes the cache in the background
   * @param key Cache key
   * @param fetchFn Data fetching function
   * @param ttl Cache expiration time (seconds)
   */
  private async refreshInBackground<T>(
    key: string,
    fetchFn: () => Promise<T>,
    ttl?: number
  ): Promise<void> {
    // Create a promise that doesn't wait for completion
    setTimeout(async () => {
      try {
        logger.debug("Starting background cache refresh", { key });
        const data = await fetchFn();
        await this.provider.set(key, data, ttl);
        logger.debug("Background cache refresh successful", { key });
      } catch (error) {
        logger.error("Background cache refresh failed", { key, error });
      }
    }, 0);
  }

  /**
   * Invalidates the cache for a specific key
   * @param key Cache key
   */
  async invalidate(key: string): Promise<void> {
    await this.provider.delete(key);
    logger.debug("Cache invalidated", { key });
  }

  /**
   * Clears all cache
   */
  async invalidateAll(): Promise<void> {
    await this.provider.clear();
    logger.debug("All cache cleared");
  }
}
