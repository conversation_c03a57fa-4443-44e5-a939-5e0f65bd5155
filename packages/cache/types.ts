/**
 * 缓存条目接口
 */
export interface CacheEntry<T> {
  data: T;
  timestamp: number;
}

/**
 * 缓存提供者接口
 */
export interface CacheProvider {
  /**
   * 从缓存中获取数据
   * @param key 缓存键
   * @returns 缓存条目或 null（如果不存在）
   */
  get<T>(key: string): Promise<CacheEntry<T> | null>;

  /**
   * 将数据存入缓存
   * @param key 缓存键
   * @param data 要缓存的数据
   * @param ttl 缓存过期时间（秒），可选
   */
  set<T>(key: string, data: T, ttl?: number): Promise<void>;

  /**
   * 删除缓存中的数据
   * @param key 缓存键
   */
  delete(key: string): Promise<void>;

  /**
   * 清除所有缓存
   */
  clear(): Promise<void>;

  /**
   * 检查缓存是否存在
   * @param key 缓存键
   */
  has(key: string): Promise<boolean>;
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  /**
   * 缓存提供者类型
   */
  provider: "memory" | "redis";

  /**
   * Redis 配置（当 provider 为 "redis" 时使用）
   */
  redis?: {
    url: string;
    password?: string;
    database?: number;
  };

  /**
   * 默认 TTL（秒）
   */
  defaultTtl?: number;
}

/**
 * 缓存结果接口
 */
export interface CacheResult<T> {
  /**
   * 缓存的数据
   */
  data: T;
  
  /**
   * 是否命中缓存
   */
  cached: boolean;
  
  /**
   * 缓存时间戳（如果命中缓存）
   */
  cacheTime: number | null;
}
