import { db } from "@repo/database";
import { createId } from "@paralleldrive/cuid2";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import type { UserCredits } from "../../types";

/**
 * 获取用户积分信息
 * @param userId 用户ID
 * @returns 用户积分信息
 */
export async function findCreditsByUserId(
	userId: string,
): Promise<UserCredits> {
	// 查询用户的信用点余额
	const creditUsage = await db.creditUsage.findFirst({
		where: { userId: userId },
		select: { balance: true, used: true, lastCreditReset: true },
	});

	return {
		userId,
		balance: creditUsage?.balance || 0,
		hasCredits: (creditUsage?.balance || 0) > 0,
		used: creditUsage?.used || 0,
		lastAward: creditUsage?.lastCreditReset || null,
	};
}

export async function createCredits(userId: string): Promise<UserCredits> {
	// 创建信用点记录，初始余额为5
	dayjs.extend(utc);
	const now = dayjs().utc().toDate();
	
	const creditUsage = await db.creditUsage.create({
		data: {
			id: createId(),
			userId,
			balance: 5,
			used: 0,
			lastCreditReset: now,
			createdAt: now,
			updatedAt: now,
		},
	});

	// 返回初始积分信息
	return {
		userId,
		balance: creditUsage.balance,
		hasCredits: creditUsage.balance > 0,
		used: creditUsage.used,
		lastAward: creditUsage.lastCreditReset,
	};
}

/**
 * 积分更新选项
 */
export interface CreditUpdateOptions {
	/** 积分余额更新 */
	balance?: { increment?: number; decrement?: number; set?: number };
	/** 已使用积分更新 */
	used?: { increment?: number; decrement?: number; set?: number };
	/** 最后奖励积分时间 */
	lastAward?: Date;
	/** 要返回的字段 */
	select?: { balance?: boolean; used?: boolean; lastCreditReset?: boolean };
}

/**
 * 更新用户积分记录
 * @param userId 用户ID
 * @param options 更新选项
 * @returns 更新后的记录
 */
export async function updateCreditUsage(
	userId: string,
	options: CreditUpdateOptions
) {
	const data: Record<string, any> = {};
	
	if (options.balance) {
		data.balance = options.balance;
	}
	
	if (options.used) {
		data.used = options.used;
	}
	
	if (options.lastAward) {
		data.lastCreditReset = options.lastAward;
	}
	
	// 添加更新时间
	dayjs.extend(utc);
	const now = dayjs().utc().toDate();
	data.updatedAt = now;

	// 更新记录
	return db.creditUsage.update({
		where: { userId },
		data,
		select: options.select || { balance: true },
	});
}

/**
 * 消费用户积分
 * @param userId 用户ID
 * @param amount 消费的积分数量（默认为1）
 * @returns 更新后的积分余额
 */
export async function consumeCredits(
	userId: string,
	amount = 1
): Promise<{ balance: number }> {
	return db.creditUsage.update({
		where: { userId },
		data: {
			balance: { decrement: amount },
			used: { increment: amount }
		},
		select: { balance: true }
	});
}
