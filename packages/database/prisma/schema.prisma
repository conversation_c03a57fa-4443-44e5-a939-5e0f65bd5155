datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}

generator client {
    provider = "prisma-client-js"
    output   = "./generated/client"
}

generator zod {
    provider         = "zod-prisma-types"
    output           = "./zod"
    createInputTypes = true
    addIncludeType   = false
    addSelectType    = false
}

generator json {
    provider = "prisma-json-types-generator"
}

model User {
    id                 String       @id @default(cuid())
    name               String
    email              String
    emailVerified      Boolean
    image              String?
    createdAt          DateTime
    updatedAt          DateTime
    username           String?
    role               String?
    banned             Boolean?
    banReason          String?
    banExpires         DateTime?
    onboardingComplete Boolean      @default(false)
    paymentsCustomerId String?
    locale             String?
    twoFactorEnabled   Boolean?
    sessions           Session[]
    accounts           Account[]
    passkeys           Passkey[]
    invitations        Invitation[]
    purchases          Purchase[]
    members            Member[]
    twofactors         TwoFactor[]
    aiChats            AiChat[]
    creditUsage        CreditUsage?
    subscriptions      Subscription[]
    paymentMethods     PaymentMethod[]
    jobs               Job[]
    generations        Generation[]
    generationLikes    GenerationLike[]  // 🆕 新增：用户点赞关系

    @@unique([email])
    @@unique([username])
    @@map("user")
}

model Session {
    id        String   @id @default(cuid())
    expiresAt DateTime
    ipAddress String?
    userAgent String?
    userId    String
    user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

    impersonatedBy String?

    activeOrganizationId String?

    token     String
    createdAt DateTime
    updatedAt DateTime

    @@unique([token])
    @@map("session")
}

model Account {
    id           String    @id @default(cuid())
    accountId    String
    providerId   String
    userId       String
    user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
    accessToken  String?   @db.Text
    refreshToken String?   @db.Text
    idToken      String?   @db.Text
    expiresAt    DateTime?
    password     String?

    accessTokenExpiresAt  DateTime?
    refreshTokenExpiresAt DateTime?
    scope                 String?
    createdAt             DateTime
    updatedAt             DateTime

    @@map("account")
}

model Verification {
    id         String   @id @default(cuid())
    identifier String
    value      String   @db.Text
    expiresAt  DateTime

    createdAt DateTime?
    updatedAt DateTime?

    @@map("verification")
}

model Passkey {
    id           String    @id @default(cuid())
    name         String?
    publicKey    String
    userId       String
    user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
    credentialID String
    counter      Int
    deviceType   String
    backedUp     Boolean
    transports   String?
    createdAt    DateTime?

    @@map("passkey")
}

model TwoFactor {
    id          String @id @default(cuid())
    secret      String
    backupCodes String
    userId      String
    user        User   @relation(fields: [userId], references: [id], onDelete: Cascade)

    @@map("twoFactor")
}

model Organization {
    id                 String       @id @default(cuid())
    name               String
    slug               String?
    logo               String?
    createdAt          DateTime
    metadata           String?
    paymentsCustomerId String?
    members            Member[]
    invitations        Invitation[]
    purchases          Purchase[]
    aiChats            AiChat[]

    @@unique([slug])
    @@map("organization")
}

model Member {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    userId         String
    user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
    role           String
    createdAt      DateTime

    @@unique([organizationId, userId])
    @@map("member")
}

model Invitation {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    email          String
    role           String?
    status         String
    expiresAt      DateTime
    inviterId      String
    user           User         @relation(fields: [inviterId], references: [id], onDelete: Cascade)

    @@map("invitation")
}

enum PurchaseType {
    SUBSCRIPTION
    ONE_TIME
}

model Purchase {
    id             String        @id @default(cuid())
    organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    organizationId String?
    user           User?         @relation(fields: [userId], references: [id], onDelete: Cascade)
    userId         String?
    type           PurchaseType
    customerId     String
    subscriptionId String?       @unique
    productId      String
    status         String?
    createdAt      DateTime      @default(now())
    updatedAt      DateTime      @updatedAt

    @@index([subscriptionId])
    @@map("purchase")
}

model AiChat {
    id             String        @id @default(cuid())
    organizationId String?
    organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    userId         String?
    user           User?         @relation(fields: [userId], references: [id], onDelete: Cascade)
    title          String?
    /// [Array<{role: "user" | "assistant"; content: string;}>]
    messages       Json          @default("[]") /// @zod.custom.use(z.array(z.object({ role: z.enum(['user', 'assistant']), content: z.string() })))
    createdAt      DateTime      @default(now())
    updatedAt      DateTime      @updatedAt

    @@map("ai_chat")
}

model CreditUsage {
  id              String     @id
  userId          String
  user            User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  balance         Int        @default(0)
  used            Int        @default(0)
  lastCreditReset DateTime?
  createdAt       DateTime
  updatedAt       DateTime
  @@unique([userId])
  @@map("credit_usage")
}

enum PlanType {
  recurring 
  one_time
}

enum PlanInterval {
  day
  week
  month
  year
}

model Plan {
  id               String       @id
  name             String
  description      String?      @db.Text
  type             PlanType
  priceId          String
  productId        String
  amount           Decimal      @db.Decimal(10, 2)
  currency         String
  interval         PlanInterval?
  intervalCount    Int?
  active           Boolean      @default(true)
  recommended      Boolean      @default(false)
  features         Json?
  createdAt        DateTime
  updatedAt        DateTime
  
  subscriptions    Subscription[]

  @@index([priceId])
  @@index([productId])
  @@map("plan")
}

enum SubscriptionStatus {
  incomplete
  incomplete_expired
  trialing
  active
  past_due
  canceled
  unpaid
  paused
}

enum PaymentProvider {
  STRIPE
  CHARGEBEE
  LEMONSQUEEZY
  CREEM
  POLAR
}

model Subscription {
  id                   String             @id
  userId               String
  user                 User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  provider             PaymentProvider
  subscriptionId       String             @unique
  planId               String
  plan                 Plan               @relation(fields: [planId], references: [id])
  status               SubscriptionStatus @default(incomplete)
  currentPeriodStart   DateTime?
  currentPeriodEnd     DateTime?
  cancelAtPeriodEnd    Boolean?           @default(false)
  canceledAt           DateTime?
  trialStart           DateTime?
  trialEnd             DateTime?
  createdAt            DateTime
  updatedAt            DateTime

  @@index([userId])
  @@index([planId])
  @@map("subscription")
}

enum AccountStatus {
  active
  suspended
  canceled
}

enum PaymentMethodType {
  card
  bank_account
  wallet
}

enum PaymentStatus {
  requires_payment_method
  requires_confirmation
  requires_action
  processing
  succeeded
  canceled
  requires_capture
}

model PaymentMethod {
  id                   String            @id
  userId               String
  provider             PaymentProvider
  user                 User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  paymentMethodId      String
  type                 PaymentMethodType
  last4                String?
  expiryMonth          Int?
  expiryYear           Int?
  brand                String?
  walletType           String?
  bankName             String?
  country              String?
  currency             String?
  isDefault            Boolean
  createdAt            DateTime
  updatedAt            DateTime

  @@index([userId])
  @@index([paymentMethodId])
  @@map("payment_method")
}

model Payment {
  id                   String          @id
  paymentIntentId      String
  invoiceId            String
  subscriptionId       String?
  amount               Int
  currency             String
  status               PaymentStatus
  paymentMethodType    String?
  userId               String
  provider             PaymentProvider
  createdAt            DateTime
  updatedAt            DateTime

  @@index([userId])
  @@index([paymentIntentId])
  @@index([invoiceId])
  @@index([subscriptionId])
  
  @@unique([provider, paymentIntentId]) // 确保每个提供商的paymentIntentId唯一
  @@unique([provider, invoiceId])       // 确保每个提供商的invoiceId唯一

  @@map("payment")
}

enum InvoiceStatus {
  draft
  open
  paid
  uncollectible
  void
}

model Invoice {
  id               String          @id
  invoiceId        String          @unique
  paymentIntentId  String          @unique
  subscriptionId   String
  amountDue        Int
  amountPaid       Int
  currency         String
  status           InvoiceStatus
  invoicePdf       String
  billingReason    String
  provider         PaymentProvider  
  userId           String
  createdAt        DateTime
  updatedAt        DateTime

  @@index([userId])
  @@index([subscriptionId])
  @@index([invoiceId])
  @@index([paymentIntentId])
  
  @@unique([provider, invoiceId])       // 确保每个提供商的invoiceId唯一  
  @@unique([provider, paymentIntentId]) // 确保每个提供商的paymentIntentId唯一
  
  @@map("invoice")
}

model WebhookEvent {
  id             String           @id
  eventId        String
  eventType      String
  subscriptionId String?
  status         String
  priceId        String?
  productId      String?
  provider       PaymentProvider
  userId         String?
  customerId     String?
  data           Json
  processed      Boolean
  createdAt      DateTime
  processedAt    DateTime?

  @@unique([provider, eventId])
  @@index([processed, createdAt])
  @@index([userId])
  @@index([eventType])
  @@index([provider, eventType])
  @@index([createdAt])
  @@index([customerId])
  @@index([priceId])
  @@index([productId])
  @@map("webhook_event")
}

enum MediaType {
  video
  image
}

// 新增枚举类型
enum JobType {
  video
  image
}

enum JobStatus  {
  waiting
  processing
  succeeded
  failed
}

// 移除 AspectRatio 枚举，使用字符串类型

enum ProcessType {
  text
  image
  video
}

enum PublishStatus {
  reviewing
  published
  rejected
}

// 任务表
model Job {
  id                String      @id
  userId            String
  featureCode       String
  type              JobType
  credit            Int
  apiProviderCost   Decimal?     @db.Decimal(10, 4)
  status            JobStatus 
  numOutputs        Int
  timeCostSeconds   Int?
  modelCode                     String
  apiProviderCode               String
  prompt                        String?          @db.Text
  image                         String?
  imageTail                     String?
  negativePrompt                String?          @db.Text
  promptStrength                Float?
  duration                      Int
  modeCode                      String?
  resolution                    String?
  aspectRatio                   String
  style                         String?
  motionRange                   String?
  seed                          Int?
  cameraType                    String?
  cameraConfig                  String?
  cameraFixed                   Boolean?
  video                         String?
  generationTemplate            String?
  templateId                    String?
  templateImage                 String?
  processType                   ProcessType
  published                     Boolean          @default(false)
  protectionMode                Boolean          @default(false)
  enableMagicPrompt             Boolean          @default(false)
  enableTranslatePrompt         Boolean          @default(false)
  enableTranslateNegativePrompt Boolean          @default(false)

  externalTaskId    String?     // API提供商的任务ID
  errorMessage      String?     @db.Text // 错误信息
  createdAt         DateTime          
  updatedAt         DateTime          

  // 关联关系
  user              User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  generations   Generation[]

  @@index([userId])
  @@index([featureCode])
  @@index([status])
  @@index([createdAt])
  @@index([apiProviderCode])
  @@map("job")
}

// 生成的媒体表
model Generation  {
  id                    String         @id
  userId                String
  jobId                String
  mediaId               String
  externalTaskId        String?        // API提供商返回的任务ID
  cover                 String?
  thumbnail             String?
  videoUrl              String?
  starNum               Int            @default(0)
  shareNum              Int            @default(0)
  playNum               Int            @default(0)
  downloadNum           Int            @default(0)
  videoRatio            String?
  duration              Int?
  klVideoId             String?
  publishStatus         PublishStatus?
  publishDate           DateTime?
  protectionMode        Boolean        @default(false) @map("protection_mode")
  isLike                Boolean        @default(false) @map("is_like")
  favorite              Boolean        @default(false) @map("favorite")
  score                 Int?
  sort                  Int            @default(0)
  mediaUrl              String?
  videoUrlNoWatermark   String?
  mediaType             MediaType
  status                JobStatus 
  deleted               Boolean        @default(false)
  canRead               Boolean        @default(true)
  canCopyPrompt         Boolean        @default(true)
  canPublish            Boolean        @default(true)
  canProtectCopy        Boolean        @default(true)
  canDelete             Boolean        @default(true)
  canOwn                Boolean        @default(true)
  canCreateSimilar      Boolean        @default(true)
  createdAt             DateTime      
  updatedAt             DateTime      

  // 关联关系
  user                 User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  job                  Job            @relation(fields: [jobId], references: [id], onDelete: Cascade)
  likes                GenerationLike[]  // 🆕 新增：点赞关系

  @@index([userId])
  @@index([jobId])
  @@index([mediaType])
  @@index([status])
  @@index([publishStatus])
  @@index([createdAt])
  @@index([externalTaskId])
  @@index([favorite])
  @@index([userId, favorite])
  @@index([favorite, createdAt])
  @@map("generation")
}

enum VideoWebhookEventProcessStatus {
  pending     // 待处理
  processing  // 处理中
  completed   // 已完成
  failed      // 永久失败
}

model VideoWebhookEvent {
  id             String    @id
  
  // 基本信息
  eventId        String    // 提供商的事件ID（幂等性）
  provider       String    // 提供商名称: replicate, fal
  eventType      String    // 事件类型: generation.completed, generation.failed
  
  // 业务数据
  externalTaskId String    // 提供商的任务ID
  generationId   String?   // 我们的生成ID（从metadata中提取）
  userId         String?   // 用户ID（从metadata中提取）
  
  // Webhook 原始数据
  rawPayload     Json      // 原始 webhook 载荷
  headers        Json      // 请求头信息
  signature      String?   // 签名信息
  
  // 🆕 新增：webhook原始状态
  status         String    // webhook原始报文中的status ("processing", "succeeded", etc.)
  
  // 🔄 重命名：内部处理状态
  processStatus  VideoWebhookEventProcessStatus @default(pending)
  processedAt    DateTime?
  retryCount     Int       @default(0)
  maxRetries     Int       @default(5)
  
  // 错误信息
  lastError      String?   @db.Text
  errorDetails   Json?     // 详细错误信息
  
  // 时间戳 (手动管理，使用dayjs UTC)
  receivedAt     DateTime
  nextRetryAt    DateTime?
  createdAt      DateTime
  updatedAt      DateTime
  
  // 索引优化
  @@unique([provider, eventId, status]) // 新的防重复策略
  @@index([processStatus, nextRetryAt]) // 处理器查询优化
  @@index([provider, externalTaskId]) // 业务查询优化
  @@index([generationId]) // 业务关联查询
  @@index([receivedAt]) // 时间范围查询
  @@index([retryCount, processStatus]) // 重试监控
  @@map("video_webhook_event")
}

// 🆕 新增：用户点赞关联表
model GenerationLike {
  id           String      @id @default(cuid())
  userId       String
  generationId String
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  
  // 关联关系
  user         User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  generation   Generation  @relation(fields: [generationId], references: [id], onDelete: Cascade)
  
  // 确保用户对同一个generation只能点赞一次
  @@unique([userId, generationId])
  @@index([userId])
  @@index([generationId])
  @@map("generation_like")
}

