# Jobs Package Usage Examples

## 使用预定义查询配置

### 之前的代码（复杂且重复）

```typescript
// 获取Job详情 - 需要写很多配置
const job = await JobsManager.findJobById(jobId, {
  videoJobParam: true,
  generations: {
    orderBy: { createdAt: "asc" },
    select: {
      id: true,
      mediaId: true,
      cover: true,
      thumbnail: true,
      videoUrl: true,
      mediaUrl: true,
      status: true,
      mediaType: true,
      duration: true,
      createdAt: true,
      updatedAt: true,
    }
  }
});
```

### 现在的代码（简洁清晰）

```typescript
// 方式1: 使用业务方法
const job = await JobsManager.getJobDetails(jobId);

// 方式2: 使用预定义查询配置
const job = await JobsManager.findJobById(jobId, JobsManager.QueryConfigs.FULL_DETAILS);
```

## 不同场景的使用示例

### 1. 获取Job完整详情
```typescript
// 用于详情页面
const job = await JobsManager.getJobDetails(jobId);
```

### 2. 获取Job基本信息
```typescript
// 用于快速状态检查
const job = await JobsManager.findJobById(jobId, JobsManager.QueryConfigs.BASIC_INFO);
```

### 3. 获取已完成的生成结果
```typescript
// 只获取成功的生成结果
const job = await JobsManager.findJobById(jobId, JobsManager.QueryConfigs.COMPLETED_GENERATIONS);
```

### 4. 获取用户Job列表
```typescript
// 用于Job列表页面
const jobs = await JobsManager.findJobsByUserId(userId, JobsManager.QueryConfigs.LIST_VIEW);
```

### 5. 获取仪表板数据
```typescript
// 用于首页仪表板
const jobs = await JobsManager.getUserJobsDashboard(userId, 10);
```

### 6. 自定义查询
```typescript
// 只获取处理中的生成结果
const job = await JobsManager.findJobById(jobId, 
  createCustomJobQuery.withGenerations({
    statusFilter: ["processing"],
    includeMedia: false
  })
);

// 分页查询
const { skip, take, include } = createCustomJobQuery.withPagination(1, 20);
const jobs = await JobsManager.queryJobs(
  { userId }, 
  { page: 1, limit: 20 }, 
  include
);
```

## 数据映射示例

### 之前的代码（冗长的字段映射）

```typescript
const videoJobParamData: CreateVideoJobParamData = {
  jobId,
  userId,
  modelId: model.code,
  modelCode: model.code,
  apiProviderCode: model.apiProviderCode,
  prompt: request.modelParam.prompt,
  image: request.modelParam.image,
  // ... 25+ 行字段映射
};
```

### 现在的代码（简洁的mapper调用）

```typescript
// 验证请求
JobDataMapper.validateJobRequest(request);

// 标准化数据
const normalizedRequest = JobDataMapper.normalizeJobRequest(request);

// 创建数据
const { jobData, videoJobParamData, type } = JobDataMapper.createJobWithParamsData(
  jobId, userId, normalizedRequest, model, totalCredit
);
```

## 业务方法示例

### 获取Job状态摘要
```typescript
const summary = await JobsManager.getJobStatusSummary(jobId);
// 返回: { job: {...}, progress: {...}, generations: [...] }
```

### 获取用户最近Jobs
```typescript
const recentJobs = await JobsManager.getUserRecentJobs(userId, 5);
```

### 获取带生成信息的用户Jobs
```typescript
const jobs = await JobsManager.getUserJobsWithGenerations(userId, 20, 0);
```

## 优势总结

1. **代码简洁**: 从30+行配置减少到1行调用
2. **复用性强**: 预定义查询可在多处使用
3. **类型安全**: 完整的TypeScript类型定义
4. **易于维护**: 修改查询逻辑只需更改一处
5. **性能优化**: 针对不同场景优化查询字段
6. **业务语义**: 方法名清晰表达业务意图