{"name": "@repo/jobs", "version": "0.0.1", "private": true, "main": "./index.ts", "types": "./**/*.ts", "license": "MIT", "scripts": {"type-check": "tsc --noEmit"}, "dependencies": {"@repo/database": "workspace:*", "@repo/logs": "workspace:*", "@repo/redis": "workspace:*", "@paralleldrive/cuid2": "^2.2.2", "dayjs": "^1.11.7", "zod": "^3.25.76"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/node": "^22.13.9"}}