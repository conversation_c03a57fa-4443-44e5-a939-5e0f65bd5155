import type { JobStatus, JobType } from "../../types";

/**
 * 标准API响应基础设施
 */

// 基础响应接口
export interface BaseApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp?: string;
  requestId?: string;
}

// 分页响应接口
export interface PaginatedApiResponse<T = any> extends BaseApiResponse<T[]> {
  pagination?: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Job相关的响应数据类型
export interface CreateJobResponseData {
  user: {
    id: string;
    name: string;
    username: string;
    image: string;
  };
  job: {
    id: string;
    userId: string;
    featureCode: string;
    type: "video" | "image";
    numOutputs: number;
    status: "waiting" | "processing" | "succeeded" | "failed";
    credit: number;
    apiProviderCost: number;
    timeCostSeconds: number;
    modelCode: string;
    prompt: string;
    image: string;
    imageTail: string;
    negativePrompt: string;
    promptStrength: number;
    duration: number;
    modeCode: string;
    resolution: string;
    aspectRatio: string;
    style: string;
    motionRange: string;
    seed: number;
    processType: string;
    createdAt: string;
    updatedAt: string;
  };
  generations: Array<{
    id: string;
    mediaId: string;
    cover: string;
    thumbnail: string;
    videoUrl: string;
    mediaUrl: string;
    status: "waiting" | "processing" | "succeeded" | "failed";
    mediaType: "video" | "image";
    duration: number;
    createdAt: string;
    updatedAt: string;
  }>;
  progress: {
    completed: number;
    total: number;
  };
}

export interface JobDetailsResponseData {
  user: {
    id: string;
    name?: string;
    username?: string;
    image?: string;
  };
  job: {
    id: string;
    userId: string;
    featureCode: string;
    type: JobType;
    numOutputs: number;
    status: "waiting" | "processing" | "succeeded" | "failed";
    credit: number;
    apiProviderCost?: number;
    timeCostSeconds?: number;
    modelCode?: string;
    prompt?: string;
    image?: string;
    imageTail?: string;
    negativePrompt?: string;
    promptStrength?: number;
    duration?: number;
    modeCode?: string;
    resolution?: string;
    aspectRatio?: string;
    style?: string;
    motionRange?: string;
    seed?: number;
    processType?: string;
    createdAt: string;
    updatedAt: string;
  };
  generations: Array<{
    id: string;
    mediaId: string;
    cover?: string;
    thumbnail?: string;
    videoUrl?: string;
    mediaUrl?: string;
    status: "waiting" | "processing" | "succeeded" | "failed";
    mediaType: "video" | "image";
    duration?: number;
    createdAt: string;
    updatedAt: string;
  }>;
  progress: {
    completed: number;
    total: number;
  };
}

export interface JobStatusResponseData {
  jobId: string;
  status: JobStatus;
  progress: {
    completed: number;
    total: number;
    percentage: number;
  };
  estimatedTimeRemaining?: number;
  lastUpdated: string;
}

export interface JobListResponseData {
  id: string;
  status: JobStatus;
  numOutputs: number;
  completedCount: number;
  createdAt: string;
  modelCode?: string;
  prompt?: string;
  image?: string;
  latestGeneration?: {
    id: string;
    status: "waiting" | "processing" | "succeeded" | "failed";
    videoUrl?: string;
    cover?: string;
  };
}

/**
 * API响应构建器
 */
export class ApiResponseBuilder {
  /**
   * 创建成功响应
   */
  static success<T>(data: T, message?: string): BaseApiResponse<T> {
    return {
      success: true,
      data,
      message,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 创建错误响应
   */
  static error(error: string, message?: string): BaseApiResponse {
    return {
      success: false,
      error,
      message,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 创建分页响应
   */
  static paginated<T>(
    data: T[],
    pagination: PaginatedApiResponse<T>["pagination"],
    message?: string
  ): PaginatedApiResponse<T> {
    return {
      success: true,
      data,
      pagination,
      message,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 从异常创建错误响应
   */
  static fromError(error: unknown): BaseApiResponse {
    if (error instanceof Error) {
      return this.error(error.message);
    }
    return this.error("Unknown error occurred");
  }
}

/**
 * Job相关的响应构建器
 */
export class JobResponseBuilder {
  /**
   * 创建Job成功响应
   */
  static createJob(data: CreateJobResponseData): BaseApiResponse<CreateJobResponseData> {
    return ApiResponseBuilder.success(data, "Job created successfully");
  }

  /**
   * 创建Job详情响应
   */
  static jobDetails(data: JobDetailsResponseData): BaseApiResponse<JobDetailsResponseData> {
    return ApiResponseBuilder.success(data, "Job details retrieved successfully");
  }

  /**
   * 创建Job状态响应
   */
  static jobStatus(data: JobStatusResponseData): BaseApiResponse<JobStatusResponseData> {
    return ApiResponseBuilder.success(data, "Job status retrieved successfully");
  }

  /**
   * 创建Job列表响应
   */
  static jobList(
    data: JobListResponseData[],
    pagination?: PaginatedApiResponse["pagination"]
  ): PaginatedApiResponse<JobListResponseData> {
    if (pagination) {
      return ApiResponseBuilder.paginated(data, pagination, "Job list retrieved successfully");
    }
    return {
      ...ApiResponseBuilder.success(data, "Job list retrieved successfully"),
      data,
    } as PaginatedApiResponse<JobListResponseData>;
  }

  /**
   * Job不存在错误
   */
  static jobNotFound(jobId?: string): BaseApiResponse {
    return ApiResponseBuilder.error(
      "Job not found",
      jobId ? `Job with ID ${jobId} was not found` : "The requested job does not exist"
    );
  }

  /**
   * 权限不足错误
   */
  static accessDenied(): BaseApiResponse {
    return ApiResponseBuilder.error(
      "Access denied",
      "You don't have permission to access this job"
    );
  }

  /**
   * 积分不足错误
   */
  static insufficientCredits(required: number, current: number): BaseApiResponse {
    return ApiResponseBuilder.error(
      "Insufficient credits",
      `This operation requires ${required} credits, but you only have ${current} credits`
    );
  }

  /**
   * 无效参数错误
   */
  static invalidParameters(details?: string): BaseApiResponse {
    return ApiResponseBuilder.error(
      "Invalid parameters",
      details || "The provided parameters are invalid"
    );
  }

  /**
   * 模型不可用错误
   */
  static modelUnavailable(modelCode?: string): BaseApiResponse {
    return ApiResponseBuilder.error(
      "Model unavailable",
      modelCode ? `Model ${modelCode} is not available` : "The requested model is not available"
    );
  }

  /**
   * 速率限制错误
   */
  static rateLimitExceeded(retryAfter?: number): BaseApiResponse {
    return ApiResponseBuilder.error(
      "Rate limit exceeded",
      retryAfter ? `Please try again in ${retryAfter} seconds` : "Too many requests, please try again later"
    );
  }

  // =====================================================
  // 业务包装方法 - 简化数据转换逻辑
  // =====================================================

  /**
   * 从创建结果创建响应 - 需要完整的job数据和用户信息
   */
  static fromCreateResult(
    job: any,
    user: any,
    generations: any[],
    progress: { completed: number; total: number }
  ): BaseApiResponse<CreateJobResponseData> {
    return this.createJob({
      user: {
        id: user.id,
        name: user.name || '',
        username: user.username || '',
        image: user.image || '',
      },
      job: {
        id: job.id,
        userId: job.userId,
        featureCode: job.featureCode,
        type: job.type,
        numOutputs: job.numOutputs,
        status: job.status,
        credit: job.credit,
        apiProviderCost: job.apiProviderCost ? Number(job.apiProviderCost) : 0,
        timeCostSeconds: job.timeCostSeconds || 0,
        modelCode: job.modelCode,
        prompt: job.prompt || '',
        image: job.image || '',
        imageTail: job.imageTail || '',
        negativePrompt: job.negativePrompt || '',
        promptStrength: job.promptStrength || 0,
        duration: job.duration,
        modeCode: job.modeCode || '',
        resolution: job.resolution || '',
        aspectRatio: job.aspectRatio,
        style: job.style || '',
        motionRange: job.motionRange || '',
        seed: job.seed || 0,
        processType: job.processType,
        createdAt: job.createdAt instanceof Date ? job.createdAt.toISOString() : job.createdAt,
        updatedAt: job.updatedAt instanceof Date ? job.updatedAt.toISOString() : job.updatedAt,
      },
      generations: generations.map((g: any) => ({
        id: g.id,
        mediaId: g.mediaId || '',
        cover: g.cover || '',
        thumbnail: g.thumbnail || '',
        videoUrl: g.videoUrl || '',
        mediaUrl: g.mediaUrl || '',
        status: g.status,
        mediaType: g.mediaType,
        duration: g.duration || 0,
        createdAt: g.createdAt instanceof Date ? g.createdAt.toISOString() : g.createdAt,
        updatedAt: g.updatedAt instanceof Date ? g.updatedAt.toISOString() : g.updatedAt,
      })),
      progress,
    });
  }

  /**
   * 从数据库 Job 对象创建详情响应
   */
  static fromJobEntity(
    job: any,
    user: any,
    progress: any
  ): BaseApiResponse<JobDetailsResponseData> {
    return this.jobDetails({
      user: {
        id: user.id,
        name: user.name,
        username: user.username,
        image: user.image,
      },
      job: {
        id: job.id,
        userId: job.userId,
        featureCode: job.featureCode,
        type: job.type,
        numOutputs: job.numOutputs,
        status: job.status,
        credit: job.credit,
        apiProviderCost: job.apiProviderCost ? Number(job.apiProviderCost) : undefined,
        timeCostSeconds: job.timeCostSeconds,
        modelCode: job.modelCode,
        prompt: job.prompt,
        image: job.image,
        imageTail: job.imageTail,
        negativePrompt: job.negativePrompt,
        promptStrength: job.promptStrength,
        duration: job.duration,
        modeCode: job.modeCode,
        resolution: job.resolution,
        aspectRatio: job.aspectRatio,
        style: job.style,
        motionRange: job.motionRange,
        seed: job.seed,
        processType: job.processType,
        createdAt: job.createdAt.toISOString(),
        updatedAt: job.updatedAt.toISOString(),
      },
      generations: job.generations?.map((g: any) => ({
        ...g,
        createdAt: g.createdAt.toISOString(),
        updatedAt: g.updatedAt.toISOString(),
      })) || [],
      progress: {
        completed: progress.completed,
        total: progress.total,
      },
    });
  }
}

/**
 * 响应装饰器 - 用于统一处理响应格式
 */
export function withApiResponse<T extends any[], R>(
  fn: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<BaseApiResponse<R>> => {
    try {
      const result = await fn(...args);
      return ApiResponseBuilder.success(result);
    } catch (error) {
      return ApiResponseBuilder.fromError(error);
    }
  };
}

/**
 * 响应类型守卫
 */
export function isSuccessResponse<T>(response: BaseApiResponse<T>): response is BaseApiResponse<T> & { success: true; data: T } {
  return response.success === true && response.data !== undefined;
}

export function isErrorResponse(response: BaseApiResponse): response is BaseApiResponse & { success: false; error: string } {
  return response.success === false && response.error !== undefined;
}

/**
 * 响应状态码映射
 */
export const ResponseStatusCode = {
  SUCCESS: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  PAYMENT_REQUIRED: 402,
  RATE_LIMITED: 429,
  INTERNAL_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

/**
 * 获取响应状态码
 */
export function getResponseStatusCode(response: BaseApiResponse): 200 | 400 | 402 | 403 | 404 | 429 | 500 | 503 {
  if (response.success) {
    return ResponseStatusCode.SUCCESS;
  }

  // 根据错误类型返回适当的状态码
  if (response.error?.includes("not found")) {
    return ResponseStatusCode.NOT_FOUND;
  }
  if (response.error?.includes("access denied") || response.error?.includes("permission")) {
    return ResponseStatusCode.FORBIDDEN;
  }
  if (response.error?.includes("insufficient credits")) {
    return ResponseStatusCode.PAYMENT_REQUIRED;
  }
  if (response.error?.includes("invalid parameters") || response.error?.includes("validation")) {
    return ResponseStatusCode.BAD_REQUEST;
  }
  if (response.error?.includes("rate limit")) {
    return ResponseStatusCode.RATE_LIMITED;
  }
  if (response.error?.includes("unavailable")) {
    return ResponseStatusCode.SERVICE_UNAVAILABLE;
  }

  return ResponseStatusCode.INTERNAL_ERROR;
}