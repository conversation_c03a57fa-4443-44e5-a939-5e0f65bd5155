import { redis } from '@repo/redis';
import { db } from '@repo/database';

export class FavoritesRedisManager {
  /**
   * 快速切换收藏状态（使用Redis + 异步数据库同步）
   */
  static async toggleFavoriteFast(generationId: string, userId: string): Promise<{
    success: boolean;
    favorite: boolean;
  } | null> {
    const startTime = Date.now();
    const favoriteKey = `favorites:user:${userId}`;
    
    console.log(`[FavoritesRedisManager] ⚡ Starting favorite toggle for ${generationId} by user ${userId}`);
    
    // 使用Lua脚本实现原子操作
    const luaScript = `
      local favoriteKey = KEYS[1]
      local generationId = ARGV[1]
      
      -- 检查是否已收藏
      local isFavorited = redis.call('SISMEMBER', favoriteKey, generationId)
      
      if isFavorited == 1 then
        -- 取消收藏
        redis.call('SREM', favoriteKey, generationId)
        return 0
      else
        -- 添加收藏
        redis.call('SADD', favoriteKey, generationId)
        return 1
      end
    `;
    
    try {
      // 首先检查generation是否存在且属于用户
      const generation = await db.generation.findUnique({
        where: { id: generationId },
        select: { id: true, userId: true, deleted: true }
      });

      if (!generation || generation.deleted || generation.userId !== userId) {
        return null;
      }

      const scriptStartTime = Date.now();
      const result = await redis.eval(luaScript, 1, favoriteKey, generationId) as number;
      const scriptTime = Date.now() - scriptStartTime;
      
      const isFavorited = result === 1;
      
      console.log(`[FavoritesRedisManager] ⚡ ${isFavorited ? 'Added' : 'Removed'} favorite (script: ${scriptTime}ms)`);
      
      // 异步数据库同步，不阻塞响应
      this.asyncSyncFavoriteToDatabase(generationId, isFavorited);
      
      const totalTime = Date.now() - startTime;
      console.log(`[FavoritesRedisManager] ✅ Favorite toggle completed: ${isFavorited ? 'favorited' : 'unfavorited'} (script: ${scriptTime}ms, total: ${totalTime}ms)`);
      
      return {
        success: true,
        favorite: isFavorited
      };
      
    } catch (error) {
      console.error(`[FavoritesRedisManager] ❌ Toggle favorite failed:`, error);
      return null;
    }
  }

  /**
   * 批量获取用户的收藏状态
   */
  static async batchGetFavoriteStatus(generationIds: string[], userId: string): Promise<Record<string, boolean>> {
    if (generationIds.length === 0) return {};
    
    const favoriteKey = `favorites:user:${userId}`;
    
    try {
      const pipeline = redis.pipeline();
      
      for (const genId of generationIds) {
        pipeline.sismember(favoriteKey, genId);
      }
      
      const results = await pipeline.exec();
      const status: Record<string, boolean> = {};
      
      for (let i = 0; i < generationIds.length; i++) {
        const genId = generationIds[i];
        status[genId] = (results?.[i]?.[1] as number) === 1;
      }
      
      return status;
    } catch (error) {
      console.error('[FavoritesRedisManager] Batch get favorite status failed:', error);
      return {};
    }
  }

  /**
   * 获取用户的所有收藏
   */
  static async getUserFavorites(userId: string): Promise<string[]> {
    const favoriteKey = `favorites:user:${userId}`;
    
    try {
      const favorites = await redis.smembers(favoriteKey);
      return favorites;
    } catch (error) {
      console.error('[FavoritesRedisManager] Get user favorites failed:', error);
      return [];
    }
  }

  /**
   * 异步同步到数据库
   */
  private static asyncSyncFavoriteToDatabase(generationId: string, isFavorited: boolean) {
    setImmediate(async () => {
      try {
        await db.generation.update({
          where: { id: generationId },
          data: { favorite: isFavorited }
        });
        
        console.log(`[FavoritesRedisManager] Background DB sync completed: ${generationId} -> ${isFavorited ? 'favorited' : 'unfavorited'}`);
      } catch (error) {
        console.error('[FavoritesRedisManager] Background DB sync failed:', error);
      }
    });
  }

  /**
   * 将数据库中的收藏数据迁移到Redis
   */
  static async migrateFavoritesToRedis(): Promise<{ success: boolean; migrated: number }> {
    try {
      const generations = await db.generation.findMany({
        where: { 
          deleted: false,
          favorite: true
        },
        select: {
          id: true,
          userId: true
        }
      });
      
      let migratedCount = 0;
      const userFavorites: Record<string, string[]> = {};
      
      // 按用户分组收藏
      for (const gen of generations) {
        if (!userFavorites[gen.userId]) {
          userFavorites[gen.userId] = [];
        }
        userFavorites[gen.userId].push(gen.id);
      }
      
      // 批量写入Redis
      const pipeline = redis.pipeline();
      for (const [userId, favoriteIds] of Object.entries(userFavorites)) {
        const favoriteKey = `favorites:user:${userId}`;
        if (favoriteIds.length > 0) {
          pipeline.sadd(favoriteKey, ...favoriteIds);
          migratedCount += favoriteIds.length;
        }
      }
      
      await pipeline.exec();
      
      console.log(`[FavoritesRedisManager] Migration completed: ${migratedCount} favorites migrated for ${Object.keys(userFavorites).length} users`);
      return { success: true, migrated: migratedCount };
      
    } catch (error) {
      console.error('[FavoritesRedisManager] Migration failed:', error);
      return { success: false, migrated: 0 };
    }
  }

  /**
   * 批量设置收藏状态（优化版）
   */
  static async batchSetFavorites(generationIds: string[], userId: string, favorite: boolean): Promise<{
    success: boolean;
    updated: number;
  }> {
    if (generationIds.length === 0) {
      return { success: true, updated: 0 };
    }

    const favoriteKey = `favorites:user:${userId}`;
    
    try {
      // 验证所有generation都属于该用户
      const validGenerations = await db.generation.findMany({
        where: {
          id: { in: generationIds },
          userId: userId,
          deleted: false
        },
        select: { id: true }
      });
      
      const validIds = validGenerations.map(g => g.id);
      if (validIds.length === 0) {
        return { success: true, updated: 0 };
      }

      // Redis批量操作
      const pipeline = redis.pipeline();
      if (favorite) {
        pipeline.sadd(favoriteKey, ...validIds);
      } else {
        for (const id of validIds) {
          pipeline.srem(favoriteKey, id);
        }
      }
      await pipeline.exec();

      // 异步数据库同步
      this.asyncBatchSyncFavoriteToDatabase(validIds, favorite);
      
      console.log(`[FavoritesRedisManager] Batch ${favorite ? 'favorited' : 'unfavorited'} ${validIds.length} generations for user ${userId}`);
      
      return { success: true, updated: validIds.length };
      
    } catch (error) {
      console.error('[FavoritesRedisManager] Batch set favorites failed:', error);
      return { success: false, updated: 0 };
    }
  }

  /**
   * 异步批量同步到数据库
   */
  private static asyncBatchSyncFavoriteToDatabase(generationIds: string[], favorite: boolean) {
    setImmediate(async () => {
      try {
        await db.generation.updateMany({
          where: { id: { in: generationIds } },
          data: { favorite }
        });
        
        console.log(`[FavoritesRedisManager] Background batch DB sync completed: ${generationIds.length} generations -> ${favorite ? 'favorited' : 'unfavorited'}`);
      } catch (error) {
        console.error('[FavoritesRedisManager] Background batch DB sync failed:', error);
      }
    });
  }
}