import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { LikesRedisManager } from "./likes-redis-manager";

// 简化的类型定义，避免与API包的类型冲突
type GenerationWhereCondition = {
  deleted: false;
  userId?: string;
  mediaType?: 'video' | 'image';
  publishStatus?: 'reviewing' | 'published' | 'rejected';
  favorite?: boolean;
  job?: {
    type?: 'video' | 'image';
  };
};

type QueryOptions = {
  page: number;
  pageSize: number;
  orderBy?: {
    createdAt: 'desc' | 'asc';
  };
};

// API 响应类型定义
type ApiResponse<T> = {
  success: true;
  data: T;
} | {
  success: false;
  error: string;
};

type PaginationInfo = {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
};

type UserSummary = {
  id: string;
  name: string | null;
  image: string | null;
};

type GetGenerationsRequest = {
  userId?: string;
  generationType?: 'video' | 'image';
  pageSize: number;
  page: number;
  publishStatus?: 'reviewing' | 'published' | 'rejected';
  mediaType?: 'video' | 'image';
  favorite?: boolean;
};

type GetGenerationsData = {
  generations: any[];
  jobs: any[];
  users: UserSummary[];
  pagination: PaginationInfo;
};

type LikeData = {
  generationId: string;
  likeCount: number;
};

/**
 * GenerationsManager - 专门处理Generation相关的数据库操作
 * 统一管理所有Generation表的CRUD操作
 */
export class GenerationsManager {
  // =====================================
  // 核心查询方法
  // =====================================
  
  /**
   * 获取Generation列表（带分页和过滤，包含用户点赞状态）
   */
  static async getGenerations(
    whereCondition: GenerationWhereCondition,
    options: QueryOptions,
    currentUserId?: string
  ): Promise<{
    generations: any[];
    total: number;
  }> {
    try {
      const [generations, total] = await Promise.all([
        db.generation.findMany({
          where: whereCondition,
          include: {
            job: true,
            user: {
              select: { 
                id: true, 
                name: true, 
                image: true 
              }
            },
            likes: currentUserId ? {
              where: { userId: currentUserId },
              select: { id: true }
            } : false
          },
          orderBy: { 
            createdAt: 'desc' 
          },
          skip: (options.page - 1) * options.pageSize,
          take: options.pageSize,
        }),
        db.generation.count({ 
          where: whereCondition 
        })
      ]);

      // 添加isLiked字段
      const generationsWithLikeStatus = generations.map(gen => ({
        ...gen,
        isLiked: currentUserId ? gen.likes.length > 0 : false,
        likes: undefined // 删除likes字段，只保留isLiked
      }));

      return { 
        generations: generationsWithLikeStatus, 
        total 
      };
    } catch (error) {
      logger.error('[GenerationsManager] Error getting generations:', {
        whereCondition,
        options,
        currentUserId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 根据ID获取单个Generation（包含用户点赞状态）
   */
  static async getGenerationById(
    generationId: string, 
    currentUserId?: string
  ): Promise<any | null> {
    try {
      const generation = await db.generation.findUnique({
        where: { id: generationId },
        include: {
          job: true,
          user: {
            select: { 
              id: true, 
              name: true, 
              image: true 
            }
          },
          likes: currentUserId ? {
            where: { userId: currentUserId },
            select: { id: true }
          } : false
        }
      });

      if (!generation) {
        return null;
      }

      // 添加isLiked字段
      return {
        ...generation,
        isLiked: currentUserId ? generation.likes.length > 0 : false,
        likes: undefined // 删除likes字段，只保留isLiked
      };
    } catch (error) {
      logger.error('[GenerationsManager] Error getting generation by ID:', {
        generationId,
        currentUserId,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * 检查用户是否有权限访问Generation
   */
  static canUserAccessGeneration(user: any, generation: any): boolean {
    // 管理员可以访问所有内容
    if (user.role?.includes('admin')) {
      return true;
    }
    
    // 用户只能访问自己的内容
    if (generation.userId === user.id) {
      return true;
    }
    
    // 检查内容是否公开发布
    if (generation.publishStatus === 'published' && generation.canRead) {
      return true;
    }
    
    return false;
  }

  // =====================================
  // 点赞功能相关方法
  // =====================================

  /**
   * 切换用户对Generation的点赞状态（优化版本）
   * 如果用户已点赞则取消点赞，如果未点赞则添加点赞
   */
  static async toggleLikeGeneration(
    generationId: string, 
    userId: string
  ): Promise<{
    id: string;
    starNum: number;
    isLiked: boolean;
  } | null> {
    try {
      // 使用单个事务处理所有操作，减少数据库往返
      const result = await db.$transaction(async (tx) => {
        // 1. 同时检查generation和用户点赞状态
        const [generation, existingLike] = await Promise.all([
          tx.generation.findUnique({
            where: { id: generationId },
            select: {
              id: true,
              starNum: true,
              deleted: true,
            }
          }),
          tx.generationLike.findUnique({
            where: {
              userId_generationId: {
                userId,
                generationId
              }
            },
            select: { id: true }
          })
        ]);

        // 2. 验证generation存在且未删除
        if (!generation || generation.deleted) {
          return null;
        }

        if (existingLike) {
          // 已点赞，执行取消点赞 - 并行执行删除和更新
          const [, updatedGeneration] = await Promise.all([
            tx.generationLike.delete({
              where: { id: existingLike.id }
            }),
            tx.generation.update({
              where: { id: generationId },
              data: {
                starNum: Math.max(0, generation.starNum - 1) // 防止负数
              },
              select: {
                id: true,
                starNum: true,
              }
            })
          ]);

          return {
            ...updatedGeneration,
            isLiked: false
          };
        } else {
          // 未点赞，执行点赞 - 并行执行创建和更新
          const [, updatedGeneration] = await Promise.all([
            tx.generationLike.create({
              data: {
                userId,
                generationId
              }
            }),
            tx.generation.update({
              where: { id: generationId },
              data: {
                starNum: generation.starNum + 1
              },
              select: {
                id: true,
                starNum: true,
              }
            })
          ]);

          return {
            ...updatedGeneration,
            isLiked: true
          };
        }
      });

      if (!result) {
        return null;
      }

      logger.info('[GenerationsManager] Like toggled for generation:', {
        generationId,
        userId,
        newStarNum: result.starNum,
        isLiked: result.isLiked
      });

      return result;
    } catch (error) {
      logger.error('[GenerationsManager] Error toggling like:', {
        generationId,
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 批量获取用户对多个Generation的点赞状态
   */
  static async getUserLikeStates(
    generationIds: string[],
    userId: string
  ): Promise<Record<string, boolean>> {
    try {
      const likes = await db.generationLike.findMany({
        where: {
          userId,
          generationId: { in: generationIds }
        },
        select: {
          generationId: true
        }
      });

      const likeStates: Record<string, boolean> = {};
      for (const generationId of generationIds) {
        likeStates[generationId] = likes.some(like => like.generationId === generationId);
      }

      return likeStates;
    } catch (error) {
      logger.error('[GenerationsManager] Error getting user like states:', {
        generationIds,
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
      return {};
    }
  }

  /**
   * 超快速点赞切换（极致优化版本）- 使用原生SQL和upsert
   * 适用于高并发场景
   */
  static async toggleLikeGenerationFast(
    generationId: string, 
    userId: string
  ): Promise<{
    id: string;
    starNum: number;
    isLiked: boolean;
  } | null> {
    try {
      // 使用原生SQL进行操作，性能最优
      const result = await db.$transaction(async (tx) => {
        // 1. 检查generation是否存在（简化查询）
        const generation = await tx.generation.findUnique({
          where: { id: generationId, deleted: false },
          select: { id: true, starNum: true }
        });

        if (!generation) {
          return null;
        }

        // 2. 尝试删除点赞记录，如果删除成功说明之前已点赞
        const deleteResult = await tx.generationLike.deleteMany({
          where: {
            userId,
            generationId
          }
        });

        const wasLiked = deleteResult.count > 0;

        if (wasLiked) {
          // 之前已点赞，现在取消点赞
          const updatedGeneration = await tx.generation.update({
            where: { id: generationId },
            data: { starNum: Math.max(0, generation.starNum - 1) },
            select: { id: true, starNum: true }
          });

          return {
            ...updatedGeneration,
            isLiked: false
          };
        } else {
          // 之前未点赞，现在添加点赞
          await tx.generationLike.create({
            data: { userId, generationId }
          });

          const updatedGeneration = await tx.generation.update({
            where: { id: generationId },
            data: { starNum: generation.starNum + 1 },
            select: { id: true, starNum: true }
          });

          return {
            ...updatedGeneration,
            isLiked: true
          };
        }
      });

      return result;
    } catch (error) {
      logger.error('[GenerationsManager] Error in fast toggle like:', {
        generationId,
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Redis超快速点赞切换 - 20-50ms响应时间
   * 直接使用Redis，跳过数据库预检查以获得最佳性能
   */
  static async toggleLikeGenerationRedis(
    generationId: string, 
    userId: string
  ): Promise<{
    id: string;
    starNum: number;
    isLiked: boolean;
  } | null> {
    try {
      // 直接使用Redis进行超快速点赞操作，跳过数据库预检查
      const result = await LikesRedisManager.toggleLikeFast(generationId, userId);

      if (!result.success) {
        return null;
      }

      logger.info('[GenerationsManager] ⚡ Redis fast like toggle:', {
        generationId,
        userId,
        newCount: result.likeCount,
        isLiked: result.isLiked
      });

      return {
        id: generationId,
        starNum: result.likeCount,
        isLiked: result.isLiked
      };

    } catch (error) {
      logger.error('[GenerationsManager] Error in Redis fast toggle like:', {
        generationId,
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }


  // =====================================
  // 数据处理辅助方法
  // =====================================

  /**
   * 提取唯一的Job数据
   */
  static extractUniqueJobs(generations: any[]): any[] {
    const jobMap = new Map();
    
    for (const generation of generations) {
      if (generation.job && !jobMap.has(generation.job.id)) {
        // 转换日期为ISO字符串
        const job = {
          ...generation.job,
          createdAt: generation.job.createdAt.toISOString(),
          updatedAt: generation.job.updatedAt.toISOString(),
        };
        jobMap.set(job.id, job);
      }
    }
    
    return Array.from(jobMap.values());
  }

  /**
   * 提取唯一的用户数据
   */
  static extractUniqueUsers(generations: any[]): UserSummary[] {
    const userMap = new Map();
    
    for (const generation of generations) {
      if (generation.user && !userMap.has(generation.user.id)) {
        userMap.set(generation.user.id, generation.user);
      }
    }
    
    return Array.from(userMap.values());
  }

  /**
   * 清理Generation数据（移除重复的关联数据）
   */
  static cleanGenerationsData(generations: any[]): any[] {
    return generations.map(generation => {
      const { job, user, ...cleanGeneration } = generation;
      
      // 转换日期为ISO字符串
      return {
        ...cleanGeneration,
        createdAt: cleanGeneration.createdAt.toISOString(),
        updatedAt: cleanGeneration.updatedAt.toISOString(),
        publishDate: cleanGeneration.publishDate?.toISOString() || null,
      };
    });
  }

  /**
   * 构建分页信息
   */
  static buildPaginationInfo(
    page: number,
    pageSize: number, 
    total: number
  ): PaginationInfo {
    const totalPages = Math.ceil(total / pageSize);
    
    return {
      page,
      pageSize,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  // =====================================
  // 下载功能相关的数据库操作方法  
  // =====================================

  /**
   * 查找用户的Generation（用于下载权限检查）
   */
  static async findUserGeneration(
    generationId: string,
    userId: string
  ): Promise<{
    id: string;
    userId: string;
    videoUrl: string | null;
    mediaUrl: string | null;
    downloadNum: number;
  } | null> {
    try {
      const generation = await db.generation.findFirst({
        where: {
          id: generationId,
          userId: userId, // 确保用户只能访问自己的内容
          deleted: false, // 确保不返回已删除的记录
        },
        select: {
          id: true,
          userId: true,
          videoUrl: true,
          mediaUrl: true,
          downloadNum: true,
        },
      });

      return generation;
    } catch (error) {
      logger.error('[GenerationsManager] Error finding user generation:', {
        generationId,
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * 增加Generation的下载次数
   */
  static async incrementDownloadCount(generationId: string): Promise<boolean> {
    try {
      await db.generation.update({
        where: { id: generationId },
        data: {
          downloadNum: {
            increment: 1
          }
        }
      });

      logger.info('[GenerationsManager] Download count incremented:', {
        generationId
      });

      return true;
    } catch (error) {
      logger.error('[GenerationsManager] Error incrementing download count:', {
        generationId,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * 获取Generation的下载统计信息
   */
  static async getDownloadStats(generationId: string): Promise<{
    downloadNum: number;
  } | null> {
    try {
      const generation = await db.generation.findUnique({
        where: { id: generationId },
        select: {
          downloadNum: true,
        },
      });

      return generation;
    } catch (error) {
      logger.error('[GenerationsManager] Error getting download stats:', {
        generationId,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * 批量获取多个Generation的基本信息（用于列表页面）
   */
  static async findGenerationsByIds(
    generationIds: string[],
    userId?: string
  ): Promise<Array<{
    id: string;
    userId: string;
    videoUrl: string | null;
    mediaUrl: string | null;
    downloadNum: number;
  }>> {
    try {
      const where: any = {
        id: { in: generationIds },
        deleted: false  // 确保不返回已删除的记录
      };

      // 如果指定了userId，则只返回该用户的内容
      if (userId) {
        where.userId = userId;
      }

      const generations = await db.generation.findMany({
        where,
        select: {
          id: true,
          userId: true,
          videoUrl: true,
          mediaUrl: true,
          downloadNum: true,
        },
      });

      return generations;
    } catch (error) {
      logger.error('[GenerationsManager] Error finding generations by IDs:', {
        generationIds,
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
      return [];
    }
  }

  /**
   * 检查Generation是否存在且属于用户
   */
  static async checkGenerationOwnership(
    generationId: string,
    userId: string
  ): Promise<boolean> {
    try {
      const generation = await db.generation.findFirst({
        where: {
          id: generationId,
          userId: userId,
          deleted: false,  // 确保不检查已删除的记录
        },
        select: {
          id: true,
        },
      });

      return !!generation;
    } catch (error) {
      logger.error('[GenerationsManager] Error checking generation ownership:', {
        generationId,
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * 获取用户的Generation下载历史统计
   */
  static async getUserDownloadStats(userId: string): Promise<{
    totalDownloads: number;
    totalGenerations: number;
  }> {
    try {
      const stats = await db.generation.aggregate({
        where: { 
          userId,
          deleted: false  // 确保只统计未删除的记录
        },
        _sum: {
          downloadNum: true,
        },
        _count: {
          id: true,
        },
      });

      return {
        totalDownloads: stats._sum.downloadNum || 0,
        totalGenerations: stats._count.id || 0,
      };
    } catch (error) {
      logger.error('[GenerationsManager] Error getting user download stats:', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
      return {
        totalDownloads: 0,
        totalGenerations: 0,
      };
    }
  }

  /**
   * 事务操作：同时更新下载次数并记录日志
   * 为将来扩展审计功能预留接口
   */
  static async downloadWithAudit(
    generationId: string,
    userId: string,
    downloadType: 'watermark' | 'no-watermark',
    metadata?: {
      ipAddress?: string;
      userAgent?: string;
    }
  ): Promise<boolean> {
    try {
      await db.$transaction(async (tx) => {
        // 更新下载次数
        await tx.generation.update({
          where: { id: generationId },
          data: {
            downloadNum: {
              increment: 1
            }
          }
        });

        // 这里将来可以添加审计记录
        // await tx.downloadRecord.create({ ... });
      });

      logger.info('[GenerationsManager] Download completed with audit:', {
        generationId,
        userId,
        downloadType,
        metadata
      });

      return true;
    } catch (error) {
      logger.error('[GenerationsManager] Error in download with audit:', {
        generationId,
        userId,
        downloadType,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * 查找用户最近下载的Generation列表
   */
  static async getUserRecentDownloads(
    userId: string,
    limit = 10
  ): Promise<Array<{
    id: string;
    videoUrl: string | null;
    mediaUrl: string | null;
    downloadNum: number;
    createdAt: Date;
  }>> {
    try {
      const generations = await db.generation.findMany({
        where: {
          userId,
          downloadNum: { gt: 0 }, // 只返回有下载记录的
          deleted: false, // 确保不返回已删除的记录
        },
        select: {
          id: true,
          videoUrl: true,
          mediaUrl: true,
          downloadNum: true,
          createdAt: true,
        },
        orderBy: {
          updatedAt: 'desc', // 按最后更新时间排序，相当于最近下载的
        },
        take: limit,
      });

      return generations;
    } catch (error) {
      logger.error('[GenerationsManager] Error getting user recent downloads:', {
        userId,
        limit,
        error: error instanceof Error ? error.message : String(error)
      });
      return [];
    }
  }

  /**
   * 获取热门下载的Generation列表（按下载次数排序）
   */
  static async getPopularDownloads(limit = 20): Promise<Array<{
    id: string;
    userId: string;
    videoUrl: string | null;
    mediaUrl: string | null;
    downloadNum: number;
  }>> {
    try {
      const generations = await db.generation.findMany({
        where: {
          downloadNum: { gt: 0 },
          deleted: false, // 确保不返回已删除的记录
        },
        select: {
          id: true,
          userId: true,
          videoUrl: true,
          mediaUrl: true,
          downloadNum: true,
        },
        orderBy: {
          downloadNum: 'desc',
        },
        take: limit,
      });

      return generations;
    } catch (error) {
      logger.error('[GenerationsManager] Error getting popular downloads:', {
        limit,
        error: error instanceof Error ? error.message : String(error)
      });
      return [];
    }
  }

  // =====================================
  // 下载功能方法
  // =====================================

  /**
   * 带水印下载
   */
  static async downloadWithWatermark(
    userId: string, 
    generationId: string
  ): Promise<{
    success: true;
    downloadUrl: string;
  } | {
    success: false;
    error: string;
  }> {
    try {
      // 1. 查找用户的Generation并检查权限
      const generation = await this.findUserGeneration(generationId, userId);
      
      if (!generation) {
        return {
          success: false,
          error: "Generation not found"
        };
      }

      if (!generation.videoUrl) {
        return {
          success: false,
          error: "Video URL not available"
        };
      }

      // 2. 增加下载次数
      await this.incrementDownloadCount(generationId);

      return {
        success: true,
        downloadUrl: generation.videoUrl
      };
    } catch (error) {
      logger.error('[GenerationsManager] Error in downloadWithWatermark:', {
        userId,
        generationId,
        error: error instanceof Error ? error.message : String(error)
      });
      
      return {
        success: false,
        error: "Internal server error"
      };
    }
  }

  /**
   * 无水印下载
   */
  static async downloadWithoutWatermark(
    userId: string, 
    generationId: string
  ): Promise<{
    success: true;
    downloadUrl: string;
  } | {
    success: false;
    error: string;
  }> {
    try {
      // 1. 查找用户的Generation并检查权限
      const generation = await this.findUserGeneration(generationId, userId);
      
      if (!generation) {
        return {
          success: false,
          error: "Generation not found"
        };
      }

      if (!generation.mediaUrl) {
        return {
          success: false,
          error: "Media URL not available"
        };
      }

      // 2. 增加下载次数
      await this.incrementDownloadCount(generationId);

      return {
        success: true,
        downloadUrl: generation.mediaUrl
      };
    } catch (error) {
      logger.error('[GenerationsManager] Error in downloadWithoutWatermark:', {
        userId,
        generationId,
        error: error instanceof Error ? error.message : String(error)
      });
      
      return {
        success: false,
        error: "Internal server error"
      };
    }
  }

  /**
   * 批量生成下载链接
   */
  static async batchGenerateDownloadUrls(
    generationIds: string[],
    userId: string,
    isAdmin: boolean = false
  ): Promise<{
    downloadUrls?: string[];
    error?: string;
  }> {
    try {
      // 1. 参数验证
      if (!generationIds || generationIds.length === 0) {
        return { error: 'No generation IDs provided' };
      }

      if (generationIds.length > 50) {
        return { error: 'Maximum 50 generations allowed per request' };
      }

      logger.info('[GenerationsManager] Starting batch download URL generation:', {
        generationIds: generationIds.length,
        userId,
        isAdmin
      });

      // 2. 查询有效的generations
      const whereCondition: any = {
        id: { in: generationIds },
        deleted: false,
        status: 'succeeded' // 只处理成功生成的文件
      };

      // 权限检查 - 非管理员只能访问自己的文件
      if (!isAdmin) {
        whereCondition.userId = userId;
      }

      const generations = await db.generation.findMany({
        where: whereCondition,
        select: {
          id: true,
          userId: true,
          mediaUrl: true,
          videoUrl: true,
          status: true,
          downloadNum: true
        }
      });

      // 3. 验证所有文件都存在且可访问
      if (generations.length !== generationIds.length) {
        const foundIds = generations.map(g => g.id);
        const missingIds = generationIds.filter(id => !foundIds.includes(id));
        
        logger.warn('[GenerationsManager] Some generations not found or not accessible:', {
          requestedCount: generationIds.length,
          foundCount: generations.length,
          missingIds,
          userId,
          isAdmin
        });

        return { 
          error: `Some generations not found or not ready for download: ${missingIds.join(', ')}` 
        };
      }

      // 4. 生成下载URL - 保持与输入ID的顺序一致
      const downloadUrls: string[] = [];
      
      for (const generationId of generationIds) {
        const generation = generations.find(g => g.id === generationId);
        
        if (!generation) {
          return { error: `Generation ${generationId} not found` };
        }

        // 优先使用 mediaUrl (无水印)，回退到 videoUrl (带水印)
        const downloadUrl = generation.mediaUrl || generation.videoUrl;
        
        if (!downloadUrl) {
          return { error: `No download URL available for generation ${generationId}` };
        }

        downloadUrls.push(downloadUrl);
      }

      // 5. 批量增加下载次数
      try {
        await db.generation.updateMany({
          where: { id: { in: generationIds } },
          data: {
            downloadNum: {
              increment: 1
            }
          }
        });
      } catch (updateError) {
        // 下载次数更新失败不应该阻止下载，只记录错误
        logger.warn('[GenerationsManager] Failed to update download counts:', {
          generationIds,
          error: updateError instanceof Error ? updateError.message : String(updateError)
        });
      }

      logger.info('[GenerationsManager] Batch download URLs generated successfully:', {
        generationIds: generationIds.length,
        urlsGenerated: downloadUrls.length,
        userId,
        isAdmin
      });

      return { downloadUrls };

    } catch (error) {
      logger.error('[GenerationsManager] Error in batchGenerateDownloadUrls:', {
        generationIds,
        userId,
        isAdmin,
        error: error instanceof Error ? error.message : String(error)
      });
      
      return { error: 'Internal server error' };
    }
  }

  // =====================================
  // 权限检查辅助方法
  // =====================================

  /**
   * 构建查询条件
   */
  static buildWhereCondition(
    params: GetGenerationsRequest, 
    user: any
  ): GenerationWhereCondition {
    const conditions: GenerationWhereCondition = {
      deleted: false, // 排除已删除的记录
    };

    // 用户ID过滤
    if (params.userId) {
      conditions.userId = params.userId;
    } else if (!user.role?.includes('admin')) {
      // 非管理员用户默认只能查看自己的
      conditions.userId = user.id;
    }

    // 媒体类型过滤
    if (params.mediaType) {
      conditions.mediaType = params.mediaType;
    }

    // 发布状态过滤
    if (params.publishStatus) {
      conditions.publishStatus = params.publishStatus;
    }

    // 收藏状态过滤
    if (params.favorite !== undefined) {
      conditions.favorite = params.favorite;
    }

    // 生成类型过滤（需要关联Job表）
    if (params.generationType) {
      conditions.job = {
        type: params.generationType
      };
    }

    return conditions;
  }

  /**
   * 权限检查
   */
  static checkPermissions(user: any, params: GetGenerationsRequest): void {
    // 如果指定了userId且不是当前用户
    if (params.userId && params.userId !== user.id) {
      // 检查是否为管理员
      if (!user.role?.includes('admin')) {
        throw new Error('Access denied');
      }
    }
    
    // 如果没有指定userId，普通用户默认只能查看自己的
    if (!params.userId && !user.role?.includes('admin')) {
      params.userId = user.id;
    }
  }

  // =====================================
  // 收藏功能相关方法
  // =====================================


  /**
   * 切换Generation的收藏状态（使用Redis缓存优化）
   */
  static async toggleFavoriteGeneration(generationId: string, userId: string): Promise<{
    id: string;
    favorite: boolean;
  } | null> {
    try {
      // 尝试使用Redis快速切换
      const { FavoritesRedisManager } = await import("./favorites-redis-manager");
      const result = await FavoritesRedisManager.toggleFavoriteFast(generationId, userId);
      
      if (!result) {
        return null;
      }

      logger.info('[GenerationsManager] Generation收藏状态切换成功（Redis优化）:', {
        generationId,
        userId,
        newFavoriteStatus: result.favorite
      });

      return {
        id: generationId,
        favorite: result.favorite
      };
    } catch (error) {
      logger.error('[GenerationsManager] Redis切换收藏状态失败，回退到数据库模式:', {
        generationId,
        userId,
        error: error instanceof Error ? error.message : String(error)
      });

      // 回退到原始数据库方法
      return this.toggleFavoriteGenerationFallback(generationId, userId);
    }
  }

  /**
   * 回退方法：直接使用数据库切换收藏状态
   */
  private static async toggleFavoriteGenerationFallback(generationId: string, userId: string): Promise<{
    id: string;
    favorite: boolean;
  } | null> {
    try {
      // 1. 检查generation是否存在且未删除，并验证用户权限
      const generation = await db.generation.findUnique({
        where: { id: generationId },
        select: {
          id: true,
          favorite: true,
          deleted: true,
          userId: true,
        }
      });

      if (!generation || generation.deleted) {
        return null;
      }

      // 2. 验证用户只能操作自己创建的generation
      if (generation.userId !== userId) {
        throw new Error('You can only toggle favorite status for your own generations');
      }

      // 3. 切换收藏状态
      const updatedGeneration = await db.generation.update({
        where: { id: generationId },
        data: {
          favorite: !generation.favorite,
        },
        select: {
          id: true,
          favorite: true,
        }
      });

      logger.info('[GenerationsManager] Generation收藏状态切换成功（回退模式）:', {
        generationId,
        userId,
        newFavoriteStatus: updatedGeneration.favorite
      });

      return updatedGeneration;
    } catch (error) {
      logger.error('[GenerationsManager] 切换Generation收藏状态失败:', {
        generationId,
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 批量更新Generation收藏状态（Redis优化版）
   */
  static async batchUpdateFavoriteGenerations(
    generationIds: string[],
    userId: string,
    favorite: boolean,
    isAdmin: boolean = false
  ): Promise<{
    updated?: string[];
    error?: string;
  }> {
    try {
      if (generationIds.length === 0) {
        return { error: 'No generation IDs provided' };
      }

      // 尝试使用Redis优化的批量操作
      try {
        const { FavoritesRedisManager } = await import("./favorites-redis-manager");
        const result = await FavoritesRedisManager.batchSetFavorites(generationIds, userId, favorite);
        
        if (result.success) {
          logger.info('[GenerationsManager] 批量更新收藏状态完成（Redis优化）:', {
            generationIds,
            userId,
            favorite,
            updatedCount: result.updated
          });
          
          // 返回成功更新的ID列表
          return { updated: generationIds.slice(0, result.updated) };
        }
      } catch (redisError) {
        logger.warn('[GenerationsManager] Redis批量操作失败，回退到数据库模式:', redisError);
      }

      // 回退到原始数据库批量操作
      return this.batchUpdateFavoriteGenerationsFallback(generationIds, userId, favorite, isAdmin);

    } catch (error) {
      logger.error('[GenerationsManager] 批量更新Generation收藏状态失败:', {
        generationIds,
        userId,
        favorite,
        isAdmin,
        error: error instanceof Error ? error.message : String(error)
      });
      
      return {
        error: error instanceof Error ? error.message : 'Internal server error'
      };
    }
  }

  /**
   * 回退方法：数据库批量操作（优化版，使用updateMany）
   */
  private static async batchUpdateFavoriteGenerationsFallback(
    generationIds: string[],
    userId: string,
    favorite: boolean,
    isAdmin: boolean = false
  ): Promise<{
    updated?: string[];
    error?: string;
  }> {
    try {
      // 1. 验证权限和存在性
      const whereCondition: any = { 
        id: { in: generationIds },
        deleted: false
      };
      
      if (!isAdmin) {
        whereCondition.userId = userId;
      }

      // 2. 查询有效的generations
      const validGenerations = await db.generation.findMany({
        where: whereCondition,
        select: { id: true }
      });

      if (validGenerations.length === 0) {
        return { error: 'No valid generations found or access denied' };
      }

      const validIds = validGenerations.map(g => g.id);

      // 3. 使用updateMany进行批量更新（性能优化）
      await db.generation.updateMany({
        where: { id: { in: validIds } },
        data: { favorite }
      });

      logger.info('[GenerationsManager] 批量更新收藏状态完成（回退模式）:', {
        requestedIds: generationIds.length,
        updatedCount: validIds.length,
        userId,
        favorite,
        isAdmin
      });

      return { updated: validIds };

    } catch (error) {
      logger.error('[GenerationsManager] 批量更新收藏状态失败:', {
        generationIds,
        userId,
        favorite,
        isAdmin,
        error: error instanceof Error ? error.message : String(error)
      });
      
      return { 
        error: error instanceof Error ? error.message : 'Internal server error' 
      };
    }
  }

  // =====================================
  // 删除功能相关方法
  // =====================================

  /**
   * 软删除Generation（标记为已删除）
   * 返回更直观的响应格式
   */
  static async softDeleteGeneration(
    generationId: string,
    userId: string,
    isAdmin: boolean = false
  ): Promise<{
    deleted?: string;
    failed?: Record<string, string>;
  }> {
    try {
      // 1. 检查Generation是否存在
      const generation = await db.generation.findUnique({
        where: { id: generationId },
        select: {
          id: true,
          userId: true,
          deleted: true,
          canDelete: true,
        }
      });

      if (!generation) {
        return {
          failed: {
            [generationId]: 'Generation not found'
          }
        };
      }

      // 2. 检查是否已被删除
      if (generation.deleted) {
        return {
          failed: {
            [generationId]: 'Generation already deleted'
          }
        };
      }

      // 3. 权限检查
      if (!isAdmin && generation.userId !== userId) {
        return {
          failed: {
            [generationId]: 'Access denied'
          }
        };
      }

      // 4. 检查是否允许删除
      if (!generation.canDelete) {
        return {
          failed: {
            [generationId]: 'Generation cannot be deleted'
          }
        };
      }

      // 5. 执行软删除
      const deletedGeneration = await db.generation.update({
        where: { id: generationId },
        data: {
          deleted: true,
          updatedAt: new Date(),
        },
        select: {
          id: true,
          userId: true,
          deleted: true,
        }
      });

      logger.info('[GenerationsManager] Generation soft deleted:', {
        generationId,
        userId,
        isAdmin,
        deletedAt: new Date()
      });

      return {
        deleted: deletedGeneration.id
      };

    } catch (error) {
      logger.error('[GenerationsManager] Error soft deleting generation:', {
        generationId,
        userId,
        isAdmin,
        error: error instanceof Error ? error.message : String(error)
      });
      
      return {
        failed: {
          [generationId]: 'Internal server error'
        }
      };
    }
  }

  /**
   * 批量软删除Generation - 事务版本
   * 全部成功或全部失败，保证原子性
   */
  static async batchSoftDeleteGenerations(
    generationIds: string[],
    userId: string,
    isAdmin: boolean = false
  ): Promise<{
    deleted?: string[];
    error?: string;
  }> {
    try {
      if (generationIds.length === 0) {
        return { error: 'No generation IDs provided' };
      }

      // 使用事务确保原子性
      const result = await db.$transaction(async (tx) => {
        // 1. 批量查询并验证
        const generations = await tx.generation.findMany({
          where: { id: { in: generationIds } },
          select: {
            id: true,
            userId: true,
            deleted: true,
            canDelete: true,
          }
        });

        // 2. 验证所有条件
        const foundIds = generations.map(g => g.id);
        const notFoundIds = generationIds.filter(id => !foundIds.includes(id));
        
        if (notFoundIds.length > 0) {
          throw new Error(`Generations not found: ${notFoundIds.join(', ')}`);
        }

        for (const gen of generations) {
          if (gen.deleted) {
            throw new Error(`Generation ${gen.id} already deleted`);
          }
          if (!isAdmin && gen.userId !== userId) {
            throw new Error(`Access denied for generation ${gen.id}`);
          }
          if (!gen.canDelete) {
            throw new Error(`Generation ${gen.id} cannot be deleted`);
          }
        }

        // 3. 全部验证通过，执行批量删除
        await tx.generation.updateMany({
          where: { id: { in: generationIds } },
          data: {
            deleted: true,
            updatedAt: new Date(),
          }
        });

        return generationIds;
      });

      logger.info('[GenerationsManager] Batch soft delete completed:', {
        generationIds,
        userId,
        isAdmin,
        deletedCount: result.length
      });

      return { deleted: result };

    } catch (error) {
      logger.error('[GenerationsManager] Error in batch soft delete:', {
        generationIds,
        userId,
        isAdmin,
        error: error instanceof Error ? error.message : String(error)
      });
      
      return { 
        error: error instanceof Error ? error.message : 'Internal server error' 
      };
    }
  }
}