import type { CreateJobData } from "../../types";

// 请求相关的类型定义（从API层导入）
interface ModelParam {
  modelCode: string;
  prompt?: string;
  image?: string;
  imageTail?: string;
  negativePrompt?: string;
  promptStrength?: number;
  duration: number;
  modeCode?: string;
  resolution?: string;
  aspectRatio: string;
  style?: string;
  motionRange?: string;
  seed?: number;
  cameraType?: string;
  cameraConfig?: string;
  cameraFixed?: boolean;
  video?: string;
  processType: any; // ProcessType
}

interface RequestOptions {
  enableMagicPrompt?: boolean;
  enableTranslatePrompt?: boolean;
  enableTranslateNegativePrompt?: boolean;
  protectionMode?: boolean;
  published?: boolean;
}

interface CreateJobRequest {
  featureCode: string;
  type: "video" | "image";
  numOutputs: number;
  modelParam: ModelParam;
  options?: RequestOptions;
}

interface ModelConfig {
  code: string;
  apiProviderCode?: string;
}

/**
 * Job数据映射器 - 负责将API请求数据转换为数据库实体数据
 */
export class JobDataMapper {
  /**
   * 创建Job数据 - 现在包含原VideoJobParam的所有字段
   */
  static createJobData(
    jobId: string,
    userId: string,
    request: CreateJobRequest,
    model: ModelConfig,
    totalCredit: number
  ): CreateJobData {
    return {
      id: jobId,
      userId,
      featureCode: request.featureCode,
      type: request.type as any,
      numOutputs: request.numOutputs,
      credit: totalCredit,
      status: "waiting" as any,
      apiProviderCode: model.apiProviderCode || "unknown",
      // 原VideoJobParam字段
      modelCode: model.code,
      prompt: request.modelParam.prompt,
      image: request.modelParam.image,
      imageTail: request.modelParam.imageTail,
      negativePrompt: request.modelParam.negativePrompt,
      promptStrength: request.modelParam.promptStrength,
      duration: request.modelParam.duration,
      modeCode: request.modelParam.modeCode,
      resolution: request.modelParam.resolution,
      aspectRatio: request.modelParam.aspectRatio,
      style: request.modelParam.style,
      motionRange: request.modelParam.motionRange,
      seed: request.modelParam.seed,
      cameraType: request.modelParam.cameraType,
      cameraConfig: request.modelParam.cameraConfig,
      cameraFixed: request.modelParam.cameraFixed,
      video: request.modelParam.video,
      processType: request.modelParam.processType,
      enableMagicPrompt: request.options?.enableMagicPrompt ?? false,
      enableTranslatePrompt: request.options?.enableTranslatePrompt ?? false,
      enableTranslateNegativePrompt: request.options?.enableTranslateNegativePrompt ?? false,
      protectionMode: request.options?.protectionMode ?? false,
      published: request.options?.published ?? false,
    };
  }

  /**
   * 创建完整的Job和参数数据（用于事务创建）
   */
  static createJobWithParamsData(
    jobId: string,
    userId: string,
    request: CreateJobRequest,
    model: ModelConfig,
    totalCredit: number
  ) {
    const jobData = this.createJobData(jobId, userId, request, model, totalCredit);
    
    return { jobData};
  }

  /**
   * 验证必需字段
   */
  static validateJobRequest(request: CreateJobRequest): void {
    if (!request.featureCode) {
      throw new Error("Feature code is required");
    }
    
    if (!request.modelParam.modelCode) {
      throw new Error("Model code is required");
    }
    
    if (request.type === "video") {
      if (!request.modelParam.duration) {
        throw new Error("Duration is required for video jobs");
      }
      if (!request.modelParam.aspectRatio) {
        throw new Error("Aspect ratio is required for video jobs");
      }
    }
    
    if (request.numOutputs < 1 || request.numOutputs > 10) {
      throw new Error("Number of outputs must be between 1 and 10");
    }
  }

  /**
   * 清理和标准化请求数据
   */
  static normalizeJobRequest(request: CreateJobRequest): CreateJobRequest {
    return {
      ...request,
      modelParam: {
        ...request.modelParam,
        // 清理空字符串
        prompt: request.modelParam.prompt?.trim() || undefined,
        negativePrompt: request.modelParam.negativePrompt?.trim() || undefined,
        image: request.modelParam.image?.trim() || undefined,
        // 确保数值字段的类型正确
        duration: Number(request.modelParam.duration),
        promptStrength: request.modelParam.promptStrength ? Number(request.modelParam.promptStrength) : undefined,
        seed: request.modelParam.seed ? Number(request.modelParam.seed) : undefined,
      },
      options: {
        enableMagicPrompt: Boolean(request.options?.enableMagicPrompt),
        enableTranslatePrompt: Boolean(request.options?.enableTranslatePrompt),
        enableTranslateNegativePrompt: Boolean(request.options?.enableTranslateNegativePrompt),
        protectionMode: Boolean(request.options?.protectionMode),
        published: Boolean(request.options?.published),
      }
    };
  }
}