import { db } from "@repo/database";
import { type Prisma } from "@repo/database/prisma/generated/client";
import { logger } from "@repo/logs";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import type {
  CreateJobData,
  UpdateJobData,
  CreateGenerationData,
  UpdateGenerationData,
  JobWithRelations,
  JobIncludeOptions,
  ProgressInfo,
  BatchOperationResult,
  CreditOperation,
  JobStats,
  JobQueryFilter,
  PaginationOptions,
  PaginatedJobResult,
  JobStatus,
  JobType,
} from "../../types";

dayjs.extend(utc);

/**
 * JobsManager - 统一管理所有Job相关的数据库操作
 */
export class JobsManager {
  /**
   * 创建Job
   */
  static async createJob(data: CreateJobData): Promise<JobWithRelations> {
    const now = dayjs().utc().toDate();
    
    return await db.job.create({
      data: {
        ...data,
        createdAt: now,
        updatedAt: now,
      },
      include: {
        generations: true,
      },
    }) as any;
  }

  /**
   * 更新Job
   */
  static async updateJob(
    jobId: string,
    data: UpdateJobData
  ): Promise<JobWithRelations> {
    const now = dayjs().utc().toDate();
    
    return await db.job.update({
      where: { id: jobId },
      data: {
        ...data,
        updatedAt: now,
      },
      include: {
        generations: true,
      },
    }) as any;
  }

  /**
   * 根据ID查找Job
   */
  static async findJobById(
    jobId: string,
    include?: JobIncludeOptions
  ): Promise<JobWithRelations | null> {
    return await db.job.findUnique({
      where: { id: jobId },
      include: include || {
        generations: {
          orderBy: { createdAt: "asc" },
          select: {
            id: true,
            mediaId: true,
            cover: true,
            thumbnail: true,
            videoUrl: true,
            mediaUrl: true,
            status: true,
            mediaType: true,
            duration: true,
            createdAt: true,
            updatedAt: true,
          },
        },
      },
    }) as any;
  }

  /**
   * 根据用户ID查找Jobs
   */
  static async findJobsByUserId(
    userId: string,
    include?: JobIncludeOptions
  ): Promise<JobWithRelations[]> {
    return await db.job.findMany({
      where: { userId },
      include: include || {
        generations: true,
      },
      orderBy: { createdAt: "desc" },
    }) as any;
  }

  /**
   * 查询Jobs（支持过滤和分页）
   */
  static async queryJobs(
    filter?: JobQueryFilter,
    pagination?: PaginationOptions,
    include?: JobIncludeOptions
  ): Promise<PaginatedJobResult> {
    const where: any = {};
    
    // 构建查询条件
    if (filter?.userId) {
      where.userId = filter.userId;
    }
    
    if (filter?.status) {
      where.status = Array.isArray(filter.status)
        ? { in: filter.status }
        : filter.status;
    }
    
    if (filter?.type) {
      where.type = Array.isArray(filter.type)
        ? { in: filter.type }
        : filter.type;
    }
    
    if (filter?.featureCode) {
      where.featureCode = Array.isArray(filter.featureCode)
        ? { in: filter.featureCode }
        : filter.featureCode;
    }
    
    if (filter?.apiProviderCode) {
      where.apiProviderCode = Array.isArray(filter.apiProviderCode)
        ? { in: filter.apiProviderCode }
        : filter.apiProviderCode;
    }
    
    if (filter?.createdAfter || filter?.createdBefore) {
      where.createdAt = {};
      if (filter.createdAfter) {
        where.createdAt.gte = filter.createdAfter;
      }
      if (filter.createdBefore) {
        where.createdAt.lte = filter.createdBefore;
      }
    }
    
    if (filter?.hasErrors !== undefined) {
      where.errorMessage = filter.hasErrors
        ? { not: null }
        : null;
    }

    // 分页设置
    const page = pagination?.page || 1;
    const limit = pagination?.limit || 20;
    const skip = (page - 1) * limit;

    // 排序设置
    const orderBy = pagination?.orderBy
      ? { [pagination.orderBy.field]: pagination.orderBy.direction } as any
      : { createdAt: "desc" } as any;

    // 执行查询
    const [jobs, total] = await Promise.all([
      db.job.findMany({
        where,
        include: include || {
          generations: true,
        },
        orderBy,
        skip,
        take: limit,
      }),
      db.job.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      jobs: jobs as any,
      pagination: {
        total,
        page,
        limit,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }

  /**
   * 删除Job
   */
  static async deleteJob(jobId: string): Promise<void> {
    await db.job.delete({
      where: { id: jobId },
    });
  }

  /**
   * 批量更新Job状态
   */
  static async batchUpdateJobStatus(
    jobIds: string[],
    status: JobStatus,
    errorMessage?: string
  ): Promise<BatchOperationResult> {
    const now = dayjs().utc().toDate();
    
    try {
      const result = await db.job.updateMany({
        where: { id: { in: jobIds } },
        data: {
          status,
          errorMessage,
          updatedAt: now,
        },
      });

      return {
        successful: result.count,
        failed: 0,
        errors: [],
      };
    } catch (error) {
      return {
        successful: 0,
        failed: jobIds.length,
        errors: jobIds.map(id => ({
          id,
          error: error instanceof Error ? error.message : String(error),
        })),
      };
    }
  }

  /**
   * 创建Generation
   */
  static async createGeneration(
    data: CreateGenerationData
  ): Promise<any> {
    const now = dayjs().utc().toDate();
    
    return await db.generation.create({
      data: {
        ...data,
        createdAt: now,
        updatedAt: now,
      },
    });
  }

  /**
   * 更新Generation
   */
  static async updateGeneration(
    generationId: string,
    data: UpdateGenerationData
  ): Promise<any> {
    const now = dayjs().utc().toDate();
    
    return await db.generation.update({
      where: { id: generationId },
      data: {
        ...data,
        updatedAt: now,
      },
    });
  }

  /**
   * 根据externalTaskId查找Generation
   */
  static async findGenerationByExternalTaskId(
    externalTaskId: string,
    includeJob = false
  ): Promise<any> {
    return await db.generation.findFirst({
      where: { externalTaskId },
      include: includeJob ? {
        job: {
          select: {
            id: true,
            userId: true,
            numOutputs: true,
            credit: true,
            createdAt: true,
            status: true,
          },
        },
      } : undefined,
    });
  }

  /**
   * 根据jobId查找所有Generations
   */
  static async findGenerationsByJobId(
    jobId: string,
    statusFilter?: JobStatus[]
  ): Promise<any[]> {
    const where: any = { jobId };
    
    if (statusFilter) {
      where.status = { in: statusFilter };
    }
    
    return await db.generation.findMany({
      where,
      orderBy: { createdAt: "asc" },
    });
  }

  /**
   * 计算Job进度
   */
  static async calculateJobProgress(jobId: string): Promise<ProgressInfo> {
    const generations = await db.generation.findMany({
      where: { jobId },
      select: { status: true },
    });

    const total = generations.length;
    const completed = generations.filter(g => g.status === "succeeded").length;
    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

    return {
      completed,
      total,
      percentage,
    };
  }

  /**
   * 处理积分操作
   */
  static async handleCreditOperation(
    operation: CreditOperation
  ): Promise<any> {
    const now = dayjs().utc().toDate();
    
    const updateData: any = {
      updatedAt: now,
    };

    if (operation.operation === "increment") {
      updateData.balance = { increment: operation.amount };
      updateData.used = { decrement: operation.amount };
    } else {
      updateData.balance = { decrement: operation.amount };
      updateData.used = { increment: operation.amount };
    }

    return await db.creditUsage.update({
      where: { userId: operation.userId },
      data: updateData,
      select: { balance: true, used: true },
    });
  }

  /**
   * 获取Job统计信息
   */
  static async getJobStats(
    userId?: string,
    dateRange?: { from: Date; to: Date }
  ): Promise<JobStats> {
    const where: any = {};
    
    if (userId) {
      where.userId = userId;
    }
    
    if (dateRange) {
      where.createdAt = {
        gte: dateRange.from,
        lte: dateRange.to,
      };
    }

    const jobs = await db.job.findMany({
      where,
      select: {
        status: true,
        type: true,
        featureCode: true,
        credit: true,
        timeCostSeconds: true,
      },
    });

    const total = jobs.length;
    
    // 按状态统计
    const byStatus = jobs.reduce((acc, job) => {
      acc[job.status] = (acc[job.status] || 0) + 1;
      return acc;
    }, {} as Record<JobStatus, number>);

    // 按类型统计
    const byType = jobs.reduce((acc, job) => {
      acc[job.type] = (acc[job.type] || 0) + 1;
      return acc;
    }, {} as Record<JobType, number>);

    // 按功能统计
    const byFeature = jobs.reduce((acc, job) => {
      acc[job.featureCode] = (acc[job.featureCode] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // 积分统计
    const totalCreditsUsed = jobs.reduce((sum, job) => sum + job.credit, 0);

    // 平均处理时间
    const jobsWithTime = jobs.filter(job => job.timeCostSeconds);
    const averageProcessingTime = jobsWithTime.length > 0
      ? jobsWithTime.reduce((sum, job) => sum + (job.timeCostSeconds || 0), 0) / jobsWithTime.length
      : undefined;

    return {
      total,
      byStatus,
      byType,
      byFeature,
      totalCreditsUsed,
      averageProcessingTime,
    };
  }

  /**
   * 执行事务操作
   */
  static async transaction<T>(
    callback: (tx: any) => Promise<T>
  ): Promise<T> {
    return await db.$transaction(callback);
  }

  /**
   * 检查用户是否有足够积分
   */
  static async checkUserCredits(
    userId: string,
    requiredCredits: number
  ): Promise<{ hasEnough: boolean; currentBalance: number }> {
    const creditUsage = await db.creditUsage.findFirst({
      where: { userId },
      select: { balance: true },
    });

    const currentBalance = creditUsage?.balance || 0;
    
    return {
      hasEnough: currentBalance >= requiredCredits,
      currentBalance,
    };
  }

  /**
   * 清理过期的失败任务
   */
  static async cleanupExpiredFailedJobs(
    olderThanDays = 7
  ): Promise<number> {
    const cutoffDate = dayjs().subtract(olderThanDays, "days").toDate();
    
    const result = await db.job.deleteMany({
      where: {
        status: "failed",
        createdAt: {
          lt: cutoffDate,
        },
      },
    });

    return result.count;
  }

  /**
   * 获取用户最近的Jobs
   */
  static async getUserRecentJobs(
    userId: string,
    limit = 10,
    include?: JobIncludeOptions
  ): Promise<JobWithRelations[]> {
    return await db.job.findMany({
      where: { userId },
      include: include || {
        generations: {
          select: {
            id: true,
            status: true,
            videoUrl: true,
            mediaUrl: true,
            createdAt: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
      take: limit,
    }) as any;
  }

  // ==================== 业务包装方法 ====================

  /**
   * 获取Job的完整详情（包含所有参数和生成结果）
   */
  static async getJobDetails(jobId: string): Promise<JobWithRelations | null> {
    return await this.findJobById(jobId, {
      generations: {
        orderBy: { createdAt: "asc" },
        select: {
          id: true,
          mediaId: true,
          cover: true,
          thumbnail: true,
          videoUrl: true,
          mediaUrl: true,
          status: true,
          mediaType: true,
          duration: true,
          createdAt: true,
          updatedAt: true,
        }
      }
    });
  }

  /**
   * 获取Job的基本信息（不包含详细的生成结果）
   */
  static async getJobSummary(jobId: string): Promise<JobWithRelations | null> {
    return await this.findJobById(jobId, {
      generations: {
        select: {
          id: true,
          status: true,
          createdAt: true,
        }
      }
    });
  }

  /**
   * 获取Job及其完成的生成结果
   */
  static async getJobWithCompletedGenerations(jobId: string): Promise<JobWithRelations | null> {
    return await db.job.findUnique({
      where: { id: jobId },
      include: {
        generations: {
          where: { status: "succeeded" },
          orderBy: { createdAt: "asc" },
          select: {
            id: true,
            mediaId: true,
            cover: true,
            thumbnail: true,
            videoUrl: true,
            mediaUrl: true,
            status: true,
            mediaType: true,
            duration: true,
            createdAt: true,
            updatedAt: true,
          }
        }
      }
    }) as any;
  }

  /**
   * 获取用户的Job列表（包含基本生成信息）
   */
  static async getUserJobsWithGenerations(
    userId: string, 
    limit = 20,
    offset = 0
  ): Promise<JobWithRelations[]> {
    return await db.job.findMany({
      where: { userId },
      include: {
        generations: {
          select: {
            id: true,
            status: true,
            videoUrl: true,
            mediaUrl: true,
            cover: true,
            thumbnail: true,
            createdAt: true,
          },
          orderBy: { createdAt: "asc" },
        }
      },
      orderBy: { createdAt: "desc" },
      skip: offset,
      take: limit,
    }) as any;
  }

  /**
   * 获取Job的运行状态摘要
   */
  static async getJobStatusSummary(jobId: string): Promise<{
    job: { id: string; status: any; numOutputs: number; createdAt: Date; };
    progress: { completed: number; total: number; percentage: number; };
    generations: Array<{ id: string; status: any; createdAt: Date; }>;
  } | null> {
    const job = await db.job.findUnique({
      where: { id: jobId },
      select: {
        id: true,
        status: true,
        numOutputs: true,
        createdAt: true,
        generations: {
          select: {
            id: true,
            status: true,
            createdAt: true,
          },
          orderBy: { createdAt: "asc" },
        }
      }
    });

    if (!job) return null;

    const completedCount = job.generations.filter(g => g.status === "succeeded").length;
    const progress = {
      completed: completedCount,
      total: job.numOutputs,
      percentage: Math.round((completedCount / job.numOutputs) * 100)
    };

    return {
      job: {
        id: job.id,
        status: job.status,
        numOutputs: job.numOutputs,
        createdAt: job.createdAt,
      },
      progress,
      generations: job.generations,
    };
  }

  /**
   * 获取用户的简要Job列表（用于仪表板显示）
   */
  static async getUserJobsDashboard(
    userId: string,
    limit = 10
  ): Promise<Array<{
    id: string;
    status: any;
    numOutputs: number;
    completedCount: number;
    createdAt: Date;
    modelCode?: string;
    prompt?: string;
    image?: string;
    latestGeneration?: {
      id: string;
      status: any;
      videoUrl?: string;
      cover?: string;
    };
  }>> {
    const jobs = await db.job.findMany({
      where: { userId },
      select: {
        id: true,
        status: true,
        numOutputs: true,
        createdAt: true,
        // videoJobParam字段现在直接在job表中
        modelCode: true,
        prompt: true,
        image: true,
        generations: {
          select: {
            id: true,
            status: true,
            videoUrl: true,
            cover: true,
            createdAt: true,
          },
          orderBy: { createdAt: "desc" },
          take: 1, // 只取最新的一个
        }
      },
      orderBy: { createdAt: "desc" },
      take: limit,
    });

    return jobs.map(job => {
      const completedCount = job.generations.filter(g => g.status === "succeeded").length;
      const latestGeneration = job.generations[0];

      return {
        id: job.id,
        status: job.status,
        numOutputs: job.numOutputs,
        completedCount,
        createdAt: job.createdAt,
        modelCode: job.modelCode,
        prompt: job.prompt || undefined,
        image: job.image || undefined,
        latestGeneration: latestGeneration ? {
          id: latestGeneration.id,
          status: latestGeneration.status,
          videoUrl: latestGeneration.videoUrl || undefined,
          cover: latestGeneration.cover || undefined,
        } : undefined,
      };
    });
  }

  /**
   * 获取Job的所有外部任务ID（用于webhook处理触发）
   */
  static async getJobExternalTaskIds(jobId: string): Promise<string[]> {
    const generations = await db.generation.findMany({
      where: { jobId },
      select: { externalTaskId: true }
    });
    
    return generations
      .map(g => g.externalTaskId)
      .filter(Boolean) as string[];
  }

  /**
   * 根据generation ID查找，包含user和job信息（用于webhook处理）
   */
  static async findGenerationWithUserAndJob(generationId: string): Promise<any> {
    return await db.generation.findUnique({
      where: {
        id: generationId
      },
      include: {
        user: {
          select: {
            id: true,
            name: true
          }
        },
        job: {
          select: {
            id: true,
            type: true
          }
        }
      }
    });
  }

  /**
   * 根据external task ID查找generation，包含user和job信息（用于webhook处理）
   */
  static async findGenerationByExternalTaskIdWithUserAndJob(externalTaskId: string): Promise<any> {
    return await db.generation.findFirst({
      where: {
        externalTaskId: externalTaskId
      },
      include: {
        user: {
          select: {
            id: true,
            name: true
          }
        },
        job: {
          select: {
            id: true,
            type: true
          }
        }
      }
    });
  }

  /**
   * 更新job和generation状态（用于webhook处理）
   */
  static async updateJobStatusWithGeneration(
    jobId: string, 
    generationId: string, 
    status: 'waiting' | 'processing' | 'succeeded' | 'failed',
    errorMessage?: string
  ): Promise<void> {
    const now = dayjs().utc().toDate();
    
    // 事务处理，确保job和generation状态一致性
    await db.$transaction(async (tx) => {
      // 更新generation状态
      await tx.generation.update({
        where: { id: generationId },
        data: {
          status: status,
          updatedAt: now
        }
      });

      // 更新job状态
      const jobUpdateData: any = {
        updatedAt: now
      };

      if (status === 'failed' && errorMessage) {
        jobUpdateData.status = 'failed';
        jobUpdateData.errorMessage = errorMessage;
      } else if (status === 'succeeded') {
        jobUpdateData.status = 'succeeded';
      }

      if (Object.keys(jobUpdateData).length > 1) { // 有除了updatedAt之外的字段需要更新
        await tx.job.update({
          where: { id: jobId },
          data: jobUpdateData
        });
      }
    });
  }

  /**
   * 更新generation的视频URL（用于webhook处理）
   */
  static async updateGenerationVideoUrl(
    generationId: string, 
    videoUrl: string, 
    mediaUrl: string
  ): Promise<void> {
    const now = dayjs().utc().toDate();
    
    await db.generation.update({
      where: { id: generationId },
      data: {
        videoUrl: videoUrl,
        mediaUrl: mediaUrl,
        updatedAt: now
      }
    });
  }

  /**
   * 事务更新Job和Generation的状态及视频URL
   * 用于webhook处理中视频上传成功后的原子更新
   */
  static async updateJobAndGenerationWithVideoUrl(
    jobId: string,
    generationId: string,
    status: JobStatus,
    videoUrl: string,
    mediaUrl: string,
    errorMessage?: string
  ): Promise<void> {
    const now = dayjs().utc().toDate();
    
    await db.$transaction(async (tx) => {
      // 更新Generation
      await tx.generation.update({
        where: { id: generationId },
        data: {
          status,
          videoUrl,
          mediaUrl,
          updatedAt: now
        }
      });

      // 更新Job状态
      const jobUpdateData: Prisma.JobUpdateInput = {
        status,
        updatedAt: now
      };

      if (errorMessage) {
        jobUpdateData.errorMessage = errorMessage;
      }

      // 如果成功，计算处理时间
      if (status === 'succeeded') {
        const job = await tx.job.findUnique({
          where: { id: jobId },
          select: { createdAt: true }
        });
        
        if (job) {
          const timeCostSeconds = Math.floor((now.getTime() - job.createdAt.getTime()) / 1000);
          jobUpdateData.timeCostSeconds = timeCostSeconds;
        }
      }

      await tx.job.update({
        where: { id: jobId },
        data: jobUpdateData
      });
    });

    logger.info('[JobsManager] Updated job and generation with video URL in transaction', {
      jobId,
      generationId,
      status,
      videoUrl
    });
  }

  /**
   * 预定义的Job查询配置
   * 提供常用的include选项组合，避免重复配置
   */
  static readonly QueryConfigs = {
    /**
     * 完整详情查询 - 包含所有参数和生成结果
     */
    FULL_DETAILS: {
      generations: {
        orderBy: { createdAt: "asc" },
        select: {
          id: true,
          mediaId: true,
          cover: true,
          thumbnail: true,
          videoUrl: true,
          mediaUrl: true,
          status: true,
          mediaType: true,
          duration: true,
          createdAt: true,
          updatedAt: true,
        }
      }
    } as JobIncludeOptions,

    /**
     * 基本信息查询 - 包含参数但不包含详细生成结果
     */
    BASIC_INFO: {
      generations: {
        select: {
          id: true,
          status: true,
          createdAt: true,
        }
      }
    } as JobIncludeOptions,

    /**
     * 已完成的生成结果查询
     */
    COMPLETED_GENERATIONS: {
      generations: {
        where: { status: "succeeded" },
        orderBy: { createdAt: "asc" },
        select: {
          id: true,
          mediaId: true,
          cover: true,
          thumbnail: true,
          videoUrl: true,
          mediaUrl: true,
          status: true,
          mediaType: true,
          duration: true,
          createdAt: true,
          updatedAt: true,
        }
      }
    } as JobIncludeOptions,

    /**
     * 列表显示查询 - 用于Job列表页面
     */
    LIST_VIEW: {
      generations: {
        select: {
          id: true,
          status: true,
          videoUrl: true,
          mediaUrl: true,
          cover: true,
          thumbnail: true,
          createdAt: true,
        },
        orderBy: { createdAt: "asc" },
      }
    } as JobIncludeOptions,

    /**
     * 仪表板查询 - 用于首页展示
     */
    DASHBOARD: {
      generations: {
        select: {
          id: true,
          status: true,
          videoUrl: true,
          cover: true,
          createdAt: true,
        },
        orderBy: { createdAt: "desc" },
        take: 1, // 只取最新的一个
      }
    } as JobIncludeOptions,

    /**
     * 状态摘要查询 - 只查询状态相关信息
     */
    STATUS_SUMMARY: {
      generations: {
        select: {
          id: true,
          status: true,
          createdAt: true,
        },
        orderBy: { createdAt: "asc" },
      }
    } as JobIncludeOptions,

    /**
     * 最近Job查询 - 用于用户最近活动
     */
    RECENT_JOBS: {
      generations: {
        select: {
          id: true,
          status: true,
          videoUrl: true,
          mediaUrl: true,
          createdAt: true,
        },
      },
    } as JobIncludeOptions,
  } as const;

  /**
   * 查询配置助手函数
   */
  static createCustomQuery = {
    /**
     * 创建自定义生成结果查询
     */
    withGenerations: (options: {
      statusFilter?: ("waiting" | "processing" | "succeeded" | "failed")[];
      limit?: number;
      includeMedia?: boolean;
    } = {}): JobIncludeOptions => ({
      generations: {
        where: options.statusFilter ? { status: { in: options.statusFilter } } : undefined,
        orderBy: { createdAt: "asc" },
        take: options.limit,
        select: {
          id: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          ...(options.includeMedia && {
            mediaId: true,
            cover: true,
            thumbnail: true,
            videoUrl: true,
            mediaUrl: true,
            mediaType: true,
            duration: true,
          }),
        }
      }
    }),

    /**
     * 创建分页查询配置
     */
    withPagination: (page: number, limit: number, includeGenerations = true): {
      skip: number;
      take: number;
      include: JobIncludeOptions;
    } => ({
      skip: (page - 1) * limit,
      take: limit,
      include: includeGenerations ? JobsManager.QueryConfigs.LIST_VIEW : {}
    }),
  };
}