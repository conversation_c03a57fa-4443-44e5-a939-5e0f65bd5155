import { redis } from '@repo/redis';
import { db } from '@repo/database';

export class LikesRedisManager {
  static async toggleLikeFast(generationId: string, userId: string): Promise<{
    success: boolean;
    likeCount: number;
    isLiked: boolean;
  }> {
    const startTime = Date.now();
    const likeKey = `likes:${generationId}`;
    
    console.log(`[LikesRedisManager] ⚡ Starting SINGLE-ROUNDTRIP like toggle for ${generationId}`);
    
    // 🚀 革命性优化：使用Lua脚本实现真正的单次网络往返
    const luaScript = `
      local likeKey = KEYS[1]
      local userId = ARGV[1]
      
      -- 检查用户是否已点赞
      local isLiked = redis.call('SISMEMBER', likeKey, userId)
      
      if isLiked == 1 then
        -- 取消点赞
        redis.call('SREM', likeKey, userId)
        local newCount = redis.call('SCARD', likeKey)
        return {0, newCount}
      else
        -- 添加点赞
        redis.call('SADD', likeKey, userId)
        local newCount = redis.call('SCARD', likeKey)
        return {1, newCount}
      end
    `;
    
    try {
      const scriptStartTime = Date.now();
      const result = await redis.eval(luaScript, 1, likeKey, userId) as [number, number];
      const scriptTime = Date.now() - scriptStartTime;
      
      const isLiked = result[0] === 1;
      const newCount = result[1];
      
      console.log(`[LikesRedisManager] ⚡ ${isLiked ? 'Added' : 'Removed'} like (script: ${scriptTime}ms)`);
      
      // 异步数据库同步，不阻塞响应
      this.asyncSyncLikeToDatabase(generationId, userId, isLiked, newCount);
      
      const totalTime = Date.now() - startTime;
      console.log(`[LikesRedisManager] ✅ SINGLE-ROUNDTRIP ${isLiked ? 'Like' : 'Unlike'} completed: ${newCount} likes (script: ${scriptTime}ms, total: ${totalTime}ms)`);
      
      return {
        success: true,
        likeCount: newCount,
        isLiked: isLiked
      };
      
    } catch (error) {
      console.error(`[LikesRedisManager] ❌ Lua script failed, falling back to multi-step:`, error);
      
      // 降级到原来的方法
      const checkStartTime = Date.now();
      const isCurrentlyLiked = await redis.sismember(likeKey, userId);
      const checkTime = Date.now() - checkStartTime;
      
      const operationStartTime = Date.now();
      const multi = redis.multi();
      
      if (isCurrentlyLiked) {
        console.log(`[LikesRedisManager] ➖ Fallback: Removing like (check: ${checkTime}ms)`);
        multi.srem(likeKey, userId);
        multi.scard(likeKey);
      } else {
        console.log(`[LikesRedisManager] ➕ Fallback: Adding like (check: ${checkTime}ms)`);
        multi.sadd(likeKey, userId);
        multi.scard(likeKey);
      }
      
      const results = await multi.exec();
      const operationTime = Date.now() - operationStartTime;
      
      const newCount = isCurrentlyLiked 
        ? Math.max(0, (results?.[1]?.[1] as number) || 0)
        : (results?.[1]?.[1] as number) || 1;
      
      this.asyncSyncLikeToDatabase(generationId, userId, !isCurrentlyLiked, newCount);
      
      const totalTime = Date.now() - startTime;
      console.log(`[LikesRedisManager] ✅ Fallback ${isCurrentlyLiked ? 'Unlike' : 'Like'} completed: ${newCount} likes (check: ${checkTime}ms, operation: ${operationTime}ms, total: ${totalTime}ms)`);
      
      return {
        success: true,
        likeCount: newCount,
        isLiked: !isCurrentlyLiked
      };
    }
  }

  static async batchGetLikeStatus(generationIds: string[], userId: string): Promise<Record<string, {
    isLiked: boolean;
    likeCount: number;
  }>> {
    if (generationIds.length === 0) return {};
    
    const pipeline = redis.pipeline();
    
    for (const genId of generationIds) {
      pipeline.sismember(`likes:${genId}`, userId);
      pipeline.scard(`likes:${genId}`);
    }
    
    const results = await pipeline.exec();
    const status: Record<string, { isLiked: boolean; likeCount: number }> = {};
    
    for (let i = 0; i < generationIds.length; i++) {
      const genId = generationIds[i];
      // ioredis返回 [error, result] 格式
      status[genId] = {
        isLiked: (results?.[i * 2]?.[1] as number) === 1,
        likeCount: (results?.[i * 2 + 1]?.[1] as number) || 0
      };
    }
    
    return status;
  }

  private static asyncSyncLikeToDatabase(generationId: string, userId: string, isLiked: boolean, newCount: number) {
    // 真正的异步执行，不阻塞主线程
    setImmediate(async () => {
      try {
        if (isLiked) {
          await Promise.all([
            db.generation.update({
              where: { id: generationId },
              data: { starNum: newCount }
            }),
            db.generationLike.upsert({
              where: {
                userId_generationId: {
                  userId,
                  generationId
                }
              },
              create: { userId, generationId },
              update: {}
            })
          ]);
        } else {
          await Promise.all([
            db.generation.update({
              where: { id: generationId },
              data: { starNum: newCount }
            }),
            db.generationLike.deleteMany({
              where: { userId, generationId }
            })
          ]);
        }
        
        console.log(`[LikesRedisManager] Background DB sync completed: ${generationId} -> ${isLiked ? 'liked' : 'unliked'} by ${userId}, count: ${newCount}`);
      } catch (error) {
        console.error('[LikesRedisManager] Background DB sync failed:', error);
      }
    });
  }

  static async migrateLikesToRedis(): Promise<{ success: boolean; migrated: number }> {
    try {
      const generations = await db.generation.findMany({
        where: { deleted: false },
        include: {
          likes: {
            select: { userId: true }
          }
        }
      });
      
      let migratedCount = 0;
      
      for (const gen of generations) {
        const likeKey = `likes:${gen.id}`;
        
        if (gen.likes.length > 0) {
          const userIds = gen.likes.map(like => like.userId);
          if (userIds.length > 0) {
            await redis.sadd(likeKey, ...userIds);
          }
          migratedCount++;
        }
      }
      
      console.log(`[LikesRedisManager] Migration completed: ${migratedCount} generations migrated`);
      return { success: true, migrated: migratedCount };
      
    } catch (error) {
      console.error('[LikesRedisManager] Migration failed:', error);
      return { success: false, migrated: 0 };
    }
  }
}