import { z } from "zod";
import { 
  JobS<PERSON>usSchema,
  JobTypeSchema,
  ProcessTypeSchema,
  PublishStatusSchema
} from "@repo/database";
import type { 
  JobStatusType as JobStatus,
  JobTypeType as JobType,
  ProcessTypeType as ProcessType,
  PublishStatusType as PublishStatus
} from "@repo/database";
// import type { Decimal } from "@repo/database";

// Job related base types
export type { JobStatus, JobType, ProcessType, PublishStatus };

// Create Job data interface - 包含原VideoJobParam的所有字段
export interface CreateJobData {
  id: string;
  userId: string;
  featureCode: string;
  type: JobType;
  credit: number;
  status: JobStatus;
  numOutputs: number;
  apiProviderCode: string;
  externalTaskId?: string;
  errorMessage?: string;
  timeCostSeconds?: number;
  apiProviderCost?: number;
  // 原VideoJobParam字段
  modelCode: string;
  prompt?: string;
  image?: string;
  imageTail?: string;
  negativePrompt?: string;
  promptStrength?: number;
  duration: number;
  modeCode?: string;
  resolution?: string;
  aspectRatio: string;
  style?: string;
  motionRange?: string;
  seed?: number;
  cameraType?: string;
  cameraConfig?: string;
  cameraFixed?: boolean;
  video?: string;
  generationTemplate?: string;
  templateId?: string;
  templateImage?: string;
  processType: ProcessType;
  published?: boolean;
  protectionMode?: boolean;
  enableMagicPrompt?: boolean;
  enableTranslatePrompt?: boolean;
  enableTranslateNegativePrompt?: boolean;
}

// Update Job data interface
export interface UpdateJobData {
  status?: JobStatus;
  errorMessage?: string;
  timeCostSeconds?: number;
  apiProviderCost?: number;
  externalTaskId?: string;
}

// Generation creation data interface
export interface CreateGenerationData {
  id: string;
  userId: string;
  jobId: string;
  mediaId: string;
  externalTaskId?: string;
  mediaType: "video" | "image";
  status: JobStatus;
  provider?: string;
  metadata?: Record<string, any>;
}

// Generation update data interface
export interface UpdateGenerationData {
  status?: JobStatus;
  externalTaskId?: string;
  videoUrl?: string;
  mediaUrl?: string;
  cover?: string;
  thumbnail?: string;
  duration?: number;
  videoRatio?: string;
  publishStatus?: PublishStatus;
  publishDate?: Date;
  protectionMode?: boolean;
  isLike?: boolean;
  favorite?: boolean;
  starNum?: number;
  shareNum?: number;
  playNum?: number;
  downloadNum?: number;
}

// Job include options for queries
export interface JobIncludeOptions {
  // videoJobParam 已合并到job表，不再需要
  // imageJobParam?: boolean;
  generations?: boolean | {
    where?: any;
    select?: {
      id?: boolean;
      mediaId?: boolean;
      cover?: boolean;
      thumbnail?: boolean;
      videoUrl?: boolean;
      mediaUrl?: boolean;
      status?: boolean;
      mediaType?: boolean;
      duration?: boolean;
      createdAt?: boolean;
      updatedAt?: boolean;
      [key: string]: boolean | undefined;
    };
    orderBy?: {
      createdAt?: "asc" | "desc";
      [key: string]: "asc" | "desc" | undefined;
    };
    take?: number;
  };
  user?: boolean;
}

// Job query result with relations
export interface JobWithRelations {
  id: string;
  userId: string;
  featureCode: string;
  type: JobType;
  credit: number;
  apiProviderCost?: number | null;
  status: JobStatus;
  numOutputs: number;
  timeCostSeconds?: number | null;
  externalTaskId?: string | null;
  apiProviderCode?: string | null;
  errorMessage?: string | null;
  createdAt: Date;
  updatedAt: Date;
  // 原VideoJobParam字段现在直接在Job中
  modelCode: string;
  prompt?: string | null;
  image?: string | null;
  imageTail?: string | null;
  negativePrompt?: string | null;
  promptStrength?: number | null;
  duration: number;
  modeCode?: string | null;
  resolution?: string | null;
  aspectRatio: string;
  style?: string | null;
  motionRange?: string | null;
  seed?: number | null;
  cameraType?: string | null;
  cameraConfig?: string | null;
  cameraFixed?: boolean | null;
  video?: string | null;
  generationTemplate?: string | null;
  templateId?: string | null;
  templateImage?: string | null;
  processType: ProcessType;
  published: boolean;
  protectionMode: boolean;
  enableMagicPrompt: boolean;
  enableTranslatePrompt: boolean;
  enableTranslateNegativePrompt: boolean;
  // 关联数据
  generations?: any[];
  user?: any;
}

// Progress information
export interface ProgressInfo {
  completed: number;
  total: number;
  percentage: number;
}

// Batch operation result
export interface BatchOperationResult {
  successful: number;
  failed: number;
  errors: Array<{ id: string; error: string }>;
}

// Credit operation interface
export interface CreditOperation {
  userId: string;
  amount: number;
  operation: "increment" | "decrement";
  reason?: string;
}

// Job statistics
export interface JobStats {
  total: number;
  byStatus: Record<JobStatus, number>;
  byType: Record<JobType, number>;
  byFeature: Record<string, number>;
  totalCreditsUsed: number;
  averageProcessingTime?: number;
}

// Webhook payload for processing
export interface WebhookPayload {
  taskId: string;
  status: "waiting" | "processing" | "succeeded" | "failed";
  progress?: number;
  resultUrl?: string;
  errorMessage?: string;
  estimatedTime?: number;
}

// Query filter options
export interface JobQueryFilter {
  userId?: string;
  status?: JobStatus | JobStatus[];
  type?: JobType | JobType[];
  featureCode?: string | string[];
  apiProviderCode?: string | string[];
  createdAfter?: Date;
  createdBefore?: Date;
  hasErrors?: boolean;
}

// Pagination options
export interface PaginationOptions {
  page?: number;
  limit?: number;
  orderBy?: {
    field: "createdAt" | "updatedAt" | "credit" | "timeCostSeconds";
    direction: "asc" | "desc";
  };
}

// Paginated query result
export interface PaginatedJobResult {
  jobs: JobWithRelations[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Zod schemas for validation
export const CreateJobDataSchema = z.object({
  id: z.string(),
  userId: z.string(),
  featureCode: z.string(),
  type: JobTypeSchema,
  credit: z.number().int().min(0),
  status: JobStatusSchema,
  numOutputs: z.number().int().min(1),
  apiProviderCode: z.string().optional(),
  externalTaskId: z.string().optional(),
  errorMessage: z.string().optional(),
  timeCostSeconds: z.number().int().min(0).optional(),
  apiProviderCost: z.number().min(0).optional(),
});

export const UpdateJobDataSchema = z.object({
  status: JobStatusSchema.optional(),
  errorMessage: z.string().optional(),
  timeCostSeconds: z.number().int().min(0).optional(),
  apiProviderCost: z.number().min(0).optional(),
  externalTaskId: z.string().optional(),
});

export const CreateGenerationDataSchema = z.object({
  id: z.string(),
  userId: z.string(),
  jobId: z.string(),
  mediaId: z.string(),
  externalTaskId: z.string().optional(),
  mediaType: z.enum(["video", "image"]),
  status: JobStatusSchema,
});

export const UpdateGenerationDataSchema = z.object({
  status: JobStatusSchema.optional(),
  externalTaskId: z.string().optional(),
  videoUrl: z.string().optional(),
  mediaUrl: z.string().optional(),
  cover: z.string().optional(),
  thumbnail: z.string().optional(),
  duration: z.number().int().min(0).optional(),
  videoRatio: z.string().optional(),
  publishStatus: PublishStatusSchema.optional(),
  publishDate: z.date().optional(),
  protectionMode: z.boolean().optional(),
  isLike: z.boolean().optional(),
  favorite: z.boolean().optional(),
  starNum: z.number().int().min(0).optional(),
  shareNum: z.number().int().min(0).optional(),
  playNum: z.number().int().min(0).optional(),
  downloadNum: z.number().int().min(0).optional(),
});

export const WebhookPayloadSchema = z.object({
  taskId: z.string(),
  status: z.enum(["waiting", "processing", "succeeded", "failed"]),
  progress: z.number().int().min(0).max(100).optional(),
  resultUrl: z.string().url().optional(),
  errorMessage: z.string().optional(),
  estimatedTime: z.number().int().min(0).optional(),
});

export const JobQueryFilterSchema = z.object({
  userId: z.string().optional(),
  status: z.union([JobStatusSchema, z.array(JobStatusSchema)]).optional(),
  type: z.union([JobTypeSchema, z.array(JobTypeSchema)]).optional(),
  featureCode: z.union([z.string(), z.array(z.string())]).optional(),
  apiProviderCode: z.union([z.string(), z.array(z.string())]).optional(),
  createdAfter: z.date().optional(),
  createdBefore: z.date().optional(),
  hasErrors: z.boolean().optional(),
});

export const PaginationOptionsSchema = z.object({
  page: z.number().int().min(1).optional(),
  limit: z.number().int().min(1).max(100).optional(),
  orderBy: z.object({
    field: z.enum(["createdAt", "updatedAt", "credit", "timeCostSeconds"]),
    direction: z.enum(["asc", "desc"]),
  }).optional(),
});