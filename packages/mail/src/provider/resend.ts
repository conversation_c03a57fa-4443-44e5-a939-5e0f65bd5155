import { config } from "@repo/config";
import { logger } from "@repo/logs";
import type { Send<PERSON>mail<PERSON><PERSON><PERSON> } from "../../types";

const { from } = config.mails;

export const send: SendEmailHandler = async ({ to, subject, html, text }) => {
	const emailContent = html || text?.replace(/\n/g, '<br>') || '';
	
	const response = await fetch("https://api.resend.com/emails", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			Authorization: `Bearer ${process.env.RESEND_API_KEY}`,
		},
		body: JSON.stringify({
			from,
			to,
			subject,
			html: emailContent,
		}),
	});

	if (!response.ok) {
		logger.error(await response.json());
		throw new Error("Could not send email");
	}
};

export const addToAudience = async (email: string) => {
	const audienceId = process.env.RESEND_AUDIENCE_ID;
	
	if (!audienceId) {
		logger.warn("RESEND_AUDIENCE_ID not configured, skipping audience subscription");
		return;
	}

	try {
		const response = await fetch(`https://api.resend.com/audiences/${audienceId}/contacts`, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				Authorization: `Bearer ${process.env.RESEND_API_KEY}`,
			},
			body: JSON.stringify({
				email,
				unsubscribed: false,
			}),
		});

		if (!response.ok) {
			const errorData = await response.json();
			logger.error("Failed to add contact to Resend audience:", errorData);
			throw new Error("Could not add email to audience");
		}

		const result = await response.json();
		logger.info(`Successfully added ${email} to Resend audience: ${audienceId}`);
		return result;
	} catch (error) {
		logger.error("Error adding email to Resend audience:", error);
		throw error;
	}
};
