import { Redis } from 'ioredis';

/**
 * Redis 连接配置
 * 支持 Upstash Redis 和本地 Redis
 */
export const redisConnection = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxRetriesPerRequest: null,
  // Upstash Redis 需要的配置
  ...(process.env.REDIS_HOST?.includes('upstash.io') && {
    tls: {},
    connectTimeout: 60000,
    commandTimeout: 60000,
    retryDelayOnClusterDown: 300,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: null
  })
};

/**
 * 创建 Redis 连接实例
 */
export function createRedisConnection(): Redis {
  // 优先使用 REDIS_URL（支持 Upstash）
  if (process.env.REDIS_URL) {
    return new Redis(process.env.REDIS_URL, {
      retryDelayOnFailover: 100,
      enableReadyCheck: false,
      maxRetriesPerRequest: null,
      // 如果是 Upstash，自动启用 TLS
      ...(process.env.REDIS_URL.includes('upstash.io') && {
        connectTimeout: 60000,
        commandTimeout: 60000,
        tls: {}
      })
    });
  }
  
  return new Redis(redisConnection);
}