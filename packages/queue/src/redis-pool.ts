import { Redis } from 'ioredis';
import { logger } from '@repo/logs';
import { redisConnection } from './redis-config';

export interface PoolConfig {
  poolSize: number;
  healthCheckInterval: number;
  reconnectAttempts: number;
  lazyConnect: boolean;
}

export interface PoolStats {
  totalConnections: number;
  activeConnections: number;
  healthyConnections: number;
  failedConnections: number;
  lastHealthCheck: Date;
}

/**
 * Redis连接池管理器
 */
export class RedisConnectionPool {
  private static instance: RedisConnectionPool;
  private connectionPool: Redis[] = [];
  private poolConfig: PoolConfig;
  private currentIndex = 0;
  private healthCheckTimer?: NodeJS.Timeout;
  private poolStats: PoolStats;
  
  private constructor() {
    this.poolConfig = this.loadPoolConfig();
    this.poolStats = {
      totalConnections: 0,
      activeConnections: 0,
      healthyConnections: 0,
      failedConnections: 0,
      lastHealthCheck: new Date()
    };
    
    this.initializePool();
    this.startHealthCheck();
  }
  
  static getInstance(): RedisConnectionPool {
    if (!this.instance) {
      this.instance = new RedisConnectionPool();
    }
    return this.instance;
  }

  /**
   * 加载连接池配置
   */
  private loadPoolConfig(): PoolConfig {
    return {
      poolSize: parseInt(process.env.REDIS_POOL_SIZE || '8'),
      healthCheckInterval: parseInt(process.env.REDIS_HEALTH_CHECK_INTERVAL || '30000'), // 30秒
      reconnectAttempts: parseInt(process.env.REDIS_RECONNECT_ATTEMPTS || '3'),
      lazyConnect: process.env.REDIS_LAZY_CONNECT === 'true'
    };
  }
  
  /**
   * 初始化连接池
   */
  private initializePool() {
    logger.info('[RedisPool] Initializing connection pool', {
      poolSize: this.poolConfig.poolSize,
      redisHost: process.env.REDIS_HOST || 'localhost',
      redisPort: process.env.REDIS_PORT || '6379'
    });

    for (let i = 0; i < this.poolConfig.poolSize; i++) {
      try {
        const redis = this.createConnection(i);
        this.connectionPool.push(redis);
        
        // 设置连接事件监听
        this.setupConnectionEvents(redis, i);
        
      } catch (error) {
        logger.error('[RedisPool] Failed to create connection', {
          connectionIndex: i,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    this.poolStats.totalConnections = this.connectionPool.length;
    this.updateActiveConnections();
    
    logger.info('[RedisPool] Connection pool initialized', {
      totalConnections: this.poolStats.totalConnections,
      activeConnections: this.poolStats.activeConnections
    });
  }

  /**
   * 创建单个Redis连接
   */
  private createConnection(index: number): Redis {
    const connectionConfig = {
      ...redisConnection,
      lazyConnect: this.poolConfig.lazyConnect,
      retryDelayOnFailover: 100,
      enableReadyCheck: false,
      maxRetriesPerRequest: null,
      // 为每个连接添加唯一标识
      connectionName: `pool-${index}-${process.pid}`
    };

    // 优先使用REDIS_URL
    if (process.env.REDIS_URL) {
      return new Redis(process.env.REDIS_URL, {
        ...connectionConfig,
        retryDelayOnFailover: 100,
        enableReadyCheck: false,
        maxRetriesPerRequest: null,
        ...(process.env.REDIS_URL.includes('upstash.io') && {
          connectTimeout: 60000,
          commandTimeout: 60000,
          tls: {}
        })
      });
    }
    
    return new Redis(connectionConfig);
  }

  /**
   * 设置连接事件监听
   */
  private setupConnectionEvents(redis: Redis, index: number) {
    redis.on('connect', () => {
      logger.debug('[RedisPool] Connection established', { connectionIndex: index });
      this.updateActiveConnections();
    });

    redis.on('ready', () => {
      logger.debug('[RedisPool] Connection ready', { connectionIndex: index });
      this.updateActiveConnections();
    });

    redis.on('error', (error) => {
      logger.error('[RedisPool] Connection error', {
        connectionIndex: index,
        error: error.message
      });
      this.updateActiveConnections();
    });

    redis.on('close', () => {
      logger.warn('[RedisPool] Connection closed', { connectionIndex: index });
      this.updateActiveConnections();
    });

    redis.on('reconnecting', () => {
      logger.info('[RedisPool] Connection reconnecting', { connectionIndex: index });
    });
  }

  /**
   * 获取连接（轮询方式）
   */
  getConnection(): Redis {
    if (this.connectionPool.length === 0) {
      throw new Error('No Redis connections available in pool');
    }

    // 轮询分配连接
    const connection = this.connectionPool[this.currentIndex];
    this.currentIndex = (this.currentIndex + 1) % this.connectionPool.length;
    
    // 如果连接不健康，尝试下一个
    if (connection.status !== 'ready' && connection.status !== 'connecting') {
      logger.warn('[RedisPool] Selected connection not ready, trying next', {
        connectionStatus: connection.status,
        connectionIndex: this.currentIndex
      });
      
      // 递归尝试下一个连接（最多尝试池大小次数）
      for (let attempts = 0; attempts < this.connectionPool.length; attempts++) {
        const nextConnection = this.connectionPool[this.currentIndex];
        this.currentIndex = (this.currentIndex + 1) % this.connectionPool.length;
        
        if (nextConnection.status === 'ready' || nextConnection.status === 'connecting') {
          return nextConnection;
        }
      }
      
      // 如果所有连接都不可用，返回第一个连接并记录警告
      logger.warn('[RedisPool] All connections unhealthy, returning first connection');
      return this.connectionPool[0];
    }

    return connection;
  }

  /**
   * 获取健康的连接
   */
  async getHealthyConnection(): Promise<Redis> {
    // 先尝试获取一个可用连接
    const connection = this.getConnection();
    
    try {
      // 快速健康检查
      await connection.ping();
      return connection;
    } catch (error) {
      logger.warn('[RedisPool] Connection ping failed, trying alternatives', {
        error: error instanceof Error ? error.message : String(error)
      });
      
      // 如果ping失败，尝试其他连接
      for (const altConnection of this.connectionPool) {
        try {
          await altConnection.ping();
          return altConnection;
        } catch (altError) {
          continue;
        }
      }
      
      // 如果所有连接都失败，抛出错误
      throw new Error('No healthy Redis connections available');
    }
  }

  /**
   * 执行Redis命令（自动重试和故障转移）
   */
  async executeCommand<T>(
    command: (redis: Redis) => Promise<T>,
    maxRetries = 3
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        const connection = await this.getHealthyConnection();
        return await command(connection);
      } catch (error) {
        lastError = error as Error;
        
        logger.warn('[RedisPool] Command execution failed, retrying', {
          attempt: attempt + 1,
          maxRetries,
          error: lastError.message
        });
        
        // 等待一段时间再重试
        if (attempt < maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, 100 * (attempt + 1)));
        }
      }
    }
    
    throw lastError!;
  }

  /**
   * 健康检查
   */
  async performHealthCheck(): Promise<PoolStats> {
    const startTime = Date.now();
    let healthyCount = 0;
    let failedCount = 0;

    const healthPromises = this.connectionPool.map(async (redis, index) => {
      try {
        await redis.ping();
        healthyCount++;
        return { index, status: 'healthy' };
      } catch (error) {
        failedCount++;
        return { 
          index, 
          status: 'failed', 
          error: error instanceof Error ? error.message : String(error) 
        };
      }
    });

    const results = await Promise.allSettled(healthPromises);
    
    this.poolStats = {
      totalConnections: this.connectionPool.length,
      activeConnections: this.countActiveConnections(),
      healthyConnections: healthyCount,
      failedConnections: failedCount,
      lastHealthCheck: new Date()
    };

    const duration = Date.now() - startTime;
    
    logger.info('[RedisPool] Health check completed', {
      ...this.poolStats,
      duration: `${duration}ms`,
      healthCheckResults: results.length
    });

    // 如果失败连接过多，记录警告
    if (failedCount > this.poolConfig.poolSize / 2) {
      logger.warn('[RedisPool] High number of failed connections detected', {
        failedCount,
        totalCount: this.poolConfig.poolSize,
        failureRate: `${Math.round((failedCount / this.poolConfig.poolSize) * 100)}%`
      });
    }

    return this.poolStats;
  }

  /**
   * 启动定期健康检查
   */
  private startHealthCheck() {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }

    this.healthCheckTimer = setInterval(async () => {
      try {
        await this.performHealthCheck();
      } catch (error) {
        logger.error('[RedisPool] Health check failed', {
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }, this.poolConfig.healthCheckInterval);

    logger.info('[RedisPool] Health check started', {
      interval: `${this.poolConfig.healthCheckInterval}ms`
    });
  }

  /**
   * 更新活动连接数
   */
  private updateActiveConnections() {
    this.poolStats.activeConnections = this.countActiveConnections();
  }

  /**
   * 计算活动连接数
   */
  private countActiveConnections(): number {
    return this.connectionPool.filter(redis => 
      redis.status === 'ready' || redis.status === 'connecting'
    ).length;
  }

  /**
   * 获取连接池统计信息
   */
  getStats(): PoolStats {
    this.updateActiveConnections();
    return { ...this.poolStats };
  }

  /**
   * 关闭连接池
   */
  async shutdown(): Promise<void> {
    logger.info('[RedisPool] Shutting down connection pool');
    
    // 停止健康检查
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }

    // 关闭所有连接
    const closePromises = this.connectionPool.map(async (redis, index) => {
      try {
        await redis.disconnect();
        logger.debug('[RedisPool] Connection closed', { connectionIndex: index });
      } catch (error) {
        logger.error('[RedisPool] Error closing connection', {
          connectionIndex: index,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    });

    await Promise.allSettled(closePromises);
    this.connectionPool = [];
    
    logger.info('[RedisPool] Connection pool shutdown completed');
  }
}

/**
 * 便捷函数：获取连接池实例
 */
export function getRedisPool(): RedisConnectionPool {
  return RedisConnectionPool.getInstance();
}

/**
 * 便捷函数：获取Redis连接
 */
export function getRedisConnection(): Redis {
  return RedisConnectionPool.getInstance().getConnection();
}

/**
 * 便捷函数：执行Redis命令
 */
export async function executeRedisCommand<T>(
  command: (redis: Redis) => Promise<T>
): Promise<T> {
  return RedisConnectionPool.getInstance().executeCommand(command);
}