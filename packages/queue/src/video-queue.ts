import { Queue, QueueOptions } from 'bullmq';
import { logger } from '@repo/logs';
import { createRedisConnection } from './redis-config';

export interface VideoProcessingJobData {
  event?: any; // 单个 VideoWebhookEvent 对象
  events?: any[]; // 批量 VideoWebhookEvent 对象数组
  batchMode?: boolean; // 是否为批处理模式
}

/**
 * 视频处理队列
 */
export class VideoQueue extends Queue<VideoProcessingJobData> {
  constructor(options?: Partial<QueueOptions>) {
    super('video-processing', {
      connection: createRedisConnection(),
      defaultJobOptions: {
        // 🔑 防重复配置：保留更多完成任务用于去重检查
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 5000
        },
        // 📋 重复任务检测配置
        jobId: undefined,           // 确保每个job有唯一ID
        delay: 0,
        priority: 0,
        repeat: undefined,
      },
      
      // 🛡️ 队列级别防重复设置
      settings: {
        stalledInterval: 30000,     // 30秒检查停滞任务
        maxStalledCount: 1,         // 最多允许1次停滞
        retryProcessDelay: 5000,    // 重试延迟5秒
      },
      
      ...options
    });

    this.setupEventListeners();
  }

  /**
   * 添加单个视频处理任务（增强防重复检查）
   */
  async addVideoProcessing(event: any, priority = 1): Promise<string | null> {
    const jobId = `video-${event.id}`;
    
    try {
      // 📋 检查是否已存在相同job
      const existingJob = await this.getJob(jobId);
      if (existingJob) {
        logger.warn('[VideoQueue] Job already exists, skipping duplicate', {
          jobId,
          existingStatus: await existingJob.getState(),
          eventId: event.id
        });
        return null; // 返回null表示跳过重复任务
      }
      
      // 添加新任务
      const job = await this.add('process-video', { event }, {
        jobId,
        priority
      });

      logger.info('[VideoQueue] Single video job added to queue', { 
        jobId: job.id, 
        eventId: event.id
      });

      return job.id!;
    } catch (error) {
      if (error instanceof Error && error.message.includes('duplicate key')) {
        logger.warn('[VideoQueue] Duplicate job detected at Redis level', {
          jobId,
          eventId: event.id
        });
        return null;
      }
      
      logger.error('[VideoQueue] Failed to add single video job to queue', { 
        eventId: event.id, 
        error: error instanceof Error ? error.message : String(error) 
      });
      throw error;
    }
  }

  /**
   * 添加批量视频处理任务
   */
  async addBatchVideoProcessing(events: any[], priority = 2): Promise<string> {
    try {
      const batchId = `batch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      const job = await this.add('process-video-batch', { 
        events, 
        batchMode: true 
      }, {
        jobId: batchId,
        priority
      });

      logger.info('[VideoQueue] Batch video job added to queue', { 
        jobId: job.id, 
        eventCount: events.length,
        batchId
      });

      return job.id!;
    } catch (error) {
      logger.error('[VideoQueue] Failed to add batch video job to queue', { 
        eventCount: events.length,
        error: error instanceof Error ? error.message : String(error) 
      });
      throw error;
    }
  }

  /**
   * 获取队列统计信息
   */
  async getStats() {
    const [waiting, active, completed, failed] = await Promise.all([
      this.getWaiting(),
      this.getActive(), 
      this.getCompleted(),
      this.getFailed()
    ]);

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length
    };
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners() {
    this.on('error', (error: Error) => {
      logger.error('[VideoQueue] Queue error', { error: error.message });
    });

    this.on('waiting', (job: any) => {
      logger.debug('[VideoQueue] Job waiting', { jobId: job.id });
    });

    // 移除 stalled 事件监听，因为它在 Queue 中不可用
    // this.on('stalled', (jobId: string) => {
    //   logger.warn('[VideoQueue] Job stalled', { jobId });
    // });
  }
}