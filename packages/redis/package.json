{"name": "@repo/redis", "version": "0.0.0", "main": "./index.ts", "types": "./index.ts", "exports": {".": {"types": "./index.ts", "default": "./index.ts"}}, "scripts": {"type-check": "tsc --noEmit"}, "dependencies": {"ioredis": "^5.6.1", "@repo/database": "workspace:*", "@repo/logs": "workspace:*"}, "devDependencies": {"@biomejs/biome": "2.1.1", "@repo/tsconfig": "workspace:*", "@types/node": "^24.0.13"}}