import { Redis } from 'ioredis';

// 创建Redis连接 - 使用原生Redis协议，极致性能优化
function createOptimizedRedisConnection(): Redis {
  if (process.env.REDIS_URL) {
    return new Redis(process.env.REDIS_URL, {
      enableReadyCheck: false,
      maxRetriesPerRequest: null,
      lazyConnect: true,  // 延迟连接，减少启动时间
      keepAlive: 30000,   // 30秒保持连接活跃
      // Upstash极致优化配置 - 追求极限速度
      ...(process.env.REDIS_URL.includes('upstash.io') && {
        connectTimeout: 1000,   // 极限连接超时 - 1秒
        commandTimeout: 800,    // 极限命令超时 - 800ms  
        retryDelayOnFailover: 50,   // 超快故障转移
        enableOfflineQueue: false,  // 禁用离线队列减少延迟
        enableAutoPipelining: true, // 自动pipeline优化
        tls: {
          checkServerIdentity: () => undefined,  // 跳过证书验证以提升性能
          rejectUnauthorized: false  // 进一步减少TLS验证开销
        }
      })
    });
  }
  
  throw new Error('REDIS_URL environment variable is required');
}

// 创建连接池以提高并发性能
const connectionPool: Redis[] = [];
const POOL_SIZE = 3; // 小型连接池，适合点赞场景

function getConnectionFromPool(): Redis {
  if (connectionPool.length === 0) {
    // 如果池为空，初始化连接池
    for (let i = 0; i < POOL_SIZE; i++) {
      connectionPool.push(createOptimizedRedisConnection());
    }
  }
  
  // 轮询获取连接
  const connection = connectionPool.shift();
  if (connection) {
    connectionPool.push(connection); // 放回池中
    return connection;
  }
  
  // 后备方案
  return createOptimizedRedisConnection();
}

// 导出Redis实例 - 使用连接池
export const redis = getConnectionFromPool();

// 导出专用的快速连接获取函数
export function getFastRedisConnection(): Redis {
  return getConnectionFromPool();
}

// 连接测试
export async function testRedisConnection() {
  try {
    const result = await redis.ping();
    console.log(`✅ Redis connected successfully (Pool size: ${POOL_SIZE}):`, result);
    return true;
  } catch (error) {
    console.error('❌ Redis connection failed:', error);
    return false;
  }
}