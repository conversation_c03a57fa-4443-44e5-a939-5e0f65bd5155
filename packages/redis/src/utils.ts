import { redis } from './redis-client';

export class RedisUtils {
  static async cacheJobStatus(jobId: string, status: any, ttl: number = 3600) {
    await redis.setex(`job:${jobId}`, ttl, JSON.stringify(status));
  }
  
  static async getCachedJobStatus(jobId: string) {
    const cached = await redis.get(`job:${jobId}`);
    return cached ? JSON.parse(cached as string) : null;
  }
  
  static async clearJobCache(jobId: string) {
    await redis.del(`job:${jobId}`);
  }
  
  static async incrementCounter(key: string, ttl: number = 60) {
    const count = await redis.incr(key);
    if (count === 1) {
      await redis.expire(key, ttl);
    }
    return count;
  }
}