import path from "path";
import { randomUUID } from "crypto";
import { CONTENT_TYPE_MAP, SUPPORTED_IMAGE_TYPES, SUPPORTED_VIDEO_TYPES } from "../types";

/**
 * Generate a unique filename with timestamp and UUID
 */
export const generateFileName = (originalName: string): string => {
	const timestamp = Date.now();
	const uuid = randomUUID();
	const ext = path.extname(originalName).toLowerCase();
	return `${timestamp}-${uuid}${ext}`;
};

/**
 * Generate file path for R2 storage
 */
export const generateFilePath = (
	userId: string,
	type: 'image' | 'video' | 'temp',
	filename: string,
	environment: string = 'production'
): string => {
	return `web-cdn/${environment}/${userId}/${type}/${filename}`;
};

/**
 * Get content type from file extension
 */
export const getContentType = (filename: string): string => {
	const ext = path.extname(filename).toLowerCase();
	return CONTENT_TYPE_MAP[ext] || 'application/octet-stream';
};

/**
 * Validate file type based on the upload type
 */
export const validateFileType = (filename: string, type: 'image' | 'video'): void => {
	const ext = path.extname(filename).toLowerCase();
	
	if (type === 'image' && !SUPPORTED_IMAGE_TYPES.includes(ext)) {
		throw new Error(`Unsupported image format. Supported: ${SUPPORTED_IMAGE_TYPES.join(', ')}`);
	}
	
	if (type === 'video' && !SUPPORTED_VIDEO_TYPES.includes(ext)) {
		throw new Error(`Unsupported video format. Supported: ${SUPPORTED_VIDEO_TYPES.join(', ')}`);
	}
};

/**
 * Validate file size (in bytes)
 */
export const validateFileSize = (filename: string, maxSizeImage: number = 10 * 1024 * 1024, maxSizeVideo: number = 100 * 1024 * 1024): void => {
	const ext = path.extname(filename).toLowerCase();
	
	if (SUPPORTED_IMAGE_TYPES.includes(ext)) {
		// For images, we can't validate size from filename alone
		// This would be validated on the client side before upload
		return;
	}
	
	if (SUPPORTED_VIDEO_TYPES.includes(ext)) {
		// For videos, we can't validate size from filename alone
		// This would be validated on the client side before upload
		return;
	}
}; 