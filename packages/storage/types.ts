export type CreateBucketHandler = (
	name: string,
	options?: {
		public?: boolean;
	},
) => Promise<void>;

export type GetSignedUploadUrlHandler = (
	path: string,
	options: {
		bucket: string;
		expiresIn?: number;
		contentType?: string;
	},
) => Promise<string>;

export type GetSignedUrlHander = (
	path: string,
	options: {
		bucket: string;
		expiresIn?: number;
	},
) => Promise<string>;

// Upload API types
export interface UploadSignRequest {
	filename: string;
	type: 'image' | 'video';
}

export interface UploadSignResponse {
	sign: string;
	expired: number;
	accessURL: string;
}

// File type mappings
export const CONTENT_TYPE_MAP: Record<string, string> = {
	'.jpg': 'image/jpeg',
	'.jpeg': 'image/jpeg',
	'.png': 'image/png',
	'.webp': 'image/webp',
	'.mp4': 'video/mp4',
	'.mov': 'video/quicktime',
	'.avi': 'video/x-msvideo'
};

// Supported file extensions
export const SUPPORTED_IMAGE_TYPES = ['.jpg', '.jpeg', '.png', '.webp'];
export const SUPPORTED_VIDEO_TYPES = ['.mp4', '.mov', '.avi'];
