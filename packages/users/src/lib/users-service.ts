import { db } from "@repo/database";
import { createId } from "@paralleldrive/cuid2";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import type { UserCreateResult, UserInfo } from "../../types";

/**
 * 查找用户的最后积分重置时间
 * @param userId 用户ID
 * @returns 包含最后积分重置时间的 CreditUsage 记录，如果不存在则返回 null
 */
export async function findUserLastCreditReset(userId: string) {
	const creditUsage = await db.creditUsage.findUnique({
		where: { userId },
		select: { id: true, lastCreditReset: true },
	});
	
	return creditUsage;
}

/**
 * 创建新用户
 * @param userInfo 用户信息
 * @returns 创建结果
 */
export async function createUser(
	userInfo: UserInfo,
): Promise<UserCreateResult> {
	try {
		// 检查用户是否已存在
		const existingUser = await db.user.findUnique({
			where: { id: userInfo.id },
			select: { id: true },
		});

		if (existingUser) {
			return {
				success: false,
				userId: userInfo.id,
				error: "User already exists",
			};
		}

		// 创建新用户
		await db.user.create({
			data: {
				id: userInfo.id,
				email: userInfo.email || "",
				name: userInfo.name || "",
				emailVerified: userInfo.emailVerified || true,
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		});

		// 创建用户的积分使用记录
		dayjs.extend(utc);
		const now = dayjs().utc().toDate();
		
		await db.creditUsage.create({
			data: {
				id: createId(),
				userId: userInfo.id,
				balance: 5, // 默认积分
				used: 0,
				lastCreditReset: now,
				createdAt: now,
				updatedAt: now,
			},
		});

		return {
			success: true,
			userId: userInfo.id,
		};
	} catch (error) {
		return {
			success: false,
			userId: userInfo.id,
			error: error instanceof Error ? error.message : String(error),
		};
	}
}

/**
 * 更新用户积分重置时间
 * @param userId 用户ID
 * @param resetTime 重置时间，默认为当前时间
 * @returns 是否成功更新
 */
export async function updateUserCreditResetTime(
	userId: string,
	resetTime: Date = new Date(),
): Promise<boolean> {
	try {
		dayjs.extend(utc);
		const now = dayjs().utc().toDate();
		
		await db.creditUsage.upsert({
			where: { userId },
			update: { 
				lastCreditReset: resetTime,
				updatedAt: now,
			},
			create: {
				id: createId(),
				userId,
				balance: 5,
				used: 0,
				lastCreditReset: resetTime,
				createdAt: now,
				updatedAt: now,
			},
		});
		return true;
	} catch (error) {
		console.error("Failed to update user credit reset time:", error);
		return false;
	}
}

/**
 * 获取用户的最后积分重置时间
 * @param userId 用户ID
 * @returns 最后重置时间或null
 */
export async function getUserCreditResetTime(
	userId: string,
): Promise<Date | null> {
	const creditUsage = await db.creditUsage.findUnique({
		where: { userId },
		select: { lastCreditReset: true },
	});

	return creditUsage?.lastCreditReset || null;
}

/**
 * 获取用户信息
 * @param userId 用户ID
 * @returns 用户信息或null
 */
export async function getUser(userId: string) {
	return await db.user.findUnique({
		where: { id: userId },
	});
}

/**
 * 确保用户存在，如果不存在则创建
 * @param userInfo 用户信息
 * @returns 用户信息
 */
export async function ensureUserExists(userInfo: UserInfo): Promise<UserInfo> {
	const user = await getUser(userInfo.id);

	if (!user) {
		await createUser(userInfo);
		return userInfo;
	}

	return user;
}
