/**
 * 积分计算工具模块
 * 提供统一的积分计算逻辑，供前后端共享使用
 */

/**
 * 积分计算输入参数
 */
export interface CreditCalculationInput {
  modelConfig: {
    pricingConfig?: {
      baseCredits: number;
      multipliers: {
        videoLength?: Record<string, number>;
        mode?: Record<string, number>;
        resolution?: Record<string, number>;
        style?: Record<string, number>;
      };
    };
  };
  params: {
    duration?: number;
    modeCode?: string;
    resolution?: string;
    style?: string;
  };
}

/**
 * 应用的乘数信息
 */
export interface AppliedMultiplier {
  type: 'videoLength' | 'mode' | 'resolution' | 'style';
  value: string;
  multiplier: number;
}

/**
 * 积分计算结果
 */
export interface CreditCalculationResult {
  totalCredits: number;
  appliedMultipliers: AppliedMultiplier[];
}

/**
 * 积分计算错误
 */
export class CreditCalculationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'CreditCalculationError';
  }
}

/**
 * 计算积分消耗
 * @param input 计算输入参数
 * @returns 计算结果
 * @throws CreditCalculationError 当配置缺失或无效时
 */
export function calculateCredits(input: CreditCalculationInput): CreditCalculationResult {
  const { modelConfig, params } = input;
  
  if (!modelConfig.pricingConfig) {
    throw new CreditCalculationError("No pricing configuration found for this model");
  }

  const { baseCredits, multipliers } = modelConfig.pricingConfig;
  let totalCredits = baseCredits;
  const appliedMultipliers: AppliedMultiplier[] = [];

  // 应用视频长度乘数
  if (multipliers.videoLength && params.duration) {
    const lengthMultiplier = multipliers.videoLength[params.duration.toString()];
    if (lengthMultiplier) {
      totalCredits *= lengthMultiplier;
      appliedMultipliers.push({
        type: 'videoLength',
        value: params.duration.toString(),
        multiplier: lengthMultiplier
      });
    }
  }

  // 应用模式乘数
  if (multipliers.mode && params.modeCode) {
    const modeMultiplier = multipliers.mode[params.modeCode];
    if (modeMultiplier) {
      totalCredits *= modeMultiplier;
      appliedMultipliers.push({
        type: 'mode',
        value: params.modeCode,
        multiplier: modeMultiplier
      });
    }
  }

  // 应用分辨率乘数
  if (multipliers.resolution && params.resolution) {
    const resolutionMultiplier = multipliers.resolution[params.resolution];
    if (resolutionMultiplier) {
      totalCredits *= resolutionMultiplier;
      appliedMultipliers.push({
        type: 'resolution',
        value: params.resolution,
        multiplier: resolutionMultiplier
      });
    }
  }

  // 应用风格乘数
  if (multipliers.style && params.style) {
    const styleMultiplier = multipliers.style[params.style];
    if (styleMultiplier) {
      totalCredits *= styleMultiplier;
      appliedMultipliers.push({
        type: 'style',
        value: params.style,
        multiplier: styleMultiplier
      });
    }
  }

  return {
    totalCredits: Math.ceil(totalCredits),
    appliedMultipliers
  };
}

/**
 * 计算多个输出的总积分
 * @param input 计算输入参数
 * @param numOutputs 输出数量
 * @returns 总积分
 */
export function calculateTotalCredits(
  input: CreditCalculationInput, 
  numOutputs: number
): number {
  const result = calculateCredits(input);
  return result.totalCredits * numOutputs;
}