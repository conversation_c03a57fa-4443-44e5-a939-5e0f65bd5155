/**
 * 生成规范化URL（canonical URL）
 * 英文页面不包含语言代码，其他语言保留语言代码
 */
export function generateCanonicalUrl(locale: string, path = "") {
	const base = process.env.NEXT_PUBLIC_SITE_URL || "https://fluxfly.ai";

	// 英文页面不包含语言代码
	if (locale === "en") {
		return path ? `${base}/${path}` : base;
	}

	// 其他语言保留语言代码
	return path ? `${base}/${locale}/${path}` : `${base}/${locale}`;
}

/**
 * 生成所有支持语言的 hreflang URL
 * 返回一个对象，键为语言代码，值为对应的URL
 * 包含 x-default 指向英文版本
 */
export function generateHreflangUrls(path = "") {
	const base = process.env.NEXT_PUBLIC_SITE_URL || "https://fluxfly.ai";

	// 动态导入配置获取所有支持的语言
	let supportedLocales = ["en"]; // 默认值
	try {
		// 使用require来动态获取配置，避免循环依赖
		const { config } = require("@repo/config");
		supportedLocales = Object.keys(config.i18n.locales);
	} catch (error) {
		// 如果无法加载配置，使用默认值
		console.warn(
			"Unable to load config for supported locales, using default:",
			error,
		);
	}

	const urls: Record<string, string> = {};

	// 为每种语言生成URL
	supportedLocales.forEach((locale) => {
		if (locale === "en") {
			urls[locale] = path ? `${base}/${path}` : base;
		} else {
			urls[locale] = path
				? `${base}/${locale}/${path}`
				: `${base}/${locale}`;
		}
	});

	// 添加 x-default，指向英文版本
	urls["x-default"] = urls.en;

	return urls;
}
