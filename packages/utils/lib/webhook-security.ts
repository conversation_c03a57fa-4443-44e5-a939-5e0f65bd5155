import * as crypto from 'crypto';

/**
 * Webhook Security Utils
 * 
 * 提供基于 HMAC 的 webhook 安全 token 生成和验证功能
 * 用于确保 webhook 请求的安全性和防止重放攻击
 */

export interface WebhookTokenData {
  userId: string;
  taskId: string;
  timestamp: number;
}

export interface WebhookMetadata extends WebhookTokenData {
  provider?: string;
  generationId?: string;
  organizationId?: string;
  featureCode?: string;
  [key: string]: any; // 其他业务数据
}

export interface GenerateTokenOptions {
  userId: string;
  taskId: string;
  secretKey: string;
  businessData?: Record<string, any>; // 额外的业务数据
}

export interface VerifyTokenOptions {
  userId: string;
  taskId: string;
  timestamp: number;
  receivedToken: string;
  secretKey: string;
  maxAgeMinutes?: number; // 默认 20 分钟
}

/**
 * 生成 webhook 安全 token
 * 
 * @param options 生成选项
 * @returns 包含 token 和时间戳的对象
 */
export function generateWebhookToken(options: GenerateTokenOptions): { token: string; timestamp: number } {
  const timestamp = Math.floor(Date.now() / 1000); // Unix timestamp in seconds
  const message = `${options.userId}.${options.taskId}.${timestamp}`;
  
  const signature = crypto
    .createHmac('sha256', options.secretKey)
    .update(message)
    .digest('base64url'); // 使用 base64url 编码，URL 安全
  
  return {
    token: signature,
    timestamp
  };
}

/**
 * 验证 webhook token
 * 
 * @param options 验证选项
 * @returns 验证结果
 */
export function verifyWebhookToken(options: VerifyTokenOptions): {
  valid: boolean;
  reason?: string;
} {
  const { userId, taskId, timestamp, receivedToken, secretKey, maxAgeMinutes = 20 } = options;
  
  try {
    // 1. 检查时间戳是否在有效期内
    const currentTime = Math.floor(Date.now() / 1000);
    const maxAge = maxAgeMinutes * 60; // 转换为秒
    
    if (currentTime - timestamp > maxAge) {
      return {
        valid: false,
        reason: `Token expired. Age: ${currentTime - timestamp}s, Max age: ${maxAge}s`
      };
    }
    
    if (timestamp > currentTime + 60) { // 允许 1 分钟的时钟偏差
      return {
        valid: false,
        reason: 'Token timestamp is in the future'
      };
    }
    
    // 2. 重新生成预期的 token
    const message = `${userId}.${taskId}.${timestamp}`;
    const expectedToken = crypto
      .createHmac('sha256', secretKey)
      .update(message)
      .digest('base64url');
    
    // 3. 使用时间安全的比较
    const isValid = crypto.timingSafeEqual(
      Buffer.from(receivedToken, 'base64url'),
      Buffer.from(expectedToken, 'base64url')
    );
    
    if (!isValid) {
      return {
        valid: false,
        reason: 'Token signature mismatch'
      };
    }
    
    return { valid: true };
    
  } catch (error) {
    return {
      valid: false,
      reason: `Token validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * 从 input 数据中提取 token 相关信息 (支持metadata格式)
 * 
 * @param input Replicate input 参数
 * @returns 提取的 token 数据，如果不存在则返回 null
 */
export function extractTokenFromInput(input: any): WebhookTokenData | null {
  if (!input || typeof input !== 'object') {
    return null;
  }
  
  // 优先从metadata中提取 (新格式)
  if (input.metadata && typeof input.metadata === 'object') {
    const { webhook_user_id, webhook_task_id, webhook_timestamp } = input.metadata;
    if (webhook_user_id && webhook_task_id && webhook_timestamp) {
      return {
        userId: webhook_user_id,
        taskId: webhook_task_id,
        timestamp: Number(webhook_timestamp)
      };
    }
  }
  
  // 回退到直接从input中提取 (旧格式，向后兼容)
  const { webhook_user_id, webhook_task_id, webhook_timestamp } = input;
  
  if (!webhook_user_id || !webhook_task_id || !webhook_timestamp) {
    return null;
  }
  
  return {
    userId: webhook_user_id,
    taskId: webhook_task_id,
    timestamp: Number(webhook_timestamp)
  };
}

/**
 * 从 input 数据中提取完整的 metadata 信息
 * 
 * @param input Replicate input 参数
 * @returns 提取的完整 metadata，如果不存在则返回 null
 */
export function extractMetadataFromInput(input: any): WebhookMetadata | null {
  if (!input || typeof input !== 'object') {
    return null;
  }
  
  // 优先从metadata中提取 (新格式)
  if (input.metadata && typeof input.metadata === 'object') {
    const metadata = input.metadata;
    const { user_id, generation_id } = metadata;
    
    if (user_id && generation_id) {
      return {
        userId: user_id,
        taskId: generation_id,
        timestamp: Date.now() / 1000, // 当前时间戳
        provider: metadata.provider,
        generationId: generation_id,
        organizationId: metadata.organization_id,
        featureCode: metadata.feature_code,
        ...metadata // 包含所有其他字段
      };
    }
  }
  
  // 回退到旧格式 (向后兼容)
  const tokenData = extractTokenFromInput(input);
  if (tokenData) {
    return {
      ...tokenData,
      generationId: tokenData.taskId // 旧格式下taskId就是generationId
    };
  }
  
  return null;
}

/**
 * 为 Replicate input 添加安全 token 参数 (新的metadata格式)
 * 
 * @param input 原始 input 参数
 * @param options token 生成选项
 * @returns 添加了metadata包装的安全参数的 input
 */
export function addSecurityToInput(input: any, options: GenerateTokenOptions): any {
  const { token, timestamp } = generateWebhookToken(options);
  
  return {
    ...input,
    metadata: {
      // 安全相关信息
      webhook_user_id: options.userId,
      webhook_task_id: options.taskId,
      webhook_timestamp: timestamp,
      webhook_token: token,
      
      // 额外的业务数据
      ...(options.businessData || {})
    }
  };
}

/**
 * 为 Replicate input 添加安全 token 参数 (旧格式，向后兼容)
 * 
 * @param input 原始 input 参数
 * @param options token 生成选项
 * @returns 添加了安全参数的 input
 * @deprecated 请使用新的metadata格式的addSecurityToInput
 */
export function addSecurityToInputLegacy(input: any, options: GenerateTokenOptions): any {
  const { token, timestamp } = generateWebhookToken(options);
  
  return {
    ...input,
    webhook_user_id: options.userId,
    webhook_task_id: options.taskId,
    webhook_timestamp: timestamp,
    webhook_token: token
  };
}