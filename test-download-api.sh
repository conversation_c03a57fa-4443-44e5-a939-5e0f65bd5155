#!/bin/bash

# 测试下载API的简单脚本

echo "Testing Download API..."

# 测试带水印下载
echo "1. Testing watermark download..."
curl -X GET "http://localhost:3000/api/generations/videos/test-generation-id/downloads/watermark" \
  -H "Authorization: Bearer test-token" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n" \
  -s

echo -e "\n"

# 测试无水印下载
echo "2. Testing no-watermark download..."
curl -X GET "http://localhost:3000/api/generations/videos/test-generation-id/downloads/no-watermark" \
  -H "Authorization: Bearer test-token" \
  -H "Content-Type: application/json" \
  -w "\nStatus: %{http_code}\n" \
  -s

echo -e "\n"
echo "API endpoint testing completed."